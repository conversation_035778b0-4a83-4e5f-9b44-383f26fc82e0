// MongoDB Initialization Script for Çiftçi Not Defterim
// This script runs when MongoDB container starts for the first time

print('🌱 Çiftçi Not Defterim MongoDB initialization started...');

// Switch to the application database
db = db.getSiblingDB('ciftci-notebook');

// Create application user
db.createUser({
  user: 'ciftci-user',
  pwd: 'ciftci-password',
  roles: [
    {
      role: 'readWrite',
      db: 'ciftci-notebook'
    }
  ]
});

print('✅ Application user created: ciftci-user');

// Create collections with validation
db.createCollection('users', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['firebaseUid', 'email', 'name'],
      properties: {
        firebaseUid: {
          bsonType: 'string',
          description: 'Firebase UID is required'
        },
        email: {
          bsonType: 'string',
          pattern: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$',
          description: 'Valid email is required'
        },
        name: {
          bsonType: 'string',
          minLength: 2,
          maxLength: 100,
          description: 'Name must be 2-100 characters'
        },
        status: {
          enum: ['active', 'inactive', 'suspended', 'deleted'],
          description: 'Status must be one of the enum values'
        }
      }
    }
  }
});

db.createCollection('categories', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['name', 'emoji', 'color'],
      properties: {
        name: {
          bsonType: 'string',
          minLength: 2,
          maxLength: 50,
          description: 'Category name must be 2-50 characters'
        },
        emoji: {
          bsonType: 'string',
          description: 'Emoji is required'
        },
        color: {
          bsonType: 'string',
          pattern: '^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$',
          description: 'Color must be a valid hex color'
        }
      }
    }
  }
});

db.createCollection('expenses', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['userId', 'categoryId', 'amount', 'date', 'seasonId'],
      properties: {
        userId: {
          bsonType: 'objectId',
          description: 'User ObjectId is required'
        },
        categoryId: {
          bsonType: 'objectId',
          description: 'Category ObjectId is required'
        },
        amount: {
          bsonType: 'number',
          minimum: 0,
          maximum: 1000000,
          description: 'Amount must be between 0 and 1,000,000'
        },
        date: {
          bsonType: 'date',
          description: 'Date is required'
        },
        seasonId: {
          bsonType: 'objectId',
          description: 'Season ObjectId is required'
        },
        legacySeasonId: {
          enum: ['spring', 'summer', 'autumn', 'winter', null],
          description: 'Legacy season ID for migration support'
        },
        description: {
          bsonType: 'string',
          maxLength: 500,
          description: 'Description must be max 500 characters'
        },
        status: {
          enum: ['active', 'deleted', 'archived'],
          description: 'Status must be one of the enum values'
        }
      }
    }
  }
});

print('✅ Collections created with validation rules');
print('📝 Schema Documentation:');
print('   - expenses collection now accepts ObjectId for seasonId field');
print('   - legacySeasonId field added for migration support');
print('   - seasonId is now required field with ObjectId validation');

// Create indexes for performance
db.users.createIndex({ firebaseUid: 1 }, { unique: true });
db.users.createIndex({ email: 1 }, { unique: true });
db.users.createIndex({ status: 1, type: 1 });
db.users.createIndex({ createdAt: -1 });

db.categories.createIndex({ userId: 1, isActive: 1 });
db.categories.createIndex({ isDefault: 1, isActive: 1 });
db.categories.createIndex({ usageCount: -1 });
db.categories.createIndex({ name: 'text', description: 'text' });

db.expenses.createIndex({ userId: 1, date: -1 });
db.expenses.createIndex({ userId: 1, categoryId: 1 });
db.expenses.createIndex({ userId: 1, seasonId: 1 });
db.expenses.createIndex({ userId: 1, status: 1, date: -1 });
db.expenses.createIndex({ date: -1, amount: -1 });
db.expenses.createIndex({ description: 'text', notes: 'text' });

print('✅ Database indexes created');

// Insert default categories
const defaultCategories = [
  {
    name: 'Tohum',
    emoji: '🌱',
    color: '#4CAF50',
    icon: 'seed',
    description: 'Tohum ve fide giderleri',
    isDefault: true,
    isActive: true,
    sortOrder: 0,
    seasonalRelevance: [
      { season: 'spring', relevanceScore: 10 },
      { season: 'summer', relevanceScore: 3 },
      { season: 'autumn', relevanceScore: 7 },
      { season: 'winter', relevanceScore: 2 }
    ],
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    name: 'Gübre',
    emoji: '🧪',
    color: '#795548',
    icon: 'fertilizer',
    description: 'Gübre ve besin giderleri',
    isDefault: true,
    isActive: true,
    sortOrder: 1,
    seasonalRelevance: [
      { season: 'spring', relevanceScore: 9 },
      { season: 'summer', relevanceScore: 7 },
      { season: 'autumn', relevanceScore: 6 },
      { season: 'winter', relevanceScore: 3 }
    ],
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    name: 'İlaç',
    emoji: '💊',
    color: '#F44336',
    icon: 'medicine',
    description: 'Pestisit ve ilaç giderleri',
    isDefault: true,
    isActive: true,
    sortOrder: 2,
    seasonalRelevance: [
      { season: 'spring', relevanceScore: 8 },
      { season: 'summer', relevanceScore: 10 },
      { season: 'autumn', relevanceScore: 6 },
      { season: 'winter', relevanceScore: 2 }
    ],
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    name: 'Su',
    emoji: '💧',
    color: '#2196F3',
    icon: 'water',
    description: 'Sulama ve su giderleri',
    isDefault: true,
    isActive: true,
    sortOrder: 3,
    seasonalRelevance: [
      { season: 'spring', relevanceScore: 6 },
      { season: 'summer', relevanceScore: 10 },
      { season: 'autumn', relevanceScore: 4 },
      { season: 'winter', relevanceScore: 2 }
    ],
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    name: 'Yakıt',
    emoji: '⛽',
    color: '#FF9800',
    icon: 'fuel',
    description: 'Yakıt ve enerji giderleri',
    isDefault: true,
    isActive: true,
    sortOrder: 4,
    seasonalRelevance: [
      { season: 'spring', relevanceScore: 8 },
      { season: 'summer', relevanceScore: 9 },
      { season: 'autumn', relevanceScore: 9 },
      { season: 'winter', relevanceScore: 5 }
    ],
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    name: 'İşçilik',
    emoji: '👷',
    color: '#9C27B0',
    icon: 'worker',
    description: 'İşçi ve işçilik giderleri',
    isDefault: true,
    isActive: true,
    sortOrder: 5,
    seasonalRelevance: [
      { season: 'spring', relevanceScore: 9 },
      { season: 'summer', relevanceScore: 8 },
      { season: 'autumn', relevanceScore: 10 },
      { season: 'winter', relevanceScore: 4 }
    ],
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    name: 'Makine',
    emoji: '🚜',
    color: '#607D8B',
    icon: 'tractor',
    description: 'Makine ve ekipman giderleri',
    isDefault: true,
    isActive: true,
    sortOrder: 6,
    seasonalRelevance: [
      { season: 'spring', relevanceScore: 8 },
      { season: 'summer', relevanceScore: 7 },
      { season: 'autumn', relevanceScore: 9 },
      { season: 'winter', relevanceScore: 6 }
    ],
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    name: 'Depolama',
    emoji: '📦',
    color: '#8BC34A',
    icon: 'storage',
    description: 'Depolama ve ambar giderleri',
    isDefault: true,
    isActive: true,
    sortOrder: 7,
    seasonalRelevance: [
      { season: 'spring', relevanceScore: 4 },
      { season: 'summer', relevanceScore: 6 },
      { season: 'autumn', relevanceScore: 10 },
      { season: 'winter', relevanceScore: 8 }
    ],
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

db.categories.insertMany(defaultCategories);
print('✅ Default categories inserted: ' + defaultCategories.length + ' categories');

// Insert default seasons
const defaultSeasons = [
  {
    id: 'spring',
    name: 'Bahar',
    description: 'Bahar dönemi tarımsal faaliyetleri',
    startMonth: 3,
    endMonth: 5,
    color: '#4CAF50',
    emoji: '🌱',
    typicalCrops: ['buğday', 'arpa', 'mısır', 'ayçiçeği', 'sebze'],
    typicalExpenses: ['tohum', 'gübre', 'sulama', 'ilaç'],
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'summer',
    name: 'Yaz',
    description: 'Yaz dönemi tarımsal faaliyetleri',
    startMonth: 6,
    endMonth: 8,
    color: '#FF9800',
    emoji: '☀️',
    typicalCrops: ['mısır', 'ayçiçeği', 'sebze', 'meyve'],
    typicalExpenses: ['sulama', 'ilaç', 'işçilik', 'yakıt'],
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'autumn',
    name: 'Sonbahar',
    description: 'Sonbahar dönemi tarımsal faaliyetleri',
    startMonth: 9,
    endMonth: 11,
    color: '#795548',
    emoji: '🍂',
    typicalCrops: ['buğday', 'arpa', 'meyve', 'sebze'],
    typicalExpenses: ['hasat', 'depolama', 'işçilik', 'makine'],
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'winter',
    name: 'Kış',
    description: 'Kış dönemi tarımsal faaliyetleri',
    startMonth: 12,
    endMonth: 2,
    color: '#2196F3',
    emoji: '❄️',
    typicalCrops: ['sera', 'kışlık sebze'],
    typicalExpenses: ['ısıtma', 'bakım', 'planlama'],
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

db.createCollection('seasons');
db.seasons.insertMany(defaultSeasons);
print('✅ Default seasons inserted: ' + defaultSeasons.length + ' seasons');

print('🎉 Çiftçi Not Defterim MongoDB initialization completed successfully!');
print('📊 Database ready with default categories and seasons');
print('🔗 Connection string: *********************************************************************');
