/**
 * Schema Update Script for MongoDB Collections
 * Updates existing collection validators to match new ObjectId requirements
 */

const mongoose = require('mongoose');
require('dotenv').config();

const MONGODB_URI = process.env.MONGODB_URI || '************************************************************************************************';

async function updateExpenseSchema() {
  try {
    console.log('🔧 Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    const db = mongoose.connection.db;

    // Drop existing expenses collection validator
    console.log('🗑️ Dropping existing expenses collection...');
    try {
      await db.collection('expenses').drop();
      console.log('✅ Expenses collection dropped');
    } catch (error) {
      if (error.code === 26) {
        console.log('ℹ️ Expenses collection does not exist, creating new one');
      } else {
        throw error;
      }
    }

    // Create expenses collection with updated validator
    console.log('🔧 Creating expenses collection with updated validator...');
    await db.createCollection('expenses', {
      validator: {
        $jsonSchema: {
          bsonType: 'object',
          required: ['userId', 'categoryId', 'amount', 'date', 'seasonId'],
          properties: {
            userId: {
              bsonType: 'objectId',
              description: 'User ObjectId is required'
            },
            categoryId: {
              bsonType: 'objectId',
              description: 'Category ObjectId is required'
            },
            amount: {
              bsonType: 'number',
              minimum: 0,
              maximum: 1000000,
              description: 'Amount must be between 0 and 1,000,000'
            },
            date: {
              bsonType: 'date',
              description: 'Date is required'
            },
            seasonId: {
              bsonType: 'objectId',
              description: 'Season ObjectId is required'
            },
            legacySeasonId: {
              enum: ['spring', 'summer', 'autumn', 'winter', null],
              description: 'Legacy season ID for migration support'
            },
            description: {
              bsonType: 'string',
              maxLength: 500,
              description: 'Description must be max 500 characters'
            },
            status: {
              enum: ['active', 'deleted', 'archived'],
              description: 'Status must be one of the enum values'
            }
          }
        }
      }
    });

    console.log('✅ Expenses collection created with updated validator');

    // Create indexes for performance
    console.log('🔧 Creating indexes...');
    await db.collection('expenses').createIndex({ userId: 1, date: -1 });
    await db.collection('expenses').createIndex({ userId: 1, categoryId: 1 });
    await db.collection('expenses').createIndex({ userId: 1, seasonId: 1 });
    await db.collection('expenses').createIndex({ userId: 1, status: 1, date: -1 });
    await db.collection('expenses').createIndex({ date: -1, amount: -1 });
    await db.collection('expenses').createIndex({ description: 'text', notes: 'text' });

    console.log('✅ Indexes created successfully');

    // Test the validator with a sample document
    console.log('🧪 Testing validator...');
    const testDoc = {
      userId: new mongoose.Types.ObjectId(),
      categoryId: new mongoose.Types.ObjectId(),
      seasonId: new mongoose.Types.ObjectId(),
      amount: 100,
      date: new Date(),
      description: 'Test expense',
      status: 'active'
    };

    await db.collection('expenses').insertOne(testDoc);
    console.log('✅ Validator test passed - ObjectId format accepted');

    // Clean up test document
    await db.collection('expenses').deleteOne({ _id: testDoc._id });
    console.log('✅ Test document cleaned up');

    console.log('🎉 Schema update completed successfully!');

  } catch (error) {
    console.error('❌ Schema update failed:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('📡 Disconnected from MongoDB');
  }
}

// Run the update
updateExpenseSchema();
