/**
 * Database Optimization Script
 * Optimizes database performance for two-mode system
 */

const mongoose = require('mongoose');
const { performanceOptimizer } = require('../src/utils/performanceOptimization');

async function optimizeDatabase() {
  try {
    console.log('🚀 Starting database optimization...');
    
    // Connect to database
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/ciftcinotdefterim');
    console.log('✅ Connected to database');

    // 1. Optimize indexes
    console.log('\n📊 Optimizing database indexes...');
    await performanceOptimizer.optimizeIndexes();

    // 2. Generate health report
    console.log('\n🏥 Generating database health report...');
    const healthReport = await performanceOptimizer.getDatabaseHealthReport();
    
    console.log('\n📋 Database Health Report:');
    console.log('='.repeat(50));
    
    // Collection statistics
    console.log('\n📚 Collection Statistics:');
    for (const [collection, stats] of Object.entries(healthReport.collections)) {
      console.log(`  ${collection}:`);
      console.log(`    Documents: ${stats.count.toLocaleString()}`);
      console.log(`    Size: ${(stats.size / 1024 / 1024).toFixed(2)} MB`);
      console.log(`    Avg Document Size: ${stats.avgObjSize} bytes`);
    }

    // Index information
    console.log('\n🔍 Index Information:');
    for (const [collection, indexes] of Object.entries(healthReport.indexes)) {
      console.log(`  ${collection}: ${indexes.length} indexes`);
      indexes.forEach(index => console.log(`    - ${index}`));
    }

    // Recommendations
    if (healthReport.recommendations.length > 0) {
      console.log('\n💡 Optimization Recommendations:');
      healthReport.recommendations.forEach((rec, index) => {
        console.log(`  ${index + 1}. ${rec}`);
      });
    } else {
      console.log('\n✅ No optimization recommendations at this time');
    }

    // 3. Performance benchmarks
    console.log('\n⚡ Running performance benchmarks...');
    await runPerformanceBenchmarks();

    console.log('\n🎉 Database optimization completed successfully!');

  } catch (error) {
    console.error('❌ Database optimization failed:', error);
    process.exit(1);
  } finally {
    await mongoose.connection.close();
  }
}

async function runPerformanceBenchmarks() {
  const User = require('../src/models/User');
  const Expense = require('../src/models/Expense');
  
  try {
    // Get a test user
    const testUser = await User.findOne().lean();
    if (!testUser) {
      console.log('  ⚠️  No users found for benchmarking');
      return;
    }

    console.log(`  Testing with user: ${testUser._id}`);

    // Benchmark 1: Simple expense query
    const start1 = Date.now();
    await Expense.find({ 
      userId: testUser._id, 
      status: 'active' 
    }).limit(50).lean();
    const time1 = Date.now() - start1;
    console.log(`  📈 Simple expense query: ${time1}ms`);

    // Benchmark 2: Detailed mode query with joins
    const start2 = Date.now();
    await Expense.find({ 
      userId: testUser._id, 
      trackingMode: 'detailed',
      status: 'active' 
    })
    .populate('fieldId', 'name size')
    .populate('cropId', 'nameTr emoji')
    .populate('categoryId', 'name emoji')
    .limit(50)
    .lean();
    const time2 = Date.now() - start2;
    console.log(`  📈 Detailed mode query with joins: ${time2}ms`);

    // Benchmark 3: Aggregation query
    const start3 = Date.now();
    await Expense.aggregate([
      { $match: { userId: testUser._id, status: 'active' } },
      { $group: { 
        _id: '$categoryId', 
        total: { $sum: '$amount' },
        count: { $sum: 1 }
      }},
      { $sort: { total: -1 } },
      { $limit: 10 }
    ]);
    const time3 = Date.now() - start3;
    console.log(`  📈 Aggregation query: ${time3}ms`);

    // Performance assessment
    console.log('\n📊 Performance Assessment:');
    if (time1 < 100) console.log('  ✅ Simple queries: Excellent');
    else if (time1 < 500) console.log('  ⚠️  Simple queries: Good');
    else console.log('  ❌ Simple queries: Needs optimization');

    if (time2 < 200) console.log('  ✅ Complex queries: Excellent');
    else if (time2 < 1000) console.log('  ⚠️  Complex queries: Good');
    else console.log('  ❌ Complex queries: Needs optimization');

    if (time3 < 150) console.log('  ✅ Aggregations: Excellent');
    else if (time3 < 750) console.log('  ⚠️  Aggregations: Good');
    else console.log('  ❌ Aggregations: Needs optimization');

  } catch (error) {
    console.error('  ❌ Benchmark failed:', error.message);
  }
}

// CLI interface
if (require.main === module) {
  const args = process.argv.slice(2);
  const command = args[0] || 'optimize';

  switch (command) {
    case 'optimize':
      optimizeDatabase();
      break;
    
    case 'health':
      (async () => {
        try {
          await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/ciftcinotdefterim');
          const report = await performanceOptimizer.getDatabaseHealthReport();
          console.log(JSON.stringify(report, null, 2));
          await mongoose.connection.close();
        } catch (error) {
          console.error('Health check failed:', error);
          process.exit(1);
        }
      })();
      break;
    
    case 'indexes':
      (async () => {
        try {
          await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/ciftcinotdefterim');
          await performanceOptimizer.optimizeIndexes();
          console.log('Indexes optimized successfully');
          await mongoose.connection.close();
        } catch (error) {
          console.error('Index optimization failed:', error);
          process.exit(1);
        }
      })();
      break;
    
    case 'benchmark':
      (async () => {
        try {
          await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/ciftcinotdefterim');
          await runPerformanceBenchmarks();
          await mongoose.connection.close();
        } catch (error) {
          console.error('Benchmark failed:', error);
          process.exit(1);
        }
      })();
      break;
    
    default:
      console.log('Available commands:');
      console.log('  optimize   - Full database optimization');
      console.log('  health     - Generate health report');
      console.log('  indexes    - Optimize indexes only');
      console.log('  benchmark  - Run performance benchmarks');
  }
}

module.exports = {
  optimizeDatabase,
  runPerformanceBenchmarks
};
