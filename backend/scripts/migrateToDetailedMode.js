/**
 * Data Migration Script for Two-Mode System
 * Migrates existing user data when switching from simple to detailed mode
 */

const mongoose = require('mongoose');
const User = require('../src/models/User');
const Expense = require('../src/models/Expense');
const Field = require('../src/models/Field');
const { createDefaultField } = require('../src/utils/defaultFields');

class DataMigrationService {
  constructor() {
    this.migrationLog = [];
  }

  /**
   * Migrate user from simple to detailed mode
   * @param {string} userId - User ID to migrate
   * @param {Object} options - Migration options
   */
  async migrateUserToDetailedMode(userId, options = {}) {
    const {
      createDefaultFieldName = 'Ana Tarla',
      preserveExistingData = true,
      dryRun = false
    } = options;

    try {
      this.log(`Starting migration for user ${userId}`);
      
      // 1. Validate user exists and is in simple mode
      const user = await User.findById(userId);
      if (!user) {
        throw new Error(`User ${userId} not found`);
      }

      if (user.preferences.trackingMode === 'detailed') {
        this.log(`User ${userId} is already in detailed mode`);
        return {
          success: true,
          message: 'User already in detailed mode',
          changes: []
        };
      }

      const changes = [];

      // 2. Create default field if it doesn't exist
      let defaultField = await Field.findOne({
        userId: user._id,
        isDefault: true,
        isActive: true
      });

      if (!defaultField) {
        if (!dryRun) {
          defaultField = await createDefaultField(user._id, createDefaultFieldName);
        }
        changes.push({
          type: 'field_created',
          description: `Created default field: ${createDefaultFieldName}`,
          fieldId: defaultField?._id
        });
        this.log(`Created default field for user ${userId}: ${createDefaultFieldName}`);
      }

      // 3. Migrate existing expenses to default field
      const existingExpenses = await Expense.find({
        userId: user._id,
        trackingMode: { $in: ['simple', null, undefined] },
        status: 'active'
      });

      this.log(`Found ${existingExpenses.length} expenses to migrate`);

      if (existingExpenses.length > 0 && !dryRun) {
        const updateResult = await Expense.updateMany(
          {
            userId: user._id,
            trackingMode: { $in: ['simple', null, undefined] },
            status: 'active'
          },
          {
            $set: {
              fieldId: defaultField._id,
              trackingMode: 'detailed'
            }
          }
        );

        changes.push({
          type: 'expenses_migrated',
          description: `Migrated ${updateResult.modifiedCount} expenses to default field`,
          count: updateResult.modifiedCount
        });
        this.log(`Migrated ${updateResult.modifiedCount} expenses to default field`);
      }

      // 4. Update user preferences
      if (!dryRun) {
        await User.updateOne(
          { _id: user._id },
          {
            $set: {
              'preferences.trackingMode': 'detailed',
              'preferences.detailedModeActivatedAt': new Date()
            }
          }
        );
      }

      changes.push({
        type: 'user_mode_updated',
        description: 'Updated user tracking mode to detailed',
        previousMode: user.preferences.trackingMode,
        newMode: 'detailed'
      });

      this.log(`Successfully migrated user ${userId} to detailed mode`);

      return {
        success: true,
        message: 'Migration completed successfully',
        changes,
        defaultField: defaultField ? {
          id: defaultField._id,
          name: defaultField.name
        } : null,
        migratedExpenses: existingExpenses.length
      };

    } catch (error) {
      this.log(`Migration failed for user ${userId}: ${error.message}`, 'error');
      throw error;
    }
  }

  /**
   * Migrate user back from detailed to simple mode
   * @param {string} userId - User ID to migrate
   * @param {Object} options - Migration options
   */
  async migrateUserToSimpleMode(userId, options = {}) {
    const {
      preserveFieldData = true,
      dryRun = false
    } = options;

    try {
      this.log(`Starting reverse migration for user ${userId}`);
      
      const user = await User.findById(userId);
      if (!user) {
        throw new Error(`User ${userId} not found`);
      }

      if (user.preferences.trackingMode === 'simple') {
        this.log(`User ${userId} is already in simple mode`);
        return {
          success: true,
          message: 'User already in simple mode',
          changes: []
        };
      }

      const changes = [];

      // 1. Update existing expenses to simple mode
      const detailedExpenses = await Expense.find({
        userId: user._id,
        trackingMode: 'detailed',
        status: 'active'
      });

      if (detailedExpenses.length > 0 && !dryRun) {
        const updateData = { trackingMode: 'simple' };
        
        if (!preserveFieldData) {
          updateData.fieldId = null;
          updateData.cropId = null;
        }

        const updateResult = await Expense.updateMany(
          {
            userId: user._id,
            trackingMode: 'detailed',
            status: 'active'
          },
          { $set: updateData }
        );

        changes.push({
          type: 'expenses_simplified',
          description: `Converted ${updateResult.modifiedCount} expenses to simple mode`,
          count: updateResult.modifiedCount,
          preservedFieldData: preserveFieldData
        });
      }

      // 2. Update user preferences
      if (!dryRun) {
        await User.updateOne(
          { _id: user._id },
          {
            $set: {
              'preferences.trackingMode': 'simple'
            }
          }
        );
      }

      changes.push({
        type: 'user_mode_updated',
        description: 'Updated user tracking mode to simple',
        previousMode: 'detailed',
        newMode: 'simple'
      });

      this.log(`Successfully migrated user ${userId} back to simple mode`);

      return {
        success: true,
        message: 'Reverse migration completed successfully',
        changes,
        migratedExpenses: detailedExpenses.length
      };

    } catch (error) {
      this.log(`Reverse migration failed for user ${userId}: ${error.message}`, 'error');
      throw error;
    }
  }

  /**
   * Bulk migrate multiple users
   * @param {Array} userIds - Array of user IDs to migrate
   * @param {Object} options - Migration options
   */
  async bulkMigrateUsers(userIds, options = {}) {
    const results = [];
    const { continueOnError = true } = options;

    for (const userId of userIds) {
      try {
        const result = await this.migrateUserToDetailedMode(userId, options);
        results.push({ userId, ...result });
      } catch (error) {
        const errorResult = {
          userId,
          success: false,
          error: error.message
        };
        results.push(errorResult);

        if (!continueOnError) {
          break;
        }
      }
    }

    return {
      totalUsers: userIds.length,
      successful: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length,
      results
    };
  }

  /**
   * Validate migration integrity
   * @param {string} userId - User ID to validate
   */
  async validateMigration(userId) {
    try {
      const user = await User.findById(userId);
      if (!user) {
        throw new Error(`User ${userId} not found`);
      }

      const validation = {
        userId,
        trackingMode: user.preferences.trackingMode,
        issues: []
      };

      if (user.preferences.trackingMode === 'detailed') {
        // Check if default field exists
        const defaultField = await Field.findOne({
          userId: user._id,
          isDefault: true,
          isActive: true
        });

        if (!defaultField) {
          validation.issues.push('No default field found for detailed mode user');
        }

        // Check if expenses have proper tracking mode
        const inconsistentExpenses = await Expense.countDocuments({
          userId: user._id,
          trackingMode: { $ne: 'detailed' },
          status: 'active'
        });

        if (inconsistentExpenses > 0) {
          validation.issues.push(`${inconsistentExpenses} expenses have inconsistent tracking mode`);
        }
      }

      validation.isValid = validation.issues.length === 0;
      return validation;

    } catch (error) {
      return {
        userId,
        isValid: false,
        error: error.message
      };
    }
  }

  /**
   * Get migration statistics
   * @param {string} userId - User ID (optional)
   */
  async getMigrationStats(userId = null) {
    const query = userId ? { _id: userId } : {};
    
    const stats = await User.aggregate([
      { $match: query },
      {
        $group: {
          _id: '$preferences.trackingMode',
          count: { $sum: 1 },
          users: { $push: '$_id' }
        }
      }
    ]);

    const result = {
      total: 0,
      simple: 0,
      detailed: 0,
      breakdown: stats
    };

    stats.forEach(stat => {
      result.total += stat.count;
      if (stat._id === 'simple') result.simple = stat.count;
      if (stat._id === 'detailed') result.detailed = stat.count;
    });

    return result;
  }

  log(message, level = 'info') {
    const timestamp = new Date().toISOString();
    const logEntry = { timestamp, level, message };
    this.migrationLog.push(logEntry);
    console.log(`[${timestamp}] ${level.toUpperCase()}: ${message}`);
  }

  getMigrationLog() {
    return this.migrationLog;
  }
}

// CLI interface
if (require.main === module) {
  const args = process.argv.slice(2);
  const command = args[0];
  const userId = args[1];

  const migrationService = new DataMigrationService();

  async function runCLI() {
    try {
      await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/ciftcinotdefterim');

      switch (command) {
        case 'migrate':
          if (!userId) {
            console.error('Usage: node migrateToDetailedMode.js migrate <userId>');
            process.exit(1);
          }
          const result = await migrationService.migrateUserToDetailedMode(userId);
          console.log('Migration result:', JSON.stringify(result, null, 2));
          break;

        case 'validate':
          if (!userId) {
            console.error('Usage: node migrateToDetailedMode.js validate <userId>');
            process.exit(1);
          }
          const validation = await migrationService.validateMigration(userId);
          console.log('Validation result:', JSON.stringify(validation, null, 2));
          break;

        case 'stats':
          const stats = await migrationService.getMigrationStats(userId);
          console.log('Migration stats:', JSON.stringify(stats, null, 2));
          break;

        default:
          console.log('Available commands:');
          console.log('  migrate <userId>  - Migrate user to detailed mode');
          console.log('  validate <userId> - Validate migration integrity');
          console.log('  stats [userId]    - Get migration statistics');
      }

      await mongoose.connection.close();
    } catch (error) {
      console.error('CLI Error:', error.message);
      process.exit(1);
    }
  }

  runCLI();
}

module.exports = DataMigrationService;
