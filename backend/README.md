# Çiftçi Not Defterim Backend

Backend API for Çiftçi Not Defterim (Farmer's Notebook) mobile application - a comprehensive expense tracking solution for Turkish farmers.

## Features

- 🔐 **Firebase Authentication** - Secure user authentication with Google, Facebook, and email
- 💰 **Expense Management** - Create, read, update, delete agricultural expenses
- 📊 **Category System** - Default and custom expense categories with seasonal relevance
- 🌱 **Seasonal Tracking** - Agricultural season-based expense organization
- 📱 **Offline Sync** - Robust offline-first sync with conflict resolution
- 📸 **Photo Upload** - Receipt and expense photo management
- 📈 **Analytics** - Comprehensive expense analytics and reporting
- 🇹🇷 **Turkish Localization** - Full Turkish language support with proper formatting

## Tech Stack

- **Runtime**: Node.js 18+
- **Framework**: Express.js
- **Database**: MongoDB with Mongoose ODM
- **Authentication**: Firebase Admin SDK
- **File Upload**: Multer with Sharp for image processing
- **Validation**: Express Validator
- **Testing**: Jest with Supertest
- **Documentation**: Swagger/OpenAPI

## Quick Start

### Prerequisites

- Node.js 18 or higher
- MongoDB 5.0 or higher
- Firebase project with Admin SDK credentials

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd backend

# Install dependencies
npm install

# Copy environment file
cp .env.example .env

# Edit environment variables
nano .env
```

### Environment Configuration

Create a `.env` file with the following variables:

```env
# Server
NODE_ENV=development
PORT=3000

# Database
MONGODB_URI=mongodb://localhost:27017/ciftci-notebook

# Firebase
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>

# Security
JWT_SECRET=your-super-secret-jwt-key
CORS_ORIGIN=*

# File Upload
UPLOAD_MAX_SIZE=5242880
UPLOAD_PATH=uploads/receipts
```

### Running the Application

```bash
# Development mode with hot reload
npm run dev

# Production mode
npm start

# Run with specific port
PORT=4000 npm start
```

## API Documentation

The API follows RESTful conventions with comprehensive Turkish language support.

### Base URL
```
http://localhost:3000/api/v1
```

### Authentication

All protected endpoints require a Firebase ID token in the Authorization header:

```
Authorization: Bearer <firebase-id-token>
```

### Main Endpoints

#### Authentication
- `POST /auth/validate` - Validate Firebase token and get/create user
- `GET /auth/profile` - Get user profile
- `PUT /auth/profile` - Update user profile
- `DELETE /auth/account` - Delete user account

#### Expenses
- `GET /expenses` - List user expenses with filtering and pagination
- `POST /expenses` - Create new expense
- `GET /expenses/:id` - Get expense details
- `PUT /expenses/:id` - Update expense
- `DELETE /expenses/:id` - Delete expense
- `GET /expenses/stats` - Get expense statistics

#### Categories
- `GET /categories` - List categories (default + custom)
- `POST /categories` - Create custom category
- `PUT /categories/:id` - Update custom category
- `DELETE /categories/:id` - Delete custom category

#### Seasons
- `GET /seasons` - List all agricultural seasons
- `GET /seasons/current` - Get current season with user stats
- `GET /seasons/:id` - Get season details
- `GET /seasons/analytics` - Get seasonal analytics

#### Sync
- `POST /sync/push` - Push local changes to server
- `GET /sync/pull` - Pull server changes to local
- `GET /sync/status` - Get sync status
- `POST /sync/resolve-conflict` - Resolve sync conflicts

#### Upload
- `POST /upload/photo` - Upload single photo
- `POST /upload/photos` - Upload multiple photos
- `DELETE /upload/photo/:id` - Delete uploaded photo

## Testing

```bash
# Run all tests
npm test

# Run unit tests only
npm run test:unit

# Run integration tests only
npm run test:integration

# Run E2E tests only
npm run test:e2e

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run tests for CI/CD
npm run test:ci
```

### Test Structure

```
tests/
├── setup.js                 # Global test setup
├── utils/
│   └── testHelpers.js       # Test utilities and helpers
├── unit/
│   ├── models/              # Model unit tests
│   ├── controllers/         # Controller unit tests
│   ├── middleware/          # Middleware unit tests
│   └── utils/               # Utility unit tests
├── integration/
│   ├── auth.test.js         # Authentication API tests
│   ├── expenses.test.js     # Expense API tests
│   ├── categories.test.js   # Category API tests
│   └── sync.test.js         # Sync API tests
└── e2e/
    └── workflows.test.js    # End-to-end workflow tests
```

### Test Coverage

The project maintains minimum test coverage thresholds:
- Branches: 70%
- Functions: 70%
- Lines: 70%
- Statements: 70%

## Project Structure

```
src/
├── app.js                   # Express app configuration
├── server.js               # Server entry point
├── controllers/            # Route controllers
│   ├── authController.js
│   ├── expenseController.js
│   ├── categoryController.js
│   ├── seasonController.js
│   └── syncController.js
├── middleware/             # Custom middleware
│   ├── auth.js
│   ├── errorHandler.js
│   ├── rateLimiter.js
│   └── validation.js
├── models/                 # Mongoose models
│   ├── User.js
│   ├── Expense.js
│   ├── Category.js
│   └── Season.js
├── routes/                 # Express routes
│   ├── auth.js
│   ├── expenses.js
│   ├── categories.js
│   ├── seasons.js
│   ├── sync.js
│   ├── upload.js
│   └── users.js
├── services/               # Business logic services
│   └── syncService.js
└── utils/                  # Utility functions
    ├── constants.js
    ├── database.js
    ├── helpers.js
    └── logger.js
```

## Development

### Code Style

The project uses ESLint and Prettier for code formatting:

```bash
# Lint code
npm run lint

# Fix linting issues
npm run lint:fix

# Format code
npm run format
```

### Database Seeding

```bash
# Seed database with default categories and test data
npm run seed

# Run database migrations
npm run migrate
```

### Logging

The application uses Winston for structured logging with Turkish timezone support:

- **Development**: Console output with colors
- **Production**: File-based logging with rotation
- **Test**: Suppressed logging

Log files are stored in the `logs/` directory:
- `app-YYYY-MM-DD.log` - Application logs
- `error-YYYY-MM-DD.log` - Error logs only
- `combined-YYYY-MM-DD.log` - All logs

## Deployment

### Docker

```bash
# Build Docker image
docker build -t ciftci-backend .

# Run with Docker Compose
docker-compose up -d
```

### Environment Variables for Production

Ensure these environment variables are set in production:

```env
NODE_ENV=production
MONGODB_URI=mongodb+srv://...
FIREBASE_PROJECT_ID=...
FIREBASE_PRIVATE_KEY=...
FIREBASE_CLIENT_EMAIL=...
JWT_SECRET=...
CORS_ORIGIN=https://yourdomain.com
```

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Commit Convention

Use conventional commits:
- `feat:` - New features
- `fix:` - Bug fixes
- `docs:` - Documentation changes
- `style:` - Code style changes
- `refactor:` - Code refactoring
- `test:` - Test additions or changes
- `chore:` - Build process or auxiliary tool changes

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

For support and questions:
- Create an issue on GitHub
- Contact the development team

---

Made with ❤️ for Turkish farmers 🇹🇷
