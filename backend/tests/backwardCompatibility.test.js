/**
 * Backward Compatibility Tests for Two-Mode System
 * Ensures existing users experience no breaking changes
 */

const request = require('supertest');
const mongoose = require('mongoose');
const app = require('../src/app');
const User = require('../src/models/User');
const Expense = require('../src/models/Expense');
const Category = require('../src/models/Category');
const Field = require('../src/models/Field');
const Crop = require('../src/models/Crop');

describe('Backward Compatibility Tests', () => {
  let testUser;
  let authToken;
  let existingExpense;
  let existingCategory;

  beforeAll(async () => {
    // Connect to test database
    if (mongoose.connection.readyState === 0) {
      await mongoose.connect(process.env.MONGODB_TEST_URI || 'mongodb://localhost:27017/ciftcinotdefterim_test');
    }
  });

  beforeEach(async () => {
    // Clean database
    await User.deleteMany({});
    await Expense.deleteMany({});
    await Category.deleteMany({});
    await Field.deleteMany({});
    await Crop.deleteMany({});

    // Create test user with old schema (simple mode)
    testUser = await User.create({
      firebaseUid: 'test-user-123',
      email: '<EMAIL>',
      name: 'Test User',
      preferences: {
        currency: 'TRY',
        language: 'tr',
        notifications: {
          enabled: true,
          expenseReminders: true,
          weeklyReports: false,
        },
        // Note: No trackingMode field - should default to 'simple'
      },
      farmInfo: {
        farmName: 'Test Çiftliği',
        location: {
          country: 'Turkey',
          city: 'Test İli'
        },
        size: {
          value: 100,
          unit: 'dekar'
        },
        farmType: 'mixed',
      },
    });

    // Create existing category (old format)
    existingCategory = await Category.create({
      name: 'Gübre',
      emoji: '🌱',
      color: '#4CAF50',
      isDefault: true,
      isActive: true,
    });

    // Create existing expense (old format - no fieldId, cropId, trackingMode)
    existingExpense = await Expense.create({
      userId: testUser._id,
      categoryId: existingCategory._id,
      amount: 500,
      date: new Date('2024-01-15'),
      description: 'Test gider',
      seasonId: 'winter',
      status: 'active',
    });

    // Mock auth token
    authToken = 'mock-firebase-token';
  });

  afterAll(async () => {
    await mongoose.connection.close();
  });

  describe('User Model Backward Compatibility', () => {
    test('should default to simple tracking mode for existing users', async () => {
      const user = await User.findById(testUser._id);
      
      // Should default to simple mode
      expect(user.preferences.trackingMode).toBe('simple');
      expect(user.preferences.detailedModeActivatedAt).toBeNull();
    });

    test('should preserve existing user preferences', async () => {
      const user = await User.findById(testUser._id);
      
      expect(user.preferences.currency).toBe('TRY');
      expect(user.preferences.language).toBe('tr');
      expect(user.preferences.notifications.enabled).toBe(true);
      expect(user.farmInfo.farmName).toBe('Test Çiftliği');
    });
  });

  describe('Expense Model Backward Compatibility', () => {
    test('should handle existing expenses without new fields', async () => {
      const expense = await Expense.findById(existingExpense._id);
      
      // New fields should be null/default
      expect(expense.fieldId).toBeNull();
      expect(expense.cropId).toBeNull();
      expect(expense.trackingMode).toBe('simple');
      
      // Existing fields should be preserved
      expect(expense.amount).toBe(500);
      expect(expense.description).toBe('Test gider');
      expect(expense.seasonId).toBe('winter');
    });

    test('should auto-detect tracking mode on save', async () => {
      // Update existing expense
      existingExpense.amount = 600;
      await existingExpense.save();
      
      // Should remain simple mode
      expect(existingExpense.trackingMode).toBe('simple');
    });
  });

  describe('API Backward Compatibility', () => {
    test('GET /api/v1/expenses should work with existing data', async () => {
      const response = await request(app)
        .get('/api/v1/expenses')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveLength(1);
      
      const expense = response.body.data[0];
      expect(expense.amount).toBe(500);
      expect(expense.fieldId).toBeNull();
      expect(expense.cropId).toBeNull();
    });

    test('POST /api/v1/expenses should work without new fields', async () => {
      const newExpense = {
        categoryId: existingCategory._id,
        amount: 300,
        date: '2024-01-20',
        description: 'Yeni gider',
      };

      const response = await request(app)
        .post('/api/v1/expenses')
        .set('Authorization', `Bearer ${authToken}`)
        .send(newExpense)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.trackingMode).toBe('simple');
      expect(response.body.data.fieldId).toBeNull();
      expect(response.body.data.cropId).toBeNull();
    });

    test('GET /api/v1/categories should include system defaults', async () => {
      const response = await request(app)
        .get('/api/v1/categories')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.length).toBeGreaterThan(0);
      
      const systemCategory = response.body.data.find(cat => cat.isDefault);
      expect(systemCategory).toBeDefined();
    });
  });

  describe('Simple Mode User Experience', () => {
    test('should not expose detailed mode features to simple mode users', async () => {
      // Check user mode
      const response = await request(app)
        .get('/api/v1/users/mode')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.data.mode).toBe('simple');
      expect(response.body.data.defaultField).toBeNull();
    });

    test('should filter out field/crop parameters in simple mode', async () => {
      // Try to create expense with field/crop in simple mode
      const newExpense = {
        categoryId: existingCategory._id,
        amount: 400,
        date: '2024-01-25',
        description: 'Test with field',
        fieldId: new mongoose.Types.ObjectId(),
        cropId: new mongoose.Types.ObjectId(),
      };

      const response = await request(app)
        .post('/api/v1/expenses')
        .set('Authorization', `Bearer ${authToken}`)
        .send(newExpense)
        .expect(201);

      // Should ignore field/crop in simple mode
      expect(response.body.data.trackingMode).toBe('simple');
    });
  });

  describe('Data Migration Safety', () => {
    test('should not lose existing expense data during migration', async () => {
      const expensesBefore = await Expense.find({ userId: testUser._id });
      expect(expensesBefore).toHaveLength(1);

      // Simulate migration by updating schema
      await Expense.updateMany(
        { userId: testUser._id },
        { $set: { trackingMode: 'simple' } }
      );

      const expensesAfter = await Expense.find({ userId: testUser._id });
      expect(expensesAfter).toHaveLength(1);
      expect(expensesAfter[0].amount).toBe(500);
      expect(expensesAfter[0].description).toBe('Test gider');
    });

    test('should preserve user preferences during migration', async () => {
      const userBefore = await User.findById(testUser._id);
      const originalPreferences = userBefore.preferences;

      // Simulate migration
      await User.updateOne(
        { _id: testUser._id },
        { 
          $set: { 
            'preferences.trackingMode': 'simple',
            'preferences.detailedModeActivatedAt': null
          }
        }
      );

      const userAfter = await User.findById(testUser._id);
      expect(userAfter.preferences.currency).toBe(originalPreferences.currency);
      expect(userAfter.preferences.language).toBe(originalPreferences.language);
      expect(userAfter.preferences.trackingMode).toBe('simple');
    });
  });

  describe('Performance Impact', () => {
    test('should not impact simple mode query performance', async () => {
      // Create multiple expenses
      const expenses = [];
      for (let i = 0; i < 100; i++) {
        expenses.push({
          userId: testUser._id,
          categoryId: existingCategory._id,
          amount: Math.floor(Math.random() * 1000),
          date: new Date(),
          description: `Test expense ${i}`,
          trackingMode: 'simple',
        });
      }
      await Expense.insertMany(expenses);

      const startTime = Date.now();
      
      const response = await request(app)
        .get('/api/v1/expenses?limit=50')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      const endTime = Date.now();
      const queryTime = endTime - startTime;

      expect(response.body.data).toHaveLength(50);
      expect(queryTime).toBeLessThan(1000); // Should complete within 1 second
    });
  });

  describe('Error Handling', () => {
    test('should handle missing new fields gracefully', async () => {
      // Try to query with new field filters in simple mode
      const response = await request(app)
        .get('/api/v1/expenses?fieldId=invalid&cropId=invalid')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      // Should ignore invalid filters and return all expenses
    });

    test('should validate mode switching properly', async () => {
      // Try to switch to detailed mode
      const response = await request(app)
        .post('/api/v1/users/mode')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ mode: 'detailed' })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.mode).toBe('detailed');
    });
  });
});
