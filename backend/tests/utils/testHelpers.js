/**
 * Test Helpers for Çiftçi Not Defterim Backend
 * Common utilities for testing
 */

const User = require('../../src/models/User');
const Category = require('../../src/models/Category');
const Expense = require('../../src/models/Expense');
const { DEFAULT_CATEGORIES } = require('../../src/utils/constants');

/**
 * Create a test user
 */
const createTestUser = async (userData = {}) => {
  const defaultUser = {
    firebaseUid: `test-uid-${Date.now()}`,
    email: `test${Date.now()}@example.com`,
    name: 'Test User',
    emailVerified: true,
    provider: 'google.com',
    ...userData
  };

  return await User.create(defaultUser);
};

/**
 * Create test categories
 */
const createTestCategories = async (userId = null) => {
  const categories = [];
  
  // Create default categories
  for (const [index, categoryData] of DEFAULT_CATEGORIES.entries()) {
    const category = await Category.create({
      ...categoryData,
      sortOrder: index,
      isDefault: true,
      isActive: true
    });
    categories.push(category);
  }

  // Create custom category if userId provided
  if (userId) {
    const customCategory = await Category.create({
      name: 'Test Custom Category',
      emoji: '🧪',
      color: '#FF0000',
      icon: 'test',
      description: 'Test category',
      userId,
      isDefault: false,
      isActive: true
    });
    categories.push(customCategory);
  }

  return categories;
};

/**
 * Create test expense
 */
const createTestExpense = async (userId, categoryId, expenseData = {}) => {
  const defaultExpense = {
    userId,
    categoryId,
    amount: 100,
    description: 'Test expense',
    date: new Date(),
    seasonId: 'spring',
    ...expenseData
  };

  return await Expense.create(defaultExpense);
};

/**
 * Create multiple test expenses
 */
const createTestExpenses = async (userId, categoryId, count = 5) => {
  const expenses = [];
  
  for (let i = 0; i < count; i++) {
    const expense = await createTestExpense(userId, categoryId, {
      amount: (i + 1) * 50,
      description: `Test expense ${i + 1}`,
      date: new Date(Date.now() - i * 24 * 60 * 60 * 1000) // Different dates
    });
    expenses.push(expense);
  }

  return expenses;
};

/**
 * Mock Firebase token verification
 */
const mockFirebaseAuth = (userData = {}) => {
  const admin = require('firebase-admin');
  const mockAuth = admin.auth();
  
  const defaultUserData = {
    uid: `test-uid-${Date.now()}`,
    email: `test${Date.now()}@example.com`,
    name: 'Test User',
    email_verified: true,
    firebase: {
      sign_in_provider: 'google.com'
    },
    ...userData
  };

  mockAuth.verifyIdToken.mockResolvedValue(defaultUserData);
  
  return defaultUserData;
};

/**
 * Generate test Firebase token
 */
const generateTestToken = (userData = {}) => {
  const tokenData = {
    uid: `test-uid-${Date.now()}`,
    email: `test${Date.now()}@example.com`,
    ...userData
  };

  // Use dev-test-token format for mock authentication
  return `dev-test-token-${Date.now()}`;
};

/**
 * Create authenticated request headers
 */
const createAuthHeaders = (token = null) => {
  const testToken = token || generateTestToken();
  return {
    'Authorization': `Bearer ${testToken}`,
    'Content-Type': 'application/json'
  };
};

/**
 * Wait for a specified time (for async operations)
 */
const wait = (ms) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * Generate random Turkish text
 */
const generateTurkishText = (length = 10) => {
  const turkishChars = 'abcdefghijklmnopqrstuvwxyzçğıöşüABCDEFGHIJKLMNOPQRSTUVWXYZÇĞIİÖŞÜ';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += turkishChars.charAt(Math.floor(Math.random() * turkishChars.length));
  }
  return result;
};

/**
 * Generate test expense data
 */
const generateExpenseData = (overrides = {}) => {
  return {
    amount: Math.floor(Math.random() * 1000) + 10,
    description: `Test gider ${generateTurkishText(5)}`,
    date: new Date().toISOString(),
    location: {
      latitude: 39.9334 + (Math.random() - 0.5) * 0.1,
      longitude: 32.8597 + (Math.random() - 0.5) * 0.1,
      accuracy: Math.floor(Math.random() * 100) + 5
    },
    ...overrides
  };
};

/**
 * Generate test category data
 */
const generateCategoryData = (overrides = {}) => {
  const emojis = ['🌱', '🚜', '💧', '🧪', '⛽', '👷', '📦'];
  const colors = ['#4CAF50', '#FF9800', '#2196F3', '#9C27B0', '#F44336', '#795548', '#607D8B'];
  
  return {
    name: `Test Kategori ${generateTurkishText(5)}`,
    emoji: emojis[Math.floor(Math.random() * emojis.length)],
    color: colors[Math.floor(Math.random() * colors.length)],
    icon: 'test',
    description: `Test kategori açıklaması ${generateTurkishText(10)}`,
    ...overrides
  };
};

/**
 * Assert response structure
 */
const assertSuccessResponse = (response, expectedData = null) => {
  expect(response.body).toHaveProperty('success', true);
  expect(response.body).toHaveProperty('message');
  expect(response.body).toHaveProperty('timestamp');
  
  if (expectedData) {
    expect(response.body).toHaveProperty('data');
    if (typeof expectedData === 'object') {
      expect(response.body.data).toMatchObject(expectedData);
    }
  }
};

/**
 * Assert error response structure
 */
const assertErrorResponse = (response, expectedCode = null) => {
  expect(response.body).toHaveProperty('success', false);
  expect(response.body).toHaveProperty('error');
  expect(response.body.error).toHaveProperty('code');
  expect(response.body.error).toHaveProperty('message');
  expect(response.body).toHaveProperty('timestamp');
  
  if (expectedCode) {
    expect(response.body.error.code).toBe(expectedCode);
  }
};

/**
 * Assert paginated response structure
 */
const assertPaginatedResponse = (response, expectedCount = null) => {
  expect(response.body).toHaveProperty('success', true);
  expect(response.body).toHaveProperty('data');
  expect(response.body).toHaveProperty('pagination');
  
  const pagination = response.body.pagination;
  expect(pagination).toHaveProperty('page');
  expect(pagination).toHaveProperty('limit');
  expect(pagination).toHaveProperty('total');
  expect(pagination).toHaveProperty('totalPages');
  expect(pagination).toHaveProperty('hasNext');
  expect(pagination).toHaveProperty('hasPrev');
  
  if (expectedCount !== null) {
    expect(response.body.data).toHaveLength(expectedCount);
  }
};

/**
 * Clean up test data
 */
const cleanupTestData = async () => {
  await Promise.all([
    User.deleteMany({}),
    Category.deleteMany({}),
    Expense.deleteMany({})
  ]);
};

module.exports = {
  createTestUser,
  createTestCategories,
  createTestExpense,
  createTestExpenses,
  mockFirebaseAuth,
  generateTestToken,
  createAuthHeaders,
  wait,
  generateTurkishText,
  generateExpenseData,
  generateCategoryData,
  assertSuccessResponse,
  assertErrorResponse,
  assertPaginatedResponse,
  cleanupTestData
};
