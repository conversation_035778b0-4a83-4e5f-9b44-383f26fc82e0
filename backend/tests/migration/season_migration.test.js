/**
 * Season Migration Tests
 * Tests for the season migration script
 */

const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const { migrateLegacySeasons, rollbackMigration, checkMigrationStatus, MigrationStatus } = require('../../migrations/001_season_migration');

// Import models
const User = require('../../src/models/User');
const Season = require('../../src/models/Season');
const Expense = require('../../src/models/Expense');
const Category = require('../../src/models/Category');

describe('Season Migration', () => {
  let mongoServer;
  let testUser;
  let testCategory;

  beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    await mongoose.connect(mongoUri);
  });

  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });

  beforeEach(async () => {
    // Clean up collections
    await User.deleteMany({});
    await Season.deleteMany({});
    await Expense.deleteMany({});
    await Category.deleteMany({});
    await MigrationStatus.deleteMany({});

    // Create test user
    testUser = await User.create({
      email: '<EMAIL>',
      name: 'Test User',
      createdAt: new Date('2024-01-01')
    });

    // Create test category
    testCategory = await Category.create({
      name: 'Test Category',
      emoji: '🧪'
    });
  });

  describe('Migration Process', () => {
    test('should create default season for user without seasons', async () => {
      // Create legacy expenses
      await Expense.create([
        {
          userId: testUser._id,
          categoryId: testCategory._id,
          amount: 100,
          description: 'Test expense 1',
          date: new Date('2024-03-15'),
          seasonId: 'spring'
        },
        {
          userId: testUser._id,
          categoryId: testCategory._id,
          amount: 200,
          description: 'Test expense 2',
          date: new Date('2024-07-15'),
          seasonId: 'summer'
        }
      ]);

      // Run migration
      await migrateLegacySeasons();

      // Check if default season was created
      const userSeasons = await Season.find({ userId: testUser._id });
      expect(userSeasons).toHaveLength(1);

      const defaultSeason = userSeasons[0];
      expect(defaultSeason.name).toBe('2025 Sezonu');
      expect(defaultSeason.isActive).toBe(true);
      expect(defaultSeason.isDefault).toBe(true);
      expect(defaultSeason.userId.toString()).toBe(testUser._id.toString());

      // Check if expenses were migrated
      const migratedExpenses = await Expense.find({ userId: testUser._id });
      expect(migratedExpenses).toHaveLength(2);

      migratedExpenses.forEach(expense => {
        expect(expense.seasonId.toString()).toBe(defaultSeason._id.toString());
        expect(['spring', 'summer']).toContain(expense.legacySeasonId);
      });
    });

    test('should skip users who already have seasons', async () => {
      // Create existing season for user
      await Season.create({
        userId: testUser._id,
        name: 'Existing Season',
        isActive: true
      });

      // Create legacy expense
      await Expense.create({
        userId: testUser._id,
        categoryId: testCategory._id,
        amount: 100,
        description: 'Test expense',
        date: new Date('2024-03-15'),
        seasonId: 'spring'
      });

      // Run migration
      await migrateLegacySeasons();

      // Check that no new seasons were created
      const userSeasons = await Season.find({ userId: testUser._id });
      expect(userSeasons).toHaveLength(1);
      expect(userSeasons[0].name).toBe('Existing Season');

      // Check migration status
      const migrationStatus = await checkMigrationStatus('season_migration_v1');
      expect(migrationStatus.status).toBe('completed');
      
      const userResult = migrationStatus.results.find(r => r.userId.toString() === testUser._id.toString());
      expect(userResult.status).toBe('skipped');
      expect(userResult.reason).toBe('already_has_seasons');
    });

    test('should handle users with no expenses', async () => {
      // Run migration without creating any expenses
      await migrateLegacySeasons();

      // Check if default season was still created
      const userSeasons = await Season.find({ userId: testUser._id });
      expect(userSeasons).toHaveLength(1);

      const defaultSeason = userSeasons[0];
      expect(defaultSeason.name).toBe('2025 Sezonu');

      // Check migration status
      const migrationStatus = await checkMigrationStatus('season_migration_v1');
      const userResult = migrationStatus.results.find(r => r.userId.toString() === testUser._id.toString());
      expect(userResult.status).toBe('success');
      expect(userResult.expensesUpdated).toBe(0);
    });

    test('should create backup collections', async () => {
      // Create legacy expenses
      await Expense.create({
        userId: testUser._id,
        categoryId: testCategory._id,
        amount: 100,
        description: 'Test expense',
        date: new Date('2024-03-15'),
        seasonId: 'spring'
      });

      // Run migration
      await migrateLegacySeasons();

      // Check migration status for backup info
      const migrationStatus = await checkMigrationStatus('season_migration_v1');
      expect(migrationStatus.backupCollections).toBeDefined();
      expect(migrationStatus.backupCollections.length).toBeGreaterThan(0);

      // Check if backup collections exist
      const collections = await mongoose.connection.db.listCollections().toArray();
      const backupCollections = collections.filter(col => col.name.includes('backup'));
      expect(backupCollections.length).toBeGreaterThan(0);
    });

    test('should track migration status correctly', async () => {
      await migrateLegacySeasons();

      const migrationStatus = await checkMigrationStatus('season_migration_v1');
      expect(migrationStatus).toBeDefined();
      expect(migrationStatus.migrationName).toBe('season_migration_v1');
      expect(migrationStatus.status).toBe('completed');
      expect(migrationStatus.startedAt).toBeDefined();
      expect(migrationStatus.completedAt).toBeDefined();
      expect(migrationStatus.results).toBeDefined();
      expect(Array.isArray(migrationStatus.results)).toBe(true);
    });
  });

  describe('Error Handling', () => {
    test('should handle database errors gracefully', async () => {
      // Close connection to simulate database error
      await mongoose.disconnect();

      await expect(migrateLegacySeasons()).rejects.toThrow();

      // Reconnect for cleanup
      const mongoUri = mongoServer.getUri();
      await mongoose.connect(mongoUri);
    });

    test('should rollback on transaction failure', async () => {
      // This test would require mocking to simulate a transaction failure
      // For now, we'll test the rollback function directly
      
      // Create a completed migration status
      await MigrationStatus.create({
        migrationName: 'test_migration',
        status: 'completed',
        backupCollections: ['expenses_backup_test', 'seasons_backup_test']
      });

      // Create mock backup collections
      await mongoose.connection.db.collection('expenses_backup_test').insertOne({
        _id: new mongoose.Types.ObjectId(),
        userId: testUser._id,
        amount: 100,
        seasonId: 'spring'
      });

      await mongoose.connection.db.collection('seasons_backup_test').insertOne({
        _id: new mongoose.Types.ObjectId(),
        id: 'spring',
        name: 'Spring'
      });

      // Test rollback (this would normally restore data)
      // Note: This is a simplified test - full rollback testing would require more setup
      const migrationStatus = await checkMigrationStatus('test_migration');
      expect(migrationStatus).toBeDefined();
      expect(migrationStatus.backupCollections).toContain('expenses_backup_test');
    });
  });

  describe('Data Integrity', () => {
    test('should preserve all expense data during migration', async () => {
      const originalExpense = {
        userId: testUser._id,
        categoryId: testCategory._id,
        amount: 150.75,
        description: 'Detailed test expense',
        date: new Date('2024-06-15'),
        seasonId: 'summer',
        notes: 'Test notes',
        tags: ['test', 'migration']
      };

      await Expense.create(originalExpense);

      // Run migration
      await migrateLegacySeasons();

      // Check migrated expense
      const migratedExpense = await Expense.findOne({ userId: testUser._id });
      expect(migratedExpense.amount).toBe(originalExpense.amount);
      expect(migratedExpense.description).toBe(originalExpense.description);
      expect(migratedExpense.notes).toBe(originalExpense.notes);
      expect(migratedExpense.tags).toEqual(originalExpense.tags);
      expect(migratedExpense.legacySeasonId).toBe('summer');
      expect(migratedExpense.seasonId).toBeDefined();
      expect(migratedExpense.seasonId.toString()).not.toBe('summer');
    });

    test('should maintain user isolation', async () => {
      // Create second user
      const testUser2 = await User.create({
        email: '<EMAIL>',
        name: 'Test User 2'
      });

      // Create expenses for both users
      await Expense.create([
        {
          userId: testUser._id,
          categoryId: testCategory._id,
          amount: 100,
          description: 'User 1 expense',
          date: new Date('2024-03-15'),
          seasonId: 'spring'
        },
        {
          userId: testUser2._id,
          categoryId: testCategory._id,
          amount: 200,
          description: 'User 2 expense',
          date: new Date('2024-03-15'),
          seasonId: 'spring'
        }
      ]);

      // Run migration
      await migrateLegacySeasons();

      // Check that each user has their own season
      const user1Seasons = await Season.find({ userId: testUser._id });
      const user2Seasons = await Season.find({ userId: testUser2._id });

      expect(user1Seasons).toHaveLength(1);
      expect(user2Seasons).toHaveLength(1);
      expect(user1Seasons[0]._id.toString()).not.toBe(user2Seasons[0]._id.toString());

      // Check that expenses are assigned to correct user's season
      const user1Expense = await Expense.findOne({ userId: testUser._id });
      const user2Expense = await Expense.findOne({ userId: testUser2._id });

      expect(user1Expense.seasonId.toString()).toBe(user1Seasons[0]._id.toString());
      expect(user2Expense.seasonId.toString()).toBe(user2Seasons[0]._id.toString());
    });
  });
});
