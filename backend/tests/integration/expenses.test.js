/**
 * Integration Tests for Expense Routes
 */

const request = require('supertest');
const app = require('../../src/app');
const { 
  mockFirebaseAuth, 
  createAuthHeaders, 
  assertSuccessResponse, 
  assertErrorResponse,
  assertPaginatedResponse,
  createTestUser,
  createTestCategories,
  createTestExpense,
  createTestExpenses,
  generateExpenseData
} = require('../utils/testHelpers');

describe('Expense Routes', () => {
  let testApp;
  let user;
  let categories;
  let category;

  beforeAll(async () => {
    const appInstance = new app();
    await appInstance.initialize();
    testApp = appInstance.app;
  });

  beforeEach(async () => {
    user = await createTestUser();
    categories = await createTestCategories(user._id);
    category = categories[0]; // Use first default category
    mockFirebaseAuth({ uid: user.firebaseUid });
  });

  describe('GET /api/v1/expenses', () => {
    beforeEach(async () => {
      // Create test expenses
      await createTestExpenses(user._id, category._id, 10);
    });

    it('should get user expenses with pagination', async () => {
      const response = await request(testApp)
        .get('/api/v1/expenses')
        .set(createAuthHeaders())
        .expect(200);

      assertPaginatedResponse(response);
      expect(response.body.data.length).toBeLessThanOrEqual(20); // Default limit
      expect(response.body.data[0]).toHaveProperty('formattedAmount');
      expect(response.body.data[0]).toHaveProperty('formattedDate');
    });

    it('should filter expenses by date range', async () => {
      const startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString();
      const endDate = new Date().toISOString();

      const response = await request(testApp)
        .get('/api/v1/expenses')
        .query({ startDate, endDate })
        .set(createAuthHeaders())
        .expect(200);

      assertPaginatedResponse(response);
    });

    it('should filter expenses by category', async () => {
      const response = await request(testApp)
        .get('/api/v1/expenses')
        .query({ categoryId: category._id.toString() })
        .set(createAuthHeaders())
        .expect(200);

      assertPaginatedResponse(response);
      response.body.data.forEach(expense => {
        expect(expense.categoryId._id).toBe(category._id.toString());
      });
    });

    it('should search expenses', async () => {
      const response = await request(testApp)
        .get('/api/v1/expenses')
        .query({ search: 'Test' })
        .set(createAuthHeaders())
        .expect(200);

      assertPaginatedResponse(response);
    });

    it('should validate pagination parameters', async () => {
      const response = await request(testApp)
        .get('/api/v1/expenses')
        .query({ page: 0, limit: 1000 })
        .set(createAuthHeaders())
        .expect(400);

      assertErrorResponse(response, 'VALIDATION_ERROR');
    });
  });

  describe('POST /api/v1/expenses', () => {
    it('should create new expense', async () => {
      const expenseData = generateExpenseData({
        categoryId: category._id.toString()
      });

      const response = await request(testApp)
        .post('/api/v1/expenses')
        .set(createAuthHeaders())
        .send(expenseData)
        .expect(201);

      assertSuccessResponse(response);
      expect(response.body.data.amount).toBe(expenseData.amount);
      expect(response.body.data.description).toBe(expenseData.description);
      expect(response.body.data.categoryId._id).toBe(category._id.toString());
      expect(response.body.data).toHaveProperty('seasonId');
    });

    it('should validate required fields', async () => {
      const invalidExpenseData = {
        // Missing categoryId and amount
        description: 'Test expense'
      };

      const response = await request(testApp)
        .post('/api/v1/expenses')
        .set(createAuthHeaders())
        .send(invalidExpenseData)
        .expect(400);

      assertErrorResponse(response, 'VALIDATION_ERROR');
    });

    it('should validate amount range', async () => {
      const invalidExpenseData = {
        categoryId: category._id.toString(),
        amount: -100, // Negative amount
        description: 'Test expense',
        date: new Date().toISOString()
      };

      const response = await request(testApp)
        .post('/api/v1/expenses')
        .set(createAuthHeaders())
        .send(invalidExpenseData)
        .expect(400);

      assertErrorResponse(response, 'VALIDATION_ERROR');
    });

    it('should validate category exists', async () => {
      const expenseData = generateExpenseData({
        categoryId: '507f1f77bcf86cd799439011' // Non-existent category
      });

      const response = await request(testApp)
        .post('/api/v1/expenses')
        .set(createAuthHeaders())
        .send(expenseData)
        .expect(400);

      assertErrorResponse(response, 'VALIDATION_ERROR');
    });

    it('should auto-detect season from date', async () => {
      const springDate = new Date(2024, 3, 15); // April 15
      const expenseData = generateExpenseData({
        categoryId: category._id.toString(),
        date: springDate.toISOString()
      });

      const response = await request(testApp)
        .post('/api/v1/expenses')
        .set(createAuthHeaders())
        .send(expenseData)
        .expect(201);

      assertSuccessResponse(response);
      expect(response.body.data.seasonId).toBe('spring');
    });
  });

  describe('GET /api/v1/expenses/:id', () => {
    let expense;

    beforeEach(async () => {
      expense = await createTestExpense(user._id, category._id);
    });

    it('should get expense by ID', async () => {
      const response = await request(testApp)
        .get(`/api/v1/expenses/${expense._id}`)
        .set(createAuthHeaders())
        .expect(200);

      assertSuccessResponse(response);
      expect(response.body.data._id).toBe(expense._id.toString());
      expect(response.body.data.categoryId).toBeDefined();
    });

    it('should fail with invalid expense ID', async () => {
      const response = await request(testApp)
        .get('/api/v1/expenses/invalid-id')
        .set(createAuthHeaders())
        .expect(400);

      assertErrorResponse(response, 'VALIDATION_ERROR');
    });

    it('should fail with non-existent expense', async () => {
      const response = await request(testApp)
        .get('/api/v1/expenses/507f1f77bcf86cd799439011')
        .set(createAuthHeaders())
        .expect(404);

      assertErrorResponse(response, 'RECORD_NOT_FOUND');
    });
  });

  describe('PUT /api/v1/expenses/:id', () => {
    let expense;

    beforeEach(async () => {
      expense = await createTestExpense(user._id, category._id);
    });

    it('should update expense', async () => {
      const updateData = {
        amount: 200,
        description: 'Updated description'
      };

      const response = await request(testApp)
        .put(`/api/v1/expenses/${expense._id}`)
        .set(createAuthHeaders())
        .send(updateData)
        .expect(200);

      assertSuccessResponse(response);
      expect(response.body.data.amount).toBe(200);
      expect(response.body.data.description).toBe('Updated description');
    });

    it('should validate update data', async () => {
      const invalidUpdateData = {
        amount: -50 // Invalid amount
      };

      const response = await request(testApp)
        .put(`/api/v1/expenses/${expense._id}`)
        .set(createAuthHeaders())
        .send(invalidUpdateData)
        .expect(400);

      assertErrorResponse(response, 'VALIDATION_ERROR');
    });
  });

  describe('DELETE /api/v1/expenses/:id', () => {
    let expense;

    beforeEach(async () => {
      expense = await createTestExpense(user._id, category._id);
    });

    it('should delete expense', async () => {
      const response = await request(testApp)
        .delete(`/api/v1/expenses/${expense._id}`)
        .set(createAuthHeaders())
        .expect(200);

      assertSuccessResponse(response);

      // Verify expense is soft deleted
      const getResponse = await request(testApp)
        .get(`/api/v1/expenses/${expense._id}`)
        .set(createAuthHeaders())
        .expect(404);
    });
  });

  describe('GET /api/v1/expenses/stats', () => {
    beforeEach(async () => {
      await createTestExpenses(user._id, category._id, 5);
    });

    it('should get expense statistics', async () => {
      const response = await request(testApp)
        .get('/api/v1/expenses/stats')
        .set(createAuthHeaders())
        .expect(200);

      assertSuccessResponse(response);
      expect(response.body.data).toHaveProperty('totalExpenses');
      expect(response.body.data).toHaveProperty('totalAmount');
      expect(response.body.data).toHaveProperty('avgAmount');
    });

    it('should get category stats', async () => {
      const response = await request(testApp)
        .get('/api/v1/expenses/stats')
        .query({ groupBy: 'category' })
        .set(createAuthHeaders())
        .expect(200);

      assertSuccessResponse(response);
      expect(Array.isArray(response.body.data)).toBe(true);
    });

    it('should get seasonal stats', async () => {
      const response = await request(testApp)
        .get('/api/v1/expenses/stats')
        .query({ groupBy: 'season' })
        .set(createAuthHeaders())
        .expect(200);

      assertSuccessResponse(response);
      expect(Array.isArray(response.body.data)).toBe(true);
    });
  });

  describe('Photo Management', () => {
    let expense;

    beforeEach(async () => {
      expense = await createTestExpense(user._id, category._id);
    });

    it('should add photo to expense', async () => {
      const photoData = {
        photoId: 'photo-123',
        photoUrl: 'https://example.com/photo.jpg',
        thumbnailUrl: 'https://example.com/thumb.jpg',
        filename: 'photo.jpg',
        size: 1024,
        mimeType: 'image/jpeg'
      };

      const response = await request(testApp)
        .post(`/api/v1/expenses/${expense._id}/photos`)
        .set(createAuthHeaders())
        .send(photoData)
        .expect(200);

      assertSuccessResponse(response);
      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0].id).toBe('photo-123');
    });

    it('should remove photo from expense', async () => {
      // First add a photo
      await expense.addPhoto({
        id: 'photo-123',
        url: 'https://example.com/photo.jpg',
        filename: 'photo.jpg',
        size: 1024,
        mimeType: 'image/jpeg'
      });

      const response = await request(testApp)
        .delete(`/api/v1/expenses/${expense._id}/photos/photo-123`)
        .set(createAuthHeaders())
        .expect(200);

      assertSuccessResponse(response);
      expect(response.body.data).toHaveLength(0);
    });
  });

  describe('Tag Management', () => {
    let expense;

    beforeEach(async () => {
      expense = await createTestExpense(user._id, category._id);
    });

    it('should add tag to expense', async () => {
      const response = await request(testApp)
        .post(`/api/v1/expenses/${expense._id}/tags`)
        .set(createAuthHeaders())
        .send({ tag: 'urgent' })
        .expect(200);

      assertSuccessResponse(response);
      expect(response.body.data).toContain('urgent');
    });

    it('should remove tag from expense', async () => {
      // First add a tag
      await expense.addTag('urgent');

      const response = await request(testApp)
        .delete(`/api/v1/expenses/${expense._id}/tags/urgent`)
        .set(createAuthHeaders())
        .expect(200);

      assertSuccessResponse(response);
      expect(response.body.data).not.toContain('urgent');
    });

    it('should validate tag length', async () => {
      const response = await request(testApp)
        .post(`/api/v1/expenses/${expense._id}/tags`)
        .set(createAuthHeaders())
        .send({ tag: 'a' }) // Too short
        .expect(400);

      assertErrorResponse(response, 'VALIDATION_ERROR');
    });
  });

  describe('CSV Export', () => {
    beforeEach(async () => {
      await createTestExpenses(user._id, category._id, 3);
    });

    it('should export expenses as CSV', async () => {
      const response = await request(testApp)
        .get('/api/v1/expenses/export/csv')
        .set(createAuthHeaders())
        .expect(200);

      expect(response.headers['content-type']).toContain('text/csv');
      expect(response.headers['content-disposition']).toContain('attachment');
      expect(response.text).toContain('Tarih,Kategori,Tutar');
    });

    it('should export expenses with date filter', async () => {
      const startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString();
      const endDate = new Date().toISOString();

      const response = await request(testApp)
        .get('/api/v1/expenses/export/csv')
        .query({ startDate, endDate })
        .set(createAuthHeaders())
        .expect(200);

      expect(response.headers['content-type']).toContain('text/csv');
    });
  });
});
