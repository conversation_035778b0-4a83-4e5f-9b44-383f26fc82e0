/**
 * Integration Tests for Auth Routes
 */

const request = require('supertest');
const app = require('../../src/app');
const { 
  mockFirebaseAuth, 
  createAuthHeaders, 
  assertSuccessResponse, 
  assertErrorResponse,
  createTestUser 
} = require('../utils/testHelpers');

describe('Auth Routes', () => {
  let testApp;

  beforeAll(async () => {
    const appInstance = new app();
    await appInstance.initialize();
    testApp = appInstance.app;
  });

  describe('POST /api/v1/auth/validate', () => {
    it('should validate token and create new user', async () => {
      const firebaseUserData = mockFirebaseAuth({
        uid: 'new-user-uid',
        email: '<EMAIL>',
        name: 'New User'
      });

      const response = await request(testApp)
        .post('/api/v1/auth/validate')
        .send({ firebaseToken: 'valid-token' })
        .expect(200);

      assertSuccessResponse(response);
      expect(response.body.data.isNewUser).toBe(true);
      expect(response.body.data.email).toBe('<EMAIL>');
      expect(response.body.data.name).toBe('New User');
    });

    it('should validate token and return existing user', async () => {
      // Create existing user
      const existingUser = await createTestUser({
        firebaseUid: 'existing-user-uid',
        email: '<EMAIL>'
      });

      const firebaseUserData = mockFirebaseAuth({
        uid: 'existing-user-uid',
        email: '<EMAIL>',
        name: 'Existing User'
      });

      const response = await request(testApp)
        .post('/api/v1/auth/validate')
        .send({ firebaseToken: 'valid-token' })
        .expect(200);

      assertSuccessResponse(response);
      expect(response.body.data.isNewUser).toBe(false);
      expect(response.body.data.email).toBe('<EMAIL>');
    });

    it('should fail with missing token', async () => {
      const response = await request(testApp)
        .post('/api/v1/auth/validate')
        .send({})
        .expect(400);

      assertErrorResponse(response, 'MISSING_TOKEN');
    });

    it('should fail with invalid token', async () => {
      const admin = require('firebase-admin');
      const mockAuth = admin.auth();
      mockAuth.verifyIdToken.mockRejectedValue(new Error('Invalid token'));

      const response = await request(testApp)
        .post('/api/v1/auth/validate')
        .send({ firebaseToken: 'invalid-token' })
        .expect(401);

      assertErrorResponse(response, 'INVALID_TOKEN');
    });
  });

  describe('GET /api/v1/auth/profile', () => {
    let user;

    beforeEach(async () => {
      user = await createTestUser();
      mockFirebaseAuth({ uid: user.firebaseUid });
    });

    it('should get user profile', async () => {
      const response = await request(testApp)
        .get('/api/v1/auth/profile')
        .set(createAuthHeaders())
        .expect(200);

      assertSuccessResponse(response);
      expect(response.body.data.email).toBe(user.email);
      expect(response.body.data.name).toBe(user.name);
      expect(response.body.data).not.toHaveProperty('firebaseUid');
    });

    it('should fail without authentication', async () => {
      const response = await request(testApp)
        .get('/api/v1/auth/profile')
        .expect(401);

      assertErrorResponse(response, 'MISSING_TOKEN');
    });
  });

  describe('PUT /api/v1/auth/profile', () => {
    let user;

    beforeEach(async () => {
      user = await createTestUser();
      mockFirebaseAuth({ uid: user.firebaseUid });
    });

    it('should update user profile', async () => {
      const updateData = {
        name: 'Updated Name',
        preferences: {
          currency: 'USD',
          language: 'en'
        },
        farmInfo: {
          name: 'Updated Farm',
          location: {
            city: 'Istanbul'
          }
        }
      };

      const response = await request(testApp)
        .put('/api/v1/auth/profile')
        .set(createAuthHeaders())
        .send(updateData)
        .expect(200);

      assertSuccessResponse(response);
      expect(response.body.data.name).toBe('Updated Name');
      expect(response.body.data.preferences.currency).toBe('USD');
      expect(response.body.data.farmInfo.name).toBe('Updated Farm');
    });

    it('should validate profile data', async () => {
      const invalidData = {
        name: 'A', // Too short
        preferences: {
          currency: 'INVALID' // Invalid currency
        }
      };

      const response = await request(testApp)
        .put('/api/v1/auth/profile')
        .set(createAuthHeaders())
        .send(invalidData)
        .expect(400);

      assertErrorResponse(response, 'VALIDATION_ERROR');
    });
  });

  describe('POST /api/v1/auth/device', () => {
    let user;

    beforeEach(async () => {
      user = await createTestUser();
      mockFirebaseAuth({ uid: user.firebaseUid });
    });

    it('should register device', async () => {
      const deviceData = {
        deviceId: 'device-123',
        platform: 'ios',
        appVersion: '1.0.0',
        pushToken: 'push-token-123'
      };

      const response = await request(testApp)
        .post('/api/v1/auth/device')
        .set(createAuthHeaders())
        .send(deviceData)
        .expect(200);

      assertSuccessResponse(response);
    });

    it('should validate device data', async () => {
      const invalidDeviceData = {
        deviceId: 'dev', // Too short
        platform: 'invalid' // Invalid platform
      };

      const response = await request(testApp)
        .post('/api/v1/auth/device')
        .set(createAuthHeaders())
        .send(invalidDeviceData)
        .expect(400);

      assertErrorResponse(response, 'VALIDATION_ERROR');
    });
  });

  describe('GET /api/v1/auth/stats', () => {
    let user;

    beforeEach(async () => {
      user = await createTestUser();
      mockFirebaseAuth({ uid: user.firebaseUid });
    });

    it('should get user statistics', async () => {
      const response = await request(testApp)
        .get('/api/v1/auth/stats')
        .set(createAuthHeaders())
        .expect(200);

      assertSuccessResponse(response);
      expect(response.body.data).toHaveProperty('totalExpenses');
      expect(response.body.data).toHaveProperty('totalAmount');
      expect(response.body.data).toHaveProperty('loginCount');
      expect(response.body.data).toHaveProperty('accountAge');
    });
  });

  describe('PATCH /api/v1/auth/preferences', () => {
    let user;

    beforeEach(async () => {
      user = await createTestUser();
      mockFirebaseAuth({ uid: user.firebaseUid });
    });

    it('should update user preferences', async () => {
      const preferences = {
        currency: 'EUR',
        language: 'en',
        defaultSeason: 'summer',
        notifications: {
          email: false,
          push: true
        }
      };

      const response = await request(testApp)
        .patch('/api/v1/auth/preferences')
        .set(createAuthHeaders())
        .send({ preferences })
        .expect(200);

      assertSuccessResponse(response);
      expect(response.body.data.currency).toBe('EUR');
      expect(response.body.data.language).toBe('en');
      expect(response.body.data.notifications.email).toBe(false);
    });

    it('should validate preferences', async () => {
      const invalidPreferences = {
        currency: 'INVALID',
        language: 'invalid',
        defaultSeason: 'invalid'
      };

      const response = await request(testApp)
        .patch('/api/v1/auth/preferences')
        .set(createAuthHeaders())
        .send({ preferences: invalidPreferences })
        .expect(400);

      assertErrorResponse(response, 'VALIDATION_ERROR');
    });
  });

  describe('POST /api/v1/auth/logout', () => {
    let user;

    beforeEach(async () => {
      user = await createTestUser();
      mockFirebaseAuth({ uid: user.firebaseUid });
    });

    it('should logout user', async () => {
      const response = await request(testApp)
        .post('/api/v1/auth/logout')
        .set(createAuthHeaders())
        .send({ deviceId: 'device-123' })
        .expect(200);

      assertSuccessResponse(response);
    });

    it('should logout without device ID', async () => {
      const response = await request(testApp)
        .post('/api/v1/auth/logout')
        .set(createAuthHeaders())
        .send({})
        .expect(200);

      assertSuccessResponse(response);
    });
  });

  describe('GET /api/v1/auth/export', () => {
    let user;

    beforeEach(async () => {
      user = await createTestUser();
      mockFirebaseAuth({ uid: user.firebaseUid });
    });

    it('should export user data', async () => {
      const response = await request(testApp)
        .get('/api/v1/auth/export')
        .set(createAuthHeaders())
        .expect(200);

      assertSuccessResponse(response);
      expect(response.body.data).toHaveProperty('user');
      expect(response.body.data).toHaveProperty('expenses');
      expect(response.body.data).toHaveProperty('categories');
      expect(response.body.data).toHaveProperty('exportDate');
    });
  });

  describe('GET /api/v1/auth/check', () => {
    let user;

    beforeEach(async () => {
      user = await createTestUser();
      mockFirebaseAuth({ uid: user.firebaseUid });
    });

    it('should check authentication status', async () => {
      const response = await request(testApp)
        .get('/api/v1/auth/check')
        .set(createAuthHeaders())
        .expect(200);

      assertSuccessResponse(response);
      expect(response.body.data.authenticated).toBe(true);
      expect(response.body.data.email).toBe(user.email);
    });
  });

  describe('DELETE /api/v1/auth/account', () => {
    let user;

    beforeEach(async () => {
      user = await createTestUser();
      mockFirebaseAuth({ uid: user.firebaseUid });
    });

    it('should delete user account with confirmation', async () => {
      const response = await request(testApp)
        .delete('/api/v1/auth/account')
        .set(createAuthHeaders())
        .send({ confirmation: 'DELETE_MY_ACCOUNT' })
        .expect(200);

      assertSuccessResponse(response);
    });

    it('should fail without proper confirmation', async () => {
      const response = await request(testApp)
        .delete('/api/v1/auth/account')
        .set(createAuthHeaders())
        .send({ confirmation: 'wrong' })
        .expect(400);

      assertErrorResponse(response, 'VALIDATION_ERROR');
    });
  });
});
