/**
 * Unit Tests for User Model
 */

const User = require('../../../src/models/User');
const { createTestUser } = require('../../utils/testHelpers');

describe('User Model', () => {
  describe('User Creation', () => {
    it('should create a user with valid data', async () => {
      const userData = {
        firebaseUid: 'test-uid-123',
        email: '<EMAIL>',
        name: 'Test User',
        emailVerified: true,
        provider: 'google.com'
      };

      const user = await User.create(userData);

      expect(user.firebaseUid).toBe(userData.firebaseUid);
      expect(user.email).toBe(userData.email);
      expect(user.name).toBe(userData.name);
      expect(user.emailVerified).toBe(true);
      expect(user.provider).toBe('google.com');
      expect(user.status).toBe('active');
      expect(user.type).toBe('free');
      expect(user.isAdmin).toBe(false);
    });

    it('should fail to create user without required fields', async () => {
      const invalidUserData = {
        email: '<EMAIL>'
        // Missing firebaseUid and name
      };

      await expect(User.create(invalidUserData)).rejects.toThrow();
    });

    it('should fail to create user with duplicate email', async () => {
      const userData = {
        firebaseUid: 'test-uid-123',
        email: '<EMAIL>',
        name: 'Test User'
      };

      await User.create(userData);

      const duplicateUserData = {
        firebaseUid: 'test-uid-456',
        email: '<EMAIL>', // Same email
        name: 'Another User'
      };

      await expect(User.create(duplicateUserData)).rejects.toThrow();
    });

    it('should fail to create user with duplicate firebaseUid', async () => {
      const userData = {
        firebaseUid: 'test-uid-123',
        email: '<EMAIL>',
        name: 'Test User'
      };

      await User.create(userData);

      const duplicateUserData = {
        firebaseUid: 'test-uid-123', // Same firebaseUid
        email: '<EMAIL>',
        name: 'Another User'
      };

      await expect(User.create(duplicateUserData)).rejects.toThrow();
    });
  });

  describe('User Validation', () => {
    it('should validate email format', async () => {
      const invalidEmailData = {
        firebaseUid: 'test-uid-123',
        email: 'invalid-email',
        name: 'Test User'
      };

      await expect(User.create(invalidEmailData)).rejects.toThrow();
    });

    it('should validate provider enum', async () => {
      const invalidProviderData = {
        firebaseUid: 'test-uid-123',
        email: '<EMAIL>',
        name: 'Test User',
        provider: 'invalid-provider'
      };

      await expect(User.create(invalidProviderData)).rejects.toThrow();
    });

    it('should validate user type enum', async () => {
      const invalidTypeData = {
        firebaseUid: 'test-uid-123',
        email: '<EMAIL>',
        name: 'Test User',
        type: 'invalid-type'
      };

      await expect(User.create(invalidTypeData)).rejects.toThrow();
    });
  });

  describe('User Methods', () => {
    let user;

    beforeEach(async () => {
      user = await createTestUser();
    });

    it('should update login info', async () => {
      const originalLoginCount = user.stats.loginCount;
      const originalLastLogin = user.stats.lastLoginAt;

      await user.updateLoginInfo();

      expect(user.stats.loginCount).toBe(originalLoginCount + 1);
      expect(user.stats.lastLoginAt).not.toBe(originalLastLogin);
    });

    it('should add device info', async () => {
      const deviceInfo = {
        deviceId: 'device-123',
        platform: 'ios',
        appVersion: '1.0.0',
        pushToken: 'push-token-123'
      };

      await user.addDevice(deviceInfo);

      expect(user.devices).toHaveLength(1);
      expect(user.devices[0].deviceId).toBe(deviceInfo.deviceId);
      expect(user.devices[0].platform).toBe(deviceInfo.platform);
      expect(user.devices[0].lastSeen).toBeDefined();
    });

    it('should update existing device', async () => {
      const deviceInfo = {
        deviceId: 'device-123',
        platform: 'ios',
        appVersion: '1.0.0'
      };

      await user.addDevice(deviceInfo);
      expect(user.devices).toHaveLength(1);

      // Update same device
      const updatedDeviceInfo = {
        deviceId: 'device-123',
        platform: 'ios',
        appVersion: '1.1.0'
      };

      await user.addDevice(updatedDeviceInfo);
      expect(user.devices).toHaveLength(1);
      expect(user.devices[0].appVersion).toBe('1.1.0');
    });

    it('should limit devices to 5', async () => {
      // Add 6 devices
      for (let i = 0; i < 6; i++) {
        await user.addDevice({
          deviceId: `device-${i}`,
          platform: 'ios',
          appVersion: '1.0.0'
        });
      }

      expect(user.devices).toHaveLength(5);
      expect(user.devices[0].deviceId).toBe('device-1'); // First device should be removed
    });

    it('should update user stats', async () => {
      const expenseData = {
        amount: 100,
        date: new Date()
      };

      const originalExpenses = user.stats.totalExpenses;
      const originalAmount = user.stats.totalAmount;

      await user.updateStats(expenseData);

      expect(user.stats.totalExpenses).toBe(originalExpenses + 1);
      expect(user.stats.totalAmount).toBe(originalAmount + 100);
      expect(user.stats.lastExpenseDate).toEqual(expenseData.date);
    });

    it('should return public JSON without sensitive data', () => {
      const publicData = user.toPublicJSON();

      expect(publicData).not.toHaveProperty('firebaseUid');
      expect(publicData).not.toHaveProperty('devices');
      expect(publicData).not.toHaveProperty('dataRetention');
      expect(publicData).toHaveProperty('email');
      expect(publicData).toHaveProperty('name');
    });
  });

  describe('User Virtuals', () => {
    let user;

    beforeEach(async () => {
      user = await createTestUser({
        name: 'John Doe',
        farmInfo: {
          name: 'Test Farm'
        }
      });
    });

    it('should return full name virtual', () => {
      expect(user.fullName).toBe('John Doe');
    });

    it('should return farm display name virtual', () => {
      expect(user.farmDisplayName).toBe('Test Farm');
    });

    it('should return default farm display name when no farm name', async () => {
      const userWithoutFarm = await createTestUser({
        name: 'Jane Doe'
      });

      expect(userWithoutFarm.farmDisplayName).toBe('Jane Doe\'in Çiftliği');
    });

    it('should check subscription status', async () => {
      // Free user
      expect(user.isSubscriptionActive).toBe(true);

      // Premium user with active subscription
      user.subscription.plan = 'premium';
      user.subscription.endDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30 days from now
      expect(user.isSubscriptionActive).toBe(true);

      // Premium user with expired subscription
      user.subscription.endDate = new Date(Date.now() - 24 * 60 * 60 * 1000); // Yesterday
      expect(user.isSubscriptionActive).toBe(false);
    });
  });

  describe('User Static Methods', () => {
    beforeEach(async () => {
      // Create test users
      await createTestUser({ firebaseUid: 'uid-1', email: '<EMAIL>', status: 'active' });
      await createTestUser({ firebaseUid: 'uid-2', email: '<EMAIL>', status: 'active', type: 'premium' });
      await createTestUser({ firebaseUid: 'uid-3', email: '<EMAIL>', status: 'inactive' });
    });

    it('should find user by firebase UID', async () => {
      const user = await User.findByFirebaseUid('uid-1');
      expect(user).toBeTruthy();
      expect(user.email).toBe('<EMAIL>');
    });

    it('should find active users', async () => {
      const activeUsers = await User.findActiveUsers();
      expect(activeUsers).toHaveLength(2);
      activeUsers.forEach(user => {
        expect(user.status).toBe('active');
      });
    });

    it('should get user statistics', async () => {
      const stats = await User.getUserStats();
      expect(stats).toHaveLength(1);
      
      const stat = stats[0];
      expect(stat.totalUsers).toBe(3);
      expect(stat.activeUsers).toBe(2);
      expect(stat.premiumUsers).toBe(1);
    });
  });
});
