/**
 * Unit Tests for Boolean Sanitization in Season Routes
 * Tests the fix for migration validation errors
 */

const request = require('supertest');
const app = require('../../src/app');
const {
  createTestUser,
  createAuthHeaders,
  assertSuccessResponse,
  cleanupTestData,
  mockFirebaseAuth
} = require('../utils/testHelpers');

describe('Boolean Sanitization in Season Routes', () => {
  let testApp;
  let testUser;

  beforeAll(async () => {
    const appInstance = new app();
    testApp = appInstance.app;
  });

  beforeEach(async () => {
    await cleanupTestData();
    testUser = await createTestUser();
    // Mock Firebase authentication for each test
    mockFirebaseAuth({ uid: testUser.firebaseUid });
  });

  afterEach(async () => {
    await cleanupTestData();
  });

  describe('POST /api/v1/seasons - Boolean Sanitization', () => {
    it('should sanitize string boolean values to actual booleans', async () => {
      const seasonData = {
        name: 'Test Season String Booleans',
        description: 'Test season with string boolean values',
        startDate: new Date().toISOString(),
        isActive: 'true',    // String boolean
        isDefault: 'false',  // String boolean
        color: '#4CAF50',
        emoji: '🌱'
      };

      const response = await request(testApp)
        .post('/api/v1/seasons')
        .set(createAuthHeaders())
        .send(seasonData)
        .expect(201);

      assertSuccessResponse(response);
      
      // Verify that string booleans were converted to actual booleans
      expect(response.body.data.isActive).toBe(true);
      expect(response.body.data.isDefault).toBe(false);
      expect(typeof response.body.data.isActive).toBe('boolean');
      expect(typeof response.body.data.isDefault).toBe('boolean');
    });

    it('should handle actual boolean values normally', async () => {
      const seasonData = {
        name: 'Test Season Real Booleans',
        description: 'Test season with real boolean values',
        startDate: new Date().toISOString(),
        isActive: true,     // Real boolean
        isDefault: false,   // Real boolean
        color: '#4CAF50',
        emoji: '🌱'
      };

      const response = await request(testApp)
        .post('/api/v1/seasons')
        .set(createAuthHeaders())
        .send(seasonData)
        .expect(201);

      assertSuccessResponse(response);
      
      // Verify that real booleans remain as booleans
      expect(response.body.data.isActive).toBe(true);
      expect(response.body.data.isDefault).toBe(false);
      expect(typeof response.body.data.isActive).toBe('boolean');
      expect(typeof response.body.data.isDefault).toBe('boolean');
    });

    it('should handle mixed case string booleans', async () => {
      const seasonData = {
        name: 'Test Season Mixed Case',
        description: 'Test season with mixed case string booleans',
        startDate: new Date().toISOString(),
        isActive: 'TRUE',   // Uppercase string
        isDefault: 'False', // Mixed case string
        color: '#4CAF50',
        emoji: '🌱'
      };

      const response = await request(testApp)
        .post('/api/v1/seasons')
        .set(createAuthHeaders())
        .send(seasonData)
        .expect(201);

      assertSuccessResponse(response);
      
      // Verify that mixed case string booleans were converted correctly
      expect(response.body.data.isActive).toBe(true);
      expect(response.body.data.isDefault).toBe(false);
      expect(typeof response.body.data.isActive).toBe('boolean');
      expect(typeof response.body.data.isDefault).toBe('boolean');
    });

    it('should reject invalid string boolean values', async () => {
      const seasonData = {
        name: 'Test Season Invalid Boolean',
        description: 'Test season with invalid boolean values',
        startDate: new Date().toISOString(),
        isActive: 'invalid',  // Invalid string
        isDefault: 'yes',     // Invalid string
        color: '#4CAF50',
        emoji: '🌱'
      };

      const response = await request(testApp)
        .post('/api/v1/seasons')
        .set(createAuthHeaders())
        .send(seasonData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('VALIDATION_ERROR');
    });

    it('should handle migration-like data structure', async () => {
      // Simulate the exact data structure sent during migration
      const migrationSeasonData = {
        name: '2025 Sezonu (Migration)',
        description: 'Guest verilerinden aktarılan sezon',
        startDate: new Date().toISOString(),
        isActive: 'true',    // String boolean as sent from frontend
        isDefault: 'true',   // String boolean as sent from frontend
        color: '#4CAF50',
        emoji: '🌱'
      };

      const response = await request(testApp)
        .post('/api/v1/seasons')
        .set(createAuthHeaders())
        .send(migrationSeasonData)
        .expect(201);

      assertSuccessResponse(response);
      
      // Verify migration data was processed correctly
      expect(response.body.data.name).toBe('2025 Sezonu (Migration)');
      expect(response.body.data.description).toBe('Guest verilerinden aktarılan sezon');
      expect(response.body.data.isActive).toBe(true);
      expect(response.body.data.isDefault).toBe(true);
      expect(typeof response.body.data.isActive).toBe('boolean');
      expect(typeof response.body.data.isDefault).toBe('boolean');
    });

    it('should handle optional boolean fields', async () => {
      const seasonData = {
        name: 'Test Season Optional Fields',
        description: 'Test season without boolean fields',
        startDate: new Date().toISOString(),
        // isActive and isDefault are optional
        color: '#4CAF50',
        emoji: '🌱'
      };

      const response = await request(testApp)
        .post('/api/v1/seasons')
        .set(createAuthHeaders())
        .send(seasonData)
        .expect(201);

      assertSuccessResponse(response);
      
      // Verify default values are applied
      expect(response.body.data.isActive).toBe(false); // Default value
      expect(response.body.data.isDefault).toBe(false); // Default value
    });
  });
});
