/**
 * Database Index Performance Tests
 * Tests for Season and Expense model index performance
 */

const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');

// Import models
const User = require('../../src/models/User');
const Season = require('../../src/models/Season');
const Expense = require('../../src/models/Expense');
const Category = require('../../src/models/Category');

describe('Database Index Performance', () => {
  let mongoServer;
  let testUsers = [];
  let testCategories = [];
  let testSeasons = [];

  beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    await mongoose.connect(mongoUri);

    // Create test data
    await setupTestData();
  });

  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });

  async function setupTestData() {
    console.log('Setting up test data...');

    // Create test users
    for (let i = 0; i < 10; i++) {
      const user = await User.create({
        email: `user${i}@test.com`,
        name: `Test User ${i}`,
        createdAt: new Date(2024, 0, i + 1)
      });
      testUsers.push(user);
    }

    // Create test categories
    const categoryNames = ['Gübre', 'İşçilik', 'İlaç', 'Su', 'Yakıt'];
    for (const name of categoryNames) {
      const category = await Category.create({
        name,
        emoji: '🧪'
      });
      testCategories.push(category);
    }

    // Create test seasons for each user
    for (const user of testUsers) {
      for (let i = 0; i < 3; i++) {
        const season = await Season.create({
          userId: user._id,
          name: `${2024 - i} Sezonu`,
          description: `Test season ${i}`,
          startDate: new Date(2024 - i, 0, 1),
          endDate: new Date(2024 - i, 11, 31),
          isActive: i === 0,
          isDefault: i === 0,
          color: '#4CAF50',
          emoji: '🌱'
        });
        testSeasons.push(season);
      }
    }

    // Create test expenses
    for (const user of testUsers) {
      const userSeasons = testSeasons.filter(s => s.userId.toString() === user._id.toString());
      
      for (let i = 0; i < 50; i++) {
        const randomSeason = userSeasons[Math.floor(Math.random() * userSeasons.length)];
        const randomCategory = testCategories[Math.floor(Math.random() * testCategories.length)];
        
        await Expense.create({
          userId: user._id,
          categoryId: randomCategory._id,
          seasonId: randomSeason._id,
          amount: Math.floor(Math.random() * 1000) + 100,
          description: `Test expense ${i}`,
          date: new Date(2024, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1),
          notes: `Test notes for expense ${i}`,
          tags: [`tag${i % 5}`, `category${i % 3}`]
        });
      }
    }

    console.log(`Created ${testUsers.length} users, ${testSeasons.length} seasons, ${testUsers.length * 50} expenses`);
  }

  describe('Season Index Performance', () => {
    test('should efficiently query active season for user', async () => {
      const testUser = testUsers[0];
      
      const startTime = Date.now();
      const activeSeason = await Season.findOne({
        userId: testUser._id,
        isActive: true
      });
      const endTime = Date.now();
      
      expect(activeSeason).toBeDefined();
      expect(activeSeason.isActive).toBe(true);
      expect(endTime - startTime).toBeLessThan(50); // Should be very fast with index
    });

    test('should efficiently list user seasons', async () => {
      const testUser = testUsers[0];
      
      const startTime = Date.now();
      const userSeasons = await Season.find({
        userId: testUser._id
      }).sort({ createdAt: -1 });
      const endTime = Date.now();
      
      expect(userSeasons).toHaveLength(3);
      expect(endTime - startTime).toBeLessThan(50);
    });

    test('should efficiently query seasons by date range', async () => {
      const testUser = testUsers[0];
      const startDate = new Date(2024, 0, 1);
      const endDate = new Date(2024, 11, 31);
      
      const startTime = Date.now();
      const seasonsInRange = await Season.find({
        userId: testUser._id,
        startDate: { $lte: endDate },
        endDate: { $gte: startDate }
      });
      const endTime = Date.now();
      
      expect(seasonsInRange.length).toBeGreaterThan(0);
      expect(endTime - startTime).toBeLessThan(50);
    });

    test('should efficiently search seasons by text', async () => {
      const startTime = Date.now();
      const searchResults = await Season.find({
        $text: { $search: "2024" }
      });
      const endTime = Date.now();
      
      expect(searchResults.length).toBeGreaterThan(0);
      expect(endTime - startTime).toBeLessThan(100);
    });

    test('should efficiently query default season', async () => {
      const testUser = testUsers[0];
      
      const startTime = Date.now();
      const defaultSeason = await Season.findOne({
        userId: testUser._id,
        isDefault: true
      });
      const endTime = Date.now();
      
      expect(defaultSeason).toBeDefined();
      expect(defaultSeason.isDefault).toBe(true);
      expect(endTime - startTime).toBeLessThan(50);
    });
  });

  describe('Expense Index Performance', () => {
    test('should efficiently query expenses by user and date', async () => {
      const testUser = testUsers[0];
      
      const startTime = Date.now();
      const userExpenses = await Expense.find({
        userId: testUser._id
      }).sort({ date: -1 }).limit(10);
      const endTime = Date.now();
      
      expect(userExpenses).toHaveLength(10);
      expect(endTime - startTime).toBeLessThan(50);
    });

    test('should efficiently query expenses by season', async () => {
      const testSeason = testSeasons[0];
      
      const startTime = Date.now();
      const seasonExpenses = await Expense.find({
        seasonId: testSeason._id
      }).sort({ date: -1 });
      const endTime = Date.now();
      
      expect(seasonExpenses.length).toBeGreaterThan(0);
      expect(endTime - startTime).toBeLessThan(50);
    });

    test('should efficiently query expenses by user, season and date', async () => {
      const testUser = testUsers[0];
      const testSeason = testSeasons.find(s => s.userId.toString() === testUser._id.toString());
      
      const startTime = Date.now();
      const expenses = await Expense.find({
        userId: testUser._id,
        seasonId: testSeason._id
      }).sort({ date: -1 });
      const endTime = Date.now();
      
      expect(expenses.length).toBeGreaterThan(0);
      expect(endTime - startTime).toBeLessThan(50);
    });

    test('should efficiently query expenses by season and category', async () => {
      const testSeason = testSeasons[0];
      const testCategory = testCategories[0];
      
      const startTime = Date.now();
      const expenses = await Expense.find({
        seasonId: testSeason._id,
        categoryId: testCategory._id
      });
      const endTime = Date.now();
      
      expect(endTime - startTime).toBeLessThan(50);
    });

    test('should efficiently aggregate season totals', async () => {
      const testSeason = testSeasons[0];
      
      const startTime = Date.now();
      const aggregation = await Expense.aggregate([
        {
          $match: {
            seasonId: testSeason._id
          }
        },
        {
          $group: {
            _id: '$categoryId',
            totalAmount: { $sum: '$amount' },
            count: { $sum: 1 }
          }
        },
        {
          $sort: { totalAmount: -1 }
        }
      ]);
      const endTime = Date.now();
      
      expect(aggregation.length).toBeGreaterThan(0);
      expect(endTime - startTime).toBeLessThan(100);
    });

    test('should efficiently search expenses by text', async () => {
      const startTime = Date.now();
      const searchResults = await Expense.find({
        $text: { $search: "test" }
      }).limit(10);
      const endTime = Date.now();
      
      expect(searchResults.length).toBeGreaterThan(0);
      expect(endTime - startTime).toBeLessThan(100);
    });
  });

  describe('Complex Query Performance', () => {
    test('should efficiently get season summary with expenses', async () => {
      const testSeason = testSeasons[0];
      
      const startTime = Date.now();
      const summary = await Season.aggregate([
        {
          $match: {
            _id: testSeason._id
          }
        },
        {
          $lookup: {
            from: 'expenses',
            localField: '_id',
            foreignField: 'seasonId',
            as: 'expenses'
          }
        },
        {
          $addFields: {
            totalExpenses: { $size: '$expenses' },
            totalAmount: { $sum: '$expenses.amount' },
            avgAmount: { $avg: '$expenses.amount' }
          }
        }
      ]);
      const endTime = Date.now();
      
      expect(summary).toHaveLength(1);
      expect(summary[0].totalExpenses).toBeGreaterThan(0);
      expect(endTime - startTime).toBeLessThan(100);
    });

    test('should efficiently get user dashboard data', async () => {
      const testUser = testUsers[0];
      
      const startTime = Date.now();
      
      // Simulate dashboard queries
      const [activeSeason, recentExpenses, monthlyTotal] = await Promise.all([
        Season.findOne({ userId: testUser._id, isActive: true }),
        Expense.find({ userId: testUser._id }).sort({ date: -1 }).limit(5),
        Expense.aggregate([
          {
            $match: {
              userId: testUser._id,
              date: {
                $gte: new Date(2024, 0, 1),
                $lte: new Date(2024, 0, 31)
              }
            }
          },
          {
            $group: {
              _id: null,
              total: { $sum: '$amount' }
            }
          }
        ])
      ]);
      
      const endTime = Date.now();
      
      expect(activeSeason).toBeDefined();
      expect(recentExpenses.length).toBeGreaterThan(0);
      expect(endTime - startTime).toBeLessThan(150);
    });
  });

  describe('Index Usage Verification', () => {
    test('should use indexes for season queries', async () => {
      const testUser = testUsers[0];
      
      // Test active season query plan
      const explainResult = await Season.find({
        userId: testUser._id,
        isActive: true
      }).explain('executionStats');
      
      expect(explainResult.executionStats.executionSuccess).toBe(true);
      expect(explainResult.executionStats.totalDocsExamined).toBeLessThanOrEqual(
        explainResult.executionStats.totalDocsReturned + 5
      );
    });

    test('should use indexes for expense queries', async () => {
      const testUser = testUsers[0];
      const testSeason = testSeasons.find(s => s.userId.toString() === testUser._id.toString());
      
      // Test season expense query plan
      const explainResult = await Expense.find({
        userId: testUser._id,
        seasonId: testSeason._id
      }).explain('executionStats');
      
      expect(explainResult.executionStats.executionSuccess).toBe(true);
      expect(explainResult.executionStats.totalDocsExamined).toBeLessThanOrEqual(
        explainResult.executionStats.totalDocsReturned + 10
      );
    });
  });
});
