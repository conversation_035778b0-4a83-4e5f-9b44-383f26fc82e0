/**
 * End-to-End Tests for Two-Mode System
 * Tests complete user workflows from simple to detailed mode
 */

const request = require('supertest');
const mongoose = require('mongoose');
const app = require('../../src/app');
const User = require('../../src/models/User');
const Expense = require('../../src/models/Expense');
const Category = require('../../src/models/Category');
const Field = require('../../src/models/Field');
const Crop = require('../../src/models/Crop');

describe('Two-Mode System E2E Tests', () => {
  let testUser;
  let authToken;
  let testCategory;

  beforeAll(async () => {
    if (mongoose.connection.readyState === 0) {
      await mongoose.connect(process.env.MONGODB_TEST_URI || 'mongodb://localhost:27017/ciftcinotdefterim_test');
    }
  });

  beforeEach(async () => {
    // Clean database
    await User.deleteMany({});
    await Expense.deleteMany({});
    await Category.deleteMany({});
    await Field.deleteMany({});
    await Crop.deleteMany({});

    // Create test user
    testUser = await User.create({
      firebaseUid: 'test-user-e2e',
      email: '<EMAIL>',
      name: 'E2E Test User',
      preferences: {
        currency: 'TRY',
        language: 'tr',
        trackingMode: 'simple'
      }
    });

    // Create test category
    testCategory = await Category.create({
      name: 'Test Kategori',
      emoji: '🧪',
      color: '#4CAF50',
      isDefault: true,
      isActive: true
    });

    authToken = 'mock-firebase-token';
  });

  afterAll(async () => {
    await mongoose.connection.close();
  });

  describe('Complete User Journey: Simple to Detailed Mode', () => {
    test('should complete full workflow from simple to detailed mode', async () => {
      // 1. Start in simple mode - add some expenses
      console.log('Step 1: Adding expenses in simple mode');
      
      const simpleExpense1 = await request(app)
        .post('/api/v1/expenses')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          categoryId: testCategory._id,
          amount: 500,
          date: '2024-01-15',
          description: 'Basit mod gider 1'
        })
        .expect(201);

      const simpleExpense2 = await request(app)
        .post('/api/v1/expenses')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          categoryId: testCategory._id,
          amount: 300,
          date: '2024-01-20',
          description: 'Basit mod gider 2'
        })
        .expect(201);

      expect(simpleExpense1.body.data.trackingMode).toBe('simple');
      expect(simpleExpense2.body.data.trackingMode).toBe('simple');

      // 2. Check current mode
      console.log('Step 2: Checking current mode');
      
      const modeCheck = await request(app)
        .get('/api/v1/users/mode')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(modeCheck.body.data.mode).toBe('simple');
      expect(modeCheck.body.data.defaultField).toBeNull();

      // 3. Switch to detailed mode
      console.log('Step 3: Switching to detailed mode');
      
      const modeSwitch = await request(app)
        .post('/api/v1/users/mode')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ mode: 'detailed', confirmed: true })
        .expect(200);

      expect(modeSwitch.body.data.mode).toBe('detailed');
      expect(modeSwitch.body.data.defaultField).toBeDefined();
      expect(modeSwitch.body.data.defaultField.name).toBe('Ana Tarla');

      // 4. Verify expenses were migrated
      console.log('Step 4: Verifying expense migration');
      
      const migratedExpenses = await request(app)
        .get('/api/v1/expenses')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(migratedExpenses.body.data).toHaveLength(2);
      migratedExpenses.body.data.forEach(expense => {
        expect(expense.trackingMode).toBe('detailed');
        expect(expense.fieldId).toBeDefined();
      });

      // 5. Get fields list
      console.log('Step 5: Getting fields list');
      
      const fieldsResponse = await request(app)
        .get('/api/v1/fields')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(fieldsResponse.body.data).toHaveLength(1);
      const defaultField = fieldsResponse.body.data[0];
      expect(defaultField.name).toBe('Ana Tarla');
      expect(defaultField.isDefault).toBe(true);

      // 6. Add new field
      console.log('Step 6: Adding new field');
      
      const newField = await request(app)
        .post('/api/v1/fields')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          name: 'Kuzey Tarla',
          size: { value: 5, unit: 'dekar' },
          notes: 'Test tarlası'
        })
        .expect(201);

      expect(newField.body.data.name).toBe('Kuzey Tarla');
      expect(newField.body.data.isDefault).toBe(false);

      // 7. Get crops list
      console.log('Step 7: Getting crops list');
      
      const cropsResponse = await request(app)
        .get('/api/v1/crops')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(cropsResponse.body.data.length).toBeGreaterThan(0);
      const testCrop = cropsResponse.body.data.find(crop => crop.name === 'wheat');
      expect(testCrop).toBeDefined();

      // 8. Add expense in detailed mode with field and crop
      console.log('Step 8: Adding expense in detailed mode');
      
      const detailedExpense = await request(app)
        .post('/api/v1/expenses')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          categoryId: testCategory._id,
          fieldId: newField.body.data._id,
          cropId: testCrop._id,
          amount: 750,
          date: '2024-01-25',
          description: 'Detaylı mod gider'
        })
        .expect(201);

      expect(detailedExpense.body.data.trackingMode).toBe('detailed');
      expect(detailedExpense.body.data.fieldId).toBe(newField.body.data._id);
      expect(detailedExpense.body.data.cropId).toBe(testCrop._id);

      // 9. Filter expenses by field
      console.log('Step 9: Filtering expenses by field');
      
      const fieldFilteredExpenses = await request(app)
        .get(`/api/v1/expenses?fieldId=${newField.body.data._id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(fieldFilteredExpenses.body.data).toHaveLength(1);
      expect(fieldFilteredExpenses.body.data[0].description).toBe('Detaylı mod gider');

      // 10. Test field statistics
      console.log('Step 10: Testing field statistics');
      
      const fieldStats = await request(app)
        .get(`/api/v1/fields/${newField.body.data._id}/stats`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(fieldStats.body.data.stats.totalExpenses).toBe(1);
      expect(fieldStats.body.data.stats.totalAmount).toBe(750);

      console.log('✅ Complete user journey test passed!');
    });
  });

  describe('Mode Switching Validation', () => {
    test('should require confirmation for mode switch with data impact', async () => {
      // Add expense in simple mode
      await request(app)
        .post('/api/v1/expenses')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          categoryId: testCategory._id,
          amount: 500,
          date: '2024-01-15',
          description: 'Test gider'
        })
        .expect(201);

      // Try to switch without confirmation
      const switchAttempt = await request(app)
        .post('/api/v1/users/mode')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ mode: 'detailed' })
        .expect(400);

      expect(switchAttempt.body.requiresConfirmation).toBe(true);
      expect(switchAttempt.body.validation.warnings).toBeDefined();
      expect(switchAttempt.body.validation.dataImpact.existingExpenses).toBe(1);

      // Switch with confirmation
      const confirmedSwitch = await request(app)
        .post('/api/v1/users/mode')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ mode: 'detailed', confirmed: true })
        .expect(200);

      expect(confirmedSwitch.body.data.mode).toBe('detailed');
    });

    test('should validate switching back to simple mode', async () => {
      // Switch to detailed mode first
      await request(app)
        .post('/api/v1/users/mode')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ mode: 'detailed', confirmed: true })
        .expect(200);

      // Add detailed expense
      const fieldsResponse = await request(app)
        .get('/api/v1/fields')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      const defaultField = fieldsResponse.body.data[0];

      await request(app)
        .post('/api/v1/expenses')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          categoryId: testCategory._id,
          fieldId: defaultField._id,
          amount: 600,
          date: '2024-01-20',
          description: 'Detaylı gider'
        })
        .expect(201);

      // Try to switch back to simple mode
      const switchBack = await request(app)
        .post('/api/v1/users/mode')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ mode: 'simple' })
        .expect(400);

      expect(switchBack.body.requiresConfirmation).toBe(true);
      expect(switchBack.body.validation.warnings).toBeDefined();
      expect(switchBack.body.validation.consequences).toBeDefined();

      // Confirm switch back
      const confirmedSwitchBack = await request(app)
        .post('/api/v1/users/mode')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ mode: 'simple', confirmed: true })
        .expect(200);

      expect(confirmedSwitchBack.body.data.mode).toBe('simple');
    });
  });

  describe('Field Management Workflow', () => {
    test('should complete field management operations', async () => {
      // Switch to detailed mode
      await request(app)
        .post('/api/v1/users/mode')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ mode: 'detailed', confirmed: true })
        .expect(200);

      // Create multiple fields
      const field1 = await request(app)
        .post('/api/v1/fields')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          name: 'Güney Tarla',
          size: { value: 10, unit: 'dekar' },
          location: { address: 'Güney bölge' }
        })
        .expect(201);

      const field2 = await request(app)
        .post('/api/v1/fields')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          name: 'Doğu Tarla',
          size: { value: 8, unit: 'dekar' }
        })
        .expect(201);

      // List all fields
      const allFields = await request(app)
        .get('/api/v1/fields')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(allFields.body.data).toHaveLength(3); // Including default field

      // Update field
      const updatedField = await request(app)
        .put(`/api/v1/fields/${field1.body.data._id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          name: 'Güney Tarla Güncellenmiş',
          notes: 'Güncellenen notlar'
        })
        .expect(200);

      expect(updatedField.body.data.name).toBe('Güney Tarla Güncellenmiş');

      // Set as default
      await request(app)
        .post(`/api/v1/fields/${field2.body.data._id}/set-default`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      // Verify default changed
      const fieldsAfterDefault = await request(app)
        .get('/api/v1/fields')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      const newDefaultField = fieldsAfterDefault.body.data.find(f => f.isDefault);
      expect(newDefaultField._id).toBe(field2.body.data._id);

      // Delete field (should fail if has expenses)
      await request(app)
        .delete(`/api/v1/fields/${field1.body.data._id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);
    });
  });

  describe('Crop Management Workflow', () => {
    test('should complete crop management operations', async () => {
      // Switch to detailed mode
      await request(app)
        .post('/api/v1/users/mode')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ mode: 'detailed', confirmed: true })
        .expect(200);

      // Get system crops
      const systemCrops = await request(app)
        .get('/api/v1/crops')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(systemCrops.body.data.length).toBeGreaterThan(0);

      // Create custom crop
      const customCrop = await request(app)
        .post('/api/v1/crops')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          name: 'custom-crop',
          nameTr: 'Özel Ürün',
          category: 'sebze',
          productionType: 'seasonal',
          emoji: '🥕',
          description: 'Test için özel ürün'
        })
        .expect(201);

      expect(customCrop.body.data.nameTr).toBe('Özel Ürün');
      expect(customCrop.body.data.isDefault).toBe(false);

      // Get crops by category
      const vegetableCrops = await request(app)
        .get('/api/v1/crops/category/sebze')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(vegetableCrops.body.data.some(crop => crop.nameTr === 'Özel Ürün')).toBe(true);

      // Update custom crop
      const updatedCrop = await request(app)
        .put(`/api/v1/crops/${customCrop.body.data._id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          description: 'Güncellenmiş açıklama'
        })
        .expect(200);

      expect(updatedCrop.body.data.description).toBe('Güncellenmiş açıklama');

      // Delete custom crop
      await request(app)
        .delete(`/api/v1/crops/${customCrop.body.data._id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);
    });
  });

  describe('Data Integrity Tests', () => {
    test('should maintain data integrity during mode switches', async () => {
      // Create expenses in simple mode
      const simpleExpenses = [];
      for (let i = 0; i < 5; i++) {
        const expense = await request(app)
          .post('/api/v1/expenses')
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            categoryId: testCategory._id,
            amount: 100 * (i + 1),
            date: `2024-01-${10 + i}`,
            description: `Basit gider ${i + 1}`
          })
          .expect(201);
        simpleExpenses.push(expense.body.data);
      }

      // Switch to detailed mode
      await request(app)
        .post('/api/v1/users/mode')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ mode: 'detailed', confirmed: true })
        .expect(200);

      // Verify all expenses are preserved and migrated
      const detailedExpenses = await request(app)
        .get('/api/v1/expenses')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(detailedExpenses.body.data).toHaveLength(5);
      detailedExpenses.body.data.forEach((expense, index) => {
        expect(expense.amount).toBe(simpleExpenses[index].amount);
        expect(expense.description).toBe(simpleExpenses[index].description);
        expect(expense.trackingMode).toBe('detailed');
        expect(expense.fieldId).toBeDefined();
      });

      // Switch back to simple mode
      await request(app)
        .post('/api/v1/users/mode')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ mode: 'simple', confirmed: true })
        .expect(200);

      // Verify expenses are still preserved
      const backToSimpleExpenses = await request(app)
        .get('/api/v1/expenses')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(backToSimpleExpenses.body.data).toHaveLength(5);
      backToSimpleExpenses.body.data.forEach((expense, index) => {
        expect(expense.amount).toBe(simpleExpenses[index].amount);
        expect(expense.description).toBe(simpleExpenses[index].description);
        expect(expense.trackingMode).toBe('simple');
      });
    });
  });
});
