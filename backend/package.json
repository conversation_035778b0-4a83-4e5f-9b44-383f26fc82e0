{"name": "ciftci-not-defterim-backend", "version": "1.0.0", "description": "Backend API for Çiftçi Not Defterim (Farmer's Notebook) mobile application", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:unit": "jest tests/unit", "test:integration": "jest tests/integration", "test:e2e": "jest tests/e2e", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "format": "prettier --write src/", "seed": "node scripts/seed.js", "migrate": "node scripts/migrate.js"}, "keywords": ["agriculture", "expense-tracking", "farming", "nodejs", "express", "mongodb", "firebase"], "author": "Çiftçi Not Defterim Team", "license": "MIT", "dependencies": {"@google/generative-ai": "^0.24.1", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "exceljs": "^4.4.0", "express": "^4.18.2", "express-fileupload": "^1.4.3", "express-rate-limit": "^7.1.5", "express-slow-down": "^2.1.0", "express-validator": "^7.0.1", "firebase-admin": "^12.0.0", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "mongoose": "^8.0.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cache": "^5.1.2", "sharp": "^0.33.1", "slugify": "^1.6.6", "uuid": "^9.0.1", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/node": "^20.10.4", "@types/supertest": "^2.0.16", "eslint": "^8.55.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.0", "jest": "^29.7.0", "mongodb-memory-server": "^9.1.3", "nock": "^13.4.0", "nodemon": "^3.0.2", "prettier": "^3.1.1", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/ciftci-not-defterim-backend.git"}, "bugs": {"url": "https://github.com/your-org/ciftci-not-defterim-backend/issues"}, "homepage": "https://github.com/your-org/ciftci-not-defterim-backend#readme"}