# Server Configuration
NODE_ENV=development
PORT=3000
API_VERSION=v1

# Database Configuration (MongoDB)
MONGODB_URI=mongodb://localhost:27017/ciftci-notebook

# Alternative: PostgreSQL Configuration
# DATABASE_URL=postgresql://username:password@localhost:5432/ciftci_notebook
# DB_HOST=localhost
# DB_PORT=5432
# DB_NAME=ciftci_notebook
# DB_USER=your_username
# DB_PASSWORD=your_password

# Firebase Configuration
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_PRIVATE_KEY_ID=your-private-key-id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYOUR_PRIVATE_KEY_HERE\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_CLIENT_ID=your-client-id
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token
FIREBASE_AUTH_PROVIDER_X509_CERT_URL=https://www.googleapis.com/oauth2/v1/certs
FIREBASE_CLIENT_X509_CERT_URL=https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-xxx%40your-project-id.iam.gserviceaccount.com

# File Upload Configuration
UPLOAD_MAX_SIZE=5242880
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/webp
UPLOAD_DESTINATION=uploads/
UPLOAD_PUBLIC_URL=http://localhost:3000/uploads/

# Cloud Storage (Optional - for production)
# AWS_ACCESS_KEY_ID=your-aws-access-key
# AWS_SECRET_ACCESS_KEY=your-aws-secret-key
# AWS_REGION=eu-west-1
# AWS_S3_BUCKET=ciftci-not-defterim-uploads

# Security
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
BCRYPT_ROUNDS=12
CORS_ORIGIN=http://localhost:19006,exp://*************:19000

# Rate Limiting
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_UPLOAD_MAX=10
RATE_LIMIT_SYNC_MAX=30

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log
LOG_MAX_SIZE=20m
LOG_MAX_FILES=14d

# Application Settings
DEFAULT_LANGUAGE=tr
DEFAULT_CURRENCY=TRY
DEFAULT_TIMEZONE=Europe/Istanbul

# Agricultural Settings
ENABLE_SEASONAL_DETECTION=true
ENABLE_WEATHER_INTEGRATION=false
WEATHER_API_KEY=your-weather-api-key

# Monitoring and Analytics (Optional)
SENTRY_DSN=your-sentry-dsn
GOOGLE_ANALYTICS_ID=your-ga-id

# Email Configuration (Optional - for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME=Çiftçi Not Defterim

# AI Import/Export System Configuration
GEMINI_API_KEY=your-gemini-api-key
GEMINI_MODEL=gemini-pro
GEMINI_TEMPERATURE=0.3
GEMINI_MAX_TOKENS=1024

# Redis Configuration (for background jobs)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Import/Export Settings
IMPORT_MAX_FILE_SIZE=10485760
IMPORT_ALLOWED_TYPES=application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel
EXPORT_TEMP_DIR=uploads/exports/
IMPORT_TEMP_DIR=uploads/temp/

# Development Settings
ENABLE_SWAGGER=true
ENABLE_DEBUG_LOGS=true
MOCK_EXTERNAL_APIS=false

# Production Settings (Override in production)
# NODE_ENV=production
# LOG_LEVEL=warn
# ENABLE_DEBUG_LOGS=false
# ENABLE_SWAGGER=false
