
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/controllers</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> src/controllers</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">11.63% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>102/877</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/420</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/82</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">12.11% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>102/842</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="authController.js"><a href="authController.js.html">authController.js</a></td>
	<td data-value="18.6" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 18%"></div><div class="cover-empty" style="width: 82%"></div></div>
	</td>
	<td data-value="18.6" class="pct low">18.6%</td>
	<td data-value="86" class="abs low">16/86</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="22" class="abs low">0/22</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="12" class="abs low">0/12</td>
	<td data-value="19.51" class="pct low">19.51%</td>
	<td data-value="82" class="abs low">16/82</td>
	</tr>

<tr>
	<td class="file low" data-value="categoryController.js"><a href="categoryController.js.html">categoryController.js</a></td>
	<td data-value="14.01" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 14%"></div><div class="cover-empty" style="width: 86%"></div></div>
	</td>
	<td data-value="14.01" class="pct low">14.01%</td>
	<td data-value="107" class="abs low">15/107</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="56" class="abs low">0/56</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="10" class="abs low">0/10</td>
	<td data-value="15" class="pct low">15%</td>
	<td data-value="100" class="abs low">15/100</td>
	</tr>

<tr>
	<td class="file low" data-value="cropController.js"><a href="cropController.js.html">cropController.js</a></td>
	<td data-value="11.57" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 11%"></div><div class="cover-empty" style="width: 89%"></div></div>
	</td>
	<td data-value="11.57" class="pct low">11.57%</td>
	<td data-value="121" class="abs low">14/121</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="49" class="abs low">0/49</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="12" class="abs low">0/12</td>
	<td data-value="11.57" class="pct low">11.57%</td>
	<td data-value="121" class="abs low">14/121</td>
	</tr>

<tr>
	<td class="file low" data-value="expenseController.js"><a href="expenseController.js.html">expenseController.js</a></td>
	<td data-value="8.44" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 8%"></div><div class="cover-empty" style="width: 92%"></div></div>
	</td>
	<td data-value="8.44" class="pct low">8.44%</td>
	<td data-value="154" class="abs low">13/154</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="111" class="abs low">0/111</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="7" class="abs low">0/7</td>
	<td data-value="9.28" class="pct low">9.28%</td>
	<td data-value="140" class="abs low">13/140</td>
	</tr>

<tr>
	<td class="file low" data-value="fieldController.js"><a href="fieldController.js.html">fieldController.js</a></td>
	<td data-value="11.76" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 11%"></div><div class="cover-empty" style="width: 89%"></div></div>
	</td>
	<td data-value="11.76" class="pct low">11.76%</td>
	<td data-value="102" class="abs low">12/102</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="39" class="abs low">0/39</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="8" class="abs low">0/8</td>
	<td data-value="11.76" class="pct low">11.76%</td>
	<td data-value="102" class="abs low">12/102</td>
	</tr>

<tr>
	<td class="file low" data-value="seasonController.js"><a href="seasonController.js.html">seasonController.js</a></td>
	<td data-value="11.44" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 11%"></div><div class="cover-empty" style="width: 89%"></div></div>
	</td>
	<td data-value="11.44" class="pct low">11.44%</td>
	<td data-value="166" class="abs low">19/166</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="76" class="abs low">0/76</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="21" class="abs low">0/21</td>
	<td data-value="12.02" class="pct low">12.02%</td>
	<td data-value="158" class="abs low">19/158</td>
	</tr>

<tr>
	<td class="file low" data-value="syncController.js"><a href="syncController.js.html">syncController.js</a></td>
	<td data-value="9.21" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 9%"></div><div class="cover-empty" style="width: 91%"></div></div>
	</td>
	<td data-value="9.21" class="pct low">9.21%</td>
	<td data-value="141" class="abs low">13/141</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="67" class="abs low">0/67</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="12" class="abs low">0/12</td>
	<td data-value="9.35" class="pct low">9.35%</td>
	<td data-value="139" class="abs low">13/139</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-10T21:16:28.037Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    