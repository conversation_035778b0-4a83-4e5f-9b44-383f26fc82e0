
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/middleware</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> src/middleware</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">31.52% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>122/387</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">16.74% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>35/209</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">27.08% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>13/48</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">31.52% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>122/387</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="auth.js"><a href="auth.js.html">auth.js</a></td>
	<td data-value="36.89" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 36%"></div><div class="cover-empty" style="width: 64%"></div></div>
	</td>
	<td data-value="36.89" class="pct low">36.89%</td>
	<td data-value="103" class="abs low">38/103</td>
	<td data-value="17.91" class="pct low">17.91%</td>
	<td data-value="67" class="abs low">12/67</td>
	<td data-value="27.27" class="pct low">27.27%</td>
	<td data-value="11" class="abs low">3/11</td>
	<td data-value="36.89" class="pct low">36.89%</td>
	<td data-value="103" class="abs low">38/103</td>
	</tr>

<tr>
	<td class="file low" data-value="errorHandler.js"><a href="errorHandler.js.html">errorHandler.js</a></td>
	<td data-value="47.19" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 47%"></div><div class="cover-empty" style="width: 53%"></div></div>
	</td>
	<td data-value="47.19" class="pct low">47.19%</td>
	<td data-value="89" class="abs low">42/89</td>
	<td data-value="44.18" class="pct low">44.18%</td>
	<td data-value="43" class="abs low">19/43</td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="10" class="abs medium">5/10</td>
	<td data-value="47.19" class="pct low">47.19%</td>
	<td data-value="89" class="abs low">42/89</td>
	</tr>

<tr>
	<td class="file low" data-value="modeValidation.js"><a href="modeValidation.js.html">modeValidation.js</a></td>
	<td data-value="9" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 9%"></div><div class="cover-empty" style="width: 91%"></div></div>
	</td>
	<td data-value="9" class="pct low">9%</td>
	<td data-value="100" class="abs low">9/100</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="52" class="abs low">0/52</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="6" class="abs low">0/6</td>
	<td data-value="9" class="pct low">9%</td>
	<td data-value="100" class="abs low">9/100</td>
	</tr>

<tr>
	<td class="file medium" data-value="rateLimiter.js"><a href="rateLimiter.js.html">rateLimiter.js</a></td>
	<td data-value="52.63" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 52%"></div><div class="cover-empty" style="width: 48%"></div></div>
	</td>
	<td data-value="52.63" class="pct medium">52.63%</td>
	<td data-value="38" class="abs medium">20/38</td>
	<td data-value="16.66" class="pct low">16.66%</td>
	<td data-value="24" class="abs low">4/24</td>
	<td data-value="36.36" class="pct low">36.36%</td>
	<td data-value="11" class="abs low">4/11</td>
	<td data-value="52.63" class="pct medium">52.63%</td>
	<td data-value="38" class="abs medium">20/38</td>
	</tr>

<tr>
	<td class="file low" data-value="validation.js"><a href="validation.js.html">validation.js</a></td>
	<td data-value="22.8" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 22%"></div><div class="cover-empty" style="width: 78%"></div></div>
	</td>
	<td data-value="22.8" class="pct low">22.8%</td>
	<td data-value="57" class="abs low">13/57</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="23" class="abs low">0/23</td>
	<td data-value="10" class="pct low">10%</td>
	<td data-value="10" class="abs low">1/10</td>
	<td data-value="22.8" class="pct low">22.8%</td>
	<td data-value="57" class="abs low">13/57</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-10T21:16:28.079Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    