
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/models</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> src/models</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">30.82% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>139/451</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">2.28% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>5/219</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">1.12% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>1/89</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">32.32% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>139/430</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="Category.js"><a href="Category.js.html">Category.js</a></td>
	<td data-value="27.65" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 27%"></div><div class="cover-empty" style="width: 73%"></div></div>
	</td>
	<td data-value="27.65" class="pct low">27.65%</td>
	<td data-value="94" class="abs low">26/94</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="56" class="abs low">0/56</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="21" class="abs low">0/21</td>
	<td data-value="30.58" class="pct low">30.58%</td>
	<td data-value="85" class="abs low">26/85</td>
	</tr>

<tr>
	<td class="file low" data-value="Crop.js"><a href="Crop.js.html">Crop.js</a></td>
	<td data-value="31.14" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 31%"></div><div class="cover-empty" style="width: 69%"></div></div>
	</td>
	<td data-value="31.14" class="pct low">31.14%</td>
	<td data-value="61" class="abs low">19/61</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="19" class="abs low">0/19</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="13" class="abs low">0/13</td>
	<td data-value="31.14" class="pct low">31.14%</td>
	<td data-value="61" class="abs low">19/61</td>
	</tr>

<tr>
	<td class="file low" data-value="Expense.js"><a href="Expense.js.html">Expense.js</a></td>
	<td data-value="28.03" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 28%"></div><div class="cover-empty" style="width: 72%"></div></div>
	</td>
	<td data-value="28.03" class="pct low">28.03%</td>
	<td data-value="132" class="abs low">37/132</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="72" class="abs low">0/72</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="22" class="abs low">0/22</td>
	<td data-value="29.83" class="pct low">29.83%</td>
	<td data-value="124" class="abs low">37/124</td>
	</tr>

<tr>
	<td class="file low" data-value="Field.js"><a href="Field.js.html">Field.js</a></td>
	<td data-value="26.31" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 26%"></div><div class="cover-empty" style="width: 74%"></div></div>
	</td>
	<td data-value="26.31" class="pct low">26.31%</td>
	<td data-value="57" class="abs low">15/57</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="25" class="abs low">0/25</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="10" class="abs low">0/10</td>
	<td data-value="26.78" class="pct low">26.78%</td>
	<td data-value="56" class="abs low">15/56</td>
	</tr>

<tr>
	<td class="file low" data-value="Season.js"><a href="Season.js.html">Season.js</a></td>
	<td data-value="35.18" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 35%"></div><div class="cover-empty" style="width: 65%"></div></div>
	</td>
	<td data-value="35.18" class="pct low">35.18%</td>
	<td data-value="54" class="abs low">19/54</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="32" class="abs low">0/32</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="11" class="abs low">0/11</td>
	<td data-value="35.18" class="pct low">35.18%</td>
	<td data-value="54" class="abs low">19/54</td>
	</tr>

<tr>
	<td class="file low" data-value="User.js"><a href="User.js.html">User.js</a></td>
	<td data-value="43.39" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 43%"></div><div class="cover-empty" style="width: 57%"></div></div>
	</td>
	<td data-value="43.39" class="pct low">43.39%</td>
	<td data-value="53" class="abs low">23/53</td>
	<td data-value="33.33" class="pct low">33.33%</td>
	<td data-value="15" class="abs low">5/15</td>
	<td data-value="8.33" class="pct low">8.33%</td>
	<td data-value="12" class="abs low">1/12</td>
	<td data-value="46" class="pct low">46%</td>
	<td data-value="50" class="abs low">23/50</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-10T21:16:28.079Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    