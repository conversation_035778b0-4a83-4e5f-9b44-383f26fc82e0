
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/utils</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> src/utils</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">19.27% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>69/358</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">8.24% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>15/182</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">8.45% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>6/71</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">20.84% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>69/331</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="constants.js"><a href="constants.js.html">constants.js</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="11" class="abs high">11/11</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="11" class="abs high">11/11</td>
	</tr>

<tr>
	<td class="file low" data-value="database.js"><a href="database.js.html">database.js</a></td>
	<td data-value="18.18" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 18%"></div><div class="cover-empty" style="width: 82%"></div></div>
	</td>
	<td data-value="18.18" class="pct low">18.18%</td>
	<td data-value="55" class="abs low">10/55</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="8" class="abs low">0/8</td>
	<td data-value="7.69" class="pct low">7.69%</td>
	<td data-value="13" class="abs low">1/13</td>
	<td data-value="19.6" class="pct low">19.6%</td>
	<td data-value="51" class="abs low">10/51</td>
	</tr>

<tr>
	<td class="file low" data-value="defaultCrops.js"><a href="defaultCrops.js.html">defaultCrops.js</a></td>
	<td data-value="10.52" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 10%"></div><div class="cover-empty" style="width: 90%"></div></div>
	</td>
	<td data-value="10.52" class="pct low">10.52%</td>
	<td data-value="19" class="abs low">2/19</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="2" class="abs low">0/2</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="5" class="abs low">0/5</td>
	<td data-value="11.76" class="pct low">11.76%</td>
	<td data-value="17" class="abs low">2/17</td>
	</tr>

<tr>
	<td class="file low" data-value="defaultFields.js"><a href="defaultFields.js.html">defaultFields.js</a></td>
	<td data-value="9.8" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 9%"></div><div class="cover-empty" style="width: 91%"></div></div>
	</td>
	<td data-value="9.8" class="pct low">9.8%</td>
	<td data-value="51" class="abs low">5/51</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="35" class="abs low">0/35</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="14" class="abs low">0/14</td>
	<td data-value="11.36" class="pct low">11.36%</td>
	<td data-value="44" class="abs low">5/44</td>
	</tr>

<tr>
	<td class="file low" data-value="helpers.js"><a href="helpers.js.html">helpers.js</a></td>
	<td data-value="17.74" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 17%"></div><div class="cover-empty" style="width: 83%"></div></div>
	</td>
	<td data-value="17.74" class="pct low">17.74%</td>
	<td data-value="62" class="abs low">11/62</td>
	<td data-value="4.25" class="pct low">4.25%</td>
	<td data-value="47" class="abs low">2/47</td>
	<td data-value="5" class="pct low">5%</td>
	<td data-value="20" class="abs low">1/20</td>
	<td data-value="20.37" class="pct low">20.37%</td>
	<td data-value="54" class="abs low">11/54</td>
	</tr>

<tr>
	<td class="file medium" data-value="logger.js"><a href="logger.js.html">logger.js</a></td>
	<td data-value="66.66" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 66%"></div><div class="cover-empty" style="width: 34%"></div></div>
	</td>
	<td data-value="66.66" class="pct medium">66.66%</td>
	<td data-value="45" class="abs medium">30/45</td>
	<td data-value="40.62" class="pct low">40.62%</td>
	<td data-value="32" class="abs low">13/32</td>
	<td data-value="57.14" class="pct medium">57.14%</td>
	<td data-value="7" class="abs medium">4/7</td>
	<td data-value="66.66" class="pct medium">66.66%</td>
	<td data-value="45" class="abs medium">30/45</td>
	</tr>

<tr>
	<td class="file low" data-value="performanceOptimization.js"><a href="performanceOptimization.js.html">performanceOptimization.js</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="115" class="abs low">0/115</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="58" class="abs low">0/58</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="12" class="abs low">0/12</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="109" class="abs low">0/109</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-10T21:16:28.079Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    