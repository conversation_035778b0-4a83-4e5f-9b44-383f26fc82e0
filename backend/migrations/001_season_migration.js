/**
 * Season Migration Script for Çiftçi Not Defterim
 * Migrates existing expense data to new user-defined season system
 * 
 * CRITICAL WARNING: This migration will transform the season data structure.
 * Only run in development environment after testing.
 */

const mongoose = require('mongoose');
const { v4: uuidv4 } = require('uuid');

// Import models
const User = require('../src/models/User');
const Season = require('../src/models/Season');
const Expense = require('../src/models/Expense');

/**
 * Migration Status Model for tracking
 */
const migrationStatusSchema = new mongoose.Schema({
  migrationName: {
    type: String,
    required: true,
    unique: true
  },
  status: {
    type: String,
    enum: ['pending', 'running', 'completed', 'failed', 'rolled_back'],
    default: 'pending'
  },
  startedAt: {
    type: Date,
    default: Date.now
  },
  completedAt: Date,
  results: mongoose.Schema.Types.Mixed,
  error: String,
  backupCollections: [String]
});

const MigrationStatus = mongoose.model('MigrationStatus', migrationStatusSchema);

/**
 * Create backup collections before migration
 */
async function createBackup(session) {
  console.log('🔄 Creating backup collections...');
  
  try {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupCollections = [];
    
    // Backup existing seasons
    const existingSeasons = await mongoose.connection.db
      .collection('seasons')
      .find({})
      .toArray();
    
    if (existingSeasons.length > 0) {
      const seasonBackupName = `seasons_backup_${timestamp}`;
      await mongoose.connection.db
        .collection(seasonBackupName)
        .insertMany(existingSeasons, { session });
      backupCollections.push(seasonBackupName);
      console.log(`✅ Backed up ${existingSeasons.length} seasons to ${seasonBackupName}`);
    }
    
    // Backup existing expenses
    const existingExpenses = await mongoose.connection.db
      .collection('expenses')
      .find({})
      .toArray();
    
    if (existingExpenses.length > 0) {
      const expenseBackupName = `expenses_backup_${timestamp}`;
      await mongoose.connection.db
        .collection(expenseBackupName)
        .insertMany(existingExpenses, { session });
      backupCollections.push(expenseBackupName);
      console.log(`✅ Backed up ${existingExpenses.length} expenses to ${expenseBackupName}`);
    }
    
    return {
      success: true,
      backupCollections
    };
  } catch (error) {
    console.error('❌ Backup creation failed:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Main migration function with transaction support
 */
async function migrateLegacySeasons() {
  const session = await mongoose.startSession();
  let migrationRecord = null;
  
  try {
    console.log('🚀 Starting season migration with transaction...');
    
    // Create migration status record
    migrationRecord = await MigrationStatus.create([{
      migrationName: 'season_migration_v1',
      status: 'running',
      startedAt: new Date()
    }], { session });
    
    await session.withTransaction(async () => {
      // 1. Create backup
      const backupResult = await createBackup(session);
      if (!backupResult.success) {
        throw new Error(`Backup creation failed: ${backupResult.error}`);
      }
      
      // 2. Get all users
      const users = await User.find({}).session(session);
      console.log(`📊 Found ${users.length} users to migrate`);
      
      if (users.length === 0) {
        console.log('ℹ️ No users found, skipping migration');
        return [];
      }
      
      const migrationResults = [];
      
      // 3. Process each user
      for (const user of users) {
        try {
          console.log(`👤 Processing user: ${user.email || user._id}`);
          
          // Check if user already has seasons
          const existingUserSeasons = await Season.find({ userId: user._id }).session(session);
          
          if (existingUserSeasons.length > 0) {
            console.log(`⚠️ User ${user._id} already has ${existingUserSeasons.length} seasons, skipping`);
            migrationResults.push({
              userId: user._id,
              status: 'skipped',
              reason: 'already_has_seasons',
              existingSeasons: existingUserSeasons.length
            });
            continue;
          }
          
          // Create default season for user
          const currentYear = new Date().getFullYear();
          const defaultSeason = await Season.create([{
            userId: user._id,
            name: `${currentYear} Sezonu`,
            description: 'Otomatik oluşturulan varsayılan sezon (migration)',
            startDate: user.createdAt || new Date(),
            isActive: true,
            isDefault: true,
            color: '#4CAF50',
            emoji: '🌱'
          }], { session });
          
          console.log(`✅ Created default season for user ${user._id}: ${defaultSeason[0]._id}`);
          
          // Get user's expenses that need migration
          const userExpenses = await Expense.find({ 
            userId: user._id,
            seasonId: { $in: ['spring', 'summer', 'autumn', 'winter'] }
          }).session(session);
          
          console.log(`📝 Found ${userExpenses.length} expenses to migrate for user ${user._id}`);
          
          // Update expenses to use new season
          let expensesUpdated = 0;
          if (userExpenses.length > 0) {
            for (const expense of userExpenses) {
              // Store legacy season ID
              expense.legacySeasonId = expense.seasonId;
              // Assign to new default season
              expense.seasonId = defaultSeason[0]._id;
              await expense.save({ session });
              expensesUpdated++;
            }
          }
          
          migrationResults.push({
            userId: user._id,
            status: 'success',
            seasonId: defaultSeason[0]._id,
            expensesUpdated: expensesUpdated,
            legacyExpenses: userExpenses.length
          });
          
          console.log(`✅ Migrated ${expensesUpdated} expenses for user ${user._id}`);
          
        } catch (userError) {
          console.error(`❌ User migration failed for ${user._id}:`, userError);
          migrationResults.push({
            userId: user._id,
            status: 'failed',
            error: userError.message
          });
          throw userError; // This will rollback the transaction
        }
      }
      
      // 4. Update migration status
      migrationRecord[0].status = 'completed';
      migrationRecord[0].completedAt = new Date();
      migrationRecord[0].results = migrationResults;
      migrationRecord[0].backupCollections = backupResult.backupCollections;
      await migrationRecord[0].save({ session });
      
      console.log('✅ Season migration completed successfully');
      console.log(`📊 Migration summary:`, {
        totalUsers: users.length,
        successful: migrationResults.filter(r => r.status === 'success').length,
        skipped: migrationResults.filter(r => r.status === 'skipped').length,
        failed: migrationResults.filter(r => r.status === 'failed').length
      });
      
      return migrationResults;
    });
    
  } catch (error) {
    console.error('💥 Migration failed, transaction rolled back:', error);
    
    // Update migration status to failed
    if (migrationRecord && migrationRecord[0]) {
      try {
        migrationRecord[0].status = 'failed';
        migrationRecord[0].error = error.message;
        migrationRecord[0].completedAt = new Date();
        await migrationRecord[0].save();
      } catch (statusError) {
        console.error('Failed to update migration status:', statusError);
      }
    }
    
    throw error;
  } finally {
    await session.endSession();
  }
}

/**
 * Rollback function to restore from backup
 */
async function rollbackMigration(migrationName) {
  console.log(`🔄 Rolling back migration: ${migrationName}`);
  
  const migrationStatus = await MigrationStatus.findOne({ migrationName });
  if (!migrationStatus) {
    throw new Error(`Migration ${migrationName} not found`);
  }
  
  if (!migrationStatus.backupCollections || migrationStatus.backupCollections.length === 0) {
    throw new Error('No backup collections found for rollback');
  }
  
  const session = await mongoose.startSession();
  
  try {
    await session.withTransaction(async () => {
      // Restore from backup collections
      for (const backupCollection of migrationStatus.backupCollections) {
        const originalCollection = backupCollection.replace(/_backup_.*/, '');
        
        console.log(`🔄 Restoring ${originalCollection} from ${backupCollection}`);
        
        // Drop current collection
        await mongoose.connection.db.collection(originalCollection).drop();
        
        // Restore from backup
        const backupData = await mongoose.connection.db
          .collection(backupCollection)
          .find({})
          .toArray();
        
        if (backupData.length > 0) {
          await mongoose.connection.db
            .collection(originalCollection)
            .insertMany(backupData, { session });
        }
        
        console.log(`✅ Restored ${backupData.length} documents to ${originalCollection}`);
      }
      
      // Update migration status
      migrationStatus.status = 'rolled_back';
      migrationStatus.completedAt = new Date();
      await migrationStatus.save({ session });
    });
    
    console.log('✅ Rollback completed successfully');
  } catch (error) {
    console.error('❌ Rollback failed:', error);
    throw error;
  } finally {
    await session.endSession();
  }
}

/**
 * Check migration status
 */
async function checkMigrationStatus(migrationName) {
  const status = await MigrationStatus.findOne({ migrationName });
  return status;
}

module.exports = {
  migrateLegacySeasons,
  rollbackMigration,
  checkMigrationStatus,
  MigrationStatus
};

// CLI execution
if (require.main === module) {
  const command = process.argv[2];
  const migrationName = 'season_migration_v1';
  
  mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/ciftcinotdefterim')
    .then(async () => {
      console.log('📡 Connected to MongoDB');
      
      switch (command) {
        case 'migrate':
          await migrateLegacySeasons();
          break;
        case 'rollback':
          await rollbackMigration(migrationName);
          break;
        case 'status':
          const status = await checkMigrationStatus(migrationName);
          console.log('Migration status:', status);
          break;
        default:
          console.log('Usage: node 001_season_migration.js [migrate|rollback|status]');
      }
      
      process.exit(0);
    })
    .catch(error => {
      console.error('Database connection failed:', error);
      process.exit(1);
    });
}
