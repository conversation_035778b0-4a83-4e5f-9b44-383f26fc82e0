/**
 * AI Controller for Çiftçi Not Defterim
 * Handles AI-powered text processing for agricultural expenses
 */

const aiService = require('../services/aiService');
const logger = require('../utils/logger');
const { importErrorHandler } = require('../middleware/importErrorHandler');

class AIController {
  /**
   * Process text with AI
   */
  async processText(req, res) {
    try {
      const { text, context = {} } = req.body;

      logger.info('AI text processing request:', {
        userId: req.user?.id,
        textLength: text?.length || 0,
        hasContext: Object.keys(context).length > 0
      });

      // Validate input
      if (!text || typeof text !== 'string') {
        return res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_INPUT',
            message: 'Metin girişi gerekli'
          }
        });
      }

      if (text.trim().length === 0) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'EMPTY_TEXT',
            message: 'Boş metin gönderilemez'
          }
        });
      }

      if (text.length > 2000) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'TEXT_TOO_LONG',
            message: 'Metin çok uzun (maksimum 2000 karakter)'
          }
        });
      }

      // Add user context
      const enhancedContext = {
        ...context,
        userId: req.user.id,
        userLocation: req.user.farmInfo?.location?.city,
        timestamp: new Date().toISOString()
      };

      // Process with AI
      const result = await aiService.processAgriculturalText(text, enhancedContext);

      if (!result.success) {
        return res.status(500).json(result);
      }

      // Prepare response
      const response = {
        success: true,
        data: {
          originalText: text,
          confidence: result.data.confidence,
          extractedData: result.data.extractedData,
          questions: result.data.questions,
          warnings: result.data.warnings,
          processingTime: Date.now() - req.startTime,
          aiModel: aiService.config?.model || 'gemini-pro'
        },
        message: this.generateResponseMessage(result.data)
      };

      // Log successful processing
      logger.info('AI text processing completed:', {
        userId: req.user?.id,
        confidence: result.data.confidence,
        extractedCount: result.data.extractedData?.length || 0,
        questionsCount: result.data.questions?.length || 0,
        processingTime: response.data.processingTime
      });

      res.json(response);

    } catch (error) {
      logger.error('AI text processing error:', error);
      
      const errorResponse = importErrorHandler.handleAIError(error, {
        userId: req.user?.id,
        textLength: req.body?.text?.length || 0
      });

      res.status(500).json(errorResponse);
    }
  }

  /**
   * Ask follow-up question to AI
   */
  async askQuestion(req, res) {
    try {
      const { originalText, question, previousData } = req.body;

      logger.info('AI question processing request:', {
        userId: req.user?.id,
        hasOriginalText: !!originalText,
        questionLength: question?.length || 0
      });

      // Validate input
      if (!originalText || !question) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_INPUT',
            message: 'Orijinal metin ve soru gerekli'
          }
        });
      }

      // Build context with previous data
      const context = {
        previousData,
        isFollowUp: true,
        question,
        userId: req.user.id
      };

      // Create enhanced prompt for follow-up
      const enhancedText = `
ÖNCEKI ANALİZ: ${originalText}

KULLANICI SORUSU: ${question}

Lütfen önceki analizi kullanıcının sorusuna göre güncelle ve daha net sonuç ver.
`;

      // Process with AI
      const result = await aiService.processAgriculturalText(enhancedText, context);

      if (!result.success) {
        return res.status(500).json(result);
      }

      res.json({
        success: true,
        data: {
          originalText,
          question,
          confidence: result.data.confidence,
          extractedData: result.data.extractedData,
          questions: result.data.questions,
          warnings: result.data.warnings,
          processingTime: Date.now() - req.startTime
        },
        message: 'Soru işlendi ve analiz güncellendi'
      });

    } catch (error) {
      logger.error('AI question processing error:', error);
      
      const errorResponse = importErrorHandler.handleAIError(error, {
        userId: req.user?.id,
        operation: 'question'
      });

      res.status(500).json(errorResponse);
    }
  }

  /**
   * Validate AI-processed data
   */
  async validateData(req, res) {
    try {
      const { data } = req.body;

      logger.info('AI data validation request:', {
        userId: req.user?.id,
        dataCount: data?.length || 0
      });

      if (!Array.isArray(data)) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_DATA',
            message: 'Veri dizisi gerekli'
          }
        });
      }

      // Validate each data item
      const validationResults = data.map((item, index) => {
        const errors = [];
        const warnings = [];

        // Validate date
        if (!item.date) {
          errors.push('Tarih eksik');
        } else {
          const date = new Date(item.date);
          if (isNaN(date.getTime())) {
            errors.push('Geçersiz tarih formatı');
          }
        }

        // Validate category
        const validCategories = [
          'Gübre', 'İşçilik', 'İlaç', 'Su', 'Yakıt', 'Tohum', 'Makine', 'Depolama'
        ];
        if (!item.category) {
          errors.push('Kategori eksik');
        } else if (!validCategories.includes(item.category)) {
          errors.push(`Geçersiz kategori: ${item.category}`);
        }

        // Validate amount
        if (!item.amount || item.amount <= 0) {
          errors.push('Geçerli tutar gerekli');
        } else if (item.amount > 1000000) {
          warnings.push('Tutar çok yüksek görünüyor');
        }

        // Validate description
        if (!item.description || item.description.trim().length === 0) {
          errors.push('Açıklama gerekli');
        }

        // Check confidence
        if (item.confidence < 0.5) {
          warnings.push('Düşük güven skoru');
        }

        return {
          index,
          item,
          valid: errors.length === 0,
          errors,
          warnings
        };
      });

      const validItems = validationResults.filter(r => r.valid);
      const invalidItems = validationResults.filter(r => !r.valid);

      res.json({
        success: true,
        data: {
          totalItems: data.length,
          validItems: validItems.length,
          invalidItems: invalidItems.length,
          validationResults,
          summary: {
            canProceed: validItems.length > 0,
            needsAttention: invalidItems.length > 0,
            totalAmount: validItems.reduce((sum, r) => sum + (r.item.amount || 0), 0)
          }
        },
        message: `${validItems.length} geçerli, ${invalidItems.length} geçersiz kayıt`
      });

    } catch (error) {
      logger.error('AI data validation error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Veri doğrulama hatası'
        }
      });
    }
  }

  /**
   * Get AI service status
   */
  async getStatus(req, res) {
    try {
      const status = aiService.getStatus();
      
      res.json({
        success: true,
        data: {
          ...status,
          uptime: process.uptime(),
          timestamp: new Date().toISOString()
        },
        message: 'AI servis durumu'
      });

    } catch (error) {
      logger.error('AI status error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'STATUS_ERROR',
          message: 'Durum bilgisi alınamadı'
        }
      });
    }
  }

  /**
   * Generate response message based on AI result
   */
  generateResponseMessage(data) {
    const { confidence, extractedData, questions, warnings } = data;

    if (confidence >= 0.8 && extractedData.length > 0 && questions.length === 0) {
      return `${extractedData.length} kayıt başarıyla analiz edildi`;
    } else if (confidence >= 0.6 && questions.length > 0) {
      return `${extractedData.length} kayıt bulundu, ${questions.length} soru var`;
    } else if (confidence < 0.4 || extractedData.length === 0) {
      return 'Analiz başarısız, manuel girişe geçin';
    } else {
      return `${extractedData.length} kayıt bulundu, kontrol edin`;
    }
  }

  /**
   * Middleware to add request start time
   */
  addRequestTime = (req, res, next) => {
    req.startTime = Date.now();
    next();
  };
}

module.exports = new AIController();
