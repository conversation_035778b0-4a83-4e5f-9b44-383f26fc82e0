/**
 * Authentication Controller for Çiftçi Not Defterim
 * Handles Firebase authentication and user management
 */

const User = require('../models/User');
const Category = require('../models/Category');
const { asyncHandler } = require('../middleware/errorHandler');
const { ResponseHelpers } = require('../utils/helpers');
const { verifyFirebaseToken } = require('../middleware/auth');
const logger = require('../utils/logger');

/**
 * Validate Firebase token and get/create user
 * POST /api/v1/auth/validate
 */
const validateToken = asyncHandler(async (req, res) => {
  const { firebaseToken } = req.body;
  
  if (!firebaseToken) {
    return res.status(400).json(
      ResponseHelpers.error('MISSING_TOKEN', 'Firebase token gerekli')
    );
  }
  
  // Verify Firebase token
  const verificationResult = await verifyFirebaseToken(firebaseToken);
  
  if (!verificationResult.success) {
    return res.status(401).json(
      ResponseHelpers.error('INVALID_TOKEN', verificationResult.error)
    );
  }
  
  const firebaseUser = verificationResult.user;
  
  // Find or create user
  let user = await User.findOne({ firebaseUid: firebaseUser.uid });
  let isNewUser = false;
  
  if (!user) {
    // Create new user
    user = await User.create({
      firebaseUid: firebaseUser.uid,
      email: firebaseUser.email,
      name: firebaseUser.name,
      emailVerified: firebaseUser.emailVerified,
      picture: firebaseUser.picture,
      provider: firebaseUser.provider,
      stats: {
        lastLoginAt: new Date(),
        loginCount: 1
      }
    });
    
    isNewUser = true;
    
    // Create default categories for new user
    await Category.ensureDefaultCategories();
    
    logger.info('New user registered:', {
      userId: user._id,
      email: user.email,
      provider: firebaseUser.provider
    });
  } else {
    // Update existing user login info
    await user.updateLoginInfo();
    
    logger.info('User logged in:', {
      userId: user._id,
      email: user.email
    });
  }
  
  // Log authentication success
  logger.logAuth('token_validation', user._id, true, {
    provider: firebaseUser.provider,
    isNewUser,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });
  
  res.status(200).json(
    ResponseHelpers.success({
      userId: user._id,
      email: user.email,
      name: user.name,
      picture: user.picture,
      isNewUser,
      emailVerified: user.emailVerified,
      preferences: user.preferences,
      lastLoginAt: user.stats.lastLoginAt,
      registrationCompleted: user.stats.registrationCompleted
    }, 'Kimlik doğrulama başarılı')
  );
});

/**
 * Get current user profile
 * GET /api/v1/auth/profile
 */
const getProfile = asyncHandler(async (req, res) => {
  const user = await User.findById(req.user._id)
    .select('-firebaseUid -devices -dataRetention');
  
  if (!user) {
    return res.status(404).json(
      ResponseHelpers.error('USER_NOT_FOUND', 'Kullanıcı bulunamadı')
    );
  }
  
  res.status(200).json(
    ResponseHelpers.success(user.toPublicJSON(), 'Profil bilgileri getirildi')
  );
});

/**
 * Update user profile
 * PUT /api/v1/auth/profile
 */
const updateProfile = asyncHandler(async (req, res) => {
  const { name, preferences, farmInfo } = req.body;
  
  const updateData = {};
  
  if (name) updateData.name = name;
  if (preferences) updateData.preferences = { ...req.user.preferences, ...preferences };
  if (farmInfo) updateData.farmInfo = { ...req.user.farmInfo, ...farmInfo };
  
  const user = await User.findByIdAndUpdate(
    req.user._id,
    updateData,
    { new: true, runValidators: true }
  ).select('-firebaseUid -devices -dataRetention');
  
  logger.info('User profile updated:', {
    userId: user._id,
    updatedFields: Object.keys(updateData)
  });
  
  res.status(200).json(
    ResponseHelpers.success(user.toPublicJSON(), 'Profil güncellendi')
  );
});

/**
 * Delete user account
 * DELETE /api/v1/auth/account
 */
const deleteAccount = asyncHandler(async (req, res) => {
  const userId = req.user._id;
  
  // Mark user as deleted instead of hard delete
  await User.findByIdAndUpdate(userId, {
    status: 'deleted',
    email: `deleted_${Date.now()}_${req.user.email}`,
    name: 'Deleted User'
  });
  
  // TODO: Also mark user's expenses and categories as deleted
  // This should be done in a background job for better performance
  
  logger.info('User account deleted:', {
    userId,
    email: req.user.email
  });
  
  res.status(200).json(
    ResponseHelpers.success(null, 'Hesap başarıyla silindi')
  );
});

/**
 * Register device for push notifications
 * POST /api/v1/auth/device
 */
const registerDevice = asyncHandler(async (req, res) => {
  const { deviceId, platform, appVersion, pushToken } = req.body;
  
  if (!deviceId || !platform) {
    return res.status(400).json(
      ResponseHelpers.error('MISSING_REQUIRED_FIELDS', 'Device ID ve platform gerekli')
    );
  }
  
  await req.user.addDevice({
    deviceId,
    platform,
    appVersion,
    pushToken
  });
  
  logger.info('Device registered:', {
    userId: req.user._id,
    deviceId,
    platform
  });
  
  res.status(200).json(
    ResponseHelpers.success(null, 'Cihaz kaydedildi')
  );
});

/**
 * Logout (remove device)
 * POST /api/v1/auth/logout
 */
const logout = asyncHandler(async (req, res) => {
  const { deviceId } = req.body;
  
  if (deviceId) {
    // Remove specific device
    req.user.devices = req.user.devices.filter(d => d.deviceId !== deviceId);
    await req.user.save();
    
    logger.info('Device logged out:', {
      userId: req.user._id,
      deviceId
    });
  }
  
  res.status(200).json(
    ResponseHelpers.success(null, 'Çıkış yapıldı')
  );
});

/**
 * Get user statistics
 * GET /api/v1/auth/stats
 */
const getUserStats = asyncHandler(async (req, res) => {
  const user = await User.findById(req.user._id);
  
  const stats = {
    totalExpenses: user.stats.totalExpenses,
    totalAmount: user.stats.totalAmount,
    categoriesCreated: user.stats.categoriesCreated,
    lastExpenseDate: user.stats.lastExpenseDate,
    loginCount: user.stats.loginCount,
    lastLoginAt: user.stats.lastLoginAt,
    registrationDate: user.createdAt,
    accountAge: Math.floor((new Date() - user.createdAt) / (1000 * 60 * 60 * 24)) // days
  };
  
  res.status(200).json(
    ResponseHelpers.success(stats, 'Kullanıcı istatistikleri getirildi')
  );
});

/**
 * Update user preferences
 * PATCH /api/v1/auth/preferences
 */
const updatePreferences = asyncHandler(async (req, res) => {
  const { preferences } = req.body;
  
  if (!preferences) {
    return res.status(400).json(
      ResponseHelpers.error('MISSING_PREFERENCES', 'Tercihler gerekli')
    );
  }
  
  const user = await User.findByIdAndUpdate(
    req.user._id,
    { 
      preferences: { ...req.user.preferences, ...preferences }
    },
    { new: true, runValidators: true }
  );
  
  logger.info('User preferences updated:', {
    userId: user._id,
    updatedPreferences: Object.keys(preferences)
  });
  
  res.status(200).json(
    ResponseHelpers.success(user.preferences, 'Tercihler güncellendi')
  );
});

/**
 * Export user data (GDPR compliance)
 * GET /api/v1/auth/export
 */
const exportUserData = asyncHandler(async (req, res) => {
  const user = await User.findById(req.user._id);
  
  // Get user's expenses and categories
  const Expense = require('../models/Expense');
  const expenses = await Expense.find({ userId: req.user._id, status: 'active' })
    .populate('categoryId', 'name emoji color');
  
  const categories = await Category.find({ userId: req.user._id, isActive: true });
  
  const exportData = {
    user: user.toPublicJSON(),
    expenses: expenses.map(e => e.toPublicJSON()),
    categories: categories.map(c => c.toPublicJSON()),
    exportDate: new Date().toISOString(),
    dataRetentionInfo: user.dataRetention
  };
  
  // Update last export date
  user.dataRetention.lastDataExport = new Date();
  await user.save();
  
  logger.info('User data exported:', {
    userId: user._id,
    expenseCount: expenses.length,
    categoryCount: categories.length
  });
  
  res.status(200).json(
    ResponseHelpers.success(exportData, 'Veriler dışa aktarıldı')
  );
});

module.exports = {
  validateToken,
  getProfile,
  updateProfile,
  deleteAccount,
  registerDevice,
  logout,
  getUserStats,
  updatePreferences,
  exportUserData
};
