/**
 * Crop Controller for Çiftçi Not Defterim
 * Handles crop management operations for detailed tracking mode
 */

const Crop = require('../models/Crop');
const Expense = require('../models/Expense');
const { initializeDefaultCrops } = require('../utils/defaultCrops');
const Logger = require('../utils/logger');

/**
 * Get all crops (system default + user custom)
 * GET /api/v1/crops
 */
const getCrops = async (req, res) => {
  try {
    const userId = req.user.uid;
    const { category, productionType, includeInactive = false } = req.query;
    
    let query = {
      $or: [
        { userId: null, isDefault: true }, // System crops
        { userId: userId }                 // User crops
      ]
    };
    
    if (!includeInactive) {
      query.isActive = true;
    }
    
    if (category) {
      query.category = category;
    }
    
    if (productionType) {
      query.productionType = productionType;
    }
    
    const crops = await Crop.find(query)
      .sort({ isDefault: -1, category: 1, nameTr: 1 });
    
    res.json({
      success: true,
      data: crops.map(crop => crop.toPublicJSON()),
      count: crops.length
    });
    
  } catch (error) {
    Logger.error('Error fetching crops:', error);
    res.status(500).json({
      success: false,
      message: 'Ürünler getirilirken hata oluştu',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get single crop by ID
 * GET /api/v1/crops/:id
 */
const getCrop = async (req, res) => {
  try {
    const userId = req.user.uid;
    const { id } = req.params;
    
    const crop = await Crop.findOne({
      _id: id,
      $or: [
        { userId: null, isDefault: true },
        { userId: userId }
      ],
      isActive: true
    }).populate('expenseCount');
    
    if (!crop) {
      return res.status(404).json({
        success: false,
        message: 'Ürün bulunamadı'
      });
    }
    
    res.json({
      success: true,
      data: crop.toPublicJSON()
    });
    
  } catch (error) {
    Logger.error('Error fetching crop:', error);
    res.status(500).json({
      success: false,
      message: 'Ürün getirilirken hata oluştu',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Create new custom crop
 * POST /api/v1/crops
 */
const createCrop = async (req, res) => {
  try {
    const userId = req.user.uid;
    const cropData = { 
      ...req.body, 
      userId,
      isDefault: false // User crops are never default
    };
    
    // Check if user already has a crop with this name
    const existingCrop = await Crop.findOne({
      userId,
      name: cropData.name.toLowerCase(),
      isActive: true
    });
    
    if (existingCrop) {
      return res.status(400).json({
        success: false,
        message: 'Bu isimde bir ürün zaten mevcut'
      });
    }
    
    const crop = new Crop(cropData);
    await crop.save();
    
    Logger.info(`Custom crop created: ${crop.nameTr} for user ${userId}`);
    
    res.status(201).json({
      success: true,
      message: 'Özel ürün başarıyla oluşturuldu',
      data: crop.toPublicJSON()
    });
    
  } catch (error) {
    Logger.error('Error creating crop:', error);
    
    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: 'Bu isimde bir ürün zaten mevcut'
      });
    }
    
    res.status(500).json({
      success: false,
      message: 'Ürün oluşturulurken hata oluştu',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Update custom crop
 * PUT /api/v1/crops/:id
 */
const updateCrop = async (req, res) => {
  try {
    const userId = req.user.uid;
    const { id } = req.params;
    const updateData = req.body;
    
    // Remove fields that shouldn't be updated
    delete updateData.userId;
    delete updateData.isDefault;
    
    const crop = await Crop.findOne({
      _id: id,
      userId, // Only user's custom crops can be updated
      isActive: true
    });
    
    if (!crop) {
      return res.status(404).json({
        success: false,
        message: 'Ürün bulunamadı veya düzenleme yetkiniz yok'
      });
    }
    
    // Check if name is being changed and if it conflicts
    if (updateData.name && updateData.name.toLowerCase() !== crop.name) {
      const existingCrop = await Crop.findOne({
        userId,
        name: updateData.name.toLowerCase(),
        isActive: true,
        _id: { $ne: id }
      });
      
      if (existingCrop) {
        return res.status(400).json({
          success: false,
          message: 'Bu isimde bir ürün zaten mevcut'
        });
      }
    }
    
    // Update crop
    Object.assign(crop, updateData);
    await crop.save();
    
    Logger.info(`Custom crop updated: ${crop.nameTr} for user ${userId}`);
    
    res.json({
      success: true,
      message: 'Ürün başarıyla güncellendi',
      data: crop.toPublicJSON()
    });
    
  } catch (error) {
    Logger.error('Error updating crop:', error);
    res.status(500).json({
      success: false,
      message: 'Ürün güncellenirken hata oluştu',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Delete custom crop (soft delete)
 * DELETE /api/v1/crops/:id
 */
const deleteCrop = async (req, res) => {
  try {
    const userId = req.user.uid;
    const { id } = req.params;
    
    const crop = await Crop.findOne({
      _id: id,
      userId, // Only user's custom crops can be deleted
      isActive: true
    });
    
    if (!crop) {
      return res.status(404).json({
        success: false,
        message: 'Ürün bulunamadı veya silme yetkiniz yok'
      });
    }
    
    // Check if crop has expenses
    const expenseCount = await Expense.countDocuments({
      cropId: id,
      status: 'active'
    });
    
    if (expenseCount > 0) {
      return res.status(400).json({
        success: false,
        message: `Bu ürün silinemez. ${expenseCount} adet gider kaydı bulunmaktadır.`,
        expenseCount
      });
    }
    
    // Soft delete
    crop.isActive = false;
    await crop.save();
    
    Logger.info(`Custom crop deleted: ${crop.nameTr} for user ${userId}`);
    
    res.json({
      success: true,
      message: 'Ürün başarıyla silindi'
    });
    
  } catch (error) {
    Logger.error('Error deleting crop:', error);
    res.status(500).json({
      success: false,
      message: 'Ürün silinirken hata oluştu',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get crops by category
 * GET /api/v1/crops/category/:category
 */
const getCropsByCategory = async (req, res) => {
  try {
    const userId = req.user.uid;
    const { category } = req.params;
    
    const crops = await Crop.getCropsByCategory(category, userId);
    
    res.json({
      success: true,
      data: crops.map(crop => crop.toPublicJSON()),
      count: crops.length,
      category
    });
    
  } catch (error) {
    Logger.error('Error fetching crops by category:', error);
    res.status(500).json({
      success: false,
      message: 'Kategori ürünleri getirilirken hata oluştu',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get crops by production type
 * GET /api/v1/crops/production-type/:type
 */
const getCropsByProductionType = async (req, res) => {
  try {
    const userId = req.user.uid;
    const { type } = req.params;
    
    if (!['seasonal', 'continuous'].includes(type)) {
      return res.status(400).json({
        success: false,
        message: 'Geçersiz üretim tipi. seasonal veya continuous olmalıdır.'
      });
    }
    
    const crops = await Crop.getCropsByProductionType(type, userId);
    
    res.json({
      success: true,
      data: crops.map(crop => crop.toPublicJSON()),
      count: crops.length,
      productionType: type
    });
    
  } catch (error) {
    Logger.error('Error fetching crops by production type:', error);
    res.status(500).json({
      success: false,
      message: 'Üretim tipi ürünleri getirilirken hata oluştu',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get crop statistics
 * GET /api/v1/crops/:id/stats
 */
const getCropStats = async (req, res) => {
  try {
    const userId = req.user.uid;
    const { id } = req.params;
    
    const crop = await Crop.findOne({
      _id: id,
      $or: [
        { userId: null, isDefault: true },
        { userId: userId }
      ],
      isActive: true
    });
    
    if (!crop) {
      return res.status(404).json({
        success: false,
        message: 'Ürün bulunamadı'
      });
    }
    
    // Update crop statistics
    await crop.updateStats();
    
    res.json({
      success: true,
      data: {
        cropId: crop._id,
        cropName: crop.nameTr,
        stats: crop.stats,
        category: crop.category,
        productionType: crop.productionType
      }
    });
    
  } catch (error) {
    Logger.error('Error fetching crop stats:', error);
    res.status(500).json({
      success: false,
      message: 'Ürün istatistikleri getirilirken hata oluştu',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Initialize default crops
 * POST /api/v1/crops/initialize-defaults
 */
const initializeDefaults = async (req, res) => {
  try {
    const result = await initializeDefaultCrops();
    
    if (result) {
      res.json({
        success: true,
        message: 'Varsayılan ürünler başarıyla yüklendi'
      });
    } else {
      res.status(500).json({
        success: false,
        message: 'Varsayılan ürünler yüklenirken hata oluştu'
      });
    }
    
  } catch (error) {
    Logger.error('Error initializing default crops:', error);
    res.status(500).json({
      success: false,
      message: 'Varsayılan ürünler yüklenirken hata oluştu',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  getCrops,
  getCrop,
  createCrop,
  updateCrop,
  deleteCrop,
  getCropsByCategory,
  getCropsByProductionType,
  getCropStats,
  initializeDefaults
};
