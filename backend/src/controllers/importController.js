/**
 * Import Controller for Çiftçi Not Defterim
 * Handles Excel import and AI-powered data processing
 */

const excelService = require('../services/excelService');
const logger = require('../utils/logger');
const path = require('path');
const fs = require('fs').promises;

class ImportController {
  /**
   * Download Excel template
   */
  async downloadTemplate(req, res) {
    try {
      logger.info('Template download requested by user:', req.user?.id);

      // Create template
      const result = await excelService.createImportTemplate();
      
      if (!result.success) {
        return res.status(500).json({
          success: false,
          error: {
            code: result.error,
            message: result.message
          }
        });
      }

      // Get buffer
      const bufferResult = await excelService.getBuffer();
      
      if (!bufferResult.success) {
        return res.status(500).json({
          success: false,
          error: {
            code: bufferResult.error,
            message: bufferResult.message
          }
        });
      }

      // Set response headers
      const filename = `ciftci-not-defterim-template-${Date.now()}.xlsx`;
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      res.setHeader('Content-Length', bufferResult.buffer.length);

      // Send file
      res.send(bufferResult.buffer);

      logger.info('Template downloaded successfully:', {
        userId: req.user?.id,
        filename,
        size: bufferResult.buffer.length
      });

    } catch (error) {
      logger.error('Template download error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'TEMPLATE_ERROR',
          message: 'Template indirilemedi'
        }
      });
    }
  }

  /**
   * Upload and process Excel file
   */
  async uploadExcel(req, res) {
    try {
      logger.info('Excel upload started by user:', req.user?.id);

      if (!req.fileInfo) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'NO_FILE',
            message: 'Dosya bulunamadı'
          }
        });
      }

      const { path: filePath, originalName, size } = req.fileInfo;

      // Validate file
      const validation = await excelService.validateFile(filePath, size);
      if (!validation.success) {
        return res.status(400).json({
          success: false,
          error: {
            code: validation.error,
            message: validation.message
          }
        });
      }

      // Read Excel file
      const readResult = await excelService.readExcelFile(filePath);
      
      if (!readResult.success) {
        return res.status(400).json({
          success: false,
          error: {
            code: readResult.error,
            message: readResult.message
          }
        });
      }

      // Process and validate data
      const processedData = await this.processExcelData(readResult.data, req.user);

      // Return processed data for preview
      res.json({
        success: true,
        data: {
          fileName: originalName,
          fileSize: size,
          totalRows: readResult.data.totalRows,
          validRows: processedData.validRows.length,
          invalidRows: processedData.invalidRows.length,
          headers: readResult.data.headers,
          validData: processedData.validRows,
          errors: processedData.invalidRows,
          summary: processedData.summary
        },
        message: `${processedData.validRows.length} geçerli kayıt bulundu`
      });

      logger.info('Excel processing completed:', {
        userId: req.user?.id,
        fileName: originalName,
        totalRows: readResult.data.totalRows,
        validRows: processedData.validRows.length,
        invalidRows: processedData.invalidRows.length
      });

    } catch (error) {
      logger.error('Excel upload error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'PROCESSING_ERROR',
          message: 'Dosya işlenirken hata oluştu'
        }
      });
    }
  }

  /**
   * Process Excel data and validate
   */
  async processExcelData(excelData, user) {
    const validRows = [];
    const invalidRows = [];
    const summary = {
      totalAmount: 0,
      categoryCount: {},
      dateRange: { min: null, max: null }
    };

    // Required fields mapping (Turkish to English)
    const fieldMapping = {
      'Tarih': 'date',
      'Kategori': 'category', 
      'Açıklama': 'description',
      'Tutar': 'amount',
      'Sezon': 'season',
      'Tarla': 'field',
      'Ürün': 'crop',
      'Notlar': 'notes'
    };

    // Valid categories
    const validCategories = [
      'Gübre', 'İşçilik', 'İlaç', 'Su', 'Yakıt', 'Tohum', 'Makine', 'Depolama'
    ];

    for (let i = 0; i < excelData.rows.length; i++) {
      const row = excelData.rows[i];
      const rowNumber = i + 2; // +2 because Excel starts from 1 and we skip header
      const errors = [];
      const processedRow = {
        rowNumber,
        originalData: row,
        processedData: {},
        userId: user.id
      };

      // Process each field
      for (const [turkishField, englishField] of Object.entries(fieldMapping)) {
        const value = row[turkishField];
        
        switch (englishField) {
          case 'date':
            const dateResult = this.validateDate(value);
            if (dateResult.valid) {
              processedRow.processedData.date = dateResult.date;
              // Update date range
              if (!summary.dateRange.min || dateResult.date < summary.dateRange.min) {
                summary.dateRange.min = dateResult.date;
              }
              if (!summary.dateRange.max || dateResult.date > summary.dateRange.max) {
                summary.dateRange.max = dateResult.date;
              }
            } else {
              errors.push(`Tarih: ${dateResult.error}`);
            }
            break;

          case 'category':
            if (!value || !value.trim()) {
              errors.push('Kategori: Zorunlu alan');
            } else if (!validCategories.includes(value.trim())) {
              errors.push(`Kategori: Geçersiz kategori "${value}". Geçerli kategoriler: ${validCategories.join(', ')}`);
            } else {
              processedRow.processedData.category = value.trim();
              // Update category count
              summary.categoryCount[value.trim()] = (summary.categoryCount[value.trim()] || 0) + 1;
            }
            break;

          case 'amount':
            const amountResult = this.validateAmount(value);
            if (amountResult.valid) {
              processedRow.processedData.amount = amountResult.amount;
              summary.totalAmount += amountResult.amount;
            } else {
              errors.push(`Tutar: ${amountResult.error}`);
            }
            break;

          case 'description':
            if (!value || !value.trim()) {
              errors.push('Açıklama: Zorunlu alan');
            } else {
              processedRow.processedData.description = value.trim();
            }
            break;

          default:
            // Optional fields
            if (value && value.trim()) {
              processedRow.processedData[englishField] = value.trim();
            }
            break;
        }
      }

      // Add timestamps
      processedRow.processedData.createdAt = new Date();
      processedRow.processedData.importedAt = new Date();

      // Categorize row
      if (errors.length === 0) {
        validRows.push(processedRow);
      } else {
        processedRow.errors = errors;
        invalidRows.push(processedRow);
      }
    }

    return {
      validRows,
      invalidRows,
      summary
    };
  }

  /**
   * Validate date field
   */
  validateDate(value) {
    if (!value) {
      return { valid: false, error: 'Tarih zorunlu' };
    }

    let date;
    
    // Try different date formats
    if (value instanceof Date) {
      date = value;
    } else if (typeof value === 'string') {
      // Try ISO format (YYYY-MM-DD)
      if (/^\d{4}-\d{2}-\d{2}$/.test(value)) {
        date = new Date(value + 'T00:00:00.000Z');
      } else {
        date = new Date(value);
      }
    } else if (typeof value === 'number') {
      // Excel date serial number
      date = new Date((value - 25569) * 86400 * 1000);
    } else {
      return { valid: false, error: 'Geçersiz tarih formatı' };
    }

    if (isNaN(date.getTime())) {
      return { valid: false, error: 'Geçersiz tarih' };
    }

    // Check reasonable date range (not too old or too future)
    const now = new Date();
    const minDate = new Date(now.getFullYear() - 10, 0, 1); // 10 years ago
    const maxDate = new Date(now.getFullYear() + 1, 11, 31); // 1 year in future

    if (date < minDate || date > maxDate) {
      return { valid: false, error: 'Tarih makul aralıkta olmalı' };
    }

    return { valid: true, date };
  }

  /**
   * Validate amount field
   */
  validateAmount(value) {
    if (!value && value !== 0) {
      return { valid: false, error: 'Tutar zorunlu' };
    }

    let amount;
    
    if (typeof value === 'number') {
      amount = value;
    } else if (typeof value === 'string') {
      // Remove Turkish number formatting
      const cleanValue = value.replace(/[.,]/g, match => match === ',' ? '.' : '');
      amount = parseFloat(cleanValue);
    } else {
      return { valid: false, error: 'Geçersiz tutar formatı' };
    }

    if (isNaN(amount)) {
      return { valid: false, error: 'Geçersiz tutar' };
    }

    if (amount < 0) {
      return { valid: false, error: 'Tutar negatif olamaz' };
    }

    if (amount > 1000000) { // 1 million limit
      return { valid: false, error: 'Tutar çok yüksek' };
    }

    return { valid: true, amount };
  }

  /**
   * Get import history
   */
  async getImportHistory(req, res) {
    try {
      // This will be implemented when we add ImportHistory model
      res.json({
        success: true,
        data: [],
        message: 'Import geçmişi özelliği yakında eklenecek'
      });
    } catch (error) {
      logger.error('Import history error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'HISTORY_ERROR',
          message: 'Geçmiş alınamadı'
        }
      });
    }
  }
}

module.exports = new ImportController();
