/**
 * Expense Controller for Çiftçi Not Defterim
 * Handles CRUD operations for agricultural expenses
 */

const Expense = require('../models/Expense');
const Category = require('../models/Category');
const { asyncHandler, notFoundError, validationError } = require('../middleware/errorHandler');
const { ResponseHelpers, TurkishHelpers, DateHelpers } = require('../utils/helpers');
const { PAGINATION } = require('../utils/constants');
const logger = require('../utils/logger');

/**
 * Get user expenses with filtering and pagination
 * GET /api/v1/expenses
 */
const getExpenses = asyncHandler(async (req, res) => {
  const {
    page = PAGINATION.DEFAULT_PAGE,
    limit = PAGINATION.DEFAULT_LIMIT,
    startDate,
    endDate,
    categoryId,
    seasonId,
    fieldId,
    cropId,
    trackingMode,
    search,
    sortBy = 'date',
    sortOrder = 'desc'
  } = req.query;

  // Build query
  const query = { 
    userId: req.user._id, 
    status: 'active' 
  };

  // Date range filter
  if (startDate || endDate) {
    query.date = {};
    if (startDate) query.date.$gte = new Date(startDate);
    if (endDate) query.date.$lte = new Date(endDate);
  }

  // Category filter
  if (categoryId) {
    query.categoryId = categoryId;
  }

  // Season filter
  if (seasonId) {
    query.seasonId = seasonId;
  }

  // TWO-MODE SYSTEM: Field filter
  if (fieldId) {
    query.fieldId = fieldId;
  }

  // TWO-MODE SYSTEM: Crop filter
  if (cropId) {
    query.cropId = cropId;
  }

  // TWO-MODE SYSTEM: Tracking mode filter
  if (trackingMode) {
    query.trackingMode = trackingMode;
  }

  // Search filter
  if (search) {
    query.$text = { $search: search };
  }

  // Pagination
  const pageNum = parseInt(page);
  const limitNum = Math.min(parseInt(limit), PAGINATION.MAX_LIMIT);
  const skip = (pageNum - 1) * limitNum;

  // Sort options
  const sortOptions = {};
  sortOptions[sortBy] = sortOrder === 'asc' ? 1 : -1;

  // Add secondary sort by createdAt for consistent ordering
  if (sortBy !== 'createdAt') {
    sortOptions.createdAt = -1; // Always newest first for same date
  }

  // Execute query with TWO-MODE SYSTEM population
  const [expenses, total] = await Promise.all([
    Expense.find(query)
      .populate('categoryId', 'name emoji color icon')
      .populate('seasonId', 'name emoji color isActive startDate endDate')
      .populate('fieldId', 'name size location isDefault')
      .populate('cropId', 'name nameTr category productionType emoji')
      .sort(sortOptions)
      .skip(skip)
      .limit(limitNum)
      .lean(),
    Expense.countDocuments(query)
  ]);

  // Calculate summary
  const summary = await Expense.aggregate([
    { $match: query },
    {
      $group: {
        _id: null,
        totalAmount: { $sum: '$amount' },
        expenseCount: { $sum: 1 },
        avgAmount: { $avg: '$amount' }
      }
    }
  ]);

  const summaryData = summary[0] || {
    totalAmount: 0,
    expenseCount: 0,
    avgAmount: 0
  };

  logger.logDatabase('expense_list', 'expenses', query, expenses);

  // Format expenses
  const formattedExpenses = expenses.map(expense => ({
    ...expense,
    formattedAmount: TurkishHelpers.formatCurrency(expense.amount),
    formattedDate: TurkishHelpers.formatDate(expense.date)
  }));

  // Create response with pagination and summary
  const response = ResponseHelpers.paginated(formattedExpenses, pageNum, limitNum, total);
  response.data.summary = {
    totalAmount: summaryData.totalAmount,
    formattedTotalAmount: TurkishHelpers.formatCurrency(summaryData.totalAmount),
    expenseCount: summaryData.expenseCount,
    avgAmount: Math.round(summaryData.avgAmount * 100) / 100
  };

  // Log response for debugging
  logger.info(`💰 EXPENSE RESPONSE: User ${req.user._id} - Found ${formattedExpenses.length} expenses, Total: ${summaryData.totalAmount} TL`);
  logger.info(`💰 QUERY: ${JSON.stringify(query)}`);
  if (formattedExpenses.length > 0) {
    const firstExpense = formattedExpenses[0];
    logger.info(`💰 FIRST EXPENSE: ID=${firstExpense._id}, Amount=${firstExpense.amount}, Category=${firstExpense.categoryId}, Date=${firstExpense.date}`);
  } else {
    logger.info(`💰 NO EXPENSES FOUND FOR USER ${req.user._id}`);
  }

  // Log the actual response structure
  logger.info(`💰 RESPONSE STRUCTURE: success=${response.success}, dataLength=${response.data?.length}, totalAmount=${response.data?.summary?.totalAmount}`);

  res.status(200).json(response);
});

/**
 * Get single expense by ID
 * GET /api/v1/expenses/:id
 */
const getExpenseById = asyncHandler(async (req, res) => {
  const expense = await Expense.findOne({
    _id: req.params.id,
    userId: req.user._id,
    status: 'active'
  })
  .populate('categoryId', 'name emoji color icon')
  .populate('seasonId', 'name emoji color isActive startDate endDate')
  .populate('fieldId', 'name size location isDefault')
  .populate('cropId', 'name nameTr category productionType emoji');

  if (!expense) {
    throw notFoundError('Gider');
  }

  res.status(200).json(
    ResponseHelpers.success({
      ...expense.toObject(),
      formattedAmount: TurkishHelpers.formatCurrency(expense.amount),
      formattedDate: TurkishHelpers.formatDate(expense.date)
    }, 'Gider detayları getirildi')
  );
});

/**
 * Create new expense
 * POST /api/v1/expenses
 */
const createExpense = asyncHandler(async (req, res) => {
  // DEBUG: Log the incoming request body
  console.log('📝 CREATE EXPENSE REQUEST BODY:', JSON.stringify(req.body, null, 2));

  const {
    categoryId,
    seasonId,
    amount,
    description,
    date,
    location,
    photos,
    cropType,
    farmArea,
    // TWO-MODE SYSTEM: New fields
    fieldId,
    cropId,
    trackingMode
  } = req.body;

  // Verify category exists and belongs to user or is default
  const category = await Category.findOne({
    _id: categoryId,
    $or: [
      { isDefault: true },
      { userId: req.user._id }
    ],
    isActive: true
  });

  if (!category) {
    throw validationError('categoryId', 'Geçersiz kategori seçimi');
  }

  // Parse amount if it's a string (Turkish format)
  const parsedAmount = typeof amount === 'string' ? TurkishHelpers.parseNumber(amount) : amount;

  // Validate and get season
  let validatedSeasonId = seasonId;

  if (seasonId) {
    // Verify season exists and belongs to user
    const Season = require('../models/Season');
    const season = await Season.findOne({
      _id: seasonId,
      userId: req.user._id
    });

    if (!season) {
      throw validationError('seasonId', 'Geçersiz sezon seçimi');
    }

    validatedSeasonId = season._id;
  } else {
    // If no seasonId provided, use active season
    const Season = require('../models/Season');
    const activeSeason = await Season.getActiveSeasonForUser(req.user._id);

    if (!activeSeason) {
      throw validationError('seasonId', 'Aktif sezon bulunamadı. Lütfen bir sezon seçin.');
    }

    validatedSeasonId = activeSeason._id;
  }

  // TWO-MODE SYSTEM: Validate field and crop if provided
  if (fieldId) {
    const Field = require('../models/Field');
    const field = await Field.findOne({
      _id: fieldId,
      userId: req.user._id,
      isActive: true
    });

    if (!field) {
      throw validationError('fieldId', 'Geçersiz tarla seçimi');
    }
  }

  if (cropId) {
    const Crop = require('../models/Crop');
    const crop = await Crop.findOne({
      _id: cropId,
      $or: [
        { userId: req.user._id },
        { userId: null, isDefault: true }
      ],
      isActive: true
    });

    if (!crop) {
      throw validationError('cropId', 'Geçersiz ürün seçimi');
    }
  }

  // Create expense
  const expenseDate = new Date(date);

  // DEBUG: Log expense data before creation
  const expenseData = {
    userId: req.user._id,
    categoryId,
    amount: parsedAmount,
    description: description?.trim(),
    date: expenseDate,
    seasonId: validatedSeasonId,
    // TWO-MODE SYSTEM: New fields
    fieldId: fieldId || null,
    cropId: cropId || null,
    trackingMode: trackingMode || 'simple',
    location,
    photos: photos || [],
    cropType: cropType?.trim(),
    farmArea: farmArea?.trim(),
    createdBy: 'user'
  };

  console.log('🔧 EXPENSE DATA BEFORE CREATION:', JSON.stringify(expenseData, null, 2));

  const expense = await Expense.create(expenseData);

  // Update category usage
  await category.incrementUsage(parsedAmount);

  // Update user stats
  await req.user.updateStats({ amount: parsedAmount, date: expenseDate });

  // Populate category, season, field, and crop for response
  await expense.populate([
    { path: 'categoryId', select: 'name emoji color icon' },
    { path: 'seasonId', select: 'name emoji color isActive startDate endDate' },
    { path: 'fieldId', select: 'name size location isDefault' },
    { path: 'cropId', select: 'name nameTr category productionType emoji' }
  ]);

  logger.info('Expense created:', {
    userId: req.user._id,
    expenseId: expense._id,
    amount: parsedAmount,
    categoryId
  });

  res.status(201).json(
    ResponseHelpers.success({
      ...expense.toObject(),
      formattedAmount: TurkishHelpers.formatCurrency(expense.amount),
      formattedDate: TurkishHelpers.formatDate(expense.date)
    }, 'Gider başarıyla eklendi')
  );
});

/**
 * Update expense
 * PUT /api/v1/expenses/:id
 */
const updateExpense = asyncHandler(async (req, res) => {
  const { categoryId, amount, description, date, location, photos, cropType, farmArea } = req.body;

  // Find expense
  const expense = await Expense.findOne({
    _id: req.params.id,
    userId: req.user._id,
    status: 'active'
  });

  if (!expense) {
    throw notFoundError('Gider');
  }

  // Verify category if provided
  if (categoryId) {
    const category = await Category.findOne({
      _id: categoryId,
      $or: [
        { isDefault: true },
        { userId: req.user._id }
      ],
      isActive: true
    });

    if (!category) {
      throw validationError('categoryId', 'Geçersiz kategori seçimi');
    }
  }

  // Update fields
  const updateData = {};
  if (categoryId) updateData.categoryId = categoryId;
  if (amount !== undefined) {
    updateData.amount = typeof amount === 'string' ? TurkishHelpers.parseNumber(amount) : amount;
  }
  if (description !== undefined) updateData.description = description?.trim();
  if (date) {
    updateData.date = new Date(date);
    // Update season based on new date
    const month = updateData.date.getMonth() + 1;
    if (month >= 3 && month <= 5) updateData.seasonId = 'spring';
    else if (month >= 6 && month <= 8) updateData.seasonId = 'summer';
    else if (month >= 9 && month <= 11) updateData.seasonId = 'autumn';
    else updateData.seasonId = 'winter';
  }
  if (location) updateData.location = location;
  if (photos) updateData.photos = photos;
  if (cropType !== undefined) updateData.cropType = cropType?.trim();
  if (farmArea !== undefined) updateData.farmArea = farmArea?.trim();

  // Update expense
  const updatedExpense = await Expense.findByIdAndUpdate(
    req.params.id,
    updateData,
    { new: true, runValidators: true }
  ).populate('categoryId', 'name emoji color icon');

  logger.info('Expense updated:', {
    userId: req.user._id,
    expenseId: expense._id,
    updatedFields: Object.keys(updateData)
  });

  res.status(200).json(
    ResponseHelpers.success({
      ...updatedExpense.toObject(),
      formattedAmount: TurkishHelpers.formatCurrency(updatedExpense.amount),
      formattedDate: TurkishHelpers.formatDate(updatedExpense.date)
    }, 'Gider güncellendi')
  );
});

/**
 * Delete expense (soft delete)
 * DELETE /api/v1/expenses/:id
 */
const deleteExpense = asyncHandler(async (req, res) => {
  const expense = await Expense.findOne({
    _id: req.params.id,
    userId: req.user._id,
    status: 'active'
  });

  if (!expense) {
    throw notFoundError('Gider');
  }

  // Soft delete
  expense.status = 'deleted';
  await expense.save();

  logger.info('Expense deleted:', {
    userId: req.user._id,
    expenseId: expense._id
  });

  res.status(200).json(
    ResponseHelpers.success(null, 'Gider silindi')
  );
});

/**
 * Get expense statistics
 * GET /api/v1/expenses/stats
 */
const getExpenseStats = asyncHandler(async (req, res) => {
  const { startDate, endDate, groupBy = 'category' } = req.query;

  const matchQuery = { 
    userId: req.user._id, 
    status: 'active' 
  };

  // Date range filter
  if (startDate || endDate) {
    matchQuery.date = {};
    if (startDate) matchQuery.date.$gte = new Date(startDate);
    if (endDate) matchQuery.date.$lte = new Date(endDate);
  }

  let stats;

  if (groupBy === 'category') {
    stats = await Expense.getCategoryStats(req.user._id, { startDate, endDate });
  } else if (groupBy === 'season') {
    stats = await Expense.getSeasonalStats(req.user._id);
  } else {
    // General stats
    stats = await Expense.getExpenseStats(req.user._id, { startDate, endDate });
  }

  res.status(200).json(
    ResponseHelpers.success(stats, 'İstatistikler getirildi')
  );
});

module.exports = {
  getExpenses,
  getExpenseById,
  createExpense,
  updateExpense,
  deleteExpense,
  getExpenseStats
};
