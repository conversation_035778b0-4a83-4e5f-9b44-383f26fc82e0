/**
 * Season Controller for Çiftçi Not Defterim
 * Handles user-defined seasons with flexible date ranges and active season management
 */
const Season = require('../models/Season');
const Expense = require('../models/Expense');
const Category = require('../models/Category');
const { asyncHandler, notFoundError, validationError, authError } = require('../middleware/errorHandler');
const { ResponseHelpers, DateHelpers, TurkishHelpers } = require('../utils/helpers');
const logger = require('../utils/logger');
const Joi = require('joi');
// Validation schemas
const seasonValidationSchema = Joi.object({
  name: Joi.string().trim().min(1).max(100).required()
    .messages({
      'string.empty': 'Sezon adı gereklidir',
      'string.max': 'Sezon adı en fazla 100 karakter olabilir'
    }),
  description: Joi.string().trim().max(500).allow('', null)
    .messages({
      'string.max': 'Açıklama en fazla 500 karakter olabilir'
    }),
  startDate: Joi.date().iso().allow(null)
    .messages({
      'date.format': 'Geçerli bir başlangıç tarihi giriniz'
    }),
  endDate: Joi.date().iso().min(Joi.ref('startDate')).allow(null)
    .messages({
      'date.format': 'Geçerli bir bitiş tarihi giriniz',
      'date.min': 'Bitiş tarihi başlangıç tarihinden sonra olmalıdır'
    }),
  color: Joi.string().pattern(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/)
    .default('#4CAF50')
    .messages({
      'string.pattern.base': 'Geçerli bir renk kodu giriniz (#RRGGBB formatında)'
    }),
  emoji: Joi.string().default('🌱')
    .messages({
      'string.base': 'Geçerli bir emoji seçiniz'
    }),
  isActive: Joi.boolean().default(false),
  isDefault: Joi.boolean().default(false)
});
/**
 * Get user's seasons
 * GET /api/v1/seasons
 */
const getSeasons = asyncHandler(async (req, res) => {
  console.log('🚨🚨🚨 SEASONS CONTROLLER HIT! 🚨🚨🚨');
  console.log('🔄 BACKEND GET SEASONS: User ID:', req.user._id);
  const seasons = await Season.getUserSeasons(req.user._id);
  console.log('🔄 BACKEND GET SEASONS: Found seasons count:', seasons.length);
  console.log('🔄 BACKEND GET SEASONS: Seasons data:', seasons.map(s => ({
    id: s._id,
    name: s.name,
    isActive: s.isActive,
    userId: s.userId
  })));
  // Add computed fields
  const seasonsWithStatus = seasons.map(season => ({
    ...season.toObject(),
    isCurrent: season.isCurrent,
    durationInDays: season.durationInDays
  }));
  console.log('🔄 BACKEND GET SEASONS: Response data:', seasonsWithStatus.map(s => ({
    id: s._id,
    name: s.name,
    isActive: s.isActive,
    userId: s.userId
  })));
  res.status(200).json(
    ResponseHelpers.success(seasonsWithStatus, 'Kullanıcı sezonları getirildi')
  );
});
/**
 * Create new season
 * POST /api/v1/seasons
 */
const createSeason = asyncHandler(async (req, res) => {
  // DEBUG: Log the incoming request
  console.log('🔄 BACKEND SEASON CREATE: POST /seasons endpoint hit');
  console.log('🔄 BACKEND SEASON CREATE: Request headers:', {
    authorization: req.headers.authorization ? `Bearer ${req.headers.authorization.substring(0, 20)}...` : 'missing',
    contentType: req.headers['content-type'],
    userAgent: req.headers['user-agent']
  });
  console.log('🔄 BACKEND SEASON CREATE: Request body:', JSON.stringify(req.body, null, 2));
  console.log('🔄 BACKEND SEASON CREATE: User from auth middleware:', {
    exists: !!req.user,
    userId: req.user?._id,
    userEmail: req.user?.email,
    userUid: req.user?.firebaseUid
  });
  // Check if user exists (auth middleware should have set this)
  if (!req.user) {
    console.error('❌ BACKEND SEASON CREATE: No user found in request - auth middleware failed');
    throw authError('Kimlik doğrulama gerekli');
  }
  // Validate input with detailed error logging
  console.log('🔄 BACKEND SEASON CREATE: Starting Joi validation...');
  console.log('🔄 BACKEND SEASON CREATE: Input data types:', {
    name: typeof req.body.name,
    description: typeof req.body.description,
    startDate: typeof req.body.startDate,
    endDate: typeof req.body.endDate,
    isActive: typeof req.body.isActive,
    isDefault: typeof req.body.isDefault,
    color: typeof req.body.color,
    emoji: typeof req.body.emoji
  });
  console.log('🔄 BACKEND SEASON CREATE: Input data values:', {
    name: req.body.name,
    description: req.body.description,
    startDate: req.body.startDate,
    endDate: req.body.endDate,
    isActive: req.body.isActive,
    isDefault: req.body.isDefault,
    color: req.body.color,
    emoji: req.body.emoji,
    emojiLength: req.body.emoji?.length,
    emojiCharCodes: req.body.emoji ? Array.from(req.body.emoji).map(c => c.charCodeAt(0)) : []
  });
  const { error, value } = seasonValidationSchema.validate(req.body);
  console.log('🔄 BACKEND SEASON CREATE: Joi validation completed:', {
    hasError: !!error,
    errorMessage: error?.details?.[0]?.message,
    errorPath: error?.details?.[0]?.path,
    errorType: error?.details?.[0]?.type,
    allErrors: error?.details?.map(d => ({ message: d.message, path: d.path, type: d.type })),
    validatedValue: value,
    originalBody: req.body
  });
  if (error) {
    console.error('❌ BACKEND SEASON CREATE: Detailed validation error:', {
      message: error.details[0].message,
      path: error.details[0].path,
      type: error.details[0].type,
      context: error.details[0].context
    });
    throw validationError(error.details[0].message);
  }
  // Check if user already has a season with this name with migration support
  let finalSeasonName = value.name;
  const existingSeason = await Season.findOne({
    userId: req.user._id,
    name: finalSeasonName
  });
  if (existingSeason) {
    console.log('🔄 BACKEND SEASON CREATE: Duplicate season name found:', {
      existingSeasonId: existingSeason._id,
      existingSeasonName: existingSeason.name,
      requestedName: finalSeasonName
    });
    // Special handling for migration seasons (both old "Migration" and new "Aktarım" formats)
    if (finalSeasonName.includes('Migration') || finalSeasonName.includes('Aktarım')) {
      console.log('🔄 BACKEND SEASON CREATE: Migration season detected, generating unique name...');
      // Generate unique name for migration with user-friendly numbering
      let counter = 2;
      let baseName = finalSeasonName;
      // Extract base name without existing counter for cleaner naming
      const migrationMatch = baseName.match(/^(.+?)(?: \((?:Migration|Aktarım)[^)]*\))(.*)$/);
      if (migrationMatch) {
        const yearPart = migrationMatch[1]; // "2025 Sezonu"
        const datePart = migrationMatch[2] || ''; // any remaining part
        baseName = `${yearPart}${datePart}`;
      }
      while (await Season.findOne({ name: finalSeasonName, userId: req.user._id })) {
        finalSeasonName = `${baseName} (Aktarım ${counter})`;
        counter++;
        // Safety limit
        if (counter > 100) {
          const timestamp = new Date().toLocaleDateString('tr-TR', {
            day: '2-digit',
            month: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
          });
          finalSeasonName = `${baseName} (Aktarım ${timestamp})`;
          break;
        }
      }
      console.log('🔄 BACKEND SEASON CREATE: Generated unique migration season name:', {
        originalName: value.name,
        uniqueName: finalSeasonName
      });
      // Update value with unique name
      value.name = finalSeasonName;
    } else {
      throw validationError('Bu isimde bir sezon zaten mevcut');
    }
  }
  // Create new season
  const seasonData = {
    ...value,
    userId: req.user._id
  };
  const season = await Season.create(seasonData);
  logger.info(`Season created: ${season._id} by user ${req.user._id}`);
  res.status(201).json(
    ResponseHelpers.success(season, 'Sezon başarıyla oluşturuldu')
  );
});
/**
 * Get active season for user
 * GET /api/v1/seasons/active
 */
const getActiveSeason = asyncHandler(async (req, res) => {
  const activeSeason = await Season.getActiveSeasonForUser(req.user._id);
  if (!activeSeason) {
    throw notFoundError('Aktif sezon bulunamadı');
  }
  res.status(200).json(
    ResponseHelpers.success(activeSeason, 'Aktif sezon getirildi')
  );
});
/**
 * Get season by ID
 * GET /api/v1/seasons/:id
 */
const getSeasonById = asyncHandler(async (req, res) => {
  const season = await Season.findOne({
    _id: req.params.id,
    userId: req.user._id
  });
  if (!season) {
    throw notFoundError('Sezon bulunamadı');
  }
  // Add computed fields
  const seasonData = {
    ...season.toObject(),
    isCurrent: season.isCurrent,
    durationInDays: season.durationInDays
  };
  res.status(200).json(
    ResponseHelpers.success(seasonData, 'Sezon detayları getirildi')
  );
});
/**
 * Update season
 * PUT /api/v1/seasons/:id
 */
const updateSeason = asyncHandler(async (req, res) => {
  // Validate input
  const { error, value } = seasonValidationSchema.validate(req.body);
  if (error) {
    throw validationError(error.details[0].message);
  }
  // Find season
  const season = await Season.findOne({
    _id: req.params.id,
    userId: req.user._id
  });
  if (!season) {
    throw notFoundError('Sezon bulunamadı');
  }
  // Check if name is being changed and if it conflicts
  if (value.name !== season.name) {
    const existingSeason = await Season.findOne({
      userId: req.user._id,
      name: value.name,
      _id: { $ne: req.params.id }
    });
    if (existingSeason) {
      throw validationError('Bu isimde bir sezon zaten mevcut');
    }
  }
  // Update season
  Object.assign(season, value);
  season.updatedAt = new Date();
  await season.save();
  logger.info(`Season updated: ${season._id} by user ${req.user._id}`);
  res.status(200).json(
    ResponseHelpers.success(season, 'Sezon başarıyla güncellendi')
  );
});
/**
 * Activate season
 * PATCH /api/v1/seasons/:id/activate
 */
const activateSeason = asyncHandler(async (req, res) => {
  const mongoose = require('mongoose');
  const session = await mongoose.startSession();
  try {
    await session.withTransaction(async () => {
      // Find the season to activate
      const seasonToActivate = await Season.findOne({
        _id: req.params.id,
        userId: req.user._id
      }).session(session);
      if (!seasonToActivate) {
        throw notFoundError('Sezon bulunamadı');
      }
      // Get current active season
      const currentActiveSeason = await Season.findOne({
        userId: req.user._id,
        isActive: true
      }).session(session);
      // If this season is already active, no need to change
      if (seasonToActivate.isActive) {
        return {
          previousActiveSeason: null,
          newActiveSeason: seasonToActivate
        };
      }
      // Deactivate current active season
      if (currentActiveSeason) {
        currentActiveSeason.isActive = false;
        currentActiveSeason.updatedAt = new Date();
        await currentActiveSeason.save({ session });
      }
      // Activate the new season
      seasonToActivate.isActive = true;
      seasonToActivate.updatedAt = new Date();
      await seasonToActivate.save({ session });
      logger.info(`Season activated: ${seasonToActivate._id} by user ${req.user._id}, previous: ${currentActiveSeason?._id || 'none'}`);
      return {
        previousActiveSeason: currentActiveSeason,
        newActiveSeason: seasonToActivate
      };
    });
    // Get fresh data after transaction
    const newActiveSeason = await Season.findById(req.params.id);
    res.status(200).json(
      ResponseHelpers.success({
        activeSeason: newActiveSeason,
        message: 'Sezon başarıyla aktif hale getirildi'
      }, 'Aktif sezon değiştirildi')
    );
  } catch (error) {
    logger.error(`Season activation failed: ${error.message}`);
    throw error;
  } finally {
    await session.endSession();
  }
});
/**
 * Get season summary with expenses
 * GET /api/v1/seasons/:id/summary
 */
const getSeasonSummary = asyncHandler(async (req, res) => {
  const season = await Season.findOne({
    _id: req.params.id,
    userId: req.user._id
  });
  if (!season) {
    throw notFoundError('Sezon bulunamadı');
  }
  // Get season summary using aggregation
  const summaryData = await Season.aggregate([
    {
      $match: {
        _id: season._id
      }
    },
    {
      $lookup: {
        from: 'expenses',
        localField: '_id',
        foreignField: 'seasonId',
        as: 'expenses'
      }
    },
    {
      $addFields: {
        totalExpenses: { $size: '$expenses' },
        totalAmount: { $sum: '$expenses.amount' },
        avgAmount: { $avg: '$expenses.amount' }
      }
    },
    {
      $lookup: {
        from: 'expenses',
        let: { seasonId: '$_id' },
        pipeline: [
          {
            $match: {
              $expr: { $eq: ['$seasonId', '$$seasonId'] }
            }
          },
          {
            $lookup: {
              from: 'categories',
              localField: 'categoryId',
              foreignField: '_id',
              as: 'category'
            }
          },
          {
            $unwind: '$category'
          },
          {
            $group: {
              _id: '$categoryId',
              categoryName: { $first: '$category.name' },
              categoryEmoji: { $first: '$category.emoji' },
              categoryColor: { $first: '$category.color' },
              totalAmount: { $sum: '$amount' },
              expenseCount: { $sum: 1 },
              avgAmount: { $avg: '$amount' }
            }
          },
          {
            $sort: { totalAmount: -1 }
          }
        ],
        as: 'categoryBreakdown'
      }
    }
  ]);
  const summary = summaryData[0];
  if (!summary) {
    throw notFoundError('Sezon özet bilgileri bulunamadı');
  }
  // Add computed fields
  const responseData = {
    season: {
      ...season.toObject(),
      isCurrent: season.isCurrent,
      durationInDays: season.durationInDays
    },
    summary: {
      totalExpenses: summary.totalExpenses || 0,
      totalAmount: summary.totalAmount || 0,
      avgAmount: summary.avgAmount || 0,
      formattedTotalAmount: TurkishHelpers.formatCurrency(summary.totalAmount || 0)
    },
    categoryBreakdown: summary.categoryBreakdown || []
  };
  res.status(200).json(
    ResponseHelpers.success(responseData, 'Sezon özet bilgileri getirildi')
  );
});
/**
 * Get season expenses
 * GET /api/v1/seasons/:id/expenses
 */
const getSeasonExpenses = asyncHandler(async (req, res) => {
  const season = await Season.findOne({
    _id: req.params.id,
    userId: req.user._id
  });
  if (!season) {
    throw notFoundError('Sezon bulunamadı');
  }
  const { page = 1, limit = 20, sortBy = 'date', sortOrder = 'desc' } = req.query;
  // Build query
  const query = {
    seasonId: season._id,
    status: 'active'
  };
  // Pagination
  const pageNum = parseInt(page);
  const limitNum = Math.min(parseInt(limit), 100);
  const skip = (pageNum - 1) * limitNum;
  // Sort options
  const sortOptions = {};
  sortOptions[sortBy] = sortOrder === 'asc' ? 1 : -1;
  // Execute query
  const [expenses, total] = await Promise.all([
    Expense.find(query)
      .populate('categoryId', 'name emoji color')
      .sort(sortOptions)
      .skip(skip)
      .limit(limitNum)
      .lean(),
    Expense.countDocuments(query)
  ]);
  const response = ResponseHelpers.paginated(expenses, pageNum, limitNum, total);
  res.status(200).json(response);
});
/**
 * Delete season
 * DELETE /api/v1/seasons/:id
 */
const deleteSeason = asyncHandler(async (req, res) => {
  const season = await Season.findOne({
    _id: req.params.id,
    userId: req.user._id
  });
  if (!season) {
    throw notFoundError('Sezon bulunamadı');
  }
  // Check if season has expenses
  const expenseCount = await Expense.countDocuments({
    seasonId: season._id
  });
  if (expenseCount > 0) {
    throw validationError(`Bu sezona ait ${expenseCount} gider kaydı bulunmaktadır. Önce giderleri silin veya başka bir sezona taşıyın.`);
  }
  // Check if this is the only season for the user
  const userSeasonCount = await Season.countDocuments({
    userId: req.user._id
  });
  if (userSeasonCount === 1) {
    throw validationError('En az bir sezonunuz olmalıdır. Bu sezon silinemez.');
  }
  // If this is the active season, activate another season
  if (season.isActive) {
    const anotherSeason = await Season.findOne({
      userId: req.user._id,
      _id: { $ne: season._id }
    });
    if (anotherSeason) {
      anotherSeason.isActive = true;
      await anotherSeason.save();
    }
  }
  await season.deleteOne();
  logger.info(`Season deleted: ${season._id} by user ${req.user._id}`);
  res.status(200).json(
    ResponseHelpers.success(null, 'Sezon başarıyla silindi')
  );
});
/**
 * Compare multiple seasons
 * GET /api/v1/seasons/compare?seasons=id1,id2,id3
 */
const compareSeasons = asyncHandler(async (req, res) => {
  const { seasons } = req.query;
  if (!seasons) {
    throw validationError('Karşılaştırılacak sezonlar belirtilmelidir');
  }
  const seasonIds = seasons.split(',').map(id => id.trim());
  if (seasonIds.length < 2) {
    throw validationError('En az 2 sezon karşılaştırılmalıdır');
  }
  if (seasonIds.length > 5) {
    throw validationError('En fazla 5 sezon karşılaştırılabilir');
  }
  // Validate season IDs and ownership
  const mongoose = require('mongoose');
  const validSeasonIds = seasonIds.filter(id => mongoose.Types.ObjectId.isValid(id));
  if (validSeasonIds.length !== seasonIds.length) {
    throw validationError('Geçersiz sezon ID\'si bulunmaktadır');
  }
  // Get seasons and verify ownership
  const userSeasons = await Season.find({
    _id: { $in: validSeasonIds },
    userId: req.user._id
  });
  if (userSeasons.length !== validSeasonIds.length) {
    throw validationError('Bazı sezonlar bulunamadı veya size ait değil');
  }
  // Get comparison data using aggregation
  const comparisonData = await Season.aggregate([
    {
      $match: {
        _id: { $in: userSeasons.map(s => s._id) },
        userId: req.user._id
      }
    },
    {
      $lookup: {
        from: 'expenses',
        localField: '_id',
        foreignField: 'seasonId',
        as: 'expenses'
      }
    },
    {
      $addFields: {
        totalExpenses: { $size: '$expenses' },
        totalAmount: { $sum: '$expenses.amount' },
        avgAmount: { $avg: '$expenses.amount' }
      }
    },
    {
      $lookup: {
        from: 'expenses',
        let: { seasonId: '$_id' },
        pipeline: [
          {
            $match: {
              $expr: { $eq: ['$seasonId', '$$seasonId'] }
            }
          },
          {
            $lookup: {
              from: 'categories',
              localField: 'categoryId',
              foreignField: '_id',
              as: 'category'
            }
          },
          {
            $unwind: '$category'
          },
          {
            $group: {
              _id: '$categoryId',
              categoryName: { $first: '$category.name' },
              totalAmount: { $sum: '$amount' },
              expenseCount: { $sum: 1 }
            }
          },
          {
            $sort: { totalAmount: -1 }
          },
          {
            $limit: 5
          }
        ],
        as: 'topCategories'
      }
    },
    {
      $project: {
        name: 1,
        description: 1,
        startDate: 1,
        endDate: 1,
        isActive: 1,
        color: 1,
        emoji: 1,
        totalExpenses: 1,
        totalAmount: 1,
        avgAmount: 1,
        topCategories: 1,
        formattedTotalAmount: {
          $concat: [
            { $toString: '$totalAmount' },
            ' TL'
          ]
        }
      }
    },
    {
      $sort: { totalAmount: -1 }
    }
  ]);
  // Calculate comparison metrics
  const totalAmounts = comparisonData.map(s => s.totalAmount || 0);
  const maxAmount = Math.max(...totalAmounts);
  const minAmount = Math.min(...totalAmounts);
  const avgAmount = totalAmounts.reduce((sum, amount) => sum + amount, 0) / totalAmounts.length;
  const seasonsWithMetrics = comparisonData.map(season => ({
    ...season,
    metrics: {
      isHighest: season.totalAmount === maxAmount,
      isLowest: season.totalAmount === minAmount,
      percentageOfMax: maxAmount > 0 ? Math.round((season.totalAmount / maxAmount) * 100) : 0,
      differenceFromAvg: season.totalAmount - avgAmount,
      percentageDifferenceFromAvg: avgAmount > 0 ? Math.round(((season.totalAmount - avgAmount) / avgAmount) * 100) : 0
    }
  }));
  const responseData = {
    seasons: seasonsWithMetrics,
    summary: {
      totalSeasons: comparisonData.length,
      maxAmount,
      minAmount,
      avgAmount: Math.round(avgAmount * 100) / 100,
      totalCombinedAmount: totalAmounts.reduce((sum, amount) => sum + amount, 0),
      formattedMaxAmount: TurkishHelpers.formatCurrency(maxAmount),
      formattedMinAmount: TurkishHelpers.formatCurrency(minAmount),
      formattedAvgAmount: TurkishHelpers.formatCurrency(avgAmount)
    }
  };
  res.status(200).json(
    ResponseHelpers.success(responseData, 'Sezon karşılaştırması getirildi')
  );
});
module.exports = {
  getSeasons,
  createSeason,
  getActiveSeason,
  getSeasonById,
  updateSeason,
  activateSeason,
  getSeasonSummary,
  getSeasonExpenses,
  compareSeasons,
  deleteSeason
};
