/**
 * Sync Controller for Çiftçi Not Defterim
 * Handles offline sync operations and conflict resolution
 */

const Expense = require('../models/Expense');
const Category = require('../models/Category');
const User = require('../models/User');
const { asyncHandler, validationError } = require('../middleware/errorHandler');
const { ResponseHelpers, DateHelpers } = require('../utils/helpers');
const { SYNC_OPERATIONS, SYNC_TABLES } = require('../utils/constants');
const logger = require('../utils/logger');

/**
 * Push local changes to server
 * POST /api/v1/sync/push
 */
const pushChanges = asyncHandler(async (req, res) => {
  const { changes, lastSyncTime } = req.body;
  const userId = req.user._id;

  if (!Array.isArray(changes) || changes.length === 0) {
    return res.status(400).json(
      ResponseHelpers.error('INVALID_CHANGES', 'Değ<PERSON>şiklik listesi geçersiz')
    );
  }

  const results = {
    processed: 0,
    conflicts: [],
    errors: [],
    serverIds: {} // Map local IDs to server IDs
  };

  // Process changes in transaction
  const session = await require('mongoose').startSession();
  
  try {
    await session.withTransaction(async () => {
      for (const change of changes) {
        try {
          const result = await processChange(change, userId, session);
          
          if (result.conflict) {
            results.conflicts.push({
              localId: change.localId,
              conflict: result.conflict,
              serverData: result.serverData
            });
          } else if (result.error) {
            results.errors.push({
              localId: change.localId,
              error: result.error
            });
          } else {
            results.processed++;
            if (result.serverId) {
              results.serverIds[change.localId] = result.serverId;
            }
          }
        } catch (error) {
          logger.error('Error processing sync change:', error);
          results.errors.push({
            localId: change.localId,
            error: error.message
          });
        }
      }
    });

    // Update user's last sync time
    req.user.lastSyncAt = new Date();
    await req.user.save();

    logger.logSync(userId, 'push', changes.length, true);

    res.status(200).json(
      ResponseHelpers.success(results, 'Değişiklikler senkronize edildi')
    );

  } catch (error) {
    logger.logSync(userId, 'push', changes.length, false, error);
    throw error;
  } finally {
    await session.endSession();
  }
});

/**
 * Pull server changes to local
 * GET /api/v1/sync/pull
 */
const pullChanges = asyncHandler(async (req, res) => {
  const { since } = req.query;
  const userId = req.user._id;

  // Parse since timestamp
  let sinceDate;
  if (since) {
    sinceDate = new Date(since);
    if (isNaN(sinceDate.getTime())) {
      throw validationError('since', 'Geçersiz tarih formatı');
    }
  } else {
    // If no since date, get last sync time from user
    sinceDate = req.user.lastSyncAt || new Date(0);
  }

  const changes = {
    expenses: [],
    categories: [],
    lastSyncTime: new Date().toISOString()
  };

  try {
    // Get expenses modified since last sync
    const expenses = await Expense.find({
      userId,
      $or: [
        { updatedAt: { $gt: sinceDate } },
        { createdAt: { $gt: sinceDate } }
      ]
    }).populate('categoryId', 'name emoji color icon').lean();

    changes.expenses = expenses.map(expense => ({
      id: expense._id,
      localId: expense.localId,
      operation: expense.status === 'deleted' ? SYNC_OPERATIONS.DELETE : 
                 expense.createdAt > sinceDate ? SYNC_OPERATIONS.CREATE : SYNC_OPERATIONS.UPDATE,
      data: expense.status === 'deleted' ? { id: expense._id } : expense,
      timestamp: expense.updatedAt.getTime(),
      syncVersion: expense.syncVersion
    }));

    // Get categories modified since last sync (only user's custom categories)
    const categories = await Category.find({
      $or: [
        { isDefault: true, updatedAt: { $gt: sinceDate } },
        { userId, updatedAt: { $gt: sinceDate } }
      ]
    }).lean();

    changes.categories = categories.map(category => ({
      id: category._id,
      operation: !category.isActive ? SYNC_OPERATIONS.DELETE :
                 category.createdAt > sinceDate ? SYNC_OPERATIONS.CREATE : SYNC_OPERATIONS.UPDATE,
      data: !category.isActive ? { id: category._id } : category,
      timestamp: category.updatedAt.getTime(),
      syncVersion: category.syncVersion
    }));

    // Update user's last sync time
    req.user.lastSyncAt = new Date();
    await req.user.save();

    logger.logSync(userId, 'pull', changes.expenses.length + changes.categories.length, true);

    res.status(200).json(
      ResponseHelpers.success(changes, 'Değişiklikler alındı')
    );

  } catch (error) {
    logger.logSync(userId, 'pull', 0, false, error);
    throw error;
  }
});

/**
 * Get sync status
 * GET /api/v1/sync/status
 */
const getSyncStatus = asyncHandler(async (req, res) => {
  const userId = req.user._id;

  // Count pending sync items
  const pendingExpenses = await Expense.countDocuments({
    userId,
    syncStatus: 'pending'
  });

  const conflictExpenses = await Expense.countDocuments({
    userId,
    syncStatus: 'conflict'
  });

  const status = {
    lastSyncAt: req.user.lastSyncAt,
    pendingChanges: pendingExpenses,
    conflicts: conflictExpenses,
    isOnline: true, // This would be determined by client
    syncVersion: req.user.syncVersion || 1
  };

  res.status(200).json(
    ResponseHelpers.success(status, 'Senkronizasyon durumu getirildi')
  );
});

/**
 * Resolve sync conflict
 * POST /api/v1/sync/resolve-conflict
 */
const resolveConflict = asyncHandler(async (req, res) => {
  const { itemId, resolution, data } = req.body;
  const userId = req.user._id;

  if (!itemId || !resolution) {
    throw validationError('resolution', 'Çakışma çözümü gerekli');
  }

  if (!['use_server', 'use_local', 'merge'].includes(resolution)) {
    throw validationError('resolution', 'Geçersiz çözüm türü');
  }

  // Find the conflicted item
  const expense = await Expense.findOne({
    _id: itemId,
    userId,
    syncStatus: 'conflict'
  });

  if (!expense) {
    return res.status(404).json(
      ResponseHelpers.error('CONFLICT_NOT_FOUND', 'Çakışma bulunamadı')
    );
  }

  let resolvedData;

  switch (resolution) {
    case 'use_server':
      // Keep server version, mark as synced
      expense.syncStatus = 'synced';
      break;

    case 'use_local':
      // Use local version, mark for sync
      expense.syncStatus = 'pending';
      expense.syncVersion += 1;
      break;

    case 'merge':
      // Merge data provided by client
      if (!data) {
        throw validationError('data', 'Birleştirme verisi gerekli');
      }
      
      // Update with merged data
      Object.assign(expense, data);
      expense.syncStatus = 'pending';
      expense.syncVersion += 1;
      break;
  }

  await expense.save();

  logger.info('Sync conflict resolved:', {
    userId,
    itemId,
    resolution
  });

  res.status(200).json(
    ResponseHelpers.success({
      itemId,
      resolution,
      newStatus: expense.syncStatus
    }, 'Çakışma çözüldü')
  );
});

/**
 * Force full sync
 * POST /api/v1/sync/full
 */
const fullSync = asyncHandler(async (req, res) => {
  const userId = req.user._id;

  try {
    // Reset all sync statuses
    await Expense.updateMany(
      { userId },
      { 
        syncStatus: 'pending',
        $inc: { syncVersion: 1 }
      }
    );

    await Category.updateMany(
      { userId },
      { 
        $inc: { syncVersion: 1 }
      }
    );

    // Reset user's last sync time
    req.user.lastSyncAt = new Date(0);
    req.user.syncVersion = (req.user.syncVersion || 1) + 1;
    await req.user.save();

    logger.info('Full sync initiated:', { userId });

    res.status(200).json(
      ResponseHelpers.success({
        message: 'Tam senkronizasyon başlatıldı',
        syncVersion: req.user.syncVersion
      }, 'Tam senkronizasyon hazır')
    );

  } catch (error) {
    logger.error('Full sync error:', error);
    throw error;
  }
});

/**
 * Process individual sync change
 */
async function processChange(change, userId, session) {
  const { operation, table, localId, data, timestamp } = change;

  if (!SYNC_OPERATIONS[operation.toUpperCase()]) {
    return { error: 'Geçersiz işlem türü' };
  }

  if (!SYNC_TABLES[table.toUpperCase()]) {
    return { error: 'Geçersiz tablo adı' };
  }

  const Model = table === 'expenses' ? Expense : Category;

  try {
    switch (operation) {
      case SYNC_OPERATIONS.CREATE:
        return await handleCreate(Model, data, userId, localId, session);
      
      case SYNC_OPERATIONS.UPDATE:
        return await handleUpdate(Model, data, userId, timestamp, session);
      
      case SYNC_OPERATIONS.DELETE:
        return await handleDelete(Model, data, userId, session);
      
      default:
        return { error: 'Desteklenmeyen işlem' };
    }
  } catch (error) {
    return { error: error.message };
  }
}

/**
 * Handle create operation
 */
async function handleCreate(Model, data, userId, localId, session) {
  // Remove local ID and set user ID
  const createData = { ...data };
  delete createData._id;
  delete createData.id;
  createData.userId = userId;
  createData.localId = localId;
  createData.syncStatus = 'synced';

  const item = await Model.create([createData], { session });
  
  return { 
    serverId: item[0]._id,
    processed: true 
  };
}

/**
 * Handle update operation
 */
async function handleUpdate(Model, data, userId, timestamp, session) {
  const existingItem = await Model.findOne({
    _id: data._id || data.id,
    userId
  }).session(session);

  if (!existingItem) {
    return { error: 'Güncellenecek öğe bulunamadı' };
  }

  // Check for conflicts
  if (existingItem.updatedAt.getTime() > timestamp) {
    return {
      conflict: 'server_newer',
      serverData: existingItem.toObject()
    };
  }

  // Update the item
  const updateData = { ...data };
  delete updateData._id;
  delete updateData.id;
  updateData.syncStatus = 'synced';
  updateData.syncVersion = (existingItem.syncVersion || 1) + 1;

  await Model.findByIdAndUpdate(
    existingItem._id,
    updateData,
    { session, runValidators: true }
  );

  return { processed: true };
}

/**
 * Handle delete operation
 */
async function handleDelete(Model, data, userId, session) {
  const item = await Model.findOne({
    _id: data._id || data.id,
    userId
  }).session(session);

  if (!item) {
    return { processed: true }; // Already deleted
  }

  // Soft delete
  item.status = 'deleted';
  item.syncStatus = 'synced';
  await item.save({ session });

  return { processed: true };
}

module.exports = {
  pushChanges,
  pullChanges,
  getSyncStatus,
  resolveConflict,
  fullSync
};
