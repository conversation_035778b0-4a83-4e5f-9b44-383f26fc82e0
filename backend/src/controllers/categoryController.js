/**
 * Category Controller for Çiftçi Not Defterim
 * Handles default and custom category management
 */

const Category = require('../models/Category');
const { asyncHandler, notFoundError, validationError } = require('../middleware/errorHandler');
const { ResponseHelpers } = require('../utils/helpers');
const logger = require('../utils/logger');

/**
 * Get user categories (default + custom)
 * GET /api/v1/categories
 */
const getCategories = asyncHandler(async (req, res) => {
  const { includeStats = false, season } = req.query;

  let categories;

  if (season) {
    // Get categories relevant to specific season
    categories = await Category.getSeasonalCategories(season);
  } else {
    // Get all user categories (default + custom)
    categories = await Category.getUserCategories(req.user._id);
  }

  // Add usage statistics if requested
  if (includeStats === 'true') {
    const Expense = require('../models/Expense');
    
    for (let category of categories) {
      const stats = await Expense.aggregate([
        {
          $match: {
            userId: req.user._id,
            categoryId: category._id,
            status: 'active'
          }
        },
        {
          $group: {
            _id: null,
            totalAmount: { $sum: '$amount' },
            expenseCount: { $sum: 1 },
            lastUsed: { $max: '$date' }
          }
        }
      ]);

      category = category.toObject();
      category.userStats = stats[0] || {
        totalAmount: 0,
        expenseCount: 0,
        lastUsed: null
      };
    }
  }

  logger.logDatabase('category_list', 'categories', { userId: req.user._id }, categories);

  res.status(200).json(
    ResponseHelpers.success(categories, 'Kategoriler getirildi')
  );
});

/**
 * Get single category by ID
 * GET /api/v1/categories/:id
 */
const getCategoryById = asyncHandler(async (req, res) => {
  const category = await Category.findOne({
    _id: req.params.id,
    $or: [
      { isDefault: true },
      { userId: req.user._id }
    ],
    isActive: true
  });

  if (!category) {
    throw notFoundError('Kategori');
  }

  // Get usage statistics for this category
  const Expense = require('../models/Expense');
  const stats = await Expense.aggregate([
    {
      $match: {
        userId: req.user._id,
        categoryId: category._id,
        status: 'active'
      }
    },
    {
      $group: {
        _id: null,
        totalAmount: { $sum: '$amount' },
        expenseCount: { $sum: 1 },
        avgAmount: { $avg: '$amount' },
        lastUsed: { $max: '$date' },
        firstUsed: { $min: '$date' }
      }
    }
  ]);

  const categoryData = category.toObject();
  categoryData.userStats = stats[0] || {
    totalAmount: 0,
    expenseCount: 0,
    avgAmount: 0,
    lastUsed: null,
    firstUsed: null
  };

  res.status(200).json(
    ResponseHelpers.success(categoryData, 'Kategori detayları getirildi')
  );
});

/**
 * Create new custom category
 * POST /api/v1/categories
 */
const createCategory = asyncHandler(async (req, res) => {
  const { name, emoji, color, icon, description, seasonalRelevance, cropTypes } = req.body;

  // Check if user already has a category with this name
  const existingCategory = await Category.findOne({
    userId: req.user._id,
    name: name.trim(),
    isActive: true
  });

  if (existingCategory) {
    throw validationError('name', 'Bu isimde bir kategori zaten mevcut');
  }

  // Create category
  const category = await Category.create({
    name: name.trim(),
    emoji,
    color,
    icon: icon?.trim(),
    description: description?.trim(),
    userId: req.user._id,
    isDefault: false,
    seasonalRelevance: seasonalRelevance || [],
    cropTypes: cropTypes || []
  });

  // Update user stats
  req.user.stats.categoriesCreated += 1;
  await req.user.save();

  logger.info('Custom category created:', {
    userId: req.user._id,
    categoryId: category._id,
    name: category.name
  });

  res.status(201).json(
    ResponseHelpers.success(category.toPublicJSON(), 'Kategori oluşturuldu')
  );
});

/**
 * Update custom category
 * PUT /api/v1/categories/:id
 */
const updateCategory = asyncHandler(async (req, res) => {
  const { name, emoji, color, icon, description, seasonalRelevance, cropTypes } = req.body;

  // Find category (only user's custom categories can be updated)
  const category = await Category.findOne({
    _id: req.params.id,
    userId: req.user._id,
    isDefault: false,
    isActive: true
  });

  if (!category) {
    throw notFoundError('Kategori bulunamadı veya düzenleme yetkiniz yok');
  }

  // Check for name conflicts if name is being changed
  if (name && name.trim() !== category.name) {
    const existingCategory = await Category.findOne({
      userId: req.user._id,
      name: name.trim(),
      isActive: true,
      _id: { $ne: category._id }
    });

    if (existingCategory) {
      throw validationError('name', 'Bu isimde bir kategori zaten mevcut');
    }
  }

  // Update fields
  const updateData = {};
  if (name) updateData.name = name.trim();
  if (emoji) updateData.emoji = emoji;
  if (color) updateData.color = color;
  if (icon !== undefined) updateData.icon = icon?.trim();
  if (description !== undefined) updateData.description = description?.trim();
  if (seasonalRelevance) updateData.seasonalRelevance = seasonalRelevance;
  if (cropTypes) updateData.cropTypes = cropTypes;

  const updatedCategory = await Category.findByIdAndUpdate(
    req.params.id,
    updateData,
    { new: true, runValidators: true }
  );

  logger.info('Category updated:', {
    userId: req.user._id,
    categoryId: category._id,
    updatedFields: Object.keys(updateData)
  });

  res.status(200).json(
    ResponseHelpers.success(updatedCategory.toPublicJSON(), 'Kategori güncellendi')
  );
});

/**
 * Delete custom category
 * DELETE /api/v1/categories/:id
 */
const deleteCategory = asyncHandler(async (req, res) => {
  // Find category (only user's custom categories can be deleted)
  const category = await Category.findOne({
    _id: req.params.id,
    userId: req.user._id,
    isDefault: false,
    isActive: true
  });

  if (!category) {
    throw notFoundError('Kategori bulunamadı veya silme yetkiniz yok');
  }

  // Check if category is being used
  const Expense = require('../models/Expense');
  const expenseCount = await Expense.countDocuments({
    categoryId: category._id,
    status: 'active'
  });

  if (expenseCount > 0) {
    return res.status(400).json(
      ResponseHelpers.error(
        'CATEGORY_IN_USE',
        `Bu kategori ${expenseCount} giderde kullanılıyor. Önce bu giderleri başka kategoriye taşıyın.`,
        { expenseCount }
      )
    );
  }

  // Soft delete
  category.isActive = false;
  await category.save();

  logger.info('Category deleted:', {
    userId: req.user._id,
    categoryId: category._id,
    name: category.name
  });

  res.status(200).json(
    ResponseHelpers.success(null, 'Kategori silindi')
  );
});

/**
 * Get popular categories
 * GET /api/v1/categories/popular
 */
const getPopularCategories = asyncHandler(async (req, res) => {
  const { limit = 10 } = req.query;

  const popularCategories = await Category.getPopularCategories(parseInt(limit));

  res.status(200).json(
    ResponseHelpers.success(popularCategories, 'Popüler kategoriler getirildi')
  );
});

/**
 * Search categories
 * GET /api/v1/categories/search
 */
const searchCategories = asyncHandler(async (req, res) => {
  const { q: query } = req.query;

  if (!query || query.trim().length < 2) {
    return res.status(400).json(
      ResponseHelpers.error('INVALID_SEARCH_QUERY', 'Arama terimi en az 2 karakter olmalıdır')
    );
  }

  const categories = await Category.searchCategories(query.trim(), req.user._id);

  res.status(200).json(
    ResponseHelpers.success(categories, 'Arama sonuçları getirildi')
  );
});

/**
 * Get category statistics
 * GET /api/v1/categories/stats
 */
const getCategoryStats = asyncHandler(async (req, res) => {
  const stats = await Category.getCategoryStats(req.user._id);

  res.status(200).json(
    ResponseHelpers.success(stats[0] || {
      totalCategories: 0,
      defaultCategories: 0,
      customCategories: 0,
      totalUsage: 0,
      totalAmount: 0,
      avgUsage: 0
    }, 'Kategori istatistikleri getirildi')
  );
});

/**
 * Reset to default categories
 * POST /api/v1/categories/reset
 */
const resetToDefaults = asyncHandler(async (req, res) => {
  // Deactivate all user's custom categories
  await Category.updateMany(
    { userId: req.user._id, isDefault: false },
    { isActive: false }
  );

  // Ensure default categories exist
  await Category.ensureDefaultCategories();

  // Get fresh default categories
  const defaultCategories = await Category.getDefaultCategories();

  logger.info('Categories reset to defaults:', {
    userId: req.user._id
  });

  res.status(200).json(
    ResponseHelpers.success(defaultCategories, 'Kategoriler varsayılanlara sıfırlandı')
  );
});

/**
 * Duplicate category
 * POST /api/v1/categories/:id/duplicate
 */
const duplicateCategory = asyncHandler(async (req, res) => {
  const { name } = req.body;

  // Find source category
  const sourceCategory = await Category.findOne({
    _id: req.params.id,
    $or: [
      { isDefault: true },
      { userId: req.user._id }
    ],
    isActive: true
  });

  if (!sourceCategory) {
    throw notFoundError('Kaynak kategori');
  }

  // Check if name is provided and unique
  const newName = name?.trim() || `${sourceCategory.name} (Kopya)`;
  const existingCategory = await Category.findOne({
    userId: req.user._id,
    name: newName,
    isActive: true
  });

  if (existingCategory) {
    throw validationError('name', 'Bu isimde bir kategori zaten mevcut');
  }

  // Create duplicate
  const duplicateCategory = await Category.create({
    name: newName,
    emoji: sourceCategory.emoji,
    color: sourceCategory.color,
    icon: sourceCategory.icon,
    description: sourceCategory.description,
    userId: req.user._id,
    isDefault: false,
    seasonalRelevance: sourceCategory.seasonalRelevance,
    cropTypes: sourceCategory.cropTypes
  });

  logger.info('Category duplicated:', {
    userId: req.user._id,
    sourceId: sourceCategory._id,
    duplicateId: duplicateCategory._id
  });

  res.status(201).json(
    ResponseHelpers.success(duplicateCategory.toPublicJSON(), 'Kategori kopyalandı')
  );
});

module.exports = {
  getCategories,
  getCategoryById,
  createCategory,
  updateCategory,
  deleteCategory,
  getPopularCategories,
  searchCategories,
  getCategoryStats,
  resetToDefaults,
  duplicateCategory
};
