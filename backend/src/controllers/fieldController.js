/**
 * Field Controller for Çiftçi Not Defterim
 * Handles field management operations for detailed tracking mode
 */

const Field = require('../models/Field');
const Expense = require('../models/Expense');
const { validateFieldData } = require('../utils/defaultFields');
const Logger = require('../utils/logger');

/**
 * Get all fields for authenticated user
 * GET /api/v1/fields
 */
const getFields = async (req, res) => {
  try {
    const userId = req.user.uid;
    const { includeInactive = false } = req.query;
    
    const fields = await Field.getUserFields(userId, includeInactive === 'true');
    
    res.json({
      success: true,
      data: fields.map(field => field.toPublicJSON()),
      count: fields.length
    });
    
  } catch (error) {
    Logger.error('Error fetching fields:', error);
    res.status(500).json({
      success: false,
      message: 'Tarlalar getirilirken hata olu<PERSON>tu',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get single field by ID
 * GET /api/v1/fields/:id
 */
const getField = async (req, res) => {
  try {
    const userId = req.user.uid;
    const { id } = req.params;
    
    const field = await Field.findOne({
      _id: id,
      userId,
      isActive: true
    }).populate('expenseCount');
    
    if (!field) {
      return res.status(404).json({
        success: false,
        message: 'Tarla bulunamadı'
      });
    }
    
    res.json({
      success: true,
      data: field.toPublicJSON()
    });
    
  } catch (error) {
    Logger.error('Error fetching field:', error);
    res.status(500).json({
      success: false,
      message: 'Tarla getirilirken hata oluştu',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Create new field
 * POST /api/v1/fields
 */
const createField = async (req, res) => {
  try {
    const userId = req.user._id;
    const fieldData = { ...req.body, userId };
    
    // Validate field data
    const validation = validateFieldData(fieldData);
    if (!validation.isValid) {
      return res.status(400).json({
        success: false,
        message: 'Geçersiz tarla bilgileri',
        errors: validation.errors
      });
    }
    
    // Check if user already has a field with this name
    const existingField = await Field.findOne({
      userId,
      name: fieldData.name,
      isActive: true
    });
    
    if (existingField) {
      return res.status(400).json({
        success: false,
        message: 'Bu isimde bir tarla zaten mevcut'
      });
    }
    
    const field = new Field(fieldData);
    await field.save();
    
    Logger.info(`Field created: ${field.name} for user ${userId}`);
    
    res.status(201).json({
      success: true,
      message: 'Tarla başarıyla oluşturuldu',
      data: field.toPublicJSON()
    });
    
  } catch (error) {
    Logger.error('Error creating field:', error);
    res.status(500).json({
      success: false,
      message: 'Tarla oluşturulurken hata oluştu',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Update field
 * PUT /api/v1/fields/:id
 */
const updateField = async (req, res) => {
  try {
    const userId = req.user.uid;
    const { id } = req.params;
    const updateData = req.body;
    
    // Remove userId from update data to prevent tampering
    delete updateData.userId;
    
    // Validate field data
    const validation = validateFieldData(updateData);
    if (!validation.isValid) {
      return res.status(400).json({
        success: false,
        message: 'Geçersiz tarla bilgileri',
        errors: validation.errors
      });
    }
    
    const field = await Field.findOne({
      _id: id,
      userId,
      isActive: true
    });
    
    if (!field) {
      return res.status(404).json({
        success: false,
        message: 'Tarla bulunamadı'
      });
    }
    
    // Check if name is being changed and if it conflicts
    if (updateData.name && updateData.name !== field.name) {
      const existingField = await Field.findOne({
        userId,
        name: updateData.name,
        isActive: true,
        _id: { $ne: id }
      });
      
      if (existingField) {
        return res.status(400).json({
          success: false,
          message: 'Bu isimde bir tarla zaten mevcut'
        });
      }
    }
    
    // Update field
    Object.assign(field, updateData);
    await field.save();
    
    Logger.info(`Field updated: ${field.name} for user ${userId}`);
    
    res.json({
      success: true,
      message: 'Tarla başarıyla güncellendi',
      data: field.toPublicJSON()
    });
    
  } catch (error) {
    Logger.error('Error updating field:', error);
    res.status(500).json({
      success: false,
      message: 'Tarla güncellenirken hata oluştu',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Delete field (soft delete)
 * DELETE /api/v1/fields/:id
 */
const deleteField = async (req, res) => {
  try {
    const userId = req.user.uid;
    const { id } = req.params;
    
    const field = await Field.findOne({
      _id: id,
      userId,
      isActive: true
    });
    
    if (!field) {
      return res.status(404).json({
        success: false,
        message: 'Tarla bulunamadı'
      });
    }
    
    // Check if field has expenses
    const expenseCount = await Expense.countDocuments({
      fieldId: id,
      status: 'active'
    });
    
    if (expenseCount > 0) {
      return res.status(400).json({
        success: false,
        message: `Bu tarla silinemez. ${expenseCount} adet gider kaydı bulunmaktadır.`,
        expenseCount
      });
    }
    
    // Soft delete
    field.isActive = false;
    await field.save();
    
    Logger.info(`Field deleted: ${field.name} for user ${userId}`);
    
    res.json({
      success: true,
      message: 'Tarla başarıyla silindi'
    });
    
  } catch (error) {
    Logger.error('Error deleting field:', error);
    res.status(500).json({
      success: false,
      message: 'Tarla silinirken hata oluştu',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get field statistics
 * GET /api/v1/fields/:id/stats
 */
const getFieldStats = async (req, res) => {
  try {
    const userId = req.user.uid;
    const { id } = req.params;
    
    const field = await Field.findOne({
      _id: id,
      userId,
      isActive: true
    });
    
    if (!field) {
      return res.status(404).json({
        success: false,
        message: 'Tarla bulunamadı'
      });
    }
    
    // Update field statistics
    await field.updateStats();
    
    res.json({
      success: true,
      data: {
        fieldId: field._id,
        fieldName: field.name,
        stats: field.stats,
        formattedSize: field.formattedSize
      }
    });
    
  } catch (error) {
    Logger.error('Error fetching field stats:', error);
    res.status(500).json({
      success: false,
      message: 'Tarla istatistikleri getirilirken hata oluştu',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Set field as default
 * POST /api/v1/fields/:id/set-default
 */
const setDefaultField = async (req, res) => {
  try {
    const userId = req.user.uid;
    const { id } = req.params;
    
    const field = await Field.findOne({
      _id: id,
      userId,
      isActive: true
    });
    
    if (!field) {
      return res.status(404).json({
        success: false,
        message: 'Tarla bulunamadı'
      });
    }
    
    // Set as default (pre-save middleware will handle unsetting others)
    field.isDefault = true;
    await field.save();
    
    Logger.info(`Field set as default: ${field.name} for user ${userId}`);
    
    res.json({
      success: true,
      message: 'Varsayılan tarla başarıyla ayarlandı',
      data: field.toPublicJSON()
    });
    
  } catch (error) {
    Logger.error('Error setting default field:', error);
    res.status(500).json({
      success: false,
      message: 'Varsayılan tarla ayarlanırken hata oluştu',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  getFields,
  getField,
  createField,
  updateField,
  deleteField,
  getFieldStats,
  setDefaultField
};
