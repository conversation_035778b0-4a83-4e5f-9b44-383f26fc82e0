/**
 * Background Job Queue Service for Çiftçi Not Defterim
 * Handles large file processing and long-running tasks
 */

const Queue = require('bull');
const redis = require('redis');
const logger = require('../utils/logger');
const excelService = require('./excelService');
const ImportHistory = require('../models/ImportHistory');
const { importErrorHandler } = require('../middleware/importErrorHandler');

class JobQueueService {
  constructor() {
    this.redisConfig = {
      host: process.env.REDIS_HOST || 'localhost',
      port: process.env.REDIS_PORT || 6379,
      password: process.env.REDIS_PASSWORD || undefined,
      db: process.env.REDIS_DB || 0
    };

    this.queues = {};
    this.isInitialized = false;
  }

  /**
   * Initialize job queue service
   */
  async initialize() {
    try {
      // Create Redis client for health checks
      this.redisClient = redis.createClient(this.redisConfig);
      
      // Test Redis connection
      await this.redisClient.ping();
      logger.info('Redis connection established');

      // Initialize queues
      this.initializeQueues();
      
      // Setup job processors
      this.setupProcessors();
      
      // Setup event listeners
      this.setupEventListeners();

      this.isInitialized = true;
      logger.info('JobQueueService initialized successfully');

    } catch (error) {
      logger.error('JobQueueService initialization failed:', error);
      
      // Fallback to in-memory processing if Redis is not available
      logger.warn('Falling back to synchronous processing (Redis unavailable)');
      this.isInitialized = false;
    }
  }

  /**
   * Initialize job queues
   */
  initializeQueues() {
    // Excel import processing queue
    this.queues.excelImport = new Queue('excel import', {
      redis: this.redisConfig,
      defaultJobOptions: {
        removeOnComplete: 10, // Keep last 10 completed jobs
        removeOnFail: 50,     // Keep last 50 failed jobs
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000
        }
      }
    });

    // AI processing queue
    this.queues.aiProcessing = new Queue('ai processing', {
      redis: this.redisConfig,
      defaultJobOptions: {
        removeOnComplete: 5,
        removeOnFail: 20,
        attempts: 2,
        backoff: {
          type: 'exponential',
          delay: 5000
        }
      }
    });

    // Export generation queue
    this.queues.exportGeneration = new Queue('export generation', {
      redis: this.redisConfig,
      defaultJobOptions: {
        removeOnComplete: 5,
        removeOnFail: 10,
        attempts: 2,
        backoff: {
          type: 'exponential',
          delay: 3000
        }
      }
    });

    logger.info('Job queues initialized');
  }

  /**
   * Setup job processors
   */
  setupProcessors() {
    // Excel import processor
    this.queues.excelImport.process('processExcelFile', 3, async (job) => {
      return await this.processExcelImportJob(job);
    });

    // AI processing processor
    this.queues.aiProcessing.process('processAIText', 2, async (job) => {
      return await this.processAIJob(job);
    });

    // Export generation processor
    this.queues.exportGeneration.process('generateExport', 2, async (job) => {
      return await this.processExportJob(job);
    });

    logger.info('Job processors setup completed');
  }

  /**
   * Setup event listeners for monitoring
   */
  setupEventListeners() {
    Object.entries(this.queues).forEach(([queueName, queue]) => {
      queue.on('completed', (job, result) => {
        logger.info(`Job completed in ${queueName}:`, {
          jobId: job.id,
          duration: Date.now() - job.timestamp,
          result: result?.summary || 'completed'
        });
      });

      queue.on('failed', (job, err) => {
        logger.error(`Job failed in ${queueName}:`, {
          jobId: job.id,
          error: err.message,
          attempts: job.attemptsMade,
          data: job.data
        });
      });

      queue.on('stalled', (job) => {
        logger.warn(`Job stalled in ${queueName}:`, {
          jobId: job.id,
          data: job.data
        });
      });
    });
  }

  /**
   * Add Excel import job to queue
   */
  async addExcelImportJob(jobData) {
    try {
      if (!this.isInitialized) {
        // Fallback to synchronous processing
        return await this.processExcelImportSync(jobData);
      }

      const job = await this.queues.excelImport.add('processExcelFile', jobData, {
        priority: jobData.priority || 0,
        delay: jobData.delay || 0
      });

      logger.info('Excel import job added to queue:', {
        jobId: job.id,
        userId: jobData.userId,
        fileName: jobData.fileName
      });

      return {
        success: true,
        jobId: job.id,
        message: 'Dosya işleme kuyruğuna eklendi'
      };

    } catch (error) {
      logger.error('Failed to add Excel import job:', error);
      throw error;
    }
  }

  /**
   * Process Excel import job
   */
  async processExcelImportJob(job) {
    const { importHistoryId, filePath, userId, fileName } = job.data;
    
    try {
      // Update job progress
      await job.progress(10);

      // Find import history record
      const importHistory = await ImportHistory.findById(importHistoryId);
      if (!importHistory) {
        throw new Error('Import history record not found');
      }

      // Update status to processing
      await importHistory.updateProgress('processing');
      await job.progress(20);

      // Validate file
      const validation = await excelService.validateFile(filePath, job.data.fileSize);
      if (!validation.success) {
        throw new Error(validation.message);
      }
      await job.progress(30);

      // Read Excel file
      const readResult = await excelService.readExcelFile(filePath);
      if (!readResult.success) {
        throw new Error(readResult.message);
      }
      await job.progress(50);

      // Process data (this would integrate with your existing expense creation logic)
      const processedData = await this.processExcelData(readResult.data, userId);
      await job.progress(80);

      // Update import history with results
      await importHistory.markAsCompleted({
        totalRows: readResult.data.totalRows,
        validRows: processedData.validRows.length,
        invalidRows: processedData.invalidRows.length,
        recordsImported: processedData.validRows.length
      });

      await job.progress(100);

      return {
        success: true,
        summary: {
          totalRows: readResult.data.totalRows,
          validRows: processedData.validRows.length,
          invalidRows: processedData.invalidRows.length,
          recordsImported: processedData.validRows.length
        }
      };

    } catch (error) {
      // Update import history as failed
      const importHistory = await ImportHistory.findById(importHistoryId);
      if (importHistory) {
        await importHistory.markAsFailed(error);
      }

      throw error;
    }
  }

  /**
   * Process Excel data (placeholder - integrate with your expense creation logic)
   */
  async processExcelData(excelData, userId) {
    // This is a simplified version - you would integrate this with your existing
    // expense creation logic from ExpenseController
    
    const validRows = [];
    const invalidRows = [];

    // Simulate processing
    for (const row of excelData.rows) {
      // Add your validation and processing logic here
      // For now, we'll assume all rows are valid
      validRows.push({
        ...row,
        userId,
        processedAt: new Date()
      });
    }

    return { validRows, invalidRows };
  }

  /**
   * Synchronous fallback processing
   */
  async processExcelImportSync(jobData) {
    try {
      logger.info('Processing Excel import synchronously (Redis unavailable)');
      
      // This would call the same processing logic but synchronously
      const result = await this.processExcelImportJob({ data: jobData, progress: () => {} });
      
      return {
        success: true,
        jobId: `sync_${Date.now()}`,
        result,
        message: 'Dosya başarıyla işlendi (senkron)'
      };

    } catch (error) {
      logger.error('Synchronous Excel processing failed:', error);
      throw error;
    }
  }

  /**
   * Add AI processing job
   */
  async addAIProcessingJob(jobData) {
    try {
      if (!this.isInitialized) {
        throw new Error('AI processing requires Redis queue system');
      }

      const job = await this.queues.aiProcessing.add('processAIText', jobData, {
        priority: jobData.priority || 0
      });

      return {
        success: true,
        jobId: job.id,
        message: 'AI işleme kuyruğuna eklendi'
      };

    } catch (error) {
      logger.error('Failed to add AI processing job:', error);
      throw error;
    }
  }

  /**
   * Process AI job (placeholder for Task 3.1)
   */
  async processAIJob(job) {
    // This will be implemented in Task 3.1 (Gemini AI Integration)
    throw new Error('AI processing not yet implemented');
  }

  /**
   * Add export generation job
   */
  async addExportJob(jobData) {
    try {
      if (!this.isInitialized) {
        // Fallback to synchronous processing
        return await this.processExportSync(jobData);
      }

      const job = await this.queues.exportGeneration.add('generateExport', jobData);

      return {
        success: true,
        jobId: job.id,
        message: 'Export kuyruğuna eklendi'
      };

    } catch (error) {
      logger.error('Failed to add export job:', error);
      throw error;
    }
  }

  /**
   * Process export job
   */
  async processExportJob(job) {
    const { userId, filters, options } = job.data;
    
    try {
      await job.progress(10);

      // Fetch data based on filters (integrate with your existing data fetching logic)
      const data = await this.fetchExportData(userId, filters);
      await job.progress(40);

      // Generate Excel file
      const exportResult = await excelService.exportToExcel(data, options);
      if (!exportResult.success) {
        throw new Error(exportResult.message);
      }
      await job.progress(80);

      // Save file and get download link
      const fileName = `export_${userId}_${Date.now()}.xlsx`;
      const filePath = `uploads/exports/${fileName}`;
      
      const saveResult = await excelService.saveToFile(filePath);
      if (!saveResult.success) {
        throw new Error(saveResult.message);
      }

      await job.progress(100);

      return {
        success: true,
        fileName,
        filePath,
        recordCount: data.length
      };

    } catch (error) {
      throw error;
    }
  }

  /**
   * Fetch export data (placeholder)
   */
  async fetchExportData(userId, filters) {
    // This would integrate with your existing expense fetching logic
    // For now, return empty array
    return [];
  }

  /**
   * Synchronous export processing
   */
  async processExportSync(jobData) {
    try {
      logger.info('Processing export synchronously (Redis unavailable)');
      
      const result = await this.processExportJob({ data: jobData, progress: () => {} });
      
      return {
        success: true,
        jobId: `sync_${Date.now()}`,
        result,
        message: 'Export başarıyla oluşturuldu (senkron)'
      };

    } catch (error) {
      logger.error('Synchronous export processing failed:', error);
      throw error;
    }
  }

  /**
   * Get job status
   */
  async getJobStatus(queueName, jobId) {
    try {
      if (!this.isInitialized || !this.queues[queueName]) {
        return {
          success: false,
          error: 'Queue not available'
        };
      }

      const job = await this.queues[queueName].getJob(jobId);
      if (!job) {
        return {
          success: false,
          error: 'Job not found'
        };
      }

      return {
        success: true,
        status: await job.getState(),
        progress: job.progress(),
        data: job.data,
        result: job.returnvalue,
        error: job.failedReason
      };

    } catch (error) {
      logger.error('Failed to get job status:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get queue statistics
   */
  async getQueueStats() {
    try {
      if (!this.isInitialized) {
        return {
          success: false,
          error: 'Queue system not initialized'
        };
      }

      const stats = {};

      for (const [queueName, queue] of Object.entries(this.queues)) {
        const waiting = await queue.getWaiting();
        const active = await queue.getActive();
        const completed = await queue.getCompleted();
        const failed = await queue.getFailed();

        stats[queueName] = {
          waiting: waiting.length,
          active: active.length,
          completed: completed.length,
          failed: failed.length
        };
      }

      return {
        success: true,
        stats
      };

    } catch (error) {
      logger.error('Failed to get queue stats:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Cleanup old jobs
   */
  async cleanupJobs() {
    try {
      if (!this.isInitialized) return;

      for (const [queueName, queue] of Object.entries(this.queues)) {
        await queue.clean(24 * 60 * 60 * 1000, 'completed'); // Remove completed jobs older than 24 hours
        await queue.clean(7 * 24 * 60 * 60 * 1000, 'failed'); // Remove failed jobs older than 7 days
      }

      logger.info('Job cleanup completed');
    } catch (error) {
      logger.error('Job cleanup failed:', error);
    }
  }

  /**
   * Graceful shutdown
   */
  async shutdown() {
    try {
      if (this.isInitialized) {
        for (const [queueName, queue] of Object.entries(this.queues)) {
          await queue.close();
        }
        
        if (this.redisClient) {
          await this.redisClient.quit();
        }
      }

      logger.info('JobQueueService shutdown completed');
    } catch (error) {
      logger.error('JobQueueService shutdown error:', error);
    }
  }
}

// Create singleton instance
const jobQueueService = new JobQueueService();

module.exports = jobQueueService;
