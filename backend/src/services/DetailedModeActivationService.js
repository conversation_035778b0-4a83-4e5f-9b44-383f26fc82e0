/**
 * Detailed Mode Activation Service
 * Handles the activation of detailed tracking mode for users
 */

const User = require('../models/User');
const Field = require('../models/Field');
const Expense = require('../models/Expense');
const { createDefaultField } = require('../utils/defaultFields');
const { initializeDefaultCrops } = require('../utils/defaultCrops');
const Logger = require('../utils/logger');

class DetailedModeActivationService {
  /**
   * Activate detailed mode for a user
   * @param {string} userId - User ID (MongoDB ObjectId)
   * @param {Object} options - Activation options
   */
  static async activateDetailedMode(userId, options = {}) {
    const {
      defaultFieldName = 'Ana Tarla',
      migrateExistingExpenses = true,
      createDefaultCrops = true
    } = options;

    try {
      Logger.info(`Activating detailed mode for user ${userId}`);

      // 1. Validate user exists and is in simple mode
      const user = await User.findById(userId);
      if (!user) {
        throw new Error('<PERSON>llanıc<PERSON> bulunamadı');
      }

      if (user.preferences.trackingMode === 'detailed') {
        Logger.info(`User ${userId} is already in detailed mode`);
        return {
          success: true,
          message: 'Zaten detaylı moddasınız',
          defaultField: await Field.findOne({ userId, isDefault: true, isActive: true }),
          wasAlreadyDetailed: true
        };
      }

      const activationResult = {
        success: true,
        message: 'Detaylı mod başarıyla aktifleştirildi',
        defaultField: null,
        migratedExpenses: 0,
        cropsInitialized: false
      };

      // 2. Create default field
      Logger.info(`Creating default field for user ${userId}`);
      const defaultField = await this.createDefaultFieldForUser(userId, defaultFieldName);
      activationResult.defaultField = defaultField;

      // 3. Initialize default crops if requested
      if (createDefaultCrops) {
        Logger.info('Initializing default crops');
        const cropsResult = await initializeDefaultCrops();
        activationResult.cropsInitialized = cropsResult;
      }

      // 4. Migrate existing expenses if requested
      if (migrateExistingExpenses) {
        Logger.info(`Migrating existing expenses for user ${userId}`);
        const migrationResult = await this.migrateExistingExpenses(userId, defaultField._id);
        activationResult.migratedExpenses = migrationResult.migratedCount;
      }

      // 5. Update user preferences
      await User.updateOne(
        { _id: userId },
        {
          $set: {
            'preferences.trackingMode': 'detailed',
            'preferences.detailedModeActivatedAt': new Date()
          }
        }
      );

      Logger.info(`Successfully activated detailed mode for user ${userId}`);
      return activationResult;

    } catch (error) {
      Logger.error(`Failed to activate detailed mode for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Deactivate detailed mode for a user
   * @param {string} userId - User ID (MongoDB ObjectId)
   * @param {Object} options - Deactivation options
   */
  static async deactivateDetailedMode(userId, options = {}) {
    const {
      preserveFieldData = true,
      preserveFields = true
    } = options;

    try {
      Logger.info(`Deactivating detailed mode for user ${userId}`);

      const user = await User.findById(userId);
      if (!user) {
        throw new Error('Kullanıcı bulunamadı');
      }

      if (user.preferences.trackingMode === 'simple') {
        Logger.info(`User ${userId} is already in simple mode`);
        return {
          success: true,
          message: 'Zaten basit moddasınız',
          wasAlreadySimple: true
        };
      }

      const deactivationResult = {
        success: true,
        message: 'Basit moda başarıyla geçildi',
        simplifiedExpenses: 0
      };

      // 1. Update existing expenses
      const updateData = { trackingMode: 'simple' };
      if (!preserveFieldData) {
        updateData.fieldId = null;
        updateData.cropId = null;
      }

      const expenseUpdateResult = await Expense.updateMany(
        { userId, trackingMode: 'detailed', status: 'active' },
        { $set: updateData }
      );

      deactivationResult.simplifiedExpenses = expenseUpdateResult.modifiedCount;

      // 2. Optionally deactivate fields (soft delete)
      if (!preserveFields) {
        await Field.updateMany(
          { userId, isActive: true },
          { $set: { isActive: false } }
        );
      }

      // 3. Update user preferences
      await User.updateOne(
        { _id: userId },
        {
          $set: {
            'preferences.trackingMode': 'simple'
          }
        }
      );

      Logger.info(`Successfully deactivated detailed mode for user ${userId}`);
      return deactivationResult;

    } catch (error) {
      Logger.error(`Failed to deactivate detailed mode for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Create default field for user
   * @param {string} userId - User ID
   * @param {string} fieldName - Name for the default field
   */
  static async createDefaultFieldForUser(userId, fieldName = 'Ana Tarla') {
    try {
      // Check if user already has a default field
      const existingDefault = await Field.findOne({
        userId,
        isDefault: true,
        isActive: true
      });

      if (existingDefault) {
        Logger.info(`User ${userId} already has a default field: ${existingDefault.name}`);
        return existingDefault;
      }

      // Create new default field
      const defaultField = await createDefaultField(userId, fieldName);
      Logger.info(`Created default field for user ${userId}: ${defaultField.name}`);
      
      return defaultField;

    } catch (error) {
      Logger.error(`Failed to create default field for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Migrate existing expenses to default field
   * @param {string} userId - User ID
   * @param {string} defaultFieldId - Default field ID
   */
  static async migrateExistingExpenses(userId, defaultFieldId) {
    try {
      // Find expenses that need migration
      const expensesToMigrate = await Expense.find({
        userId,
        trackingMode: { $in: ['simple', null, undefined] },
        status: 'active'
      });

      if (expensesToMigrate.length === 0) {
        Logger.info(`No expenses to migrate for user ${userId}`);
        return { migratedCount: 0 };
      }

      // Update expenses
      const updateResult = await Expense.updateMany(
        {
          userId,
          trackingMode: { $in: ['simple', null, undefined] },
          status: 'active'
        },
        {
          $set: {
            fieldId: defaultFieldId,
            trackingMode: 'detailed'
          }
        }
      );

      Logger.info(`Migrated ${updateResult.modifiedCount} expenses for user ${userId}`);
      
      return {
        migratedCount: updateResult.modifiedCount,
        totalFound: expensesToMigrate.length
      };

    } catch (error) {
      Logger.error(`Failed to migrate expenses for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Get activation status for user
   * @param {string} userId - User ID
   */
  static async getActivationStatus(userId) {
    try {
      const user = await User.findById(userId);
      if (!user) {
        throw new Error('Kullanıcı bulunamadı');
      }

      const status = {
        userId,
        trackingMode: user.preferences.trackingMode,
        detailedModeActivatedAt: user.preferences.detailedModeActivatedAt,
        isDetailedMode: user.preferences.trackingMode === 'detailed'
      };

      if (status.isDetailedMode) {
        // Get default field info
        const defaultField = await Field.findOne({
          userId,
          isDefault: true,
          isActive: true
        });

        status.defaultField = defaultField ? {
          id: defaultField._id,
          name: defaultField.name,
          size: defaultField.formattedSize
        } : null;

        // Get field count
        status.fieldCount = await Field.countDocuments({
          userId,
          isActive: true
        });

        // Get expense distribution
        const expenseStats = await Expense.aggregate([
          { $match: { userId: user._id, status: 'active' } },
          {
            $group: {
              _id: '$trackingMode',
              count: { $sum: 1 }
            }
          }
        ]);

        status.expenseDistribution = expenseStats.reduce((acc, stat) => {
          acc[stat._id || 'unknown'] = stat.count;
          return acc;
        }, {});
      }

      return status;

    } catch (error) {
      Logger.error(`Failed to get activation status for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Validate detailed mode setup
   * @param {string} userId - User ID
   */
  static async validateDetailedModeSetup(userId) {
    try {
      const user = await User.findById(userId);
      if (!user) {
        throw new Error('Kullanıcı bulunamadı');
      }

      const validation = {
        userId,
        isValid: true,
        issues: [],
        recommendations: []
      };

      if (user.preferences.trackingMode !== 'detailed') {
        validation.isValid = false;
        validation.issues.push('Kullanıcı detaylı modda değil');
        return validation;
      }

      // Check for default field
      const defaultField = await Field.findOne({
        userId,
        isDefault: true,
        isActive: true
      });

      if (!defaultField) {
        validation.isValid = false;
        validation.issues.push('Varsayılan tarla bulunamadı');
        validation.recommendations.push('Varsayılan tarla oluşturun');
      }

      // Check for inconsistent expenses
      const inconsistentExpenses = await Expense.countDocuments({
        userId,
        trackingMode: { $ne: 'detailed' },
        status: 'active'
      });

      if (inconsistentExpenses > 0) {
        validation.issues.push(`${inconsistentExpenses} gider detaylı modda değil`);
        validation.recommendations.push('Giderleri detaylı moda migrate edin');
      }

      // Check for expenses without field
      const expensesWithoutField = await Expense.countDocuments({
        userId,
        trackingMode: 'detailed',
        fieldId: null,
        status: 'active'
      });

      if (expensesWithoutField > 0) {
        validation.issues.push(`${expensesWithoutField} giderin tarlası yok`);
        validation.recommendations.push('Giderleri varsayılan tarlaya atayın');
      }

      return validation;

    } catch (error) {
      Logger.error(`Failed to validate detailed mode setup for user ${userId}:`, error);
      throw error;
    }
  }
}

module.exports = DetailedModeActivationService;
