/**
 * Excel Processing Service for Çiftçi Not Defterim
 * Handles Excel file reading, writing, and data processing
 */

const ExcelJS = require('exceljs');
const path = require('path');
const fs = require('fs').promises;
const logger = require('../utils/logger');

class ExcelService {
  constructor() {
    this.workbook = null;
    this.supportedFormats = ['.xlsx', '.xls'];
    this.maxFileSize = 10 * 1024 * 1024; // 10MB
  }

  /**
   * Initialize Excel service
   */
  async initialize() {
    try {
      logger.info('ExcelService initialized successfully');
      return { success: true };
    } catch (error) {
      logger.error('ExcelService initialization failed:', error);
      throw error;
    }
  }

  /**
   * Validate Excel file
   * @param {string} filePath - Path to Excel file
   * @param {number} fileSize - File size in bytes
   * @returns {Object} Validation result
   */
  async validateFile(filePath, fileSize) {
    try {
      // Check file size
      if (fileSize > this.maxFileSize) {
        return {
          success: false,
          error: 'FILE_TOO_LARGE',
          message: 'Do<PERSON>a boyutu 10MB\'dan b<PERSON>y<PERSON>k olamaz'
        };
      }

      // Check file extension
      const ext = path.extname(filePath).toLowerCase();
      if (!this.supportedFormats.includes(ext)) {
        return {
          success: false,
          error: 'INVALID_FORMAT',
          message: 'Sadece .xlsx ve .xls dosyaları desteklenir'
        };
      }

      // Check if file exists
      try {
        await fs.access(filePath);
      } catch (error) {
        return {
          success: false,
          error: 'FILE_NOT_FOUND',
          message: 'Dosya bulunamadı'
        };
      }

      return { success: true };
    } catch (error) {
      logger.error('File validation error:', error);
      return {
        success: false,
        error: 'VALIDATION_ERROR',
        message: 'Dosya doğrulama hatası'
      };
    }
  }

  /**
   * Read Excel file and extract data
   * @param {string} filePath - Path to Excel file
   * @returns {Object} Extracted data
   */
  async readExcelFile(filePath) {
    try {
      logger.info('Reading Excel file:', filePath);

      this.workbook = new ExcelJS.Workbook();
      await this.workbook.xlsx.readFile(filePath);

      const worksheet = this.workbook.getWorksheet(1); // Get first worksheet
      if (!worksheet) {
        throw new Error('Excel dosyasında çalışma sayfası bulunamadı');
      }

      const data = [];
      const headers = [];

      // Extract headers from first row
      const headerRow = worksheet.getRow(1);
      headerRow.eachCell((cell, colNumber) => {
        headers.push(cell.value?.toString().trim() || `Column${colNumber}`);
      });

      // Extract data rows
      worksheet.eachRow((row, rowNumber) => {
        if (rowNumber === 1) return; // Skip header row

        const rowData = {};
        let hasData = false;

        row.eachCell((cell, colNumber) => {
          const header = headers[colNumber - 1];
          const value = this.formatCellValue(cell.value);
          
          if (value !== null && value !== '') {
            hasData = true;
          }
          
          rowData[header] = value;
        });

        if (hasData) {
          data.push(rowData);
        }
      });

      logger.info(`Excel file read successfully. Rows: ${data.length}`);

      return {
        success: true,
        data: {
          headers,
          rows: data,
          totalRows: data.length,
          worksheetName: worksheet.name
        }
      };

    } catch (error) {
      logger.error('Excel read error:', error);
      return {
        success: false,
        error: 'READ_ERROR',
        message: `Excel dosyası okunamadı: ${error.message}`
      };
    }
  }

  /**
   * Create Excel template for import
   * @returns {Object} Template creation result
   */
  async createImportTemplate() {
    try {
      logger.info('Creating Excel import template');

      this.workbook = new ExcelJS.Workbook();
      const worksheet = this.workbook.addWorksheet('Gider Verileri');

      // Define headers
      const headers = [
        'Tarih',
        'Kategori',
        'Açıklama',
        'Tutar',
        'Sezon',
        'Tarla',
        'Ürün',
        'Notlar'
      ];

      // Add headers
      const headerRow = worksheet.addRow(headers);
      headerRow.font = { bold: true };
      headerRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' }
      };

      // Add example data
      const exampleData = [
        [
          '2024-01-15',
          'Gübre',
          'Organik gübre alımı',
          2500,
          '2024 Kış',
          'Ana Tarla',
          'Buğday',
          'Kaliteli gübre tercih edildi'
        ],
        [
          '2024-01-20',
          'İşçilik',
          'Budama işçiliği',
          800,
          '2024 Kış',
          'Bağ Tarlası',
          'Üzüm',
          '3 işçi 1 gün çalıştı'
        ]
      ];

      exampleData.forEach(row => {
        worksheet.addRow(row);
      });

      // Auto-fit columns
      worksheet.columns.forEach(column => {
        column.width = 15;
      });

      // Add instructions worksheet
      const instructionsSheet = this.workbook.addWorksheet('Kullanım Kılavuzu');
      const instructions = [
        ['Çiftçi Not Defterim - Excel İçe Aktarma Kılavuzu'],
        [''],
        ['Sütun Açıklamaları:'],
        ['• Tarih: YYYY-MM-DD formatında (örn: 2024-01-15)'],
        ['• Kategori: Gübre, İşçilik, İlaç, Su, Yakıt, Tohum, Makine, Depolama'],
        ['• Açıklama: Giderin kısa açıklaması'],
        ['• Tutar: Sadece sayı (örn: 2500)'],
        ['• Sezon: Sezon adı (isteğe bağlı)'],
        ['• Tarla: Tarla adı (isteğe bağlı)'],
        ['• Ürün: Ürün adı (isteğe bağlı)'],
        ['• Notlar: Ek bilgiler (isteğe bağlı)'],
        [''],
        ['Önemli Notlar:'],
        ['• İlk satır başlık satırıdır, değiştirmeyin'],
        ['• Tarih ve Kategori alanları zorunludur'],
        ['• Örnek verileri silebilir, kendi verilerinizi ekleyebilirsiniz'],
        ['• Dosyayı .xlsx formatında kaydedin']
      ];

      instructions.forEach(instruction => {
        const row = instructionsSheet.addRow(instruction);
        if (instruction[0] && instruction[0].includes('Kılavuzu')) {
          row.font = { bold: true, size: 14 };
        } else if (instruction[0] && instruction[0].includes(':')) {
          row.font = { bold: true };
        }
      });

      instructionsSheet.columns.forEach(column => {
        column.width = 50;
      });

      return {
        success: true,
        workbook: this.workbook
      };

    } catch (error) {
      logger.error('Template creation error:', error);
      return {
        success: false,
        error: 'TEMPLATE_ERROR',
        message: `Template oluşturulamadı: ${error.message}`
      };
    }
  }

  /**
   * Export data to Excel
   * @param {Array} data - Data to export
   * @param {Object} options - Export options
   * @returns {Object} Export result
   */
  async exportToExcel(data, options = {}) {
    try {
      logger.info('Exporting data to Excel:', { recordCount: data.length });

      this.workbook = new ExcelJS.Workbook();
      const worksheet = this.workbook.addWorksheet(options.sheetName || 'Gider Raporu');

      if (data.length === 0) {
        worksheet.addRow(['Veri bulunamadı']);
        return {
          success: true,
          workbook: this.workbook
        };
      }

      // Add headers
      const headers = Object.keys(data[0]);
      const headerRow = worksheet.addRow(headers);
      headerRow.font = { bold: true };
      headerRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' }
      };

      // Add data rows
      data.forEach(item => {
        const row = headers.map(header => item[header]);
        worksheet.addRow(row);
      });

      // Auto-fit columns
      worksheet.columns.forEach(column => {
        column.width = 15;
      });

      // Add summary if requested
      if (options.includeSummary && data.length > 0) {
        worksheet.addRow([]); // Empty row
        worksheet.addRow(['ÖZET']);
        worksheet.addRow(['Toplam Kayıt:', data.length]);
        
        // Calculate total amount if amount field exists
        const amountField = headers.find(h => 
          h.toLowerCase().includes('tutar') || 
          h.toLowerCase().includes('amount')
        );
        
        if (amountField) {
          const total = data.reduce((sum, item) => {
            const amount = parseFloat(item[amountField]) || 0;
            return sum + amount;
          }, 0);
          worksheet.addRow(['Toplam Tutar:', total]);
        }
      }

      return {
        success: true,
        workbook: this.workbook
      };

    } catch (error) {
      logger.error('Excel export error:', error);
      return {
        success: false,
        error: 'EXPORT_ERROR',
        message: `Excel dışa aktarma hatası: ${error.message}`
      };
    }
  }

  /**
   * Format cell value for consistent data types
   * @param {*} value - Cell value
   * @returns {*} Formatted value
   */
  formatCellValue(value) {
    if (value === null || value === undefined) {
      return null;
    }

    // Handle dates
    if (value instanceof Date) {
      return value.toISOString().split('T')[0]; // YYYY-MM-DD format
    }

    // Handle numbers
    if (typeof value === 'number') {
      return value;
    }

    // Handle strings
    if (typeof value === 'string') {
      return value.trim();
    }

    // Handle other types
    return value.toString().trim();
  }

  /**
   * Save workbook to file
   * @param {string} filePath - Output file path
   * @returns {Object} Save result
   */
  async saveToFile(filePath) {
    try {
      if (!this.workbook) {
        throw new Error('Workbook bulunamadı');
      }

      await this.workbook.xlsx.writeFile(filePath);
      logger.info('Excel file saved:', filePath);

      return { success: true, filePath };
    } catch (error) {
      logger.error('Excel save error:', error);
      return {
        success: false,
        error: 'SAVE_ERROR',
        message: `Dosya kaydedilemedi: ${error.message}`
      };
    }
  }

  /**
   * Get workbook as buffer
   * @returns {Buffer} Excel file buffer
   */
  async getBuffer() {
    try {
      if (!this.workbook) {
        throw new Error('Workbook bulunamadı');
      }

      const buffer = await this.workbook.xlsx.writeBuffer();
      return { success: true, buffer };
    } catch (error) {
      logger.error('Excel buffer error:', error);
      return {
        success: false,
        error: 'BUFFER_ERROR',
        message: `Buffer oluşturulamadı: ${error.message}`
      };
    }
  }

  /**
   * Clean up resources
   */
  cleanup() {
    this.workbook = null;
    logger.info('ExcelService cleaned up');
  }
}

module.exports = new ExcelService();
