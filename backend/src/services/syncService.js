/**
 * Sync Service for Çiftçi Not Defterim
 * Advanced sync operations and conflict resolution utilities
 */

const Expense = require('../models/Expense');
const Category = require('../models/Category');
const User = require('../models/User');
const { SYNC_OPERATIONS, SYNC_TABLES } = require('../utils/constants');
const logger = require('../utils/logger');

class SyncService {
  /**
   * Detect conflicts between local and server data
   * @param {Object} localData - Local data
   * @param {Object} serverData - Server data
   * @returns {Object} Conflict analysis
   */
  static detectConflict(localData, serverData) {
    if (!serverData) {
      return { hasConflict: false, type: 'no_server_data' };
    }

    const localTimestamp = new Date(localData.updatedAt || localData.timestamp).getTime();
    const serverTimestamp = new Date(serverData.updatedAt).getTime();

    if (serverTimestamp > localTimestamp) {
      return {
        hasConflict: true,
        type: 'server_newer',
        serverData,
        localData,
        timeDiff: serverTimestamp - localTimestamp
      };
    }

    if (localTimestamp > serverTimestamp) {
      return {
        hasConflict: true,
        type: 'local_newer',
        serverData,
        localData,
        timeDiff: localTimestamp - serverTimestamp
      };
    }

    // Check for data differences even with same timestamp
    const dataConflict = this.compareDataFields(localData, serverData);
    if (dataConflict.hasDifferences) {
      return {
        hasConflict: true,
        type: 'data_conflict',
        serverData,
        localData,
        differences: dataConflict.differences
      };
    }

    return { hasConflict: false, type: 'no_conflict' };
  }

  /**
   * Compare data fields between local and server
   * @param {Object} localData - Local data
   * @param {Object} serverData - Server data
   * @returns {Object} Comparison result
   */
  static compareDataFields(localData, serverData) {
    const differences = [];
    const fieldsToCompare = ['amount', 'description', 'date', 'categoryId', 'location'];

    for (const field of fieldsToCompare) {
      const localValue = localData[field];
      const serverValue = serverData[field];

      if (this.isDifferent(localValue, serverValue)) {
        differences.push({
          field,
          localValue,
          serverValue
        });
      }
    }

    return {
      hasDifferences: differences.length > 0,
      differences
    };
  }

  /**
   * Check if two values are different
   * @param {any} value1 - First value
   * @param {any} value2 - Second value
   * @returns {boolean} Are values different
   */
  static isDifferent(value1, value2) {
    // Handle null/undefined
    if (value1 == null && value2 == null) return false;
    if (value1 == null || value2 == null) return true;

    // Handle dates
    if (value1 instanceof Date && value2 instanceof Date) {
      return value1.getTime() !== value2.getTime();
    }

    // Handle objects (like location)
    if (typeof value1 === 'object' && typeof value2 === 'object') {
      return JSON.stringify(value1) !== JSON.stringify(value2);
    }

    // Handle primitives
    return value1 !== value2;
  }

  /**
   * Auto-resolve conflicts based on rules
   * @param {Object} conflict - Conflict data
   * @param {Object} rules - Resolution rules
   * @returns {Object} Resolution result
   */
  static autoResolveConflict(conflict, rules = {}) {
    const defaultRules = {
      preferServer: false,
      preferLocal: false,
      preferNewer: true,
      autoMerge: true,
      ...rules
    };

    if (defaultRules.preferServer) {
      return {
        resolution: 'use_server',
        data: conflict.serverData,
        reason: 'Server preference rule'
      };
    }

    if (defaultRules.preferLocal) {
      return {
        resolution: 'use_local',
        data: conflict.localData,
        reason: 'Local preference rule'
      };
    }

    if (defaultRules.preferNewer && conflict.type === 'server_newer') {
      return {
        resolution: 'use_server',
        data: conflict.serverData,
        reason: 'Server data is newer'
      };
    }

    if (defaultRules.preferNewer && conflict.type === 'local_newer') {
      return {
        resolution: 'use_local',
        data: conflict.localData,
        reason: 'Local data is newer'
      };
    }

    if (defaultRules.autoMerge && conflict.type === 'data_conflict') {
      const mergedData = this.mergeData(conflict.localData, conflict.serverData, conflict.differences);
      return {
        resolution: 'merge',
        data: mergedData,
        reason: 'Auto-merged conflicting fields'
      };
    }

    // Default to manual resolution
    return {
      resolution: 'manual',
      reason: 'Requires manual resolution'
    };
  }

  /**
   * Merge conflicting data intelligently
   * @param {Object} localData - Local data
   * @param {Object} serverData - Server data
   * @param {Array} differences - Field differences
   * @returns {Object} Merged data
   */
  static mergeData(localData, serverData, differences) {
    const merged = { ...serverData }; // Start with server data

    for (const diff of differences) {
      const { field, localValue, serverValue } = diff;

      switch (field) {
        case 'amount':
          // For amounts, prefer the higher value (assuming user might have updated with more accurate info)
          merged[field] = Math.max(localValue || 0, serverValue || 0);
          break;

        case 'description':
          // For descriptions, prefer the longer one (more detailed)
          if (localValue && serverValue) {
            merged[field] = localValue.length > serverValue.length ? localValue : serverValue;
          } else {
            merged[field] = localValue || serverValue;
          }
          break;

        case 'date':
          // For dates, prefer the more recent update (local usually wins)
          merged[field] = localValue || serverValue;
          break;

        case 'location':
          // For location, merge coordinates if both exist
          if (localValue && serverValue) {
            merged[field] = {
              ...serverValue,
              ...localValue,
              // Keep the more accurate location (higher accuracy = lower number)
              accuracy: Math.min(localValue.accuracy || 999999, serverValue.accuracy || 999999)
            };
          } else {
            merged[field] = localValue || serverValue;
          }
          break;

        default:
          // For other fields, prefer local value
          merged[field] = localValue !== undefined ? localValue : serverValue;
      }
    }

    return merged;
  }

  /**
   * Validate sync data before processing
   * @param {Object} syncData - Sync data to validate
   * @returns {Object} Validation result
   */
  static validateSyncData(syncData) {
    const errors = [];

    if (!syncData.operation || !Object.values(SYNC_OPERATIONS).includes(syncData.operation)) {
      errors.push('Invalid or missing operation');
    }

    if (!syncData.table || !Object.values(SYNC_TABLES).includes(syncData.table)) {
      errors.push('Invalid or missing table');
    }

    if (!syncData.localId) {
      errors.push('Missing localId');
    }

    if (!syncData.timestamp || isNaN(new Date(syncData.timestamp).getTime())) {
      errors.push('Invalid or missing timestamp');
    }

    if (syncData.operation !== SYNC_OPERATIONS.DELETE && !syncData.data) {
      errors.push('Missing data for non-delete operation');
    }

    // Validate specific data based on table
    if (syncData.table === SYNC_TABLES.EXPENSES && syncData.data) {
      if (!syncData.data.amount || syncData.data.amount <= 0) {
        errors.push('Invalid expense amount');
      }
      if (!syncData.data.categoryId) {
        errors.push('Missing category ID for expense');
      }
      if (!syncData.data.date) {
        errors.push('Missing date for expense');
      }
    }

    if (syncData.table === SYNC_TABLES.CATEGORIES && syncData.data) {
      if (!syncData.data.name || syncData.data.name.trim().length < 2) {
        errors.push('Invalid category name');
      }
      if (!syncData.data.emoji) {
        errors.push('Missing emoji for category');
      }
      if (!syncData.data.color) {
        errors.push('Missing color for category');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Calculate sync priority based on data importance
   * @param {Object} syncData - Sync data
   * @returns {number} Priority score (higher = more important)
   */
  static calculateSyncPriority(syncData) {
    let priority = 0;

    // Operation priority
    switch (syncData.operation) {
      case SYNC_OPERATIONS.DELETE:
        priority += 100; // Deletes are high priority
        break;
      case SYNC_OPERATIONS.CREATE:
        priority += 80; // Creates are important
        break;
      case SYNC_OPERATIONS.UPDATE:
        priority += 60; // Updates are medium priority
        break;
    }

    // Table priority
    switch (syncData.table) {
      case SYNC_TABLES.EXPENSES:
        priority += 50; // Expenses are most important
        break;
      case SYNC_TABLES.CATEGORIES:
        priority += 30; // Categories are medium importance
        break;
      case SYNC_TABLES.SETTINGS:
        priority += 10; // Settings are least important
        break;
    }

    // Recency priority (more recent = higher priority)
    const age = Date.now() - new Date(syncData.timestamp).getTime();
    const ageInHours = age / (1000 * 60 * 60);
    if (ageInHours < 1) priority += 20;
    else if (ageInHours < 24) priority += 10;
    else if (ageInHours < 168) priority += 5; // 1 week

    return priority;
  }

  /**
   * Batch sync operations for efficiency
   * @param {Array} syncItems - Array of sync items
   * @param {number} batchSize - Size of each batch
   * @returns {Array} Batched sync items
   */
  static batchSyncItems(syncItems, batchSize = 50) {
    // Sort by priority
    const sortedItems = syncItems.sort((a, b) => 
      this.calculateSyncPriority(b) - this.calculateSyncPriority(a)
    );

    // Group into batches
    const batches = [];
    for (let i = 0; i < sortedItems.length; i += batchSize) {
      batches.push(sortedItems.slice(i, i + batchSize));
    }

    return batches;
  }

  /**
   * Generate sync statistics
   * @param {string} userId - User ID
   * @returns {Object} Sync statistics
   */
  static async getSyncStatistics(userId) {
    const [pendingExpenses, conflictExpenses, pendingCategories] = await Promise.all([
      Expense.countDocuments({ userId, syncStatus: 'pending' }),
      Expense.countDocuments({ userId, syncStatus: 'conflict' }),
      Category.countDocuments({ userId, syncStatus: 'pending' })
    ]);

    const user = await User.findById(userId);
    const lastSyncAge = user.lastSyncAt ? Date.now() - user.lastSyncAt.getTime() : null;

    return {
      pendingItems: pendingExpenses + pendingCategories,
      pendingExpenses,
      pendingCategories,
      conflicts: conflictExpenses,
      lastSyncAt: user.lastSyncAt,
      lastSyncAge: lastSyncAge ? Math.floor(lastSyncAge / (1000 * 60)) : null, // minutes
      syncVersion: user.syncVersion || 1,
      needsSync: pendingExpenses + pendingCategories + conflictExpenses > 0
    };
  }
}

module.exports = SyncService;
