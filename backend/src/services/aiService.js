/**
 * AI Service for Çiftçi Not Defterim
 * Handles Gemini AI integration for agricultural expense processing
 */

const { GoogleGenerativeAI } = require('@google/generative-ai');
const logger = require('../utils/logger');
const { importErrorHandler } = require('../middleware/importErrorHandler');

class AIService {
  constructor() {
    this.genAI = null;
    this.model = null;
    this.isInitialized = false;
    this.requestCount = 0;
    this.lastRequestTime = 0;
    this.rateLimitWindow = 60 * 1000; // 1 minute
    this.maxRequestsPerMinute = 5;
    
    // Configuration
    this.config = {
      model: process.env.GEMINI_MODEL || 'gemini-pro',
      temperature: parseFloat(process.env.GEMINI_TEMPERATURE) || 0.3,
      topK: 40,
      topP: 0.95,
      maxOutputTokens: parseInt(process.env.GEMINI_MAX_TOKENS) || 1024,
      timeout: 10000 // 10 seconds
    };
  }

  /**
   * Initialize AI service
   */
  async initialize() {
    try {
      const apiKey = process.env.GEMINI_API_KEY;
      
      if (!apiKey) {
        throw new Error('GEMINI_API_KEY environment variable is required');
      }

      this.genAI = new GoogleGenerativeAI(apiKey);
      this.model = this.genAI.getGenerativeModel({ 
        model: this.config.model,
        generationConfig: {
          temperature: this.config.temperature,
          topK: this.config.topK,
          topP: this.config.topP,
          maxOutputTokens: this.config.maxOutputTokens,
        },
        safetySettings: [
          {
            category: 'HARM_CATEGORY_HARASSMENT',
            threshold: 'BLOCK_MEDIUM_AND_ABOVE'
          },
          {
            category: 'HARM_CATEGORY_HATE_SPEECH',
            threshold: 'BLOCK_MEDIUM_AND_ABOVE'
          }
        ]
      });

      // Test the connection
      await this.testConnection();
      
      this.isInitialized = true;
      logger.info('AI Service initialized successfully');

    } catch (error) {
      logger.error('AI Service initialization failed:', error);
      throw error;
    }
  }

  /**
   * Test AI connection
   */
  async testConnection() {
    try {
      const testPrompt = "Test bağlantısı. Sadece 'OK' yanıtla.";
      const result = await this.model.generateContent(testPrompt);
      const response = await result.response;
      const text = response.text();
      
      if (!text) {
        throw new Error('AI service test failed - no response');
      }

      logger.info('AI Service connection test successful');
    } catch (error) {
      logger.error('AI Service connection test failed:', error);
      throw error;
    }
  }

  /**
   * Check rate limiting
   */
  checkRateLimit() {
    const now = Date.now();
    
    // Reset counter if window has passed
    if (now - this.lastRequestTime > this.rateLimitWindow) {
      this.requestCount = 0;
      this.lastRequestTime = now;
    }

    if (this.requestCount >= this.maxRequestsPerMinute) {
      const waitTime = this.rateLimitWindow - (now - this.lastRequestTime);
      throw new Error(`AI rate limit exceeded. Wait ${Math.ceil(waitTime / 1000)} seconds.`);
    }

    this.requestCount++;
  }

  /**
   * Process agricultural text with AI
   */
  async processAgriculturalText(text, context = {}) {
    try {
      if (!this.isInitialized) {
        throw new Error('AI Service not initialized');
      }

      // Check rate limiting
      this.checkRateLimit();

      // Validate input
      if (!text || typeof text !== 'string' || text.trim().length === 0) {
        throw new Error('Geçersiz metin girişi');
      }

      if (text.length > 2000) {
        throw new Error('Metin çok uzun (maksimum 2000 karakter)');
      }

      // Build context-aware prompt
      const prompt = this.buildPrompt(text, context);
      
      logger.info('Processing agricultural text with AI:', {
        textLength: text.length,
        hasContext: Object.keys(context).length > 0
      });

      // Generate content with timeout
      const result = await Promise.race([
        this.model.generateContent(prompt),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('AI request timeout')), this.config.timeout)
        )
      ]);

      const response = await result.response;
      const responseText = response.text();

      // Parse AI response
      const parsedResult = this.parseAIResponse(responseText);
      
      logger.info('AI processing completed:', {
        confidence: parsedResult.confidence,
        extractedCount: parsedResult.extractedData?.length || 0,
        questionsCount: parsedResult.questions?.length || 0
      });

      return {
        success: true,
        data: parsedResult
      };

    } catch (error) {
      logger.error('AI processing error:', error);
      
      // Handle specific error types
      if (error.message.includes('rate limit')) {
        return importErrorHandler.handleAIError(error, { type: 'rate_limit' });
      } else if (error.message.includes('timeout')) {
        return importErrorHandler.handleAIError(error, { type: 'timeout' });
      } else {
        return importErrorHandler.handleAIError(error, { type: 'processing' });
      }
    }
  }

  /**
   * Build context-aware prompt for AI
   */
  buildPrompt(text, context) {
    const systemPrompt = `
Sen Türkiye'deki çiftçilerin tarım giderlerini analiz eden uzman bir asistansın.

GÖREVIN:
- Düzensiz Türkçe tarım notlarını yapılandırılmış veriye dönüştür
- Belirsizliklerde kullanıcıya soru sor
- Güven seviyeni her veri için belirt (0.0-1.0)

TARİM KATEGORİLERİ (sadece bunları kullan):
1. Gübre (organik gübre, kimyasal gübre, kompost)
2. İşçilik (budama, hasat, ilaçlama, sulama işçiliği)
3. İlaç (fungisit, insektisit, herbisit, akarisit)
4. Su (sulama, fatura)
5. Yakıt (mazot, benzin, traktör yakıtı)
6. Tohum (fide, fidan, tohum)
7. Makine (kira, tamir, yedek parça)
8. Depolama (ambar, soğuk hava deposu)

TARİH ANLAMA KURALLARI:
- "geçen hafta" = 7 gün öncesi
- "dün" = 1 gün öncesi
- "bu ay" = şu anki ay
- "geçen ay" = önceki ay
- Belirsizse kullanıcıya sor

ÇIKTI FORMATI (JSON):
{
  "confidence": 0.85,
  "extractedData": [
    {
      "date": "2024-01-15",
      "category": "Gübre",
      "description": "Organik gübre alımı",
      "amount": 2500,
      "confidence": 0.9,
      "notes": "Ek bilgiler"
    }
  ],
  "questions": [
    "Hangi tarihte gübre aldınız?"
  ],
  "warnings": [
    "Tutar belirsiz, lütfen netleştirin"
  ]
}

BAĞLAMSAL BİLGİLER:
${context.season ? `- Mevsim: ${context.season}` : ''}
${context.previousCategories ? `- Önceki kategoriler: ${context.previousCategories.join(', ')}` : ''}
${context.userLocation ? `- Bölge: ${context.userLocation}` : ''}

ANALİZ EDİLECEK METİN:
"${text}"

Lütfen yukarıdaki metni analiz et ve JSON formatında yanıtla:`;

    return systemPrompt;
  }

  /**
   * Parse AI response
   */
  parseAIResponse(responseText) {
    try {
      // Clean response text
      let cleanText = responseText.trim();
      
      // Extract JSON from response (handle markdown code blocks)
      const jsonMatch = cleanText.match(/```(?:json)?\s*(\{[\s\S]*\})\s*```/);
      if (jsonMatch) {
        cleanText = jsonMatch[1];
      }

      // Try to parse JSON
      const parsed = JSON.parse(cleanText);

      // Validate required fields
      if (!parsed.confidence || !Array.isArray(parsed.extractedData)) {
        throw new Error('Invalid AI response format');
      }

      // Ensure confidence is between 0 and 1
      parsed.confidence = Math.max(0, Math.min(1, parsed.confidence));

      // Validate extracted data
      parsed.extractedData = parsed.extractedData.map(item => ({
        date: item.date || null,
        category: item.category || null,
        description: item.description || '',
        amount: parseFloat(item.amount) || 0,
        confidence: Math.max(0, Math.min(1, item.confidence || 0)),
        notes: item.notes || ''
      }));

      // Ensure arrays exist
      parsed.questions = parsed.questions || [];
      parsed.warnings = parsed.warnings || [];

      return parsed;

    } catch (error) {
      logger.error('AI response parsing error:', error);
      
      // Return fallback response
      return {
        confidence: 0.1,
        extractedData: [],
        questions: ['Metninizi daha net bir şekilde yazabilir misiniz?'],
        warnings: ['AI analizi başarısız oldu, manuel girişe geçin']
      };
    }
  }

  /**
   * Get AI service status
   */
  getStatus() {
    return {
      initialized: this.isInitialized,
      model: this.config.model,
      requestCount: this.requestCount,
      rateLimitRemaining: Math.max(0, this.maxRequestsPerMinute - this.requestCount),
      lastRequestTime: this.lastRequestTime
    };
  }

  /**
   * Reset rate limiting (for testing)
   */
  resetRateLimit() {
    this.requestCount = 0;
    this.lastRequestTime = 0;
  }
}

// Create singleton instance
const aiService = new AIService();

module.exports = aiService;
