/**
 * Swagger API Documentation Configuration
 * OpenAPI 3.0 specification for Çiftçi Not Defterim Season Management API
 */

const swaggerJsdoc = require('swagger-jsdoc');
const swaggerUi = require('swagger-ui-express');

const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'Çiftçi Not Defterim - Season Management API',
      version: '2.0.0',
      description: 'User-defined seasons with flexible date ranges and active season management API',
      contact: {
        name: 'API Support',
        email: '<EMAIL>'
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT'
      }
    },
    servers: [
      {
        url: process.env.API_BASE_URL || 'http://localhost:3000',
        description: 'Development server'
      },
      {
        url: 'https://api.ciftcinotdefterim.com',
        description: 'Production server'
      }
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT'
        }
      },
      schemas: {
        Season: {
          type: 'object',
          required: ['name'],
          properties: {
            _id: {
              type: 'string',
              format: 'objectId',
              description: 'Unique season identifier'
            },
            userId: {
              type: 'string',
              format: 'objectId',
              description: 'Owner user ID'
            },
            name: {
              type: 'string',
              maxLength: 100,
              description: 'Season name',
              example: '2025 Sezonu'
            },
            description: {
              type: 'string',
              maxLength: 500,
              description: 'Season description',
              example: 'Ana üretim sezonu'
            },
            startDate: {
              type: 'string',
              format: 'date',
              nullable: true,
              description: 'Season start date (optional)',
              example: '2025-01-01'
            },
            endDate: {
              type: 'string',
              format: 'date',
              nullable: true,
              description: 'Season end date (optional)',
              example: '2025-12-31'
            },
            isActive: {
              type: 'boolean',
              description: 'Whether this season is currently active',
              example: true
            },
            isDefault: {
              type: 'boolean',
              description: 'Whether this is the default season',
              example: false
            },
            color: {
              type: 'string',
              pattern: '^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$',
              description: 'Season color in hex format',
              example: '#4CAF50'
            },
            emoji: {
              type: 'string',
              description: 'Season emoji',
              example: '🌱'
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
              description: 'Creation timestamp'
            },
            updatedAt: {
              type: 'string',
              format: 'date-time',
              description: 'Last update timestamp'
            }
          }
        },
        SeasonSummary: {
          type: 'object',
          properties: {
            season: {
              $ref: '#/components/schemas/Season'
            },
            summary: {
              type: 'object',
              properties: {
                totalExpenses: {
                  type: 'integer',
                  description: 'Total number of expenses'
                },
                totalAmount: {
                  type: 'number',
                  description: 'Total expense amount'
                },
                avgAmount: {
                  type: 'number',
                  description: 'Average expense amount'
                },
                formattedTotalAmount: {
                  type: 'string',
                  description: 'Formatted total amount in Turkish Lira'
                }
              }
            },
            categoryBreakdown: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  _id: {
                    type: 'string',
                    format: 'objectId'
                  },
                  categoryName: {
                    type: 'string'
                  },
                  categoryEmoji: {
                    type: 'string'
                  },
                  totalAmount: {
                    type: 'number'
                  },
                  expenseCount: {
                    type: 'integer'
                  }
                }
              }
            }
          }
        },
        SeasonComparison: {
          type: 'object',
          properties: {
            seasons: {
              type: 'array',
              items: {
                allOf: [
                  { $ref: '#/components/schemas/Season' },
                  {
                    type: 'object',
                    properties: {
                      totalExpenses: {
                        type: 'integer'
                      },
                      totalAmount: {
                        type: 'number'
                      },
                      metrics: {
                        type: 'object',
                        properties: {
                          isHighest: {
                            type: 'boolean'
                          },
                          isLowest: {
                            type: 'boolean'
                          },
                          percentageOfMax: {
                            type: 'number'
                          }
                        }
                      }
                    }
                  }
                ]
              }
            },
            summary: {
              type: 'object',
              properties: {
                totalSeasons: {
                  type: 'integer'
                },
                maxAmount: {
                  type: 'number'
                },
                minAmount: {
                  type: 'number'
                },
                avgAmount: {
                  type: 'number'
                }
              }
            }
          }
        },
        Error: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean',
              example: false
            },
            error: {
              type: 'object',
              properties: {
                code: {
                  type: 'string',
                  example: 'VALIDATION_ERROR'
                },
                message: {
                  type: 'string',
                  example: 'Geçersiz veri girişi'
                },
                details: {
                  type: 'object',
                  nullable: true
                }
              }
            },
            requestId: {
              type: 'string',
              example: 'req_123456789'
            }
          }
        }
      },
      responses: {
        BadRequest: {
          description: 'Bad Request',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Error'
              }
            }
          }
        },
        Unauthorized: {
          description: 'Unauthorized',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Error'
              }
            }
          }
        },
        NotFound: {
          description: 'Not Found',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Error'
              }
            }
          }
        },
        TooManyRequests: {
          description: 'Too Many Requests',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Error'
              }
            }
          }
        },
        InternalServerError: {
          description: 'Internal Server Error',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Error'
              }
            }
          }
        }
      }
    },
    security: [
      {
        bearerAuth: []
      }
    ],
    tags: [
      {
        name: 'Seasons',
        description: 'User-defined season management operations'
      },
      {
        name: 'Season Analytics',
        description: 'Season analytics and reporting operations'
      }
    ]
  },
  apis: [
    './src/routes/seasons.js',
    './src/controllers/seasonController.js'
  ]
};

const specs = swaggerJsdoc(options);

/**
 * Setup Swagger UI middleware
 * @param {Object} app - Express app instance
 */
const setupSwagger = (app) => {
  // Swagger UI options
  const swaggerOptions = {
    explorer: true,
    customCss: '.swagger-ui .topbar { display: none }',
    customSiteTitle: 'Çiftçi Not Defterim API Documentation',
    swaggerOptions: {
      persistAuthorization: true,
      displayRequestDuration: true,
      filter: true,
      tryItOutEnabled: true
    }
  };

  // Serve Swagger UI
  app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(specs, swaggerOptions));
  
  // Serve raw OpenAPI spec
  app.get('/api-docs.json', (req, res) => {
    res.setHeader('Content-Type', 'application/json');
    res.send(specs);
  });

  console.log('📚 Swagger documentation available at /api-docs');
};

module.exports = {
  setupSwagger,
  specs
};
