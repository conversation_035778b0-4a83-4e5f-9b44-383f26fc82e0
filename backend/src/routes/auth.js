/**
 * Authentication Routes for Çiftçi Not Defterim
 * Handles user authentication, registration, and profile management
 */

const express = require('express');
const router = express.Router();

// Import controllers
const authController = require('../controllers/authController');

// Import middleware
const { authenticateUser } = require('../middleware/auth');
const { userValidation } = require('../middleware/validation');
const rateLimiter = require('../middleware/rateLimiter');

/**
 * @route   POST /api/v1/auth/validate
 * @desc    Validate Firebase token and get/create user
 * @access  Public
 */
router.post('/validate', 
  rateLimiter.auth,
  [
    // Validation
    require('express-validator').body('firebaseToken')
      .notEmpty()
      .withMessage('Firebase token gerekli')
      .isLength({ min: 10 })
      .withMessage('Geçersiz token formatı'),
    
    require('../middleware/validation').handleValidationErrors
  ],
  authController.validateToken
);

/**
 * @route   GET /api/v1/auth/profile
 * @desc    Get current user profile
 * @access  Private
 */
router.get('/profile',
  authenticateUser,
  authController.getProfile
);

/**
 * @route   PUT /api/v1/auth/profile
 * @desc    Update user profile
 * @access  Private
 */
router.put('/profile',
  authenticateUser,
  userValidation.updateProfile,
  authController.updateProfile
);

/**
 * @route   PATCH /api/v1/auth/preferences
 * @desc    Update user preferences only
 * @access  Private
 */
router.patch('/preferences',
  authenticateUser,
  [
    require('express-validator').body('preferences')
      .isObject()
      .withMessage('Tercihler obje formatında olmalıdır'),
    
    require('express-validator').body('preferences.currency')
      .optional()
      .isIn(['TRY', 'USD', 'EUR'])
      .withMessage('Desteklenen para birimi: TRY, USD, EUR'),
    
    require('express-validator').body('preferences.language')
      .optional()
      .isIn(['tr', 'en'])
      .withMessage('Desteklenen dil: tr, en'),
    
    require('express-validator').body('preferences.defaultSeason')
      .optional()
      .isIn(['spring', 'summer', 'autumn', 'winter'])
      .withMessage('Geçersiz sezon'),
    
    require('../middleware/validation').handleValidationErrors
  ],
  authController.updatePreferences
);

/**
 * @route   GET /api/v1/auth/stats
 * @desc    Get user statistics
 * @access  Private
 */
router.get('/stats',
  authenticateUser,
  authController.getUserStats
);

/**
 * @route   POST /api/v1/auth/device
 * @desc    Register device for push notifications
 * @access  Private
 */
router.post('/device',
  authenticateUser,
  [
    require('express-validator').body('deviceId')
      .notEmpty()
      .withMessage('Device ID gerekli')
      .isLength({ min: 5, max: 100 })
      .withMessage('Geçersiz device ID'),
    
    require('express-validator').body('platform')
      .isIn(['ios', 'android', 'web'])
      .withMessage('Desteklenen platform: ios, android, web'),
    
    require('express-validator').body('appVersion')
      .optional()
      .isLength({ max: 20 })
      .withMessage('App version çok uzun'),
    
    require('express-validator').body('pushToken')
      .optional()
      .isLength({ max: 500 })
      .withMessage('Push token çok uzun'),
    
    require('../middleware/validation').handleValidationErrors
  ],
  authController.registerDevice
);

/**
 * @route   POST /api/v1/auth/logout
 * @desc    Logout user (remove device)
 * @access  Private
 */
router.post('/logout',
  authenticateUser,
  [
    require('express-validator').body('deviceId')
      .optional()
      .isLength({ min: 5, max: 100 })
      .withMessage('Geçersiz device ID'),
    
    require('../middleware/validation').handleValidationErrors
  ],
  authController.logout
);

/**
 * @route   DELETE /api/v1/auth/account
 * @desc    Delete user account
 * @access  Private
 */
router.delete('/account',
  authenticateUser,
  rateLimiter.strict, // Very limited for account deletion
  [
    require('express-validator').body('confirmation')
      .equals('DELETE_MY_ACCOUNT')
      .withMessage('Hesap silme onayı gerekli: DELETE_MY_ACCOUNT'),
    
    require('../middleware/validation').handleValidationErrors
  ],
  authController.deleteAccount
);

/**
 * @route   GET /api/v1/auth/export
 * @desc    Export user data (GDPR compliance)
 * @access  Private
 */
router.get('/export',
  authenticateUser,
  rateLimiter.custom(60 * 60 * 1000, 3, 'Veri dışa aktarma çok sık talep edildi'), // 3 times per hour
  authController.exportUserData
);

/**
 * @route   POST /api/v1/auth/refresh
 * @desc    Refresh user session (update last activity)
 * @access  Private
 */
router.post('/refresh',
  authenticateUser,
  async (req, res) => {
    try {
      // Update last login time
      req.user.stats.lastLoginAt = new Date();
      await req.user.save();
      
      const { ResponseHelpers } = require('../utils/helpers');
      res.status(200).json(
        ResponseHelpers.success({
          lastLoginAt: req.user.stats.lastLoginAt
        }, 'Oturum yenilendi')
      );
    } catch (error) {
      next(error);
    }
  }
);

/**
 * @route   GET /api/v1/auth/check
 * @desc    Check if user is authenticated (health check for auth)
 * @access  Private
 */
router.get('/check',
  authenticateUser,
  (req, res) => {
    const { ResponseHelpers } = require('../utils/helpers');
    res.status(200).json(
      ResponseHelpers.success({
        authenticated: true,
        userId: req.user._id,
        email: req.user.email,
        name: req.user.name
      }, 'Kimlik doğrulama geçerli')
    );
  }
);

module.exports = router;
