/**
 * Season Routes for Çiftçi Not Defterim
 * Handles user-defined seasons with flexible date ranges and active season management
 */

const express = require('express');
const router = express.Router();
const { body, param } = require('express-validator');

// Import controllers
const seasonController = require('../controllers/seasonController');

// Import middleware
const { authenticateUser } = require('../middleware/auth');
const { handleValidationErrors } = require('../middleware/validation');
const rateLimiter = require('../middleware/rateLimiter');

// Apply authentication to all routes
router.use(authenticateUser);

// Apply rate limiting to all routes
router.use(rateLimiter.general);

/**
 * @swagger
 * /api/v1/seasons:
 *   get:
 *     summary: Get user's seasons
 *     description: Retrieve all seasons belonging to the authenticated user
 *     tags: [Seasons]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User seasons retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Season'
 *                 message:
 *                   type: string
 *                   example: "Kullanıcı sezonları getirildi"
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       429:
 *         $ref: '#/components/responses/TooManyRequests'
 *       500:
 *         $ref: '#/components/responses/InternalServerError'
 */
router.get('/',
  seasonController.getSeasons
);

/**
 * @route   POST /api/v1/seasons
 * @desc    Create new season
 * @access  Private
 */
router.post('/',
  [
    rateLimiter.custom(60 * 1000, 5, 'Çok fazla sezon oluşturuyorsunuz. Lütfen bekleyin.'),

    body('name')
      .trim()
      .notEmpty()
      .withMessage('Sezon adı gereklidir')
      .isLength({ max: 100 })
      .withMessage('Sezon adı en fazla 100 karakter olabilir'),

    body('description')
      .optional()
      .trim()
      .isLength({ max: 500 })
      .withMessage('Açıklama en fazla 500 karakter olabilir'),

    body('startDate')
      .optional()
      .isISO8601()
      .withMessage('Geçerli bir başlangıç tarihi giriniz'),

    body('endDate')
      .optional()
      .isISO8601()
      .withMessage('Geçerli bir bitiş tarihi giriniz')
      .custom((value, { req }) => {
        if (value && req.body.startDate && new Date(value) <= new Date(req.body.startDate)) {
          throw new Error('Bitiş tarihi başlangıç tarihinden sonra olmalıdır');
        }
        return true;
      }),

    body('color')
      .optional()
      .matches(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/)
      .withMessage('Geçerli bir renk kodu giriniz (#RRGGBB formatında)'),

    body('emoji')
      .optional()
      .isString()
      .withMessage('Geçerli bir emoji seçiniz'),

    body('isActive')
      .optional()
      .customSanitizer(value => {
        // Handle string boolean values from frontend (e.g., "true", "false")
        if (typeof value === 'string') {
          if (value.toLowerCase() === 'true') return true;
          if (value.toLowerCase() === 'false') return false;
          return value; // Let validation handle invalid strings
        }
        return Boolean(value);
      })
      .isBoolean()
      .withMessage('isActive boolean değer olmalıdır'),

    body('isDefault')
      .optional()
      .customSanitizer(value => {
        // Handle string boolean values from frontend (e.g., "true", "false")
        if (typeof value === 'string') {
          if (value.toLowerCase() === 'true') return true;
          if (value.toLowerCase() === 'false') return false;
          return value; // Let validation handle invalid strings
        }
        return Boolean(value);
      })
      .isBoolean()
      .withMessage('isDefault boolean değer olmalıdır'),

    handleValidationErrors
  ],
  seasonController.createSeason
);

/**
 * @route   GET /api/v1/seasons/active
 * @desc    Get active season for user
 * @access  Private
 */
router.get('/active',
  seasonController.getActiveSeason
);

/**
 * @route   GET /api/v1/seasons/compare
 * @desc    Compare multiple seasons
 * @access  Private
 */
router.get('/compare',
  [
    require('express-validator').query('seasons')
      .notEmpty()
      .withMessage('Karşılaştırılacak sezonlar belirtilmelidir')
      .custom((value) => {
        const seasonIds = value.split(',');
        if (seasonIds.length < 2) {
          throw new Error('En az 2 sezon karşılaştırılmalıdır');
        }
        if (seasonIds.length > 5) {
          throw new Error('En fazla 5 sezon karşılaştırılabilir');
        }
        return true;
      }),

    handleValidationErrors
  ],
  seasonController.compareSeasons
);

/**
 * @route   GET /api/v1/seasons/:id
 * @desc    Get season by ID
 * @access  Private
 */
router.get('/:id',
  [
    param('id')
      .isMongoId()
      .withMessage('Geçersiz sezon ID\'si'),

    handleValidationErrors
  ],
  seasonController.getSeasonById
);

/**
 * @route   PUT /api/v1/seasons/:id
 * @desc    Update season
 * @access  Private
 */
router.put('/:id',
  [
    param('id')
      .isMongoId()
      .withMessage('Geçersiz sezon ID\'si'),

    body('name')
      .optional()
      .trim()
      .notEmpty()
      .withMessage('Sezon adı boş olamaz')
      .isLength({ max: 100 })
      .withMessage('Sezon adı en fazla 100 karakter olabilir'),

    body('description')
      .optional()
      .trim()
      .isLength({ max: 500 })
      .withMessage('Açıklama en fazla 500 karakter olabilir'),

    body('startDate')
      .optional()
      .isISO8601()
      .withMessage('Geçerli bir başlangıç tarihi giriniz'),

    body('endDate')
      .optional()
      .isISO8601()
      .withMessage('Geçerli bir bitiş tarihi giriniz')
      .custom((value, { req }) => {
        if (value && req.body.startDate && new Date(value) <= new Date(req.body.startDate)) {
          throw new Error('Bitiş tarihi başlangıç tarihinden sonra olmalıdır');
        }
        return true;
      }),

    body('color')
      .optional()
      .matches(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/)
      .withMessage('Geçerli bir renk kodu giriniz (#RRGGBB formatında)'),

    body('emoji')
      .optional()
      .isString()
      .withMessage('Geçerli bir emoji seçiniz'),

    handleValidationErrors
  ],
  seasonController.updateSeason
);

/**
 * @route   GET /api/v1/seasons/:id/summary
 * @desc    Get season summary with expenses
 * @access  Private
 */
router.get('/:id/summary',
  [
    param('id')
      .isMongoId()
      .withMessage('Geçersiz sezon ID\'si'),

    handleValidationErrors
  ],
  seasonController.getSeasonSummary
);

/**
 * @route   GET /api/v1/seasons/:id/expenses
 * @desc    Get season expenses
 * @access  Private
 */
router.get('/:id/expenses',
  [
    param('id')
      .isMongoId()
      .withMessage('Geçersiz sezon ID\'si'),

    require('express-validator').query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Sayfa numarası 1 veya daha büyük olmalıdır'),

    require('express-validator').query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit 1 ile 100 arasında olmalıdır'),

    require('express-validator').query('sortBy')
      .optional()
      .isIn(['date', 'amount', 'description'])
      .withMessage('Geçersiz sıralama alanı'),

    require('express-validator').query('sortOrder')
      .optional()
      .isIn(['asc', 'desc'])
      .withMessage('Geçersiz sıralama yönü'),

    handleValidationErrors
  ],
  seasonController.getSeasonExpenses
);

/**
 * @route   PATCH /api/v1/seasons/:id/activate
 * @desc    Activate season
 * @access  Private
 */
router.patch('/:id/activate',
  [
    param('id')
      .isMongoId()
      .withMessage('Geçersiz sezon ID\'si'),

    handleValidationErrors
  ],
  seasonController.activateSeason
);

/**
 * @route   DELETE /api/v1/seasons/:id
 * @desc    Delete season
 * @access  Private
 */
router.delete('/:id',
  [
    param('id')
      .isMongoId()
      .withMessage('Geçersiz sezon ID\'si'),

    handleValidationErrors
  ],
  seasonController.deleteSeason
);

module.exports = router;

/**
 * @route   GET /api/v1/seasons/:id/expenses
 * @desc    Get expenses for specific season
 * @access  Private
 */
router.get('/:id/expenses',
  [
    require('express-validator').param('id')
      .isIn(['spring', 'summer', 'autumn', 'winter'])
      .withMessage('Geçersiz sezon ID\'si'),
    
    require('express-validator').query('year')
      .optional()
      .isInt({ min: 2020, max: 2030 })
      .withMessage('Geçersiz yıl'),
    
    require('express-validator').query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Sayfa numarası 1 veya daha büyük olmalıdır'),
    
    require('express-validator').query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit 1 ile 100 arasında olmalıdır'),
    
    require('../middleware/validation').handleValidationErrors
  ],
  async (req, res, next) => {
    try {
      const { ResponseHelpers } = require('../utils/helpers');
      const { PAGINATION } = require('../utils/constants');
      const Expense = require('../models/Expense');
      
      const {
        year = new Date().getFullYear(),
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT
      } = req.query;

      const startDate = new Date(parseInt(year), 0, 1);
      const endDate = new Date(parseInt(year) + 1, 0, 1);

      // Build query
      const query = {
        userId: req.user._id,
        seasonId: req.params.id,
        status: 'active',
        date: { $gte: startDate, $lt: endDate }
      };

      // Pagination
      const pageNum = parseInt(page);
      const limitNum = Math.min(parseInt(limit), PAGINATION.MAX_LIMIT);
      const skip = (pageNum - 1) * limitNum;

      // Execute query
      const [expenses, total] = await Promise.all([
        Expense.find(query)
          .populate('categoryId', 'name emoji color icon')
          .sort({ date: -1 })
          .skip(skip)
          .limit(limitNum)
          .lean(),
        Expense.countDocuments(query)
      ]);

      // Calculate summary
      const summary = await Expense.aggregate([
        { $match: query },
        {
          $group: {
            _id: null,
            totalAmount: { $sum: '$amount' },
            expenseCount: { $sum: 1 },
            avgAmount: { $avg: '$amount' }
          }
        }
      ]);

      const summaryData = summary[0] || {
        totalAmount: 0,
        expenseCount: 0,
        avgAmount: 0
      };

      const response = ResponseHelpers.paginated(expenses, pageNum, limitNum, total);
      response.data.summary = summaryData;

      res.status(200).json(response);
    } catch (error) {
      next(error);
    }
  }
);

/**
 * @route   GET /api/v1/seasons/:id/categories
 * @desc    Get category breakdown for specific season
 * @access  Private
 */
router.get('/:id/categories',
  [
    require('express-validator').param('id')
      .isIn(['spring', 'summer', 'autumn', 'winter'])
      .withMessage('Geçersiz sezon ID\'si'),
    
    require('express-validator').query('year')
      .optional()
      .isInt({ min: 2020, max: 2030 })
      .withMessage('Geçersiz yıl'),
    
    require('../middleware/validation').handleValidationErrors
  ],
  async (req, res, next) => {
    try {
      const { ResponseHelpers } = require('../utils/helpers');
      const Expense = require('../models/Expense');
      
      const { year = new Date().getFullYear() } = req.query;
      const startDate = new Date(parseInt(year), 0, 1);
      const endDate = new Date(parseInt(year) + 1, 0, 1);

      const categoryBreakdown = await Expense.aggregate([
        {
          $match: {
            userId: req.user._id,
            seasonId: req.params.id,
            status: 'active',
            date: { $gte: startDate, $lt: endDate }
          }
        },
        {
          $group: {
            _id: '$categoryId',
            totalAmount: { $sum: '$amount' },
            expenseCount: { $sum: 1 },
            avgAmount: { $avg: '$amount' },
            lastExpense: { $max: '$date' }
          }
        },
        {
          $lookup: {
            from: 'categories',
            localField: '_id',
            foreignField: '_id',
            as: 'category'
          }
        },
        {
          $unwind: '$category'
        },
        {
          $project: {
            categoryName: '$category.name',
            categoryEmoji: '$category.emoji',
            categoryColor: '$category.color',
            totalAmount: 1,
            expenseCount: 1,
            avgAmount: { $round: ['$avgAmount', 2] },
            lastExpense: 1
          }
        },
        {
          $sort: { totalAmount: -1 }
        }
      ]);

      // Calculate percentages
      const totalAmount = categoryBreakdown.reduce((sum, cat) => sum + cat.totalAmount, 0);
      const categoriesWithPercentage = categoryBreakdown.map(cat => ({
        ...cat,
        percentage: totalAmount > 0 ? Math.round((cat.totalAmount / totalAmount) * 100) : 0
      }));

      res.status(200).json(
        ResponseHelpers.success({
          seasonId: req.params.id,
          year: parseInt(year),
          totalAmount,
          categories: categoriesWithPercentage
        }, 'Sezon kategori dağılımı getirildi')
      );
    } catch (error) {
      next(error);
    }
  }
);

/**
 * @route   GET /api/v1/seasons/:id/trends
 * @desc    Get expense trends for specific season
 * @access  Private
 */
router.get('/:id/trends',
  [
    require('express-validator').param('id')
      .isIn(['spring', 'summer', 'autumn', 'winter'])
      .withMessage('Geçersiz sezon ID\'si'),
    
    require('express-validator').query('years')
      .optional()
      .isInt({ min: 2 })
      .withMessage('En az 2 yıl karşılaştırması gerekli'),
    
    require('../middleware/validation').handleValidationErrors
  ],
  async (req, res, next) => {
    try {
      const { ResponseHelpers } = require('../utils/helpers');
      const Expense = require('../models/Expense');
      
      const { years = 3 } = req.query;
      const currentYear = new Date().getFullYear();
      const yearCount = parseInt(years);
      
      const trends = [];
      
      for (let i = 0; i < yearCount; i++) {
        const year = currentYear - i;
        const startDate = new Date(year, 0, 1);
        const endDate = new Date(year + 1, 0, 1);
        
        const yearData = await Expense.aggregate([
          {
            $match: {
              userId: req.user._id,
              seasonId: req.params.id,
              status: 'active',
              date: { $gte: startDate, $lt: endDate }
            }
          },
          {
            $group: {
              _id: null,
              totalAmount: { $sum: '$amount' },
              expenseCount: { $sum: 1 },
              avgAmount: { $avg: '$amount' }
            }
          }
        ]);
        
        trends.unshift({
          year,
          data: yearData[0] || {
            totalAmount: 0,
            expenseCount: 0,
            avgAmount: 0
          }
        });
      }
      
      // Calculate year-over-year changes
      const trendsWithChanges = trends.map((trend, index) => {
        if (index === 0) {
          return { ...trend, change: null };
        }
        
        const previousYear = trends[index - 1];
        const change = previousYear.data.totalAmount > 0 ? 
          ((trend.data.totalAmount - previousYear.data.totalAmount) / previousYear.data.totalAmount * 100) : 0;
        
        return { ...trend, change: Math.round(change * 100) / 100 };
      });

      res.status(200).json(
        ResponseHelpers.success({
          seasonId: req.params.id,
          trends: trendsWithChanges
        }, 'Sezon trendleri getirildi')
      );
    } catch (error) {
      next(error);
    }
  }
);

module.exports = router;
