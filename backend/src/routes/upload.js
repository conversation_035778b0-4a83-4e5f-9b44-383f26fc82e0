/**
 * Upload Routes for Çiftçi Not Defterim
 * Handles file uploads for receipts and photos
 */

const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;
const sharp = require('sharp');
const { v4: uuidv4 } = require('uuid');
const router = express.Router();

// Import middleware
const { authenticateUser } = require('../middleware/auth');
const { uploadValidation } = require('../middleware/validation');
const rateLimiter = require('../middleware/rateLimiter');
const { FILE_UPLOAD } = require('../utils/constants');
const { ResponseHelpers } = require('../utils/helpers');
const logger = require('../utils/logger');

// Apply authentication to all routes
router.use(authenticateUser);

// Ensure upload directory exists
const ensureUploadDir = async () => {
  try {
    await fs.access(FILE_UPLOAD.UPLOAD_PATH);
  } catch {
    await fs.mkdir(FILE_UPLOAD.UPLOAD_PATH, { recursive: true });
  }
};

// Configure multer for file uploads
const storage = multer.memoryStorage();

const fileFilter = (req, file, cb) => {
  // Check file type
  if (FILE_UPLOAD.ALLOWED_TYPES.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('Desteklenmeyen dosya türü. Sadece JPEG, PNG ve WebP dosyaları kabul edilir.'), false);
  }
};

const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: FILE_UPLOAD.MAX_SIZE,
    files: 5 // Maximum 5 files at once
  }
});

/**
 * @route   POST /api/v1/upload/photo
 * @desc    Upload single photo
 * @access  Private
 */
router.post('/photo',
  rateLimiter.upload,
  upload.single('photo'),
  uploadValidation.photo,
  async (req, res, next) => {
    try {
      if (!req.file) {
        return res.status(400).json(
          ResponseHelpers.error('NO_FILE', 'Dosya seçilmedi')
        );
      }

      await ensureUploadDir();

      // Generate unique filename
      const fileId = uuidv4();
      const fileExtension = path.extname(req.file.originalname).toLowerCase();
      const filename = `${fileId}${fileExtension}`;
      const filepath = path.join(FILE_UPLOAD.UPLOAD_PATH, filename);

      // Generate thumbnail filename
      const thumbnailFilename = `thumb_${filename}`;
      const thumbnailPath = path.join(FILE_UPLOAD.UPLOAD_PATH, thumbnailFilename);

      // Process and save original image
      const processedImage = await sharp(req.file.buffer)
        .jpeg({ quality: 85 })
        .resize(1920, 1920, { 
          fit: 'inside',
          withoutEnlargement: true 
        });

      await processedImage.toFile(filepath);

      // Generate thumbnail
      await sharp(req.file.buffer)
        .jpeg({ quality: 70 })
        .resize(FILE_UPLOAD.THUMBNAIL_SIZE, FILE_UPLOAD.THUMBNAIL_SIZE, {
          fit: 'cover'
        })
        .toFile(thumbnailPath);

      // Get file stats
      const stats = await fs.stat(filepath);

      const photoData = {
        id: fileId,
        filename,
        originalName: req.file.originalname,
        url: `/uploads/receipts/${filename}`,
        thumbnail: `/uploads/receipts/${thumbnailFilename}`,
        size: stats.size,
        mimeType: 'image/jpeg',
        uploadedAt: new Date().toISOString()
      };

      logger.info('Photo uploaded:', {
        userId: req.user._id,
        photoId: fileId,
        filename,
        size: stats.size
      });

      res.status(201).json(
        ResponseHelpers.success(photoData, 'Fotoğraf başarıyla yüklendi')
      );

    } catch (error) {
      logger.error('Photo upload error:', error);
      next(error);
    }
  }
);

/**
 * @route   POST /api/v1/upload/photos
 * @desc    Upload multiple photos
 * @access  Private
 */
router.post('/photos',
  rateLimiter.upload,
  upload.array('photos', 5),
  uploadValidation.photo,
  async (req, res, next) => {
    try {
      if (!req.files || req.files.length === 0) {
        return res.status(400).json(
          ResponseHelpers.error('NO_FILES', 'Dosya seçilmedi')
        );
      }

      await ensureUploadDir();

      const uploadedPhotos = [];
      const errors = [];

      for (const file of req.files) {
        try {
          // Generate unique filename
          const fileId = uuidv4();
          const fileExtension = path.extname(file.originalname).toLowerCase();
          const filename = `${fileId}${fileExtension}`;
          const filepath = path.join(FILE_UPLOAD.UPLOAD_PATH, filename);

          // Generate thumbnail filename
          const thumbnailFilename = `thumb_${filename}`;
          const thumbnailPath = path.join(FILE_UPLOAD.UPLOAD_PATH, thumbnailFilename);

          // Process and save original image
          const processedImage = await sharp(file.buffer)
            .jpeg({ quality: 85 })
            .resize(1920, 1920, { 
              fit: 'inside',
              withoutEnlargement: true 
            });

          await processedImage.toFile(filepath);

          // Generate thumbnail
          await sharp(file.buffer)
            .jpeg({ quality: 70 })
            .resize(FILE_UPLOAD.THUMBNAIL_SIZE, FILE_UPLOAD.THUMBNAIL_SIZE, {
              fit: 'cover'
            })
            .toFile(thumbnailPath);

          // Get file stats
          const stats = await fs.stat(filepath);

          const photoData = {
            id: fileId,
            filename,
            originalName: file.originalname,
            url: `/uploads/receipts/${filename}`,
            thumbnail: `/uploads/receipts/${thumbnailFilename}`,
            size: stats.size,
            mimeType: 'image/jpeg',
            uploadedAt: new Date().toISOString()
          };

          uploadedPhotos.push(photoData);

        } catch (error) {
          errors.push({
            filename: file.originalname,
            error: error.message
          });
        }
      }

      logger.info('Multiple photos uploaded:', {
        userId: req.user._id,
        successCount: uploadedPhotos.length,
        errorCount: errors.length
      });

      res.status(201).json(
        ResponseHelpers.success({
          photos: uploadedPhotos,
          errors: errors.length > 0 ? errors : undefined
        }, `${uploadedPhotos.length} fotoğraf başarıyla yüklendi`)
      );

    } catch (error) {
      logger.error('Multiple photos upload error:', error);
      next(error);
    }
  }
);

/**
 * @route   DELETE /api/v1/upload/photo/:id
 * @desc    Delete uploaded photo
 * @access  Private
 */
router.delete('/photo/:id',
  [
    require('express-validator').param('id')
      .isUUID()
      .withMessage('Geçersiz fotoğraf ID\'si'),
    
    require('../middleware/validation').handleValidationErrors
  ],
  async (req, res, next) => {
    try {
      const photoId = req.params.id;
      
      // Find files with this ID
      const uploadDir = FILE_UPLOAD.UPLOAD_PATH;
      const files = await fs.readdir(uploadDir);
      
      const photoFiles = files.filter(file => file.includes(photoId));
      
      if (photoFiles.length === 0) {
        return res.status(404).json(
          ResponseHelpers.error('PHOTO_NOT_FOUND', 'Fotoğraf bulunamadı')
        );
      }

      // Delete files
      for (const file of photoFiles) {
        try {
          await fs.unlink(path.join(uploadDir, file));
        } catch (error) {
          logger.warn('Error deleting file:', { file, error: error.message });
        }
      }

      logger.info('Photo deleted:', {
        userId: req.user._id,
        photoId,
        deletedFiles: photoFiles
      });

      res.status(200).json(
        ResponseHelpers.success(null, 'Fotoğraf silindi')
      );

    } catch (error) {
      logger.error('Photo deletion error:', error);
      next(error);
    }
  }
);

/**
 * @route   GET /api/v1/upload/info/:id
 * @desc    Get photo information
 * @access  Private
 */
router.get('/info/:id',
  [
    require('express-validator').param('id')
      .isUUID()
      .withMessage('Geçersiz fotoğraf ID\'si'),
    
    require('../middleware/validation').handleValidationErrors
  ],
  async (req, res, next) => {
    try {
      const photoId = req.params.id;
      const uploadDir = FILE_UPLOAD.UPLOAD_PATH;
      
      // Find main photo file (not thumbnail)
      const files = await fs.readdir(uploadDir);
      const photoFile = files.find(file => 
        file.includes(photoId) && !file.startsWith('thumb_')
      );
      
      if (!photoFile) {
        return res.status(404).json(
          ResponseHelpers.error('PHOTO_NOT_FOUND', 'Fotoğraf bulunamadı')
        );
      }

      const filepath = path.join(uploadDir, photoFile);
      const stats = await fs.stat(filepath);
      
      // Check if thumbnail exists
      const thumbnailFile = files.find(file => 
        file.includes(photoId) && file.startsWith('thumb_')
      );

      const photoInfo = {
        id: photoId,
        filename: photoFile,
        url: `/uploads/receipts/${photoFile}`,
        thumbnail: thumbnailFile ? `/uploads/receipts/${thumbnailFile}` : null,
        size: stats.size,
        mimeType: 'image/jpeg',
        createdAt: stats.birthtime,
        modifiedAt: stats.mtime
      };

      res.status(200).json(
        ResponseHelpers.success(photoInfo, 'Fotoğraf bilgileri getirildi')
      );

    } catch (error) {
      next(error);
    }
  }
);

/**
 * Error handling for multer
 */
router.use((error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json(
        ResponseHelpers.error('FILE_TOO_LARGE', 'Dosya boyutu çok büyük (maksimum 5MB)')
      );
    }
    if (error.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json(
        ResponseHelpers.error('TOO_MANY_FILES', 'Çok fazla dosya (maksimum 5 dosya)')
      );
    }
    if (error.code === 'LIMIT_UNEXPECTED_FILE') {
      return res.status(400).json(
        ResponseHelpers.error('UNEXPECTED_FILE', 'Beklenmeyen dosya alanı')
      );
    }
  }
  
  if (error.message.includes('Desteklenmeyen dosya türü')) {
    return res.status(400).json(
      ResponseHelpers.error('INVALID_FILE_TYPE', error.message)
    );
  }
  
  next(error);
});

module.exports = router;
