/**
 * User Routes for Çiftçi Not Defterim
 * Handles user management and admin operations
 */

const express = require('express');
const router = express.Router();

// Import middleware
const { authenticateUser, requireAdmin } = require('../middleware/auth');
const { paramValidation } = require('../middleware/validation');
const rateLimiter = require('../middleware/rateLimiter');

// Import models
const User = require('../models/User');
const Expense = require('../models/Expense');
const Category = require('../models/Category');
const Field = require('../models/Field');

// Import utilities
const { createDefaultField } = require('../utils/defaultFields');
const Logger = require('../utils/logger');

// Import validation middleware
const {
  validateModeSwitch,
  requiresConfirmation,
  validatePermissions,
  logModeSwitch
} = require('../middleware/modeValidation');

// Apply authentication to all routes
router.use(authenticateUser);

// Debug: Log all routes
console.log('🔧 User routes loaded - /mode routes should be available');

/**
 * @route   GET /api/v1/users
 * @desc    Get all users (Admin only)
 * @access  Private/Admin
 */
router.get('/',
  requireAdmin,
  [
    require('express-validator').query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Sayfa numarası 1 veya daha büyük olmalıdır'),
    
    require('express-validator').query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit 1 ile 100 arasında olmalıdır'),
    
    require('express-validator').query('status')
      .optional()
      .isIn(['active', 'inactive', 'suspended', 'deleted'])
      .withMessage('Geçersiz durum'),
    
    require('express-validator').query('type')
      .optional()
      .isIn(['free', 'premium', 'admin'])
      .withMessage('Geçersiz kullanıcı türü'),
    
    require('../middleware/validation').handleValidationErrors
  ],
  async (req, res, next) => {
    try {
      const { ResponseHelpers } = require('../utils/helpers');
      const { PAGINATION } = require('../utils/constants');
      
      const {
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT,
        status,
        type,
        search
      } = req.query;

      // Build query
      const query = {};
      if (status) query.status = status;
      if (type) query.type = type;
      if (search) {
        query.$or = [
          { name: { $regex: search, $options: 'i' } },
          { email: { $regex: search, $options: 'i' } }
        ];
      }

      // Pagination
      const pageNum = parseInt(page);
      const limitNum = Math.min(parseInt(limit), PAGINATION.MAX_LIMIT);
      const skip = (pageNum - 1) * limitNum;

      // Execute query
      const [users, total] = await Promise.all([
        User.find(query)
          .select('-firebaseUid -devices -dataRetention')
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limitNum)
          .lean(),
        User.countDocuments(query)
      ]);

      res.status(200).json(
        ResponseHelpers.paginated(users, pageNum, limitNum, total)
      );
    } catch (error) {
      next(error);
    }
  }
);

/**
 * @route   GET /api/v1/users/has-data
 * @desc    Check if current user has existing data (expenses/categories)
 * @access  Private
 */
router.get('/has-data',
  async (req, res, next) => {
    try {
      const { ResponseHelpers } = require('../utils/helpers');
      const logger = require('../utils/logger');

      const userId = req.user._id;

      logger.info('Checking user data existence:', { userId });

      // Check if user has any expenses or categories
      const [expenseCount, categoryCount] = await Promise.all([
        Expense.countDocuments({ userId, status: 'active' }),
        Category.countDocuments({ userId, isActive: true })
      ]);

      const hasData = expenseCount > 0 || categoryCount > 0;

      logger.info('User data check result:', {
        userId,
        expenseCount,
        categoryCount,
        hasData
      });

      res.status(200).json(
        ResponseHelpers.success({
          hasData,
          expenseCount,
          categoryCount
        }, 'Kullanıcı veri durumu kontrol edildi')
      );
    } catch (error) {
      logger.error('Error checking user data:', error);
      next(error);
    }
  }
);

/**
 * @route   DELETE /api/v1/users/clear-data
 * @desc    Clear all user data (expenses, categories) for migration
 * @access  Private
 */
router.delete('/clear-data',
  async (req, res, next) => {
    try {
      const { ResponseHelpers } = require('../utils/helpers');
      const logger = require('../utils/logger');
      const Expense = require('../models/Expense');
      const Category = require('../models/Category');

      const userId = req.user._id;

      logger.info('🔄 BACKEND CLEAR-DATA: Starting data clear for migration:', {
        userId: userId.toString(),
        userEmail: req.user.email
      });

      // First, check what data exists
      const existingExpenses = await Expense.countDocuments({ userId, status: 'active' });
      const existingCategories = await Category.countDocuments({ userId, isActive: true });

      logger.info('🔄 BACKEND CLEAR-DATA: Found existing data:', {
        userId: userId.toString(),
        existingExpenses,
        existingCategories
      });

      // Delete all user expenses and categories
      logger.info('🔄 BACKEND CLEAR-DATA: Deleting data...');
      const [deletedExpenses, deletedCategories] = await Promise.all([
        Expense.deleteMany({ userId, status: 'active' }),
        Category.deleteMany({ userId, isActive: true })
      ]);

      logger.info('✅ BACKEND CLEAR-DATA: User data cleared successfully:', {
        userId: userId.toString(),
        deletedExpenses: deletedExpenses.deletedCount,
        deletedCategories: deletedCategories.deletedCount,
        expectedExpenses: existingExpenses,
        expectedCategories: existingCategories
      });

      res.status(200).json(
        ResponseHelpers.success({
          deletedExpenses: deletedExpenses.deletedCount,
          deletedCategories: deletedCategories.deletedCount
        }, 'Kullanıcı verileri başarıyla temizlendi')
      );
    } catch (error) {
      logger.error('Error clearing user data:', error);
      next(error);
    }
  }
);

/**
 * @route   GET /api/v1/users/stats
 * @desc    Get user statistics (Admin only)
 * @access  Private/Admin
 */
router.get('/stats',
  requireAdmin,
  async (req, res, next) => {
    try {
      const { ResponseHelpers } = require('../utils/helpers');

      const stats = await User.getUserStats();

      res.status(200).json(
        ResponseHelpers.success(stats[0] || {
          totalUsers: 0,
          activeUsers: 0,
          premiumUsers: 0,
          avgExpenses: 0,
          avgAmount: 0
        }, 'Kullanıcı istatistikleri getirildi')
      );
    } catch (error) {
      next(error);
    }
  }
);

/**
 * @route   GET /api/v1/users/mode
 * @desc    Get current user tracking mode
 * @access  Private
 */
router.get('/mode', async (req, res, next) => {
  try {
    console.log('🎯 GET /mode route hit - user:', req.user?._id, 'firebaseUid:', req.user?.firebaseUid);
    const userId = req.user.firebaseUid;

    const user = await User.findOne({ firebaseUid: userId });
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Kullanıcı bulunamadı'
      });
    }

    let defaultField = null;
    if (user.preferences.trackingMode === 'detailed') {
      defaultField = await Field.findOne({
        userId: user._id,
        isDefault: true,
        isActive: true
      });
    }

    res.json({
      success: true,
      data: {
        mode: user.preferences.trackingMode,
        activatedAt: user.preferences.detailedModeActivatedAt,
        defaultField: defaultField ? {
          id: defaultField._id,
          name: defaultField.name
        } : null
      }
    });

  } catch (error) {
    Logger.error('Error getting user mode:', error);
    next(error);
  }
});

/**
 * @route   POST /api/v1/users/mode
 * @desc    Switch user tracking mode (simple/detailed)
 * @access  Private
 * @body    { mode: 'simple' | 'detailed' }
 */
router.post('/mode',
  [
    require('express-validator').body('mode')
      .isIn(['simple', 'detailed'])
      .withMessage('Mod simple veya detailed olmalıdır'),

    require('../middleware/validation').handleValidationErrors,

    // TWO-MODE SYSTEM: Validation middleware
    validateModeSwitch,
    validatePermissions,
    // requiresConfirmation, // Temporarily disabled for testing
    logModeSwitch
  ],
  async (req, res, next) => {
    try {
      console.log('🎯 POST /mode route hit - body:', req.body, 'user:', req.user?._id);
      const userId = req.user.firebaseUid;
      const { mode } = req.body;

      // Get current user
      const user = await User.findOne({ firebaseUid: userId });
      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'Kullanıcı bulunamadı'
        });
      }

      const currentMode = user.preferences.trackingMode;

      // If already in requested mode, return success
      if (currentMode === mode) {
        return res.json({
          success: true,
          message: `Zaten ${mode === 'detailed' ? 'detaylı' : 'basit'} moddasınız`,
          data: {
            mode: currentMode,
            activatedAt: user.preferences.detailedModeActivatedAt
          }
        });
      }

      // Update user preferences
      user.preferences.trackingMode = mode;

      let defaultField = null;

      // If switching to detailed mode
      if (mode === 'detailed') {
        user.preferences.detailedModeActivatedAt = new Date();

        // Create default field if doesn't exist
        try {
          defaultField = await createDefaultField(user._id);
          Logger.info(`Default field created for user ${userId}: ${defaultField.name}`);
        } catch (error) {
          Logger.error('Error creating default field:', error);
          // Continue anyway, field can be created later
        }
      }

      await user.save();

      Logger.info(`User ${userId} switched to ${mode} mode`);

      res.json({
        success: true,
        message: `${mode === 'detailed' ? 'Detaylı' : 'Basit'} moda başarıyla geçildi`,
        data: {
          mode: mode,
          activatedAt: user.preferences.detailedModeActivatedAt,
          defaultField: defaultField ? {
            id: defaultField._id,
            name: defaultField.name
          } : null
        }
      });

    } catch (error) {
      Logger.error('Error switching user mode:', error);
      next(error);
    }
  }
);

/**
 * @route   GET /api/v1/users/:id
 * @desc    Get user by ID (Admin only)
 * @access  Private/Admin
 */
router.get('/:id',
  requireAdmin,
  paramValidation.mongoId('id'),
  async (req, res, next) => {
    try {
      const { ResponseHelpers } = require('../utils/helpers');
      const { notFoundError } = require('../middleware/errorHandler');
      
      const user = await User.findById(req.params.id)
        .select('-firebaseUid -devices -dataRetention');

      if (!user) {
        throw notFoundError('Kullanıcı');
      }

      // Get user's expense statistics
      const expenseStats = await Expense.getExpenseStats(user._id);
      const categoryStats = await Category.getCategoryStats(user._id);

      const userData = {
        ...user.toObject(),
        expenseStats: expenseStats[0] || {},
        categoryStats: categoryStats[0] || {}
      };

      res.status(200).json(
        ResponseHelpers.success(userData, 'Kullanıcı detayları getirildi')
      );
    } catch (error) {
      next(error);
    }
  }
);

/**
 * @route   PUT /api/v1/users/:id/status
 * @desc    Update user status (Admin only)
 * @access  Private/Admin
 */
router.put('/:id/status',
  requireAdmin,
  paramValidation.mongoId('id'),
  [
    require('express-validator').body('status')
      .isIn(['active', 'inactive', 'suspended', 'deleted'])
      .withMessage('Geçersiz durum'),
    
    require('express-validator').body('reason')
      .optional()
      .isLength({ max: 500 })
      .withMessage('Sebep en fazla 500 karakter olabilir'),
    
    require('../middleware/validation').handleValidationErrors
  ],
  async (req, res, next) => {
    try {
      const { ResponseHelpers } = require('../utils/helpers');
      const { notFoundError } = require('../middleware/errorHandler');
      const logger = require('../utils/logger');
      
      const { status, reason } = req.body;

      const user = await User.findById(req.params.id);

      if (!user) {
        throw notFoundError('Kullanıcı');
      }

      const oldStatus = user.status;
      user.status = status;
      await user.save();

      logger.info('User status updated by admin:', {
        adminId: req.user._id,
        userId: user._id,
        oldStatus,
        newStatus: status,
        reason
      });

      res.status(200).json(
        ResponseHelpers.success({
          userId: user._id,
          oldStatus,
          newStatus: status
        }, 'Kullanıcı durumu güncellendi')
      );
    } catch (error) {
      next(error);
    }
  }
);

/**
 * @route   PUT /api/v1/users/:id/type
 * @desc    Update user type (Admin only)
 * @access  Private/Admin
 */
router.put('/:id/type',
  requireAdmin,
  paramValidation.mongoId('id'),
  [
    require('express-validator').body('type')
      .isIn(['free', 'premium', 'admin'])
      .withMessage('Geçersiz kullanıcı türü'),
    
    require('../middleware/validation').handleValidationErrors
  ],
  async (req, res, next) => {
    try {
      const { ResponseHelpers } = require('../utils/helpers');
      const { notFoundError } = require('../middleware/errorHandler');
      const logger = require('../utils/logger');
      
      const { type } = req.body;

      const user = await User.findById(req.params.id);

      if (!user) {
        throw notFoundError('Kullanıcı');
      }

      const oldType = user.type;
      user.type = type;
      user.isAdmin = type === 'admin';
      await user.save();

      logger.info('User type updated by admin:', {
        adminId: req.user._id,
        userId: user._id,
        oldType,
        newType: type
      });

      res.status(200).json(
        ResponseHelpers.success({
          userId: user._id,
          oldType,
          newType: type
        }, 'Kullanıcı türü güncellendi')
      );
    } catch (error) {
      next(error);
    }
  }
);

/**
 * @route   GET /api/v1/users/:id/expenses
 * @desc    Get user's expenses (Admin only)
 * @access  Private/Admin
 */
router.get('/:id/expenses',
  requireAdmin,
  paramValidation.mongoId('id'),
  [
    require('express-validator').query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Sayfa numarası 1 veya daha büyük olmalıdır'),
    
    require('express-validator').query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit 1 ile 100 arasında olmalıdır'),
    
    require('../middleware/validation').handleValidationErrors
  ],
  async (req, res, next) => {
    try {
      const { ResponseHelpers } = require('../utils/helpers');
      const { PAGINATION } = require('../utils/constants');
      
      const {
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT
      } = req.query;

      // Verify user exists
      const user = await User.findById(req.params.id);
      if (!user) {
        throw notFoundError('Kullanıcı');
      }

      // Pagination
      const pageNum = parseInt(page);
      const limitNum = Math.min(parseInt(limit), PAGINATION.MAX_LIMIT);
      const skip = (pageNum - 1) * limitNum;

      // Get expenses
      const [expenses, total] = await Promise.all([
        Expense.find({ userId: req.params.id, status: 'active' })
          .populate('categoryId', 'name emoji color')
          .sort({ date: -1 })
          .skip(skip)
          .limit(limitNum)
          .lean(),
        Expense.countDocuments({ userId: req.params.id, status: 'active' })
      ]);

      res.status(200).json(
        ResponseHelpers.paginated(expenses, pageNum, limitNum, total)
      );
    } catch (error) {
      next(error);
    }
  }
);

/**
 * @route   DELETE /api/v1/users/:id
 * @desc    Delete user account (Admin only)
 * @access  Private/Admin
 */
router.delete('/:id',
  requireAdmin,
  rateLimiter.strict,
  paramValidation.mongoId('id'),
  [
    require('express-validator').body('reason')
      .notEmpty()
      .withMessage('Silme sebebi gerekli')
      .isLength({ max: 500 })
      .withMessage('Sebep en fazla 500 karakter olabilir'),
    
    require('../middleware/validation').handleValidationErrors
  ],
  async (req, res, next) => {
    try {
      const { ResponseHelpers } = require('../utils/helpers');
      const { notFoundError } = require('../middleware/errorHandler');
      const logger = require('../utils/logger');
      
      const { reason } = req.body;

      const user = await User.findById(req.params.id);

      if (!user) {
        throw notFoundError('Kullanıcı');
      }

      // Prevent admin from deleting themselves
      if (user._id.toString() === req.user._id.toString()) {
        return res.status(400).json(
          ResponseHelpers.error('CANNOT_DELETE_SELF', 'Kendi hesabınızı silemezsiniz')
        );
      }

      // Soft delete user
      user.status = 'deleted';
      user.email = `deleted_${Date.now()}_${user.email}`;
      user.name = 'Deleted User';
      await user.save();

      // Mark user's expenses as deleted
      await Expense.updateMany(
        { userId: user._id },
        { status: 'deleted' }
      );

      // Mark user's categories as inactive
      await Category.updateMany(
        { userId: user._id },
        { isActive: false }
      );

      logger.info('User deleted by admin:', {
        adminId: req.user._id,
        deletedUserId: user._id,
        reason
      });

      res.status(200).json(
        ResponseHelpers.success(null, 'Kullanıcı hesabı silindi')
      );
    } catch (error) {
      next(error);
    }
  }
);



module.exports = router;
