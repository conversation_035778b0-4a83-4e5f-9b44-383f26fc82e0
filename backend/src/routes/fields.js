/**
 * Field Routes for Çiftçi Not Defterim
 * Handles field management endpoints for detailed tracking mode
 */

const express = require('express');
const router = express.Router();
const { authenticateUser } = require('../middleware/auth');
const fieldController = require('../controllers/fieldController');

// Field validation schemas
const fieldValidationSchema = {
  name: {
    type: 'string',
    required: true,
    minLength: 1,
    maxLength: 50,
    trim: true
  },
  size: {
    type: 'object',
    properties: {
      value: {
        type: 'number',
        minimum: 0
      },
      unit: {
        type: 'string',
        enum: ['dekar', 'dönüm', 'hectare', 'acre']
      }
    }
  },
  location: {
    type: 'object',
    properties: {
      address: {
        type: 'string',
        maxLength: 200
      },
      coordinates: {
        type: 'object',
        properties: {
          latitude: {
            type: 'number',
            minimum: -90,
            maximum: 90
          },
          longitude: {
            type: 'number',
            minimum: -180,
            maximum: 180
          }
        }
      }
    }
  },
  soilType: {
    type: 'string',
    enum: ['clay', 'sandy', 'loamy', 'silty', 'peaty', 'chalky', 'other']
  },
  irrigationType: {
    type: 'string',
    enum: ['drip', 'sprinkler', 'flood', 'manual', 'none']
  },
  notes: {
    type: 'string',
    maxLength: 200
  },
  isDefault: {
    type: 'boolean'
  }
};

const updateFieldValidationSchema = {
  ...fieldValidationSchema,
  name: {
    ...fieldValidationSchema.name,
    required: false
  }
};

// Apply authentication middleware to all routes
router.use(authenticateUser);

/**
 * @route   GET /api/v1/fields
 * @desc    Get all fields for authenticated user
 * @access  Private
 * @query   includeInactive - Include inactive fields (default: false)
 */
router.get('/', fieldController.getFields);

/**
 * @route   GET /api/v1/fields/:id
 * @desc    Get single field by ID
 * @access  Private
 */
router.get('/:id', fieldController.getField);

/**
 * @route   POST /api/v1/fields
 * @desc    Create new field
 * @access  Private
 * @body    Field data (name, size, location, etc.)
 */
router.post('/',
  fieldController.createField
);

/**
 * @route   PUT /api/v1/fields/:id
 * @desc    Update field
 * @access  Private
 * @body    Updated field data
 */
router.put('/:id',
  fieldController.updateField
);

/**
 * @route   DELETE /api/v1/fields/:id
 * @desc    Delete field (soft delete)
 * @access  Private
 */
router.delete('/:id', fieldController.deleteField);

/**
 * @route   GET /api/v1/fields/:id/stats
 * @desc    Get field statistics
 * @access  Private
 */
router.get('/:id/stats', fieldController.getFieldStats);

/**
 * @route   POST /api/v1/fields/:id/set-default
 * @desc    Set field as default
 * @access  Private
 */
router.post('/:id/set-default', fieldController.setDefaultField);

// Error handling middleware for field routes
router.use((error, req, res, next) => {
  console.error('Field route error:', error);
  
  if (error.name === 'ValidationError') {
    return res.status(400).json({
      success: false,
      message: 'Geçersiz tarla bilgileri',
      errors: Object.values(error.errors).map(err => err.message)
    });
  }
  
  if (error.name === 'CastError') {
    return res.status(400).json({
      success: false,
      message: 'Geçersiz tarla ID\'si'
    });
  }
  
  res.status(500).json({
    success: false,
    message: 'Sunucu hatası',
    error: process.env.NODE_ENV === 'development' ? error.message : undefined
  });
});

module.exports = router;
