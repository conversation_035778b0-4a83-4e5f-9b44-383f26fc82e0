/**
 * Import Routes for Çiftçi Not Defterim
 * Handles Excel import and AI-powered data processing endpoints
 */

const express = require('express');
const rateLimit = require('express-rate-limit');
const { authenticateUser } = require('../middleware/auth');
const fileUploadMiddleware = require('../middleware/fileUpload');
const importSecurityMiddleware = require('../middleware/importSecurity');
const importController = require('../controllers/importController');
const aiController = require('../controllers/aiController');
const logger = require('../utils/logger');

const router = express.Router();

// Rate limiting for import operations
const importRateLimit = rateLimit({
  windowMs: 10 * 60 * 1000, // 10 minutes
  max: 10, // 10 requests per 10 minutes
  message: {
    success: false,
    error: {
      code: 'RATE_LIMIT_EXCEEDED',
      message: 'Çok fazla import isteği. 10 dakika sonra tekrar deneyin.'
    }
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res) => {
    logger.warn('Import rate limit exceeded:', {
      ip: req.ip,
      userId: req.user?.id,
      userAgent: req.get('User-Agent')
    });
    
    res.status(429).json({
      success: false,
      error: {
        code: 'RATE_LIMIT_EXCEEDED',
        message: 'Çok fazla import isteği. 10 dakika sonra tekrar deneyin.'
      }
    });
  }
});

// Template download rate limiting (more lenient)
const templateRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 50, // 50 requests per hour
  message: {
    success: false,
    error: {
      code: 'RATE_LIMIT_EXCEEDED',
      message: 'Çok fazla template indirme isteği. 1 saat sonra tekrar deneyin.'
    }
  },
  standardHeaders: true,
  legacyHeaders: false
});

/**
 * @route GET /api/v1/import/template
 * @desc Download Excel import template
 * @access Private (Google Sign-In required)
 */
router.get('/template',
  templateRateLimit,
  ...importSecurityMiddleware.getSecurityMiddleware(),
  async (req, res) => {
    try {
      logger.info('Template download request:', {
        userId: req.user?.id,
        userEmail: req.user?.email,
        ip: req.ip
      });

      await importController.downloadTemplate(req, res);
    } catch (error) {
      logger.error('Template route error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Sunucu hatası'
        }
      });
    }
  }
);

/**
 * @route POST /api/v1/import/excel
 * @desc Upload and process Excel file
 * @access Private (Google Sign-In required)
 */
router.post('/excel',
  importSecurityMiddleware.createImportRateLimit(),
  ...importSecurityMiddleware.getFileSecurityMiddleware(),
  ...fileUploadMiddleware.getExcelUploadMiddleware(),
  fileUploadMiddleware.cleanupMiddleware,
  async (req, res) => {
    try {
      logger.info('Excel upload request:', {
        userId: req.user?.id,
        userEmail: req.user?.email,
        fileName: req.fileInfo?.originalName,
        fileSize: req.fileInfo?.size,
        ip: req.ip
      });

      await importController.uploadExcel(req, res);
    } catch (error) {
      logger.error('Excel upload route error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Sunucu hatası'
        }
      });
    }
  }
);

/**
 * @route GET /api/v1/import/history
 * @desc Get import history for user
 * @access Private (Google Sign-In required)
 */
router.get('/history',
  authenticateUser,
  async (req, res) => {
    try {
      logger.info('Import history request:', {
        userId: req.user?.id,
        userEmail: req.user?.email,
        ip: req.ip
      });

      await importController.getImportHistory(req, res);
    } catch (error) {
      logger.error('Import history route error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Sunucu hatası'
        }
      });
    }
  }
);

/**
 * @route POST /api/v1/import/ai-chat
 * @desc Process text input with AI for import
 * @access Private (Google Sign-In required)
 */
router.post('/ai-chat',
  importSecurityMiddleware.createAIRateLimit(),
  ...importSecurityMiddleware.getSecurityMiddleware(),
  aiController.addRequestTime,
  async (req, res) => {
    try {
      logger.info('AI chat import request:', {
        userId: req.user?.id,
        userEmail: req.user?.email,
        textLength: req.body?.text?.length || 0,
        ip: req.ip
      });

      await aiController.processText(req, res);
    } catch (error) {
      logger.error('AI chat route error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Sunucu hatası'
        }
      });
    }
  }
);

/**
 * @route POST /api/v1/import/ai-question
 * @desc Ask follow-up question to AI
 * @access Private (Google Sign-In required)
 */
router.post('/ai-question',
  importSecurityMiddleware.createAIRateLimit(),
  ...importSecurityMiddleware.getSecurityMiddleware(),
  aiController.addRequestTime,
  async (req, res) => {
    try {
      logger.info('AI question request:', {
        userId: req.user?.id,
        userEmail: req.user?.email,
        ip: req.ip
      });

      await aiController.askQuestion(req, res);
    } catch (error) {
      logger.error('AI question route error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Sunucu hatası'
        }
      });
    }
  }
);

/**
 * @route POST /api/v1/import/ai-validate
 * @desc Validate AI-processed data
 * @access Private (Google Sign-In required)
 */
router.post('/ai-validate',
  ...importSecurityMiddleware.getSecurityMiddleware(),
  async (req, res) => {
    try {
      logger.info('AI validation request:', {
        userId: req.user?.id,
        userEmail: req.user?.email,
        dataCount: req.body?.data?.length || 0,
        ip: req.ip
      });

      await aiController.validateData(req, res);
    } catch (error) {
      logger.error('AI validation route error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Sunucu hatası'
        }
      });
    }
  }
);

/**
 * @route GET /api/v1/import/ai-status
 * @desc Get AI service status
 * @access Private (Google Sign-In required)
 */
router.get('/ai-status',
  ...importSecurityMiddleware.getSecurityMiddleware(),
  async (req, res) => {
    try {
      await aiController.getStatus(req, res);
    } catch (error) {
      logger.error('AI status route error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Sunucu hatası'
        }
      });
    }
  }
);

/**
 * Error handling middleware for import routes
 */
router.use((error, req, res, next) => {
  logger.error('Import route error:', {
    error: error.message,
    stack: error.stack,
    userId: req.user?.id,
    route: req.route?.path,
    method: req.method,
    ip: req.ip
  });

  // Handle specific error types
  if (error.code === 'LIMIT_FILE_SIZE') {
    return res.status(400).json({
      success: false,
      error: {
        code: 'FILE_TOO_LARGE',
        message: 'Dosya boyutu 10MB\'dan büyük olamaz'
      }
    });
  }

  if (error.code === 'INVALID_FILE_TYPE') {
    return res.status(400).json({
      success: false,
      error: {
        code: 'INVALID_FILE_TYPE',
        message: 'Sadece .xlsx ve .xls dosyaları kabul edilir'
      }
    });
  }

  // Generic error response
  res.status(500).json({
    success: false,
    error: {
      code: 'INTERNAL_ERROR',
      message: 'Sunucu hatası'
    }
  });
});

module.exports = router;
