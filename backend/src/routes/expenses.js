/**
 * Expense Routes for Çiftçi Not Defterim
 * Handles all expense-related API endpoints
 */

const express = require('express');
const router = express.Router();

// Import controllers
const expenseController = require('../controllers/expenseController');

// Import middleware
const { authenticateUser } = require('../middleware/auth');
const { expenseValidation, paramValidation } = require('../middleware/validation');
const rateLimiter = require('../middleware/rateLimiter');

// Apply authentication to all routes
router.use(authenticateUser);

/**
 * @route   GET /api/v1/expenses
 * @desc    Get user expenses with filtering and pagination
 * @access  Private
 * @query   page, limit, startDate, endDate, categoryId, seasonId, fieldId, cropId, trackingMode, search, sortBy, sortOrder
 */
router.get('/',
  expenseValidation.list,
  expenseController.getExpenses
);

/**
 * @route   GET /api/v1/expenses/stats
 * @desc    Get expense statistics
 * @access  Private
 * @query   startDate, endDate, groupBy
 */
router.get('/stats',
  [
    require('express-validator').query('startDate')
      .optional()
      .isISO8601()
      .withMessage('Geçersiz başlangıç tarihi formatı'),
    
    require('express-validator').query('endDate')
      .optional()
      .isISO8601()
      .withMessage('Geçersiz bitiş tarihi formatı'),
    
    require('express-validator').query('groupBy')
      .optional()
      .isIn(['category', 'season', 'general'])
      .withMessage('Geçersiz gruplama türü'),
    
    require('../middleware/validation').handleValidationErrors
  ],
  expenseController.getExpenseStats
);

/**
 * @route   POST /api/v1/expenses
 * @desc    Create new expense
 * @access  Private
 */
router.post('/',
  rateLimiter.endpointSpecific.createExpense,
  expenseValidation.create,
  expenseController.createExpense
);

/**
 * @route   GET /api/v1/expenses/:id
 * @desc    Get single expense by ID
 * @access  Private
 */
router.get('/:id',
  paramValidation.mongoId('id'),
  expenseController.getExpenseById
);

/**
 * @route   PUT /api/v1/expenses/:id
 * @desc    Update expense
 * @access  Private
 */
router.put('/:id',
  expenseValidation.update,
  expenseController.updateExpense
);

/**
 * @route   DELETE /api/v1/expenses/:id
 * @desc    Delete expense (soft delete)
 * @access  Private
 */
router.delete('/:id',
  paramValidation.mongoId('id'),
  expenseController.deleteExpense
);

/**
 * @route   POST /api/v1/expenses/:id/photos
 * @desc    Add photo to expense
 * @access  Private
 */
router.post('/:id/photos',
  paramValidation.mongoId('id'),
  [
    require('express-validator').body('photoId')
      .notEmpty()
      .withMessage('Photo ID gerekli'),
    
    require('express-validator').body('photoUrl')
      .isURL()
      .withMessage('Geçerli bir foto URL\'si gerekli'),
    
    require('../middleware/validation').handleValidationErrors
  ],
  async (req, res, next) => {
    try {
      const { asyncHandler, notFoundError } = require('../middleware/errorHandler');
      const { ResponseHelpers } = require('../utils/helpers');
      const Expense = require('../models/Expense');
      
      const expense = await Expense.findOne({
        _id: req.params.id,
        userId: req.user._id,
        status: 'active'
      });

      if (!expense) {
        throw notFoundError('Gider');
      }

      await expense.addPhoto({
        id: req.body.photoId,
        url: req.body.photoUrl,
        thumbnail: req.body.thumbnailUrl,
        filename: req.body.filename,
        size: req.body.size,
        mimeType: req.body.mimeType
      });

      res.status(200).json(
        ResponseHelpers.success(expense.photos, 'Fotoğraf eklendi')
      );
    } catch (error) {
      next(error);
    }
  }
);

/**
 * @route   DELETE /api/v1/expenses/:id/photos/:photoId
 * @desc    Remove photo from expense
 * @access  Private
 */
router.delete('/:id/photos/:photoId',
  paramValidation.mongoId('id'),
  [
    require('express-validator').param('photoId')
      .notEmpty()
      .withMessage('Photo ID gerekli'),
    
    require('../middleware/validation').handleValidationErrors
  ],
  async (req, res, next) => {
    try {
      const { asyncHandler, notFoundError } = require('../middleware/errorHandler');
      const { ResponseHelpers } = require('../utils/helpers');
      const Expense = require('../models/Expense');
      
      const expense = await Expense.findOne({
        _id: req.params.id,
        userId: req.user._id,
        status: 'active'
      });

      if (!expense) {
        throw notFoundError('Gider');
      }

      await expense.removePhoto(req.params.photoId);

      res.status(200).json(
        ResponseHelpers.success(expense.photos, 'Fotoğraf silindi')
      );
    } catch (error) {
      next(error);
    }
  }
);

/**
 * @route   POST /api/v1/expenses/:id/tags
 * @desc    Add tag to expense
 * @access  Private
 */
router.post('/:id/tags',
  paramValidation.mongoId('id'),
  [
    require('express-validator').body('tag')
      .notEmpty()
      .withMessage('Etiket gerekli')
      .isLength({ min: 2, max: 30 })
      .withMessage('Etiket 2-30 karakter arasında olmalıdır')
      .trim(),
    
    require('../middleware/validation').handleValidationErrors
  ],
  async (req, res, next) => {
    try {
      const { asyncHandler, notFoundError } = require('../middleware/errorHandler');
      const { ResponseHelpers } = require('../utils/helpers');
      const Expense = require('../models/Expense');
      
      const expense = await Expense.findOne({
        _id: req.params.id,
        userId: req.user._id,
        status: 'active'
      });

      if (!expense) {
        throw notFoundError('Gider');
      }

      await expense.addTag(req.body.tag);

      res.status(200).json(
        ResponseHelpers.success(expense.tags, 'Etiket eklendi')
      );
    } catch (error) {
      next(error);
    }
  }
);

/**
 * @route   DELETE /api/v1/expenses/:id/tags/:tag
 * @desc    Remove tag from expense
 * @access  Private
 */
router.delete('/:id/tags/:tag',
  paramValidation.mongoId('id'),
  async (req, res, next) => {
    try {
      const { asyncHandler, notFoundError } = require('../middleware/errorHandler');
      const { ResponseHelpers } = require('../utils/helpers');
      const Expense = require('../models/Expense');
      
      const expense = await Expense.findOne({
        _id: req.params.id,
        userId: req.user._id,
        status: 'active'
      });

      if (!expense) {
        throw notFoundError('Gider');
      }

      await expense.removeTag(decodeURIComponent(req.params.tag));

      res.status(200).json(
        ResponseHelpers.success(expense.tags, 'Etiket silindi')
      );
    } catch (error) {
      next(error);
    }
  }
);

/**
 * @route   GET /api/v1/expenses/export/csv
 * @desc    Export expenses as CSV
 * @access  Private
 */
router.get('/export/csv',
  [
    require('express-validator').query('startDate')
      .optional()
      .isISO8601()
      .withMessage('Geçersiz başlangıç tarihi formatı'),
    
    require('express-validator').query('endDate')
      .optional()
      .isISO8601()
      .withMessage('Geçersiz bitiş tarihi formatı'),
    
    require('../middleware/validation').handleValidationErrors
  ],
  async (req, res, next) => {
    try {
      const { startDate, endDate } = req.query;
      const Expense = require('../models/Expense');
      const { TurkishHelpers } = require('../utils/helpers');
      
      const query = { 
        userId: req.user._id, 
        status: 'active' 
      };

      if (startDate || endDate) {
        query.date = {};
        if (startDate) query.date.$gte = new Date(startDate);
        if (endDate) query.date.$lte = new Date(endDate);
      }

      const expenses = await Expense.find(query)
        .populate('categoryId', 'name emoji')
        .sort({ date: -1 });

      // Generate CSV
      const csvHeader = 'Tarih,Kategori,Tutar,Açıklama,Sezon,Mahsul,Alan\n';
      const csvRows = expenses.map(expense => {
        return [
          TurkishHelpers.formatDate(expense.date),
          `"${expense.categoryId.name}"`,
          expense.amount.toString().replace('.', ','),
          `"${expense.description || ''}"`,
          expense.seasonId,
          `"${expense.cropType || ''}"`,
          `"${expense.farmArea || ''}"`
        ].join(',');
      }).join('\n');

      const csv = csvHeader + csvRows;

      res.setHeader('Content-Type', 'text/csv; charset=utf-8');
      res.setHeader('Content-Disposition', `attachment; filename="giderler_${new Date().toISOString().split('T')[0]}.csv"`);
      res.send('\ufeff' + csv); // Add BOM for Turkish characters
    } catch (error) {
      next(error);
    }
  }
);

module.exports = router;
