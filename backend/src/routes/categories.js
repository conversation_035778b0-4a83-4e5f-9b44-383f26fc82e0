/**
 * Category Routes for Çiftçi Not Defterim
 * Handles default and custom category management
 */

const express = require('express');
const router = express.Router();

// Import controllers
const categoryController = require('../controllers/categoryController');

// Import middleware
const { authenticateUser } = require('../middleware/auth');
const { categoryValidation, paramValidation } = require('../middleware/validation');
const rateLimiter = require('../middleware/rateLimiter');

// Apply authentication to all routes
router.use(authenticateUser);

/**
 * @route   GET /api/v1/categories
 * @desc    Get user categories (default + custom)
 * @access  Private
 * @query   includeStats, season
 */
router.get('/',
  [
    require('express-validator').query('includeStats')
      .optional()
      .isBoolean()
      .withMessage('includeStats boolean değer olmalıdır'),
    
    require('express-validator').query('season')
      .optional()
      .isIn(['spring', 'summer', 'autumn', 'winter'])
      .withMessage('Geçersiz sezon'),
    
    require('../middleware/validation').handleValidationErrors
  ],
  categoryController.getCategories
);

/**
 * @route   GET /api/v1/categories/popular
 * @desc    Get popular categories
 * @access  Private
 */
router.get('/popular',
  [
    require('express-validator').query('limit')
      .optional()
      .isInt({ min: 1, max: 50 })
      .withMessage('Limit 1-50 arasında olmalıdır'),
    
    require('../middleware/validation').handleValidationErrors
  ],
  categoryController.getPopularCategories
);

/**
 * @route   GET /api/v1/categories/search
 * @desc    Search categories
 * @access  Private
 */
router.get('/search',
  [
    require('express-validator').query('q')
      .notEmpty()
      .withMessage('Arama terimi gerekli')
      .isLength({ min: 2, max: 50 })
      .withMessage('Arama terimi 2-50 karakter arasında olmalıdır')
      .trim(),
    
    require('../middleware/validation').handleValidationErrors
  ],
  categoryController.searchCategories
);

/**
 * @route   GET /api/v1/categories/stats
 * @desc    Get category statistics
 * @access  Private
 */
router.get('/stats',
  categoryController.getCategoryStats
);

/**
 * @route   POST /api/v1/categories
 * @desc    Create new custom category
 * @access  Private
 */
router.post('/',
  rateLimiter.endpointSpecific.createCategory,
  categoryValidation.create,
  categoryController.createCategory
);

/**
 * @route   POST /api/v1/categories/reset
 * @desc    Reset to default categories
 * @access  Private
 */
router.post('/reset',
  rateLimiter.strict, // Very limited for reset operations
  categoryController.resetToDefaults
);

/**
 * @route   GET /api/v1/categories/:id
 * @desc    Get single category by ID
 * @access  Private
 */
router.get('/:id',
  paramValidation.mongoId('id'),
  categoryController.getCategoryById
);

/**
 * @route   PUT /api/v1/categories/:id
 * @desc    Update custom category
 * @access  Private
 */
router.put('/:id',
  categoryValidation.update,
  categoryController.updateCategory
);

/**
 * @route   DELETE /api/v1/categories/:id
 * @desc    Delete custom category
 * @access  Private
 */
router.delete('/:id',
  paramValidation.mongoId('id'),
  categoryController.deleteCategory
);

/**
 * @route   POST /api/v1/categories/:id/duplicate
 * @desc    Duplicate category
 * @access  Private
 */
router.post('/:id/duplicate',
  paramValidation.mongoId('id'),
  [
    require('express-validator').body('name')
      .optional()
      .isLength({ min: 2, max: 50 })
      .withMessage('Kategori adı 2-50 karakter arasında olmalıdır')
      .trim(),
    
    require('../middleware/validation').handleValidationErrors
  ],
  categoryController.duplicateCategory
);

/**
 * @route   PATCH /api/v1/categories/:id/seasonal-relevance
 * @desc    Update seasonal relevance for category
 * @access  Private
 */
router.patch('/:id/seasonal-relevance',
  paramValidation.mongoId('id'),
  [
    require('express-validator').body('season')
      .isIn(['spring', 'summer', 'autumn', 'winter'])
      .withMessage('Geçersiz sezon'),
    
    require('express-validator').body('relevanceScore')
      .isInt({ min: 0, max: 10 })
      .withMessage('Relevans skoru 0-10 arasında olmalıdır'),
    
    require('../middleware/validation').handleValidationErrors
  ],
  async (req, res, next) => {
    try {
      const { asyncHandler, notFoundError } = require('../middleware/errorHandler');
      const { ResponseHelpers } = require('../utils/helpers');
      const Category = require('../models/Category');
      
      const category = await Category.findOne({
        _id: req.params.id,
        userId: req.user._id,
        isDefault: false,
        isActive: true
      });

      if (!category) {
        throw notFoundError('Kategori bulunamadı veya düzenleme yetkiniz yok');
      }

      await category.updateSeasonalRelevance(req.body.season, req.body.relevanceScore);

      res.status(200).json(
        ResponseHelpers.success(category.seasonalRelevance, 'Sezonsal relevans güncellendi')
      );
    } catch (error) {
      next(error);
    }
  }
);

/**
 * @route   GET /api/v1/categories/:id/expenses
 * @desc    Get expenses for specific category
 * @access  Private
 */
router.get('/:id/expenses',
  paramValidation.mongoId('id'),
  [
    require('express-validator').query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Sayfa numarası 1 veya daha büyük olmalıdır'),
    
    require('express-validator').query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit 1 ile 100 arasında olmalıdır'),
    
    require('express-validator').query('startDate')
      .optional()
      .isISO8601()
      .withMessage('Geçersiz başlangıç tarihi formatı'),
    
    require('express-validator').query('endDate')
      .optional()
      .isISO8601()
      .withMessage('Geçersiz bitiş tarihi formatı'),
    
    require('../middleware/validation').handleValidationErrors
  ],
  async (req, res, next) => {
    try {
      const { ResponseHelpers } = require('../utils/helpers');
      const { PAGINATION } = require('../utils/constants');
      const Category = require('../models/Category');
      const Expense = require('../models/Expense');
      
      // Verify category access
      const category = await Category.findOne({
        _id: req.params.id,
        $or: [
          { isDefault: true },
          { userId: req.user._id }
        ],
        isActive: true
      });

      if (!category) {
        throw notFoundError('Kategori');
      }

      const {
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT,
        startDate,
        endDate
      } = req.query;

      // Build query
      const query = {
        userId: req.user._id,
        categoryId: req.params.id,
        status: 'active'
      };

      // Date range filter
      if (startDate || endDate) {
        query.date = {};
        if (startDate) query.date.$gte = new Date(startDate);
        if (endDate) query.date.$lte = new Date(endDate);
      }

      // Pagination
      const pageNum = parseInt(page);
      const limitNum = Math.min(parseInt(limit), PAGINATION.MAX_LIMIT);
      const skip = (pageNum - 1) * limitNum;

      // Execute query
      const [expenses, total] = await Promise.all([
        Expense.find(query)
          .sort({ date: -1 })
          .skip(skip)
          .limit(limitNum)
          .lean(),
        Expense.countDocuments(query)
      ]);

      res.status(200).json(
        ResponseHelpers.paginated(expenses, pageNum, limitNum, total)
      );
    } catch (error) {
      next(error);
    }
  }
);

/**
 * @route   GET /api/v1/categories/:id/analytics
 * @desc    Get analytics for specific category
 * @access  Private
 */
router.get('/:id/analytics',
  paramValidation.mongoId('id'),
  [
    require('express-validator').query('period')
      .optional()
      .isIn(['week', 'month', 'quarter', 'year'])
      .withMessage('Geçersiz dönem'),
    
    require('../middleware/validation').handleValidationErrors
  ],
  async (req, res, next) => {
    try {
      const { ResponseHelpers, DateHelpers } = require('../utils/helpers');
      const Category = require('../models/Category');
      const Expense = require('../models/Expense');
      
      // Verify category access
      const category = await Category.findOne({
        _id: req.params.id,
        $or: [
          { isDefault: true },
          { userId: req.user._id }
        ],
        isActive: true
      });

      if (!category) {
        throw notFoundError('Kategori');
      }

      const { period = 'month' } = req.query;
      
      // Calculate date range based on period
      let startDate, endDate;
      const now = new Date();
      
      switch (period) {
        case 'week':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case 'quarter':
          startDate = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3, 1);
          break;
        case 'year':
          startDate = new Date(now.getFullYear(), 0, 1);
          break;
        default: // month
          startDate = new Date(now.getFullYear(), now.getMonth(), 1);
      }
      endDate = now;

      // Get analytics data
      const analytics = await Expense.aggregate([
        {
          $match: {
            userId: req.user._id,
            categoryId: category._id,
            status: 'active',
            date: { $gte: startDate, $lte: endDate }
          }
        },
        {
          $group: {
            _id: {
              $dateToString: {
                format: period === 'week' || period === 'month' ? '%Y-%m-%d' : '%Y-%m',
                date: '$date'
              }
            },
            totalAmount: { $sum: '$amount' },
            expenseCount: { $sum: 1 },
            avgAmount: { $avg: '$amount' }
          }
        },
        {
          $sort: { _id: 1 }
        }
      ]);

      res.status(200).json(
        ResponseHelpers.success({
          category: category.toPublicJSON(),
          period,
          startDate,
          endDate,
          analytics
        }, 'Kategori analitikleri getirildi')
      );
    } catch (error) {
      next(error);
    }
  }
);

module.exports = router;
