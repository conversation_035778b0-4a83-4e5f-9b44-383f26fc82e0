/**
 * Sync Routes for Çiftçi Not Defterim
 * Handles offline sync operations and conflict resolution
 */

const express = require('express');
const router = express.Router();

// Import controllers
const syncController = require('../controllers/syncController');

// Import middleware
const { authenticateUser } = require('../middleware/auth');
const { syncValidation } = require('../middleware/validation');
const rateLimiter = require('../middleware/rateLimiter');

// Apply authentication to all routes
router.use(authenticateUser);

/**
 * @route   POST /api/v1/sync/push
 * @desc    Push local changes to server
 * @access  Private
 */
router.post('/push',
  rateLimiter.sync,
  syncValidation.push,
  syncController.pushChanges
);

/**
 * @route   GET /api/v1/sync/pull
 * @desc    Pull server changes to local
 * @access  Private
 */
router.get('/pull',
  rateLimiter.sync,
  syncValidation.pull,
  syncController.pullChanges
);

/**
 * @route   GET /api/v1/sync/status
 * @desc    Get sync status
 * @access  Private
 */
router.get('/status',
  syncController.getSyncStatus
);

/**
 * @route   POST /api/v1/sync/resolve-conflict
 * @desc    Resolve sync conflict
 * @access  Private
 */
router.post('/resolve-conflict',
  [
    require('express-validator').body('itemId')
      .isMongoId()
      .withMessage('Geçersiz öğe ID\'si'),
    
    require('express-validator').body('resolution')
      .isIn(['use_server', 'use_local', 'merge'])
      .withMessage('Geçersiz çözüm türü'),
    
    require('express-validator').body('data')
      .optional()
      .isObject()
      .withMessage('Birleştirme verisi obje formatında olmalıdır'),
    
    require('../middleware/validation').handleValidationErrors
  ],
  syncController.resolveConflict
);

/**
 * @route   POST /api/v1/sync/full
 * @desc    Force full sync
 * @access  Private
 */
router.post('/full',
  rateLimiter.strict, // Very limited for full sync
  syncController.fullSync
);

/**
 * @route   GET /api/v1/sync/conflicts
 * @desc    Get all sync conflicts for user
 * @access  Private
 */
router.get('/conflicts',
  async (req, res, next) => {
    try {
      const { ResponseHelpers } = require('../utils/helpers');
      const Expense = require('../models/Expense');
      
      const conflicts = await Expense.find({
        userId: req.user._id,
        syncStatus: 'conflict'
      }).populate('categoryId', 'name emoji color').lean();

      res.status(200).json(
        ResponseHelpers.success(conflicts, 'Çakışmalar getirildi')
      );
    } catch (error) {
      next(error);
    }
  }
);

/**
 * @route   GET /api/v1/sync/pending
 * @desc    Get all pending sync items for user
 * @access  Private
 */
router.get('/pending',
  [
    require('express-validator').query('table')
      .optional()
      .isIn(['expenses', 'categories'])
      .withMessage('Geçersiz tablo adı'),
    
    require('../middleware/validation').handleValidationErrors
  ],
  async (req, res, next) => {
    try {
      const { ResponseHelpers } = require('../utils/helpers');
      const { table } = req.query;
      
      let pendingItems = [];

      if (!table || table === 'expenses') {
        const Expense = require('../models/Expense');
        const pendingExpenses = await Expense.find({
          userId: req.user._id,
          syncStatus: 'pending'
        }).populate('categoryId', 'name emoji color').lean();

        pendingItems = pendingItems.concat(
          pendingExpenses.map(item => ({ ...item, table: 'expenses' }))
        );
      }

      if (!table || table === 'categories') {
        const Category = require('../models/Category');
        const pendingCategories = await Category.find({
          userId: req.user._id,
          syncStatus: 'pending'
        }).lean();

        pendingItems = pendingItems.concat(
          pendingCategories.map(item => ({ ...item, table: 'categories' }))
        );
      }

      // Sort by updatedAt
      pendingItems.sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt));

      res.status(200).json(
        ResponseHelpers.success(pendingItems, 'Bekleyen öğeler getirildi')
      );
    } catch (error) {
      next(error);
    }
  }
);

/**
 * @route   POST /api/v1/sync/test
 * @desc    Test sync connectivity
 * @access  Private
 */
router.post('/test',
  async (req, res, next) => {
    try {
      const { ResponseHelpers } = require('../utils/helpers');
      
      // Simple connectivity test
      const testResult = {
        timestamp: new Date().toISOString(),
        userId: req.user._id,
        serverTime: new Date().getTime(),
        status: 'connected'
      };

      res.status(200).json(
        ResponseHelpers.success(testResult, 'Senkronizasyon bağlantısı başarılı')
      );
    } catch (error) {
      next(error);
    }
  }
);

/**
 * @route   DELETE /api/v1/sync/conflicts/:id
 * @desc    Delete/ignore specific conflict
 * @access  Private
 */
router.delete('/conflicts/:id',
  [
    require('express-validator').param('id')
      .isMongoId()
      .withMessage('Geçersiz çakışma ID\'si'),
    
    require('../middleware/validation').handleValidationErrors
  ],
  async (req, res, next) => {
    try {
      const { ResponseHelpers } = require('../utils/helpers');
      const Expense = require('../models/Expense');
      
      const expense = await Expense.findOne({
        _id: req.params.id,
        userId: req.user._id,
        syncStatus: 'conflict'
      });

      if (!expense) {
        return res.status(404).json(
          ResponseHelpers.error('CONFLICT_NOT_FOUND', 'Çakışma bulunamadı')
        );
      }

      // Mark as synced (ignore conflict)
      expense.syncStatus = 'synced';
      await expense.save();

      res.status(200).json(
        ResponseHelpers.success(null, 'Çakışma göz ardı edildi')
      );
    } catch (error) {
      next(error);
    }
  }
);

/**
 * @route   POST /api/v1/sync/batch-resolve
 * @desc    Resolve multiple conflicts at once
 * @access  Private
 */
router.post('/batch-resolve',
  [
    require('express-validator').body('resolutions')
      .isArray()
      .withMessage('Çözümler dizi formatında olmalıdır'),
    
    require('express-validator').body('resolutions.*.itemId')
      .isMongoId()
      .withMessage('Geçersiz öğe ID\'si'),
    
    require('express-validator').body('resolutions.*.resolution')
      .isIn(['use_server', 'use_local', 'merge', 'ignore'])
      .withMessage('Geçersiz çözüm türü'),
    
    require('../middleware/validation').handleValidationErrors
  ],
  async (req, res, next) => {
    try {
      const { ResponseHelpers } = require('../utils/helpers');
      const { resolutions } = req.body;
      const Expense = require('../models/Expense');
      
      const results = {
        processed: 0,
        errors: []
      };

      for (const resolution of resolutions) {
        try {
          const expense = await Expense.findOne({
            _id: resolution.itemId,
            userId: req.user._id,
            syncStatus: 'conflict'
          });

          if (!expense) {
            results.errors.push({
              itemId: resolution.itemId,
              error: 'Çakışma bulunamadı'
            });
            continue;
          }

          switch (resolution.resolution) {
            case 'use_server':
              expense.syncStatus = 'synced';
              break;
            case 'use_local':
              expense.syncStatus = 'pending';
              expense.syncVersion += 1;
              break;
            case 'merge':
              if (resolution.data) {
                Object.assign(expense, resolution.data);
              }
              expense.syncStatus = 'pending';
              expense.syncVersion += 1;
              break;
            case 'ignore':
              expense.syncStatus = 'synced';
              break;
          }

          await expense.save();
          results.processed++;

        } catch (error) {
          results.errors.push({
            itemId: resolution.itemId,
            error: error.message
          });
        }
      }

      res.status(200).json(
        ResponseHelpers.success(results, 'Toplu çözüm tamamlandı')
      );
    } catch (error) {
      next(error);
    }
  }
);

module.exports = router;
