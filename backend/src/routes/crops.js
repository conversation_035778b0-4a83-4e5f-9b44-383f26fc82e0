/**
 * Crop Routes for Çiftçi Not Defterim
 * Handles crop management endpoints for detailed tracking mode
 */

const express = require('express');
const router = express.Router();
const { authenticateUser } = require('../middleware/auth');
const cropController = require('../controllers/cropController');

// Crop validation schemas
const cropValidationSchema = {
  name: {
    type: 'string',
    required: true,
    minLength: 1,
    maxLength: 50,
    trim: true
  },
  nameTr: {
    type: 'string',
    required: true,
    minLength: 1,
    maxLength: 50,
    trim: true
  },
  category: {
    type: 'string',
    required: true,
    enum: ['tahil', 'sebze', 'meyve', 'baklagil', 'endüstriyel', 'diğer']
  },
  productionType: {
    type: 'string',
    required: true,
    enum: ['seasonal', 'continuous']
  },
  emoji: {
    type: 'string',
    maxLength: 10
  },
  description: {
    type: 'string',
    maxLength: 200
  },
  growingSeason: {
    type: 'object',
    properties: {
      startMonth: {
        type: 'number',
        minimum: 1,
        maximum: 12
      },
      endMonth: {
        type: 'number',
        minimum: 1,
        maximum: 12
      },
      duration: {
        type: 'number',
        minimum: 1
      }
    }
  },
  typicalExpenses: {
    type: 'array',
    items: {
      type: 'object',
      properties: {
        categoryName: {
          type: 'string'
        },
        estimatedCost: {
          type: 'object',
          properties: {
            min: { type: 'number' },
            max: { type: 'number' },
            currency: { type: 'string' }
          }
        },
        timing: {
          type: 'string'
        }
      }
    }
  }
};

const updateCropValidationSchema = {
  ...cropValidationSchema,
  name: {
    ...cropValidationSchema.name,
    required: false
  },
  nameTr: {
    ...cropValidationSchema.nameTr,
    required: false
  },
  category: {
    ...cropValidationSchema.category,
    required: false
  },
  productionType: {
    ...cropValidationSchema.productionType,
    required: false
  }
};

// Apply authentication middleware to all routes
router.use(authenticateUser);

/**
 * @route   GET /api/v1/crops
 * @desc    Get all crops (system default + user custom)
 * @access  Private
 * @query   category - Filter by category
 * @query   productionType - Filter by production type
 * @query   includeInactive - Include inactive crops (default: false)
 */
router.get('/', cropController.getCrops);

/**
 * @route   GET /api/v1/crops/category/:category
 * @desc    Get crops by category
 * @access  Private
 */
router.get('/category/:category', cropController.getCropsByCategory);

/**
 * @route   GET /api/v1/crops/production-type/:type
 * @desc    Get crops by production type
 * @access  Private
 */
router.get('/production-type/:type', cropController.getCropsByProductionType);

/**
 * @route   POST /api/v1/crops/initialize-defaults
 * @desc    Initialize default crops (admin only)
 * @access  Private
 */
router.post('/initialize-defaults', cropController.initializeDefaults);

/**
 * @route   GET /api/v1/crops/:id
 * @desc    Get single crop by ID
 * @access  Private
 */
router.get('/:id', cropController.getCrop);

/**
 * @route   POST /api/v1/crops
 * @desc    Create new custom crop
 * @access  Private
 * @body    Crop data (name, nameTr, category, productionType, etc.)
 */
router.post('/',
  cropController.createCrop
);

/**
 * @route   PUT /api/v1/crops/:id
 * @desc    Update custom crop
 * @access  Private
 * @body    Updated crop data
 */
router.put('/:id',
  cropController.updateCrop
);

/**
 * @route   DELETE /api/v1/crops/:id
 * @desc    Delete custom crop (soft delete)
 * @access  Private
 */
router.delete('/:id', cropController.deleteCrop);

/**
 * @route   GET /api/v1/crops/:id/stats
 * @desc    Get crop statistics
 * @access  Private
 */
router.get('/:id/stats', cropController.getCropStats);

// Error handling middleware for crop routes
router.use((error, req, res, next) => {
  console.error('Crop route error:', error);
  
  if (error.name === 'ValidationError') {
    return res.status(400).json({
      success: false,
      message: 'Geçersiz ürün bilgileri',
      errors: Object.values(error.errors).map(err => err.message)
    });
  }
  
  if (error.name === 'CastError') {
    return res.status(400).json({
      success: false,
      message: 'Geçersiz ürün ID\'si'
    });
  }
  
  if (error.code === 11000) {
    return res.status(400).json({
      success: false,
      message: 'Bu isimde bir ürün zaten mevcut'
    });
  }
  
  res.status(500).json({
    success: false,
    message: 'Sunucu hatası',
    error: process.env.NODE_ENV === 'development' ? error.message : undefined
  });
});

module.exports = router;
