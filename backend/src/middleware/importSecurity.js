/**
 * Enhanced Security Middleware for Import/Export System
 * Provides comprehensive security validation and protection
 */

const rateLimit = require('express-rate-limit');
const fileType = require('file-type');
const path = require('path');
const fs = require('fs').promises;
const crypto = require('crypto');
const logger = require('../utils/logger');
const { importErrorHandler } = require('./importErrorHandler');

class ImportSecurityMiddleware {
  constructor() {
    this.maxFileSize = 10 * 1024 * 1024; // 10MB
    this.allowedMimeTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel'
    ];
    this.allowedExtensions = ['.xlsx', '.xls'];
    this.suspiciousPatterns = [
      /javascript:/i,
      /<script/i,
      /vbscript:/i,
      /onload=/i,
      /onerror=/i,
      /eval\(/i,
      /document\./i,
      /window\./i
    ];
  }

  /**
   * Rate limiting for import operations
   */
  createImportRateLimit() {
    return rateLimit({
      windowMs: 10 * 60 * 1000, // 10 minutes
      max: 10, // 10 requests per 10 minutes per IP
      message: {
        success: false,
        error: {
          code: 'RATE_LIMIT_EXCEEDED',
          message: 'Çok fazla import isteği. 10 dakika sonra tekrar deneyin.',
          retryAfter: 600
        }
      },
      standardHeaders: true,
      legacyHeaders: false,
      keyGenerator: (req) => {
        // Use both IP and user ID for rate limiting
        return `${req.ip}_${req.user?.id || 'anonymous'}`;
      },
      handler: (req, res) => {
        logger.warn('Import rate limit exceeded:', {
          ip: req.ip,
          userId: req.user?.id,
          userAgent: req.get('User-Agent'),
          timestamp: new Date().toISOString()
        });
        
        res.status(429).json({
          success: false,
          error: {
            code: 'RATE_LIMIT_EXCEEDED',
            message: 'Çok fazla import isteği. 10 dakika sonra tekrar deneyin.',
            retryAfter: 600
          }
        });
      }
    });
  }

  /**
   * Rate limiting for AI operations
   */
  createAIRateLimit() {
    return rateLimit({
      windowMs: 60 * 1000, // 1 minute
      max: 5, // 5 requests per minute per user
      message: {
        success: false,
        error: {
          code: 'AI_RATE_LIMIT_EXCEEDED',
          message: 'AI kullanım limiti aşıldı. 1 dakika sonra tekrar deneyin.',
          retryAfter: 60
        }
      },
      keyGenerator: (req) => {
        return `ai_${req.user?.id || req.ip}`;
      },
      handler: (req, res) => {
        logger.warn('AI rate limit exceeded:', {
          ip: req.ip,
          userId: req.user?.id,
          userAgent: req.get('User-Agent'),
          timestamp: new Date().toISOString()
        });
        
        res.status(429).json({
          success: false,
          error: {
            code: 'AI_RATE_LIMIT_EXCEEDED',
            message: 'AI kullanım limiti aşıldı. 1 dakika sonra tekrar deneyin.',
            retryAfter: 60
          }
        });
      }
    });
  }

  /**
   * File content security validation
   */
  validateFileContent = async (req, res, next) => {
    try {
      if (!req.file) {
        return next();
      }

      const filePath = req.file.path;
      
      // Read file header for additional validation
      const fileBuffer = await fs.readFile(filePath, { encoding: null });
      const fileHeader = fileBuffer.slice(0, 512).toString('hex');
      
      // Check for Excel file signatures
      const excelSignatures = [
        '504b0304', // ZIP signature (XLSX)
        'd0cf11e0a1b11ae1', // OLE signature (XLS)
        '504b030414000600' // XLSX specific
      ];

      const hasValidSignature = excelSignatures.some(signature => 
        fileHeader.toLowerCase().startsWith(signature.toLowerCase())
      );

      if (!hasValidSignature) {
        await this.cleanupFile(filePath);
        return res.status(400).json(
          importErrorHandler.createErrorResponse(
            'INVALID_FILE_CONTENT',
            'Dosya geçerli bir Excel dosyası değil'
          )
        );
      }

      // Check file size again (double validation)
      if (fileBuffer.length > this.maxFileSize) {
        await this.cleanupFile(filePath);
        return res.status(400).json(
          importErrorHandler.createErrorResponse(
            'FILE_TOO_LARGE',
            'Dosya boyutu limiti aşıldı'
          )
        );
      }

      // Scan for suspicious content in text portions
      const textContent = fileBuffer.toString('utf8', 0, Math.min(fileBuffer.length, 4096));
      const hasSuspiciousContent = this.suspiciousPatterns.some(pattern => 
        pattern.test(textContent)
      );

      if (hasSuspiciousContent) {
        await this.cleanupFile(filePath);
        logger.warn('Suspicious file content detected:', {
          userId: req.user?.id,
          fileName: req.file.originalname,
          ip: req.ip
        });
        
        return res.status(400).json(
          importErrorHandler.createErrorResponse(
            'SUSPICIOUS_CONTENT',
            'Dosyada güvenlik riski tespit edildi'
          )
        );
      }

      // Add file hash for integrity checking
      const fileHash = crypto.createHash('sha256').update(fileBuffer).digest('hex');
      req.fileInfo = {
        ...req.fileInfo,
        hash: fileHash,
        validated: true
      };

      next();

    } catch (error) {
      logger.error('File content validation error:', error);
      
      if (req.file?.path) {
        await this.cleanupFile(req.file.path);
      }

      res.status(500).json(
        importErrorHandler.createErrorResponse(
          'VALIDATION_ERROR',
          'Dosya güvenlik kontrolü başarısız'
        )
      );
    }
  };

  /**
   * Input sanitization middleware
   */
  sanitizeInput = (req, res, next) => {
    try {
      // Sanitize request body
      if (req.body) {
        req.body = this.sanitizeObject(req.body);
      }

      // Sanitize query parameters
      if (req.query) {
        req.query = this.sanitizeObject(req.query);
      }

      // Sanitize URL parameters
      if (req.params) {
        req.params = this.sanitizeObject(req.params);
      }

      next();
    } catch (error) {
      logger.error('Input sanitization error:', error);
      res.status(400).json(
        importErrorHandler.createErrorResponse(
          'INVALID_INPUT',
          'Geçersiz girdi verisi'
        )
      );
    }
  };

  /**
   * Sanitize object recursively
   */
  sanitizeObject(obj) {
    if (typeof obj !== 'object' || obj === null) {
      return this.sanitizeString(obj);
    }

    if (Array.isArray(obj)) {
      return obj.map(item => this.sanitizeObject(item));
    }

    const sanitized = {};
    for (const [key, value] of Object.entries(obj)) {
      const sanitizedKey = this.sanitizeString(key);
      sanitized[sanitizedKey] = this.sanitizeObject(value);
    }

    return sanitized;
  }

  /**
   * Sanitize string values
   */
  sanitizeString(value) {
    if (typeof value !== 'string') {
      return value;
    }

    // Remove potentially dangerous characters and patterns
    return value
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove script tags
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .replace(/vbscript:/gi, '') // Remove vbscript: protocol
      .replace(/on\w+\s*=/gi, '') // Remove event handlers
      .replace(/eval\s*\(/gi, '') // Remove eval calls
      .trim();
  }

  /**
   * Authentication validation for import operations
   */
  validateImportAuth = (req, res, next) => {
    try {
      // Check if user is authenticated
      if (!req.user) {
        return res.status(401).json(
          importErrorHandler.createErrorResponse(
            'UNAUTHORIZED',
            'Giriş yapmanız gerekli'
          )
        );
      }

      // Check if user has Google authentication (required for import/export)
      if (!req.user.firebaseUid || req.user.provider !== 'google.com') {
        return res.status(403).json(
          importErrorHandler.createErrorResponse(
            'GOOGLE_SIGNIN_REQUIRED',
            'Import/Export özellikleri için Google ile giriş yapmanız gerekli'
          )
        );
      }

      // Check user status
      if (req.user.status !== 'active') {
        return res.status(403).json(
          importErrorHandler.createErrorResponse(
            'ACCOUNT_INACTIVE',
            'Hesabınız aktif değil'
          )
        );
      }

      next();
    } catch (error) {
      logger.error('Import auth validation error:', error);
      res.status(500).json(
        importErrorHandler.createErrorResponse(
          'AUTH_VALIDATION_ERROR',
          'Yetkilendirme kontrolü başarısız'
        )
      );
    }
  };

  /**
   * Request size validation
   */
  validateRequestSize = (req, res, next) => {
    try {
      const contentLength = parseInt(req.get('Content-Length') || '0');
      const maxRequestSize = 15 * 1024 * 1024; // 15MB (file + metadata)

      if (contentLength > maxRequestSize) {
        return res.status(413).json(
          importErrorHandler.createErrorResponse(
            'REQUEST_TOO_LARGE',
            'İstek boyutu çok büyük'
          )
        );
      }

      next();
    } catch (error) {
      logger.error('Request size validation error:', error);
      res.status(500).json(
        importErrorHandler.createErrorResponse(
          'SIZE_VALIDATION_ERROR',
          'İstek boyutu kontrolü başarısız'
        )
      );
    }
  };

  /**
   * Security headers middleware
   */
  setSecurityHeaders = (req, res, next) => {
    // Prevent MIME type sniffing
    res.setHeader('X-Content-Type-Options', 'nosniff');
    
    // Prevent clickjacking
    res.setHeader('X-Frame-Options', 'DENY');
    
    // XSS protection
    res.setHeader('X-XSS-Protection', '1; mode=block');
    
    // Referrer policy
    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
    
    // Content Security Policy for API responses
    res.setHeader('Content-Security-Policy', "default-src 'none'");

    next();
  };

  /**
   * Cleanup uploaded file
   */
  async cleanupFile(filePath) {
    try {
      await fs.unlink(filePath);
      logger.info('Security cleanup - file removed:', filePath);
    } catch (error) {
      logger.error('Security cleanup error:', error);
    }
  }

  /**
   * Get comprehensive security middleware stack
   */
  getSecurityMiddleware() {
    return [
      this.setSecurityHeaders,
      this.validateRequestSize,
      this.sanitizeInput,
      this.validateImportAuth
    ];
  }

  /**
   * Get file security middleware stack
   */
  getFileSecurityMiddleware() {
    return [
      ...this.getSecurityMiddleware(),
      this.validateFileContent
    ];
  }
}

// Create singleton instance
const importSecurityMiddleware = new ImportSecurityMiddleware();

module.exports = importSecurityMiddleware;
