/**
 * Mode Validation Middleware
 * Validates mode switching operations to prevent data loss
 */

const User = require('../models/User');
const Expense = require('../models/Expense');
const Field = require('../models/Field');
const Logger = require('../utils/logger');

/**
 * Validate mode switching request
 */
const validateModeSwitch = async (req, res, next) => {
  try {
    console.log('🔍 validateModeSwitch middleware - body:', req.body, 'user:', req.user?._id);
    const { mode } = req.body;
    const userId = req.user.firebaseUid;

    // Get current user
    const user = await User.findOne({ firebaseUid: userId });
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Kullanıcı bulunamadı'
      });
    }

    const currentMode = user.preferences.trackingMode || 'simple';
    
    // If no mode change, skip validation
    if (currentMode === mode) {
      req.modeValidation = {
        isValid: true,
        noChange: true,
        currentMode,
        targetMode: mode
      };
      return next();
    }

    const validation = {
      isValid: true,
      warnings: [],
      requirements: [],
      dataImpact: {},
      currentMode,
      targetMode: mode
    };

    // Validate switching to detailed mode
    if (mode === 'detailed') {
      console.log('🔍 Validating switch to detailed mode for user:', user._id);
      const detailedValidation = await validateSwitchToDetailed(user._id);
      console.log('🔍 Detailed validation result:', detailedValidation);
      Object.assign(validation, detailedValidation);
    }

    // Validate switching to simple mode
    if (mode === 'simple') {
      const simpleValidation = await validateSwitchToSimple(user._id);
      Object.assign(validation, simpleValidation);
    }

    req.modeValidation = validation;
    next();

  } catch (error) {
    Logger.error('Mode validation error:', error);
    res.status(500).json({
      success: false,
      message: 'Mod geçiş validasyonu sırasında hata oluştu',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Validate switching to detailed mode
 */
async function validateSwitchToDetailed(userId) {
  const validation = {
    isValid: true,
    warnings: [],
    requirements: [],
    dataImpact: {}
  };

  try {
    // Check existing expenses
    const expenseCount = await Expense.countDocuments({
      userId,
      status: 'active'
    });

    validation.dataImpact.existingExpenses = expenseCount;

    if (expenseCount > 0) {
      validation.warnings.push({
        type: 'data_migration',
        message: `${expenseCount} adet mevcut gideriniz varsayılan tarlaya atanacak`,
        severity: 'info'
      });
    }

    // Check if user already has fields
    const existingFields = await Field.countDocuments({
      userId,
      isActive: true
    });

    validation.dataImpact.existingFields = existingFields;

    if (existingFields === 0) {
      validation.requirements.push({
        type: 'default_field_creation',
        message: 'Otomatik olarak "Ana Tarla" oluşturulacak',
        action: 'create_default_field'
      });
    }

    // Add benefits information
    validation.benefits = [
      'Tarlalarınızı ayrı ayrı takip edebilirsiniz',
      'Ürün bazlı maliyet analizi yapabilirsiniz',
      'Daha detaylı raporlar alabilirsiniz'
    ];

  } catch (error) {
    validation.isValid = false;
    validation.error = error.message;
  }

  return validation;
}

/**
 * Validate switching to simple mode
 */
async function validateSwitchToSimple(userId) {
  const validation = {
    isValid: true,
    warnings: [],
    requirements: [],
    dataImpact: {}
  };

  try {
    // Check detailed mode expenses
    const detailedExpenses = await Expense.countDocuments({
      userId,
      trackingMode: 'detailed',
      status: 'active'
    });

    validation.dataImpact.detailedExpenses = detailedExpenses;

    // Check expenses with field assignments
    const expensesWithFields = await Expense.countDocuments({
      userId,
      fieldId: { $ne: null },
      status: 'active'
    });

    validation.dataImpact.expensesWithFields = expensesWithFields;

    // Check expenses with crop assignments
    const expensesWithCrops = await Expense.countDocuments({
      userId,
      cropId: { $ne: null },
      status: 'active'
    });

    validation.dataImpact.expensesWithCrops = expensesWithCrops;

    // Check user's fields
    const userFields = await Field.countDocuments({
      userId,
      isActive: true
    });

    validation.dataImpact.userFields = userFields;

    // Add warnings for potential data loss
    if (expensesWithFields > 0) {
      validation.warnings.push({
        type: 'field_data_loss',
        message: `${expensesWithFields} giderin tarla bilgisi korunacak ancak basit modda görünmeyecek`,
        severity: 'warning'
      });
    }

    if (expensesWithCrops > 0) {
      validation.warnings.push({
        type: 'crop_data_loss',
        message: `${expensesWithCrops} giderin ürün bilgisi korunacak ancak basit modda görünmeyecek`,
        severity: 'warning'
      });
    }

    if (userFields > 0) {
      validation.warnings.push({
        type: 'field_management_loss',
        message: `${userFields} tarlanız korunacak ancak yönetilemeyecek`,
        severity: 'info'
      });
    }

    // Add information about what will happen
    validation.consequences = [
      'Tüm giderler genel liste halinde görünecek',
      'Tarla ve ürün bazlı filtreleme yapılamayacak',
      'Detaylı raporlar alınamayacak',
      'Verileriniz korunacak, istediğiniz zaman detaylı moda dönebilirsiniz'
    ];

  } catch (error) {
    validation.isValid = false;
    validation.error = error.message;
  }

  return validation;
}

/**
 * Check if mode switch requires confirmation
 */
const requiresConfirmation = (req, res, next) => {
  const validation = req.modeValidation;

  if (!validation || validation.noChange) {
    return next();
  }

  // Check if there are warnings that require confirmation
  const hasWarnings = validation.warnings && validation.warnings.length > 0;
  const hasDataImpact = validation.dataImpact && 
    (validation.dataImpact.existingExpenses > 0 || 
     validation.dataImpact.expensesWithFields > 0 ||
     validation.dataImpact.expensesWithCrops > 0);

  if (hasWarnings || hasDataImpact) {
    const { confirmed } = req.body;
    
    if (!confirmed) {
      return res.status(400).json({
        success: false,
        message: 'Bu işlem onay gerektiriyor',
        requiresConfirmation: true,
        validation: {
          warnings: validation.warnings,
          dataImpact: validation.dataImpact,
          consequences: validation.consequences,
          benefits: validation.benefits
        }
      });
    }
  }

  next();
};

/**
 * Validate user permissions for mode switching
 */
const validatePermissions = async (req, res, next) => {
  try {
    console.log('🔍 validatePermissions middleware - user:', req.user?._id);
    console.log('🔍 validatePermissions middleware - firebaseUid:', req.user?.firebaseUid);
    const userId = req.user.firebaseUid;
    
    // Get user
    const user = await User.findOne({ firebaseUid: userId });
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Kullanıcı bulunamadı'
      });
    }

    // Check if user is authenticated (not guest)
    if (!user.email || user.isGuest) {
      return res.status(403).json({
        success: false,
        message: 'Mod değiştirmek için Google ile giriş yapmanız gerekiyor',
        requiresAuth: true
      });
    }

    // Check rate limiting (prevent rapid mode switching)
    const lastModeChange = user.preferences.detailedModeActivatedAt;
    if (lastModeChange) {
      const timeSinceLastChange = Date.now() - lastModeChange.getTime();
      const minInterval = 30 * 1000; // 30 seconds (reduced for testing)

      if (timeSinceLastChange < minInterval) {
        return res.status(429).json({
          success: false,
          message: 'Mod değişikliği çok sık yapılamaz. Lütfen birkaç saniye bekleyin.',
          retryAfter: Math.ceil((minInterval - timeSinceLastChange) / 1000)
        });
      }
    }

    next();

  } catch (error) {
    Logger.error('Permission validation error:', error);
    res.status(500).json({
      success: false,
      message: 'Yetki kontrolü sırasında hata oluştu'
    });
  }
};

/**
 * Log mode switch attempt
 */
const logModeSwitch = (req, res, next) => {
  const validation = req.modeValidation;
  const userId = req.user.firebaseUid;
  
  if (validation && !validation.noChange) {
    Logger.info(`Mode switch attempt: User ${userId} from ${validation.currentMode} to ${validation.targetMode}`, {
      userId,
      currentMode: validation.currentMode,
      targetMode: validation.targetMode,
      hasWarnings: validation.warnings?.length > 0,
      dataImpact: validation.dataImpact
    });
  }

  next();
};

module.exports = {
  validateModeSwitch,
  requiresConfirmation,
  validatePermissions,
  logModeSwitch
};
