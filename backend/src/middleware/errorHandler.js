/**
 * Global Error Handler Middleware for Çiftçi Not Defterim
 * Handles all application errors with Turkish language support
 */

const logger = require('../utils/logger');
const { HTTP_STATUS, ERROR_CODES, ERROR_MESSAGES } = require('../utils/constants');
const { ResponseHelpers } = require('../utils/helpers');

/**
 * Global error handling middleware
 * @param {Error} err - Error object
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const errorHandler = (err, req, res, next) => {
  // Log the error
  logger.error('Global Error Handler:', {
    error: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    requestId: req.requestId,
    userId: req.user?.id || 'anonymous'
  });

  // Default error response
  let statusCode = HTTP_STATUS.INTERNAL_SERVER_ERROR;
  let errorCode = ERROR_CODES.INTERNAL_ERROR;
  let message = ERROR_MESSAGES[ERROR_CODES.INTERNAL_ERROR];
  let details = null;

  // Handle specific error types
  if (err.name === 'ValidationError') {
    // Mongoose validation error
    statusCode = HTTP_STATUS.BAD_REQUEST;
    errorCode = ERROR_CODES.VALIDATION_ERROR;
    message = ERROR_MESSAGES[ERROR_CODES.VALIDATION_ERROR];
    
    // Extract validation details
    const validationErrors = {};
    Object.keys(err.errors).forEach(key => {
      validationErrors[key] = err.errors[key].message;
    });
    details = { validationErrors };
    
  } else if (err.name === 'CastError') {
    // MongoDB cast error (invalid ObjectId, etc.)
    statusCode = HTTP_STATUS.BAD_REQUEST;
    errorCode = ERROR_CODES.INVALID_INPUT;
    message = 'Geçersiz veri formatı';
    details = { field: err.path, value: err.value };
    
  } else if (err.code === 11000) {
    // MongoDB duplicate key error
    statusCode = HTTP_STATUS.CONFLICT;
    errorCode = ERROR_CODES.DUPLICATE_RECORD;
    message = ERROR_MESSAGES[ERROR_CODES.DUPLICATE_RECORD];
    
    // Extract duplicate field info
    const field = Object.keys(err.keyPattern)[0];
    details = { field, message: `Bu ${field} zaten kullanılıyor` };
    
  } else if (err.name === 'JsonWebTokenError') {
    // JWT token error
    statusCode = HTTP_STATUS.UNAUTHORIZED;
    errorCode = ERROR_CODES.INVALID_TOKEN;
    message = ERROR_MESSAGES[ERROR_CODES.INVALID_TOKEN];
    
  } else if (err.name === 'TokenExpiredError') {
    // JWT token expired
    statusCode = HTTP_STATUS.UNAUTHORIZED;
    errorCode = ERROR_CODES.TOKEN_EXPIRED;
    message = ERROR_MESSAGES[ERROR_CODES.TOKEN_EXPIRED];
    
  } else if (err.name === 'MulterError') {
    // File upload error
    statusCode = HTTP_STATUS.BAD_REQUEST;
    
    if (err.code === 'LIMIT_FILE_SIZE') {
      errorCode = ERROR_CODES.FILE_TOO_LARGE;
      message = ERROR_MESSAGES[ERROR_CODES.FILE_TOO_LARGE];
    } else if (err.code === 'LIMIT_UNEXPECTED_FILE') {
      errorCode = ERROR_CODES.INVALID_FILE_TYPE;
      message = ERROR_MESSAGES[ERROR_CODES.INVALID_FILE_TYPE];
    } else {
      errorCode = ERROR_CODES.UPLOAD_FAILED;
      message = ERROR_MESSAGES[ERROR_CODES.UPLOAD_FAILED];
    }
    
  } else if (err.type === 'entity.parse.failed') {
    // JSON parsing error
    statusCode = HTTP_STATUS.BAD_REQUEST;
    errorCode = ERROR_CODES.INVALID_JSON;
    message = ERROR_MESSAGES[ERROR_CODES.INVALID_JSON];
    
  } else if (err.statusCode || err.status) {
    // Custom error with status code
    statusCode = err.statusCode || err.status;
    errorCode = err.code || ERROR_CODES.INTERNAL_ERROR;
    message = err.message || ERROR_MESSAGES[errorCode];
    details = err.details || null;
    
  } else if (err.message) {
    // Generic error with message
    message = err.message;
  }

  // Handle specific HTTP status codes
  if (statusCode === HTTP_STATUS.NOT_FOUND) {
    errorCode = ERROR_CODES.RECORD_NOT_FOUND;
    message = ERROR_MESSAGES[ERROR_CODES.RECORD_NOT_FOUND];
  }

  // Create error response
  const errorResponse = ResponseHelpers.error(errorCode, message, details);
  errorResponse.requestId = req.requestId;

  // Add stack trace in development
  if (process.env.NODE_ENV === 'development') {
    errorResponse.stack = err.stack;
  }

  // Send error response
  res.status(statusCode).json(errorResponse);
};

/**
 * 404 Not Found handler for API routes
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const notFoundHandler = (req, res, next) => {
  const error = new Error(`Route ${req.originalUrl} not found`);
  error.statusCode = HTTP_STATUS.NOT_FOUND;
  error.code = ERROR_CODES.ROUTE_NOT_FOUND;
  next(error);
};

/**
 * Async error wrapper for route handlers
 * @param {Function} fn - Async function to wrap
 * @returns {Function} Wrapped function
 */
const asyncHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * Create custom error
 * @param {string} message - Error message
 * @param {number} statusCode - HTTP status code
 * @param {string} code - Error code
 * @param {any} details - Error details
 * @returns {Error} Custom error object
 */
const createError = (message, statusCode = HTTP_STATUS.INTERNAL_SERVER_ERROR, code = ERROR_CODES.INTERNAL_ERROR, details = null) => {
  const error = new Error(message);
  error.statusCode = statusCode;
  error.code = code;
  error.details = details;
  return error;
};

/**
 * Validation error creator
 * @param {string} field - Field name
 * @param {string} message - Validation message
 * @returns {Error} Validation error
 */
const validationError = (field, message) => {
  return createError(
    ERROR_MESSAGES[ERROR_CODES.VALIDATION_ERROR],
    HTTP_STATUS.BAD_REQUEST,
    ERROR_CODES.VALIDATION_ERROR,
    { field, message }
  );
};

/**
 * Authentication error creator
 * @param {string} message - Auth error message
 * @returns {Error} Authentication error
 */
const authError = (message = ERROR_MESSAGES[ERROR_CODES.UNAUTHORIZED_ACCESS]) => {
  return createError(
    message,
    HTTP_STATUS.UNAUTHORIZED,
    ERROR_CODES.UNAUTHORIZED_ACCESS
  );
};

/**
 * Not found error creator
 * @param {string} resource - Resource name
 * @returns {Error} Not found error
 */
const notFoundError = (resource = 'Kayıt') => {
  return createError(
    `${resource} bulunamadı`,
    HTTP_STATUS.NOT_FOUND,
    ERROR_CODES.RECORD_NOT_FOUND
  );
};

/**
 * Database error creator
 * @param {string} operation - Database operation
 * @param {Error} originalError - Original database error
 * @returns {Error} Database error
 */
const databaseError = (operation, originalError) => {
  logger.error(`Database error during ${operation}:`, originalError);
  
  return createError(
    ERROR_MESSAGES[ERROR_CODES.DATABASE_ERROR],
    HTTP_STATUS.INTERNAL_SERVER_ERROR,
    ERROR_CODES.DATABASE_ERROR,
    { operation, originalError: originalError.message }
  );
};

module.exports = {
  errorHandler,
  notFoundHandler,
  asyncHandler,
  createError,
  validationError,
  authError,
  notFoundError,
  databaseError
};
