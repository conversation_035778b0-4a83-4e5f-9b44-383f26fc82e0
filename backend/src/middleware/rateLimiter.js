/**
 * Rate Limiting Middleware for Çiftçi Not Defterim
 * Protects API endpoints from abuse with Turkish error messages
 */

const rateLimit = require('express-rate-limit');
const logger = require('../utils/logger');
const { RATE_LIMITS, ERROR_CODES, ERROR_MESSAGES } = require('../utils/constants');
const { ResponseHelpers } = require('../utils/helpers');

/**
 * Create rate limiter with Turkish error messages
 * @param {Object} options - Rate limit options
 * @returns {Function} Rate limiter middleware
 */
const createRateLimiter = (options) => {
  return rateLimit({
    windowMs: options.windowMs,
    max: options.max,
    message: ResponseHelpers.error(
      ERROR_CODES.RATE_LIMIT_EXCEEDED,
      ERROR_MESSAGES[ERROR_CODES.RATE_LIMIT_EXCEEDED]
    ),
    standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
    legacyHeaders: false, // Disable the `X-RateLimit-*` headers
    
    // Custom key generator (can be used for user-specific limits)
    keyGenerator: (req) => {
      // Use user ID if authenticated, otherwise use IP
      return req.user?.id || req.ip;
    },
    
    // Custom handler for rate limit exceeded
    handler: (req, res) => {
      const userId = req.user?.id || 'anonymous';
      const endpoint = req.route?.path || req.path;
      
      logger.warn('Rate limit exceeded', {
        userId,
        ip: req.ip,
        endpoint,
        method: req.method,
        userAgent: req.get('User-Agent'),
        requestId: req.requestId
      });
      
      const errorResponse = ResponseHelpers.error(
        ERROR_CODES.RATE_LIMIT_EXCEEDED,
        ERROR_MESSAGES[ERROR_CODES.RATE_LIMIT_EXCEEDED],
        {
          retryAfter: Math.round(options.windowMs / 1000),
          limit: options.max,
          endpoint
        }
      );
      
      errorResponse.requestId = req.requestId;
      
      res.status(429).json(errorResponse);
    },
    
    // Skip successful requests in development
    skip: (req, res) => {
      if (process.env.NODE_ENV === 'development' && res.statusCode < 400) {
        return false; // Don't skip in development for testing
      }
      return false;
    },
    
    // Skip rate limiting for health checks
    skipSuccessfulRequests: false,
    skipFailedRequests: false
  });
};

/**
 * General API rate limiter
 * Applied to all API routes
 */
const general = createRateLimiter({
  windowMs: RATE_LIMITS.GENERAL.windowMs,
  max: RATE_LIMITS.GENERAL.max
});

/**
 * Authentication rate limiter
 * Applied to login/register endpoints
 */
const auth = createRateLimiter({
  windowMs: RATE_LIMITS.AUTH.windowMs,
  max: RATE_LIMITS.AUTH.max
});

/**
 * File upload rate limiter
 * Applied to upload endpoints
 */
const upload = createRateLimiter({
  windowMs: RATE_LIMITS.UPLOAD.windowMs,
  max: RATE_LIMITS.UPLOAD.max
});

/**
 * Sync operation rate limiter
 * Applied to sync endpoints
 */
const sync = createRateLimiter({
  windowMs: RATE_LIMITS.SYNC.windowMs,
  max: RATE_LIMITS.SYNC.max
});

/**
 * Strict rate limiter for sensitive operations
 * Can be applied to specific sensitive endpoints
 */
const strict = createRateLimiter({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 10 // Very limited requests
});

/**
 * Custom rate limiter for specific endpoints
 * @param {number} windowMs - Time window in milliseconds
 * @param {number} max - Maximum requests per window
 * @param {string} message - Custom error message
 * @returns {Function} Custom rate limiter middleware
 */
const custom = (windowMs, max, message = null) => {
  return createRateLimiter({
    windowMs,
    max,
    customMessage: message
  });
};

/**
 * Rate limiter with user-specific limits
 * Premium users might have higher limits
 * @param {Object} limits - User type specific limits
 * @returns {Function} User-aware rate limiter middleware
 */
const userAware = (limits) => {
  return rateLimit({
    windowMs: limits.windowMs,
    
    // Dynamic max based on user type
    max: (req) => {
      const userType = req.user?.type || 'free';
      return limits[userType] || limits.free || limits.max;
    },
    
    keyGenerator: (req) => {
      return req.user?.id || req.ip;
    },
    
    message: ResponseHelpers.error(
      ERROR_CODES.RATE_LIMIT_EXCEEDED,
      ERROR_MESSAGES[ERROR_CODES.RATE_LIMIT_EXCEEDED]
    ),
    
    handler: (req, res) => {
      const userId = req.user?.id || 'anonymous';
      const userType = req.user?.type || 'free';
      
      logger.warn('User-aware rate limit exceeded', {
        userId,
        userType,
        ip: req.ip,
        endpoint: req.path,
        method: req.method,
        requestId: req.requestId
      });
      
      const errorResponse = ResponseHelpers.error(
        ERROR_CODES.RATE_LIMIT_EXCEEDED,
        'Günlük istek limitinizi aştınız. Lütfen daha sonra tekrar deneyin.',
        {
          userType,
          upgradeMessage: userType === 'free' ? 'Premium hesaba geçerek daha yüksek limitlerden yararlanabilirsiniz.' : null
        }
      );
      
      res.status(429).json(errorResponse);
    }
  });
};

/**
 * IP-based rate limiter for public endpoints
 * @param {Object} options - Rate limit options
 * @returns {Function} IP-based rate limiter middleware
 */
const ipBased = (options) => {
  return rateLimit({
    windowMs: options.windowMs,
    max: options.max,
    keyGenerator: (req) => req.ip,
    message: ResponseHelpers.error(
      ERROR_CODES.RATE_LIMIT_EXCEEDED,
      'Bu IP adresinden çok fazla istek gönderildi. Lütfen daha sonra tekrar deneyin.'
    )
  });
};

/**
 * Endpoint-specific rate limiter
 * Different limits for different endpoints
 */
const endpointSpecific = {
  // Expense creation - moderate limit
  createExpense: custom(60 * 1000, 30, 'Çok fazla gider kaydı oluşturuyorsunuz. Lütfen bekleyin.'),
  
  // Category creation - strict limit
  createCategory: custom(60 * 1000, 5, 'Çok fazla kategori oluşturuyorsunuz. Lütfen bekleyin.'),
  
  // Report generation - moderate limit
  generateReport: custom(60 * 1000, 10, 'Çok fazla rapor talebi gönderiyorsunuz. Lütfen bekleyin.'),
  
  // Password reset - very strict
  passwordReset: custom(60 * 60 * 1000, 3, 'Şifre sıfırlama talebiniz çok sık. Lütfen 1 saat bekleyin.')
};

module.exports = {
  general,
  auth,
  upload,
  sync,
  strict,
  custom,
  userAware,
  ipBased,
  endpointSpecific,
  createRateLimiter
};
