/**
 * Authentication Middleware for Çiftçi Not Defterim
 * Firebase Auth integration with user management
 */

const admin = require('firebase-admin');
const logger = require('../utils/logger');
const { authError, asyncHandler } = require('./errorHandler');
const { ERROR_CODES, ERROR_MESSAGES } = require('../utils/constants');

// Initialize Firebase Admin SDK if not already initialized
if (!admin.apps.length) {
  try {
    // For development, we'll skip Firebase initialization if credentials are not properly set
    if (!process.env.FIREBASE_PROJECT_ID || process.env.FIREBASE_PROJECT_ID === 'test-project') {
      logger.warn('Firebase Admin SDK skipped - using development mode');
    } else {
      const serviceAccount = {
        type: 'service_account',
        project_id: process.env.FIREBASE_PROJECT_ID,
        private_key_id: process.env.FIREBASE_PRIVATE_KEY_ID,
        private_key: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
        client_email: process.env.FIREBASE_CLIENT_EMAIL,
        client_id: process.env.FIREBASE_CLIENT_ID,
        auth_uri: process.env.FIREBASE_AUTH_URI || 'https://accounts.google.com/o/oauth2/auth',
        token_uri: process.env.FIREBASE_TOKEN_URI || 'https://oauth2.googleapis.com/token'
      };

      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
        projectId: process.env.FIREBASE_PROJECT_ID
      });

      logger.info('Firebase Admin SDK initialized successfully');
    }
  } catch (error) {
    logger.error('Firebase Admin SDK initialization failed:', error);
  }
}

/**
 * Extract token from Authorization header
 * @param {Object} req - Express request object
 * @returns {string|null} Extracted token or null
 */
const extractToken = (req) => {
  const authHeader = req.headers.authorization;
  
  if (!authHeader) {
    return null;
  }
  
  // Support both "Bearer token" and "token" formats
  if (authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }
  
  return authHeader;
};

/**
 * Verify Firebase ID token
 * @param {string} token - Firebase ID token
 * @returns {Object} Decoded token data
 */
const verifyFirebaseToken = async (token) => {
  try {
    // Development mode - mock token verification
    if (process.env.NODE_ENV === 'development' && (!process.env.FIREBASE_PROJECT_ID || process.env.FIREBASE_PROJECT_ID === 'test-project' || token.includes('dev-test-token'))) {
      // Simple mock for development
      const tokenSuffix = token.split('-').pop() || Date.now();
      const mockUser = {
        uid: 'dev-user-' + tokenSuffix,
        email: `dev-${tokenSuffix}@example.com`,
        name: 'Development User',
        emailVerified: true,
        picture: null,
        provider: 'google.com'
      };

      return {
        success: true,
        user: mockUser
      };
    }

    const decodedToken = await admin.auth().verifyIdToken(token);
    return {
      success: true,
      user: {
        uid: decodedToken.uid,
        email: decodedToken.email,
        name: decodedToken.name || decodedToken.email?.split('@')[0],
        emailVerified: decodedToken.email_verified,
        picture: decodedToken.picture,
        provider: decodedToken.firebase?.sign_in_provider
      }
    };
  } catch (error) {
    logger.warn('Firebase token verification failed:', {
      error: error.message,
      code: error.code
    });

    return {
      success: false,
      error: error.code === 'auth/id-token-expired'
        ? ERROR_MESSAGES[ERROR_CODES.TOKEN_EXPIRED]
        : ERROR_MESSAGES[ERROR_CODES.INVALID_TOKEN]
    };
  }
};

/**
 * Find or create user in database
 * @param {Object} firebaseUser - Firebase user data
 * @returns {Object} Database user object
 */
const findOrCreateUser = async (firebaseUser) => {
  try {
    // Import User model here to avoid circular dependency
    const User = require('../models/User');
    
    // Try to find existing user
    let user = await User.findOne({ firebaseUid: firebaseUser.uid });
    
    if (!user) {
      // Create new user
      user = await User.create({
        firebaseUid: firebaseUser.uid,
        email: firebaseUser.email,
        name: firebaseUser.name,
        emailVerified: firebaseUser.emailVerified,
        picture: firebaseUser.picture,
        provider: firebaseUser.provider,
        lastLoginAt: new Date()
      });
      
      logger.info('New user created:', {
        userId: user._id,
        email: user.email,
        provider: firebaseUser.provider
      });
    } else {
      // Update last login time
      user.lastLoginAt = new Date();
      await user.save();
      
      logger.info('User login:', {
        userId: user._id,
        email: user.email,
        name: user.name,
        firebaseUid: user.firebaseUid
      });
    }
    
    return user;
  } catch (error) {
    logger.error('Error finding/creating user:', error);
    throw error;
  }
};

/**
 * Main authentication middleware
 * Verifies Firebase token and attaches user to request
 */
const authenticateUser = asyncHandler(async (req, res, next) => {
  try {
    // Extract token from request
    const token = extractToken(req);

    logger.info('Authentication attempt:', {
      hasToken: !!token,
      tokenLength: token ? token.length : 0,
      authHeader: req.headers.authorization ? 'present' : 'missing'
    });

    if (!token) {
      logger.warn('Authentication Failed - No token provided');
      throw authError(ERROR_MESSAGES[ERROR_CODES.MISSING_TOKEN]);
    }

    // Verify Firebase token
    const verificationResult = await verifyFirebaseToken(token);

    if (!verificationResult.success) {
      logger.warn('Authentication Failed - Token verification failed:', verificationResult.error);
      throw authError(verificationResult.error);
    }
    
    // Find or create user in database
    const user = await findOrCreateUser(verificationResult.user);
    
    // Attach user to request object
    req.user = user;
    req.firebaseUser = verificationResult.user;
    
    // Log authentication success
    logger.logAuth('token_verification', user._id, true, {
      provider: verificationResult.user.provider,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });
    
    next();
  } catch (error) {
    // Log authentication failure
    logger.logAuth('token_verification', null, false, {
      error: error.message,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });
    
    next(error);
  }
});

/**
 * Optional authentication middleware
 * Attaches user if token is valid, but doesn't require authentication
 */
const optionalAuth = asyncHandler(async (req, res, next) => {
  try {
    const token = extractToken(req);
    
    if (!token) {
      return next(); // No token, continue without user
    }
    
    const verificationResult = await verifyFirebaseToken(token);
    
    if (verificationResult.success) {
      const user = await findOrCreateUser(verificationResult.user);
      req.user = user;
      req.firebaseUser = verificationResult.user;
    }
    
    next();
  } catch (error) {
    // Log but don't fail - this is optional auth
    logger.warn('Optional auth failed:', error.message);
    next();
  }
});

/**
 * Admin role check middleware
 * Requires user to have admin role
 */
const requireAdmin = asyncHandler(async (req, res, next) => {
  if (!req.user) {
    throw authError(ERROR_MESSAGES[ERROR_CODES.MISSING_TOKEN]);
  }
  
  if (!req.user.isAdmin) {
    throw authError('Bu işlem için admin yetkisi gerekli');
  }
  
  next();
});

/**
 * Email verification check middleware
 * Requires user to have verified email
 */
const requireEmailVerification = asyncHandler(async (req, res, next) => {
  if (!req.user) {
    throw authError(ERROR_MESSAGES[ERROR_CODES.MISSING_TOKEN]);
  }
  
  if (!req.user.emailVerified) {
    throw authError('Bu işlem için e-posta doğrulaması gerekli');
  }
  
  next();
});

/**
 * User ownership check middleware
 * Ensures user can only access their own resources
 * @param {string} paramName - Parameter name containing user ID
 */
const requireOwnership = (paramName = 'userId') => {
  return asyncHandler(async (req, res, next) => {
    if (!req.user) {
      throw authError(ERROR_MESSAGES[ERROR_CODES.MISSING_TOKEN]);
    }
    
    const resourceUserId = req.params[paramName] || req.body[paramName];
    
    if (!resourceUserId) {
      throw authError('Kaynak kullanıcı ID\'si bulunamadı');
    }
    
    // Allow admin access or owner access
    if (!req.user.isAdmin && req.user._id.toString() !== resourceUserId.toString()) {
      throw authError('Bu kaynağa erişim yetkiniz yok');
    }
    
    next();
  });
};

/**
 * Rate limit by user middleware
 * Applies different rate limits based on user type
 */
const rateLimitByUser = (limits) => {
  return asyncHandler(async (req, res, next) => {
    if (!req.user) {
      // Apply guest limits
      req.rateLimit = limits.guest || limits.default;
    } else {
      // Apply user-specific limits
      const userType = req.user.type || 'free';
      req.rateLimit = limits[userType] || limits.default;
    }
    
    next();
  });
};

module.exports = {
  authenticateUser,
  optionalAuth,
  requireAdmin,
  requireEmailVerification,
  requireOwnership,
  rateLimitByUser,
  verifyFirebaseToken,
  extractToken
};
