/**
 * Validation Middleware for Çiftçi Not Defterim
 * Request validation using express-validator with Turkish messages
 */

const { body, param, query, validationResult } = require('express-validator');
const { validationError } = require('./errorHandler');
const { ValidationHelpers, TurkishHelpers } = require('../utils/helpers');
const { ERROR_CODES } = require('../utils/constants');

/**
 * Handle validation errors
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);

  // DEBUG: Log validation details
  console.log('🔍 EXPRESS-VALIDATOR DEBUG: Starting validation check...');
  console.log('🔍 EXPRESS-VALIDATOR DEBUG: Request method:', req.method);
  console.log('🔍 EXPRESS-VALIDATOR DEBUG: Request path:', req.path);
  console.log('🔍 EXPRESS-VALIDATOR DEBUG: Request body:', JSON.stringify(req.body, null, 2));
  console.log('🔍 EXPRESS-VALIDATOR DEBUG: Request body types:', {
    name: typeof req.body.name,
    description: typeof req.body.description,
    startDate: typeof req.body.startDate,
    endDate: typeof req.body.endDate,
    isActive: typeof req.body.isActive,
    isDefault: typeof req.body.isDefault,
    color: typeof req.body.color,
    emoji: typeof req.body.emoji
  });
  console.log('🔍 EXPRESS-VALIDATOR DEBUG: Has errors:', !errors.isEmpty());
  console.log('🔍 EXPRESS-VALIDATOR DEBUG: Total error count:', errors.array().length);

  if (!errors.isEmpty()) {
    const validationErrors = {};
    const allErrors = errors.array();

    console.log('🔍 EXPRESS-VALIDATOR DEBUG: All validation errors:', allErrors.map(error => ({
      field: error.path,
      message: error.msg,
      value: error.value,
      location: error.location,
      type: error.type
    })));

    allErrors.forEach(error => {
      validationErrors[error.path] = error.msg;
    });

    console.log('🔍 EXPRESS-VALIDATOR DEBUG: Formatted validation errors:', JSON.stringify(validationErrors, null, 2));

    const error = validationError('validation', 'Gönderilen veriler geçersiz');
    error.details = { validationErrors };

    return next(error);
  }

  console.log('🔍 VALIDATION DEBUG: Validation passed successfully');
  next();
};

/**
 * Expense validation rules
 */
const expenseValidation = {
  create: [
    body('categoryId')
      .notEmpty()
      .withMessage('Kategori seçimi zorunludur')
      .isMongoId()
      .withMessage('Geçersiz kategori ID\'si'),

    body('seasonId')
      .optional()
      .isMongoId()
      .withMessage('Geçersiz sezon ID\'si'),
    
    body('amount')
      .notEmpty()
      .withMessage('Tutar zorunludur')
      .custom((value) => {
        const parsed = TurkishHelpers.parseNumber(value);
        if (!ValidationHelpers.isValidAmount(parsed)) {
          throw new Error('Geçersiz tutar. 0 ile 1.000.000 TL arasında olmalıdır');
        }
        return true;
      }),
    
    body('description')
      .optional()
      .isLength({ max: 500 })
      .withMessage('Açıklama en fazla 500 karakter olabilir')
      .trim(),
    
    body('date')
      .notEmpty()
      .withMessage('Tarih zorunludur')
      .isISO8601()
      .withMessage('Geçersiz tarih formatı (YYYY-MM-DD bekleniyor)')
      .custom((value) => {
        const date = new Date(value);
        const now = new Date();
        const oneYearAgo = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());
        const oneYearFromNow = new Date(now.getFullYear() + 1, now.getMonth(), now.getDate());
        
        if (date < oneYearAgo || date > oneYearFromNow) {
          throw new Error('Tarih son 1 yıl ile gelecek 1 yıl arasında olmalıdır');
        }
        return true;
      }),
    
    body('location.latitude')
      .optional()
      .isFloat({ min: -90, max: 90 })
      .withMessage('Geçersiz enlem değeri'),
    
    body('location.longitude')
      .optional()
      .isFloat({ min: -180, max: 180 })
      .withMessage('Geçersiz boylam değeri'),
    
    body('photos')
      .optional()
      .isArray()
      .withMessage('Fotoğraflar dizi formatında olmalıdır')
      .custom((photos) => {
        if (photos.length > 5) {
          throw new Error('En fazla 5 fotoğraf eklenebilir');
        }
        return true;
      }),
    
    handleValidationErrors
  ],
  
  update: [
    param('id')
      .isMongoId()
      .withMessage('Geçersiz gider ID\'si'),
    
    body('categoryId')
      .optional()
      .isMongoId()
      .withMessage('Geçersiz kategori ID\'si'),
    
    body('amount')
      .optional()
      .custom((value) => {
        if (value !== undefined) {
          const parsed = TurkishHelpers.parseNumber(value);
          if (!ValidationHelpers.isValidAmount(parsed)) {
            throw new Error('Geçersiz tutar. 0 ile 1.000.000 TL arasında olmalıdır');
          }
        }
        return true;
      }),
    
    body('description')
      .optional()
      .isLength({ max: 500 })
      .withMessage('Açıklama en fazla 500 karakter olabilir')
      .trim(),
    
    body('date')
      .optional()
      .isISO8601()
      .withMessage('Geçersiz tarih formatı'),
    
    handleValidationErrors
  ],
  
  list: [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Sayfa numarası 1 veya daha büyük olmalıdır'),
    
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit 1 ile 100 arasında olmalıdır'),
    
    query('startDate')
      .optional()
      .isISO8601()
      .withMessage('Geçersiz başlangıç tarihi formatı'),
    
    query('endDate')
      .optional()
      .isISO8601()
      .withMessage('Geçersiz bitiş tarihi formatı'),
    
    query('categoryId')
      .optional()
      .isMongoId()
      .withMessage('Geçersiz kategori ID\'si'),

    query('seasonId')
      .optional()
      .isMongoId()
      .withMessage('Geçersiz sezon ID\'si'),

    query('search')
      .optional()
      .isLength({ min: 2, max: 100 })
      .withMessage('Arama terimi 2-100 karakter arasında olmalıdır')
      .trim(),
    
    handleValidationErrors
  ]
};

/**
 * Category validation rules
 */
const categoryValidation = {
  create: [
    body('name')
      .notEmpty()
      .withMessage('Kategori adı zorunludur')
      .isLength({ min: 2, max: 50 })
      .withMessage('Kategori adı 2-50 karakter arasında olmalıdır')
      .trim(),
    
    body('emoji')
      .notEmpty()
      .withMessage('Emoji zorunludur')
      .custom((value) => {
        // Simple emoji validation - check if it's a single emoji
        const emojiRegex = /^[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]$/u;
        if (!emojiRegex.test(value)) {
          throw new Error('Geçerli bir emoji seçiniz');
        }
        return true;
      }),
    
    body('color')
      .notEmpty()
      .withMessage('Renk zorunludur')
      .matches(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/)
      .withMessage('Geçersiz renk formatı (hex renk kodu bekleniyor)'),
    
    body('icon')
      .optional()
      .isLength({ max: 50 })
      .withMessage('İkon adı en fazla 50 karakter olabilir')
      .trim(),
    
    handleValidationErrors
  ],
  
  update: [
    param('id')
      .isMongoId()
      .withMessage('Geçersiz kategori ID\'si'),
    
    body('name')
      .optional()
      .isLength({ min: 2, max: 50 })
      .withMessage('Kategori adı 2-50 karakter arasında olmalıdır')
      .trim(),
    
    body('emoji')
      .optional()
      .custom((value) => {
        if (value) {
          const emojiRegex = /^[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]$/u;
          if (!emojiRegex.test(value)) {
            throw new Error('Geçerli bir emoji seçiniz');
          }
        }
        return true;
      }),
    
    body('color')
      .optional()
      .matches(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/)
      .withMessage('Geçersiz renk formatı'),
    
    handleValidationErrors
  ]
};

/**
 * User validation rules
 */
const userValidation = {
  updateProfile: [
    body('name')
      .optional()
      .isLength({ min: 2, max: 100 })
      .withMessage('İsim 2-100 karakter arasında olmalıdır')
      .trim(),
    
    body('preferences.currency')
      .optional()
      .isIn(['TRY', 'USD', 'EUR'])
      .withMessage('Desteklenen para birimi: TRY, USD, EUR'),
    
    body('preferences.language')
      .optional()
      .isIn(['tr', 'en'])
      .withMessage('Desteklenen dil: tr, en'),
    
    body('preferences.defaultSeason')
      .optional()
      .isIn(['spring', 'summer', 'autumn', 'winter'])
      .withMessage('Geçersiz sezon'),
    
    handleValidationErrors
  ]
};

/**
 * Sync validation rules
 */
const syncValidation = {
  push: [
    body('changes')
      .isArray()
      .withMessage('Değişiklikler dizi formatında olmalıdır')
      .custom((changes) => {
        if (changes.length > 100) {
          throw new Error('Tek seferde en fazla 100 değişiklik gönderilebilir');
        }
        return true;
      }),
    
    body('changes.*.operation')
      .isIn(['create', 'update', 'delete'])
      .withMessage('Geçersiz işlem türü'),
    
    body('changes.*.table')
      .isIn(['expenses', 'categories', 'settings'])
      .withMessage('Geçersiz tablo adı'),
    
    body('changes.*.localId')
      .notEmpty()
      .withMessage('Yerel ID zorunludur'),
    
    body('changes.*.timestamp')
      .isInt({ min: 0 })
      .withMessage('Geçersiz zaman damgası'),
    
    body('lastSyncTime')
      .optional()
      .isISO8601()
      .withMessage('Geçersiz son senkronizasyon zamanı'),
    
    handleValidationErrors
  ],
  
  pull: [
    query('since')
      .optional()
      .isISO8601()
      .withMessage('Geçersiz tarih formatı'),
    
    handleValidationErrors
  ]
};

/**
 * File upload validation
 */
const uploadValidation = {
  photo: [
    body('expenseId')
      .optional()
      .isMongoId()
      .withMessage('Geçersiz gider ID\'si'),
    
    handleValidationErrors
  ]
};

/**
 * Common parameter validations
 */
const paramValidation = {
  mongoId: (paramName = 'id') => [
    param(paramName)
      .isMongoId()
      .withMessage(`Geçersiz ${paramName}`),
    
    handleValidationErrors
  ]
};

module.exports = {
  expenseValidation,
  categoryValidation,
  userValidation,
  syncValidation,
  uploadValidation,
  paramValidation,
  handleValidationErrors
};
