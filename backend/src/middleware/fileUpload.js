/**
 * File Upload Middleware for Çiftçi Not Defterim
 * Handles secure file uploads with validation and cleanup
 */

const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;
const fileType = require('file-type');
const logger = require('../utils/logger');

class FileUploadMiddleware {
  constructor() {
    this.maxFileSize = 10 * 1024 * 1024; // 10MB
    this.allowedMimeTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
      'application/vnd.ms-excel', // .xls
    ];
    this.allowedExtensions = ['.xlsx', '.xls'];
    this.uploadDir = path.join(process.cwd(), 'uploads', 'temp');
    
    this.initializeUploadDir();
  }

  /**
   * Initialize upload directory
   */
  async initializeUploadDir() {
    try {
      await fs.mkdir(this.uploadDir, { recursive: true });
      logger.info('Upload directory initialized:', this.uploadDir);
    } catch (error) {
      logger.error('Failed to initialize upload directory:', error);
    }
  }

  /**
   * Custom file filter for Excel files
   */
  fileFilter = (req, file, cb) => {
    try {
      logger.info('File filter check:', {
        originalname: file.originalname,
        mimetype: file.mimetype,
        size: file.size
      });

      // Check file extension
      const ext = path.extname(file.originalname).toLowerCase();
      if (!this.allowedExtensions.includes(ext)) {
        const error = new Error('Sadece .xlsx ve .xls dosyaları kabul edilir');
        error.code = 'INVALID_FILE_TYPE';
        return cb(error, false);
      }

      // Check MIME type
      if (!this.allowedMimeTypes.includes(file.mimetype)) {
        const error = new Error('Geçersiz dosya tipi');
        error.code = 'INVALID_MIME_TYPE';
        return cb(error, false);
      }

      cb(null, true);
    } catch (error) {
      logger.error('File filter error:', error);
      cb(error, false);
    }
  };

  /**
   * Custom filename generator
   */
  filename = (req, file, cb) => {
    try {
      const userId = req.user?.id || 'anonymous';
      const timestamp = Date.now();
      const ext = path.extname(file.originalname);
      const filename = `import_${userId}_${timestamp}${ext}`;
      
      logger.info('Generated filename:', filename);
      cb(null, filename);
    } catch (error) {
      logger.error('Filename generation error:', error);
      cb(error);
    }
  };

  /**
   * Storage configuration
   */
  storage = multer.diskStorage({
    destination: (req, file, cb) => {
      cb(null, this.uploadDir);
    },
    filename: this.filename
  });

  /**
   * Create multer upload middleware
   */
  createUploadMiddleware() {
    return multer({
      storage: this.storage,
      fileFilter: this.fileFilter,
      limits: {
        fileSize: this.maxFileSize,
        files: 1 // Only one file at a time
      }
    });
  }

  /**
   * Advanced file validation middleware
   */
  validateUploadedFile = async (req, res, next) => {
    try {
      if (!req.file) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'NO_FILE',
            message: 'Dosya yüklenmedi'
          }
        });
      }

      const filePath = req.file.path;
      
      // Additional file type validation using file-type
      const detectedType = await fileType.fromFile(filePath);
      
      if (!detectedType) {
        await this.cleanupFile(filePath);
        return res.status(400).json({
          success: false,
          error: {
            code: 'UNRECOGNIZED_FILE',
            message: 'Dosya tipi tanınamadı'
          }
        });
      }

      // Verify detected type matches expected Excel formats
      const validMimes = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-excel'
      ];

      if (!validMimes.includes(detectedType.mime)) {
        await this.cleanupFile(filePath);
        return res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_FILE_CONTENT',
            message: 'Dosya içeriği Excel formatında değil'
          }
        });
      }

      // Add file info to request
      req.fileInfo = {
        originalName: req.file.originalname,
        filename: req.file.filename,
        path: req.file.path,
        size: req.file.size,
        mimetype: detectedType.mime,
        extension: detectedType.ext
      };

      logger.info('File validation successful:', req.fileInfo);
      next();

    } catch (error) {
      logger.error('File validation error:', error);
      
      // Cleanup file on error
      if (req.file?.path) {
        await this.cleanupFile(req.file.path);
      }

      res.status(500).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Dosya doğrulama hatası'
        }
      });
    }
  };

  /**
   * Error handling middleware for multer
   */
  handleUploadError = (error, req, res, next) => {
    logger.error('Upload error:', error);

    // Cleanup file if exists
    if (req.file?.path) {
      this.cleanupFile(req.file.path).catch(cleanupError => {
        logger.error('Cleanup error:', cleanupError);
      });
    }

    // Handle specific multer errors
    if (error instanceof multer.MulterError) {
      switch (error.code) {
        case 'LIMIT_FILE_SIZE':
          return res.status(400).json({
            success: false,
            error: {
              code: 'FILE_TOO_LARGE',
              message: 'Dosya boyutu 10MB\'dan büyük olamaz'
            }
          });
        case 'LIMIT_FILE_COUNT':
          return res.status(400).json({
            success: false,
            error: {
              code: 'TOO_MANY_FILES',
              message: 'Aynı anda sadece bir dosya yükleyebilirsiniz'
            }
          });
        case 'LIMIT_UNEXPECTED_FILE':
          return res.status(400).json({
            success: false,
            error: {
              code: 'UNEXPECTED_FILE',
              message: 'Beklenmeyen dosya alanı'
            }
          });
        default:
          return res.status(400).json({
            success: false,
            error: {
              code: 'UPLOAD_ERROR',
              message: 'Dosya yükleme hatası'
            }
          });
      }
    }

    // Handle custom errors
    if (error.code === 'INVALID_FILE_TYPE' || error.code === 'INVALID_MIME_TYPE') {
      return res.status(400).json({
        success: false,
        error: {
          code: error.code,
          message: error.message
        }
      });
    }

    // Generic error
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Sunucu hatası'
      }
    });
  };

  /**
   * Cleanup uploaded file
   */
  async cleanupFile(filePath) {
    try {
      await fs.unlink(filePath);
      logger.info('File cleaned up:', filePath);
    } catch (error) {
      logger.error('File cleanup error:', error);
    }
  }

  /**
   * Cleanup old temporary files
   */
  async cleanupOldFiles(maxAgeHours = 24) {
    try {
      const files = await fs.readdir(this.uploadDir);
      const now = Date.now();
      const maxAge = maxAgeHours * 60 * 60 * 1000; // Convert to milliseconds

      for (const file of files) {
        const filePath = path.join(this.uploadDir, file);
        const stats = await fs.stat(filePath);
        
        if (now - stats.mtime.getTime() > maxAge) {
          await fs.unlink(filePath);
          logger.info('Old file cleaned up:', filePath);
        }
      }
    } catch (error) {
      logger.error('Old files cleanup error:', error);
    }
  }

  /**
   * Get upload middleware for Excel import
   */
  getExcelUploadMiddleware() {
    const upload = this.createUploadMiddleware();
    
    return [
      upload.single('file'), // Field name should be 'file'
      this.handleUploadError,
      this.validateUploadedFile
    ];
  }

  /**
   * Manual file cleanup middleware (for route handlers)
   */
  cleanupMiddleware = (req, res, next) => {
    // Store original res.end to cleanup file after response
    const originalEnd = res.end;
    
    res.end = function(...args) {
      // Cleanup file after response is sent
      if (req.fileInfo?.path) {
        fileUploadMiddleware.cleanupFile(req.fileInfo.path).catch(error => {
          logger.error('Post-response cleanup error:', error);
        });
      }
      
      // Call original end method
      originalEnd.apply(this, args);
    };
    
    next();
  };
}

// Create singleton instance
const fileUploadMiddleware = new FileUploadMiddleware();

// Schedule periodic cleanup (every 6 hours)
setInterval(() => {
  fileUploadMiddleware.cleanupOldFiles(6).catch(error => {
    logger.error('Scheduled cleanup error:', error);
  });
}, 6 * 60 * 60 * 1000);

module.exports = fileUploadMiddleware;
