/**
 * Enhanced Error Handler for Import/Export System
 * Provides structured error responses with Turkish messages
 */

const logger = require('../utils/logger');

// Error codes and Turkish messages
const ERROR_MESSAGES = {
  // File Upload Errors
  FILE_TOO_LARGE: 'Dosya boyutu 10MB\'dan b<PERSON><PERSON><PERSON>k olamaz',
  INVALID_FILE_TYPE: 'Sadece .xlsx ve .xls dosyaları kabul edilir',
  INVALID_MIME_TYPE: 'Geçersiz dosya tipi',
  UNRECOGNIZED_FILE: 'Dosya tipi tanınamadı',
  INVALID_FILE_CONTENT: 'Dosya içeriği Excel formatında değil',
  NO_FILE: 'Dosya yüklenmedi',
  FILE_NOT_FOUND: 'Dosya bulunamadı',
  
  // Excel Processing Errors
  READ_ERROR: 'Excel dosyası okunamadı',
  WRITE_ERROR: 'Excel dosyası yazılamadı',
  TEMPLATE_ERROR: 'Template oluşturulamadı',
  EXPORT_ERROR: 'Excel dışa aktarma hatası',
  SAVE_ERROR: 'Dosya kaydedilemedi',
  BUFFER_ERROR: 'Buffer oluşturulamadı',
  
  // Data Validation Errors
  VALIDATION_ERROR: 'Veri doğrulama hatası',
  INVALID_DATE: 'Geçersiz tarih formatı',
  INVALID_AMOUNT: 'Geçersiz tutar',
  INVALID_CATEGORY: 'Geçersiz kategori',
  MISSING_REQUIRED_FIELD: 'Zorunlu alan eksik',
  DATA_INTEGRITY_ERROR: 'Veri bütünlüğü hatası',
  
  // AI Processing Errors
  AI_SERVICE_ERROR: 'AI servisi hatası',
  AI_TIMEOUT: 'AI işleme zaman aşımı',
  AI_RATE_LIMIT: 'AI kullanım limiti aşıldı',
  AI_PARSING_ERROR: 'AI metin analizi hatası',
  AI_CONFIDENCE_LOW: 'AI güven skoru düşük',
  
  // Authentication Errors
  UNAUTHORIZED: 'Yetkilendirme gerekli',
  FORBIDDEN: 'Bu işlem için yetkiniz yok',
  GOOGLE_SIGNIN_REQUIRED: 'Google ile giriş yapmanız gerekli',
  
  // Rate Limiting Errors
  RATE_LIMIT_EXCEEDED: 'İstek limiti aşıldı',
  TOO_MANY_REQUESTS: 'Çok fazla istek',
  
  // Database Errors
  DATABASE_ERROR: 'Veritabanı hatası',
  SAVE_FAILED: 'Kaydetme başarısız',
  UPDATE_FAILED: 'Güncelleme başarısız',
  DELETE_FAILED: 'Silme başarısız',
  
  // Generic Errors
  INTERNAL_ERROR: 'Sunucu hatası',
  NOT_IMPLEMENTED: 'Bu özellik henüz aktif değil',
  SERVICE_UNAVAILABLE: 'Servis geçici olarak kullanılamıyor',
  TIMEOUT: 'İşlem zaman aşımına uğradı',
  NETWORK_ERROR: 'Ağ bağlantı hatası'
};

// Error severity levels
const ERROR_SEVERITY = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical'
};

// Error categories for analytics
const ERROR_CATEGORIES = {
  FILE_UPLOAD: 'file_upload',
  DATA_PROCESSING: 'data_processing',
  AI_PROCESSING: 'ai_processing',
  AUTHENTICATION: 'authentication',
  RATE_LIMITING: 'rate_limiting',
  DATABASE: 'database',
  SYSTEM: 'system'
};

class ImportErrorHandler {
  constructor() {
    this.errorStats = new Map();
  }

  /**
   * Create structured error response
   */
  createErrorResponse(code, message, details = null, severity = ERROR_SEVERITY.MEDIUM) {
    const error = {
      success: false,
      error: {
        code,
        message: message || ERROR_MESSAGES[code] || 'Bilinmeyen hata',
        severity,
        timestamp: new Date().toISOString()
      }
    };

    if (details) {
      error.error.details = details;
    }

    return error;
  }

  /**
   * Log error with context
   */
  logError(error, context = {}) {
    const errorInfo = {
      message: error.message,
      stack: error.stack,
      code: error.code,
      ...context,
      timestamp: new Date().toISOString()
    };

    // Determine log level based on severity
    const severity = this.getErrorSeverity(error);
    
    switch (severity) {
      case ERROR_SEVERITY.CRITICAL:
        logger.error('CRITICAL ERROR:', errorInfo);
        break;
      case ERROR_SEVERITY.HIGH:
        logger.error('HIGH SEVERITY ERROR:', errorInfo);
        break;
      case ERROR_SEVERITY.MEDIUM:
        logger.warn('MEDIUM SEVERITY ERROR:', errorInfo);
        break;
      default:
        logger.info('LOW SEVERITY ERROR:', errorInfo);
        break;
    }

    // Track error statistics
    this.trackError(error.code || 'UNKNOWN_ERROR');
  }

  /**
   * Determine error severity
   */
  getErrorSeverity(error) {
    const criticalErrors = [
      'DATABASE_ERROR', 'INTERNAL_ERROR', 'SERVICE_UNAVAILABLE'
    ];
    
    const highErrors = [
      'AI_SERVICE_ERROR', 'DATA_INTEGRITY_ERROR', 'UNAUTHORIZED'
    ];
    
    const mediumErrors = [
      'VALIDATION_ERROR', 'FILE_TOO_LARGE', 'RATE_LIMIT_EXCEEDED'
    ];

    if (criticalErrors.includes(error.code)) {
      return ERROR_SEVERITY.CRITICAL;
    } else if (highErrors.includes(error.code)) {
      return ERROR_SEVERITY.HIGH;
    } else if (mediumErrors.includes(error.code)) {
      return ERROR_SEVERITY.MEDIUM;
    } else {
      return ERROR_SEVERITY.LOW;
    }
  }

  /**
   * Track error statistics
   */
  trackError(errorCode) {
    const count = this.errorStats.get(errorCode) || 0;
    this.errorStats.set(errorCode, count + 1);
  }

  /**
   * Get error statistics
   */
  getErrorStats() {
    return Object.fromEntries(this.errorStats);
  }

  /**
   * Main error handling middleware
   */
  handleError = (error, req, res, next) => {
    const context = {
      userId: req.user?.id,
      userEmail: req.user?.email,
      route: req.route?.path,
      method: req.method,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      body: req.method === 'POST' ? this.sanitizeBody(req.body) : undefined,
      fileInfo: req.fileInfo ? {
        originalName: req.fileInfo.originalName,
        size: req.fileInfo.size,
        mimetype: req.fileInfo.mimetype
      } : undefined
    };

    // Log the error
    this.logError(error, context);

    // Handle specific error types
    let statusCode = 500;
    let errorResponse;

    if (error.code) {
      switch (error.code) {
        case 'LIMIT_FILE_SIZE':
          statusCode = 400;
          errorResponse = this.createErrorResponse('FILE_TOO_LARGE', null, null, ERROR_SEVERITY.LOW);
          break;
        
        case 'INVALID_FILE_TYPE':
        case 'INVALID_MIME_TYPE':
          statusCode = 400;
          errorResponse = this.createErrorResponse(error.code, error.message, null, ERROR_SEVERITY.LOW);
          break;
        
        case 'UNAUTHORIZED':
          statusCode = 401;
          errorResponse = this.createErrorResponse('UNAUTHORIZED', null, null, ERROR_SEVERITY.MEDIUM);
          break;
        
        case 'FORBIDDEN':
          statusCode = 403;
          errorResponse = this.createErrorResponse('FORBIDDEN', null, null, ERROR_SEVERITY.MEDIUM);
          break;
        
        case 'RATE_LIMIT_EXCEEDED':
          statusCode = 429;
          errorResponse = this.createErrorResponse('RATE_LIMIT_EXCEEDED', null, {
            retryAfter: error.retryAfter || 600 // 10 minutes default
          }, ERROR_SEVERITY.MEDIUM);
          break;
        
        default:
          errorResponse = this.createErrorResponse(error.code, error.message);
          break;
      }
    } else {
      // Generic error
      errorResponse = this.createErrorResponse('INTERNAL_ERROR', null, null, ERROR_SEVERITY.HIGH);
    }

    // Add request ID for tracking
    errorResponse.error.requestId = req.id || this.generateRequestId();

    // Send error response
    res.status(statusCode).json(errorResponse);
  };

  /**
   * Sanitize request body for logging
   */
  sanitizeBody(body) {
    if (!body) return undefined;
    
    const sanitized = { ...body };
    
    // Remove sensitive fields
    const sensitiveFields = ['password', 'token', 'apiKey', 'secret'];
    sensitiveFields.forEach(field => {
      if (sanitized[field]) {
        sanitized[field] = '[REDACTED]';
      }
    });

    return sanitized;
  }

  /**
   * Generate unique request ID
   */
  generateRequestId() {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Validation error handler
   */
  handleValidationError = (errors) => {
    const details = errors.map(error => ({
      field: error.path || error.param,
      message: error.msg || error.message,
      value: error.value
    }));

    return this.createErrorResponse('VALIDATION_ERROR', 'Veri doğrulama hatası', {
      validationErrors: details
    }, ERROR_SEVERITY.LOW);
  };

  /**
   * AI processing error handler
   */
  handleAIError = (error, context = {}) => {
    let errorCode = 'AI_SERVICE_ERROR';
    let severity = ERROR_SEVERITY.MEDIUM;

    if (error.message?.includes('timeout')) {
      errorCode = 'AI_TIMEOUT';
    } else if (error.message?.includes('rate limit')) {
      errorCode = 'AI_RATE_LIMIT';
      severity = ERROR_SEVERITY.HIGH;
    } else if (error.message?.includes('parsing')) {
      errorCode = 'AI_PARSING_ERROR';
      severity = ERROR_SEVERITY.LOW;
    }

    this.logError(error, { ...context, category: ERROR_CATEGORIES.AI_PROCESSING });

    return this.createErrorResponse(errorCode, null, {
      originalError: error.message,
      aiContext: context
    }, severity);
  };

  /**
   * Database error handler
   */
  handleDatabaseError = (error, operation = 'unknown') => {
    let errorCode = 'DATABASE_ERROR';
    
    if (error.name === 'ValidationError') {
      errorCode = 'VALIDATION_ERROR';
    } else if (error.name === 'CastError') {
      errorCode = 'DATA_INTEGRITY_ERROR';
    } else if (error.code === 11000) {
      errorCode = 'DUPLICATE_ENTRY';
    }

    this.logError(error, { 
      operation, 
      category: ERROR_CATEGORIES.DATABASE 
    });

    return this.createErrorResponse(errorCode, null, {
      operation,
      mongoError: error.message
    }, ERROR_SEVERITY.HIGH);
  };
}

// Create singleton instance
const importErrorHandler = new ImportErrorHandler();

module.exports = {
  importErrorHandler,
  ERROR_MESSAGES,
  ERROR_SEVERITY,
  ERROR_CATEGORIES
};
