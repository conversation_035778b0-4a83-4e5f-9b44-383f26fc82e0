/**
 * Constants for <PERSON><PERSON><PERSON><PERSON> Not Defterim Backend
 * Application-wide constants and configuration values
 */

/**
 * HTTP Status Codes
 */
const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
  SERVICE_UNAVAILABLE: 503
};

/**
 * Error Codes for Turkish API responses
 */
const ERROR_CODES = {
  // Authentication errors
  INVALID_TOKEN: 'INVALID_TOKEN',
  MISSING_TOKEN: 'MISSING_TOKEN',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  UNAUTHORIZED_ACCESS: 'UNAUTHORIZED_ACCESS',
  
  // Validation errors
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  INVALID_INPUT: 'INVALID_INPUT',
  MISSING_REQUIRED_FIELD: 'MISSING_REQUIRED_FIELD',
  INVALID_EMAIL: 'INVALID_EMAIL',
  INVALID_AMOUNT: 'INVALID_AMOUNT',
  INVALID_DATE: 'INVALID_DATE',
  
  // Database errors
  DATABASE_ERROR: 'DATABASE_ERROR',
  RECORD_NOT_FOUND: 'RECORD_NOT_FOUND',
  DUPLICATE_RECORD: 'DUPLICATE_RECORD',
  
  // File upload errors
  FILE_TOO_LARGE: 'FILE_TOO_LARGE',
  INVALID_FILE_TYPE: 'INVALID_FILE_TYPE',
  UPLOAD_FAILED: 'UPLOAD_FAILED',
  
  // Sync errors
  SYNC_CONFLICT: 'SYNC_CONFLICT',
  SYNC_FAILED: 'SYNC_FAILED',
  
  // Rate limiting
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  
  // General errors
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
  INVALID_JSON: 'INVALID_JSON',
  ENDPOINT_NOT_FOUND: 'ENDPOINT_NOT_FOUND',
  ROUTE_NOT_FOUND: 'ROUTE_NOT_FOUND'
};

/**
 * Turkish error messages
 */
const ERROR_MESSAGES = {
  [ERROR_CODES.INVALID_TOKEN]: 'Geçersiz kimlik doğrulama token\'ı',
  [ERROR_CODES.MISSING_TOKEN]: 'Kimlik doğrulama token\'ı gerekli',
  [ERROR_CODES.TOKEN_EXPIRED]: 'Kimlik doğrulama token\'ı süresi dolmuş',
  [ERROR_CODES.UNAUTHORIZED_ACCESS]: 'Bu işlem için yetkiniz bulunmuyor',
  
  [ERROR_CODES.VALIDATION_ERROR]: 'Gönderilen veriler geçersiz',
  [ERROR_CODES.INVALID_INPUT]: 'Geçersiz veri formatı',
  [ERROR_CODES.MISSING_REQUIRED_FIELD]: 'Zorunlu alan eksik',
  [ERROR_CODES.INVALID_EMAIL]: 'Geçersiz e-posta adresi',
  [ERROR_CODES.INVALID_AMOUNT]: 'Geçersiz tutar',
  [ERROR_CODES.INVALID_DATE]: 'Geçersiz tarih formatı',
  
  [ERROR_CODES.DATABASE_ERROR]: 'Veritabanı hatası oluştu',
  [ERROR_CODES.RECORD_NOT_FOUND]: 'Kayıt bulunamadı',
  [ERROR_CODES.DUPLICATE_RECORD]: 'Bu kayıt zaten mevcut',
  
  [ERROR_CODES.FILE_TOO_LARGE]: 'Dosya boyutu çok büyük',
  [ERROR_CODES.INVALID_FILE_TYPE]: 'Desteklenmeyen dosya türü',
  [ERROR_CODES.UPLOAD_FAILED]: 'Dosya yükleme başarısız',
  
  [ERROR_CODES.SYNC_CONFLICT]: 'Senkronizasyon çakışması',
  [ERROR_CODES.SYNC_FAILED]: 'Senkronizasyon başarısız',
  
  [ERROR_CODES.RATE_LIMIT_EXCEEDED]: 'Çok fazla istek gönderildi. Lütfen daha sonra tekrar deneyin',
  
  [ERROR_CODES.INTERNAL_ERROR]: 'Sunucu hatası oluştu',
  [ERROR_CODES.SERVICE_UNAVAILABLE]: 'Servis şu anda kullanılamıyor',
  [ERROR_CODES.INVALID_JSON]: 'Geçersiz JSON formatı',
  [ERROR_CODES.ENDPOINT_NOT_FOUND]: 'İstenen API endpoint\'i bulunamadı',
  [ERROR_CODES.ROUTE_NOT_FOUND]: 'İstenen sayfa bulunamadı'
};

/**
 * Default agricultural categories with Turkish names
 */
const DEFAULT_CATEGORIES = [
  {
    name: 'Gübre',
    emoji: '🌱',
    color: '#4CAF50',
    icon: 'fertilizer',
    isDefault: true,
    description: 'Organik ve kimyasal gübreler'
  },
  {
    name: 'İşçilik',
    emoji: '👷',
    color: '#9C27B0',
    icon: 'labor',
    isDefault: true,
    description: 'İşçi ücretleri ve hizmet bedelleri'
  },
  {
    name: 'İlaç',
    emoji: '🐛',
    color: '#FF5722',
    icon: 'pesticide',
    isDefault: true,
    description: 'Böcek ilacı, fungisit ve herbisit'
  },
  {
    name: 'Su',
    emoji: '💧',
    color: '#2196F3',
    icon: 'water',
    isDefault: true,
    description: 'Su faturası ve sulama sistemleri'
  },
  {
    name: 'Yakıt',
    emoji: '⛽',
    color: '#FF9800',
    icon: 'fuel',
    isDefault: true,
    description: 'Mazot, benzin ve elektrik giderleri'
  },
  {
    name: 'Tohum',
    emoji: '🌾',
    color: '#8BC34A',
    icon: 'seed',
    isDefault: true,
    description: 'Tohum, fide ve ekim malzemeleri'
  },
  {
    name: 'Makine',
    emoji: '🚜',
    color: '#795548',
    icon: 'machinery',
    isDefault: true,
    description: 'Makine alımı, kiralama ve bakım'
  },
  {
    name: 'Depolama',
    emoji: '🏪',
    color: '#607D8B',
    icon: 'storage',
    isDefault: true,
    description: 'Depolama ve ambar giderleri'
  }
];

/**
 * Agricultural seasons for Turkey
 */
const AGRICULTURAL_SEASONS = [
  {
    id: 'winter',
    name: 'Kış Hazırlığı',
    startMonth: 12,
    endMonth: 2,
    description: 'Kış dönemi hazırlık ve planlama',
    typicalCrops: ['buğday', 'arpa', 'çavdar'],
    typicalExpenses: ['gübre', 'tohum', 'makine bakımı'],
    budgetRecommendations: {
      seedPercentage: 40,
      fertilizerPercentage: 30,
      machineryPercentage: 20,
      otherPercentage: 10
    }
  },
  {
    id: 'spring',
    name: 'Bahar Ekimi',
    startMonth: 3,
    endMonth: 5,
    description: 'Bahar dönemi ekim ve bakım',
    typicalCrops: ['mısır', 'ayçiçeği', 'pamuk', 'sebze'],
    typicalExpenses: ['tohum', 'gübre', 'ilaç', 'işçilik'],
    budgetRecommendations: {
      seedPercentage: 35,
      fertilizerPercentage: 25,
      pesticidePercentage: 20,
      laborPercentage: 20
    }
  },
  {
    id: 'summer',
    name: 'Yaz Bakımı',
    startMonth: 6,
    endMonth: 8,
    description: 'Yaz dönemi bakım ve sulama',
    typicalCrops: ['meyve', 'sebze', 'üzüm'],
    typicalExpenses: ['su', 'ilaç', 'işçilik', 'yakıt'],
    budgetRecommendations: {
      waterPercentage: 40,
      pesticidePercentage: 25,
      laborPercentage: 20,
      fuelPercentage: 15
    }
  },
  {
    id: 'autumn',
    name: 'Sonbahar Hasadı',
    startMonth: 9,
    endMonth: 11,
    description: 'Hasat dönemi ve kış hazırlığı',
    typicalCrops: ['tahıl', 'meyve', 'sebze'],
    typicalExpenses: ['işçilik', 'yakıt', 'makine', 'depolama'],
    budgetRecommendations: {
      laborPercentage: 50,
      fuelPercentage: 20,
      machineryPercentage: 20,
      storagePercentage: 10
    }
  }
];

/**
 * File upload configuration
 */
const FILE_UPLOAD = {
  MAX_SIZE: 5 * 1024 * 1024, // 5MB
  ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/webp'],
  ALLOWED_EXTENSIONS: ['.jpg', '.jpeg', '.png', '.webp'],
  UPLOAD_PATH: 'uploads/receipts',
  THUMBNAIL_SIZE: 200
};

/**
 * Pagination defaults
 */
const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_LIMIT: 20,
  MAX_LIMIT: 100
};

/**
 * Rate limiting configuration
 */
const RATE_LIMITS = {
  GENERAL: {
    windowMs: 60 * 1000, // 1 minute
    max: 100 // requests per window
  },
  AUTH: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5 // login attempts per window
  },
  UPLOAD: {
    windowMs: 60 * 1000, // 1 minute
    max: 10 // uploads per window
  },
  SYNC: {
    windowMs: 60 * 1000, // 1 minute
    max: 30 // sync requests per window
  }
};

/**
 * Sync operation types
 */
const SYNC_OPERATIONS = {
  CREATE: 'create',
  UPDATE: 'update',
  DELETE: 'delete'
};

/**
 * Sync table names
 */
const SYNC_TABLES = {
  EXPENSES: 'expenses',
  CATEGORIES: 'categories',
  SETTINGS: 'settings'
};

module.exports = {
  HTTP_STATUS,
  ERROR_CODES,
  ERROR_MESSAGES,
  DEFAULT_CATEGORIES,
  AGRICULTURAL_SEASONS,
  FILE_UPLOAD,
  PAGINATION,
  RATE_LIMITS,
  SYNC_OPERATIONS,
  SYNC_TABLES
};
