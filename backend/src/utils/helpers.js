/**
 * Helper Functions for Çiftçi Not Defterim Backend
 * Common utility functions with Turkish language support
 */

const moment = require('moment-timezone');
const slugify = require('slugify');

// Set Turkish locale for moment
moment.locale('tr');

/**
 * Turkish-specific helper functions
 */
const TurkishHelpers = {
  /**
   * Format Turkish Lira amount
   * @param {number} amount - Amount to format
   * @param {boolean} showCurrency - Whether to show currency symbol
   * @returns {string} Formatted amount
   */
  formatCurrency(amount, showCurrency = true) {
    if (typeof amount !== 'number' || isNaN(amount)) {
      return showCurrency ? '0,00 ₺' : '0,00';
    }

    const formatted = amount.toLocaleString('tr-TR', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });

    return showCurrency ? `${formatted} ₺` : formatted;
  },

  /**
   * Parse Turkish number format (comma as decimal separator)
   * @param {string} value - String value to parse
   * @returns {number} Parsed number
   */
  parseNumber(value) {
    if (typeof value === 'number') return value;
    if (!value || typeof value !== 'string') return 0;

    // Replace Turkish decimal separator (comma) with dot
    const normalized = value
      .replace(/\./g, '') // Remove thousand separators
      .replace(',', '.'); // Replace decimal comma with dot

    const parsed = parseFloat(normalized);
    return isNaN(parsed) ? 0 : parsed;
  },

  /**
   * Format date in Turkish format
   * @param {Date|string} date - Date to format
   * @param {string} format - Moment.js format string
   * @returns {string} Formatted date
   */
  formatDate(date, format = 'DD.MM.YYYY') {
    return moment(date).tz('Europe/Istanbul').format(format);
  },

  /**
   * Format date with Turkish day/month names
   * @param {Date|string} date - Date to format
   * @returns {string} Formatted date with Turkish names
   */
  formatDateTurkish(date) {
    return moment(date).tz('Europe/Istanbul').format('DD MMMM YYYY, dddd');
  },

  /**
   * Create Turkish-friendly slug
   * @param {string} text - Text to slugify
   * @returns {string} Slugified text
   */
  createSlug(text) {
    if (!text) return '';
    
    return slugify(text, {
      replacement: '-',
      lower: true,
      strict: true,
      locale: 'tr'
    });
  },

  /**
   * Validate Turkish phone number
   * @param {string} phone - Phone number to validate
   * @returns {boolean} Is valid phone number
   */
  isValidTurkishPhone(phone) {
    if (!phone) return false;
    
    // Remove all non-digit characters
    const cleaned = phone.replace(/\D/g, '');
    
    // Turkish mobile numbers: 5XX XXX XX XX (10 digits after country code)
    // With country code: +90 5XX XXX XX XX or 905XXXXXXXX
    const turkishMobileRegex = /^(90)?5[0-9]{9}$/;
    
    return turkishMobileRegex.test(cleaned);
  }
};

/**
 * Date and time utilities
 */
const DateHelpers = {
  /**
   * Get current Turkish time
   * @returns {Date} Current date in Turkish timezone
   */
  now() {
    return moment.tz('Europe/Istanbul').toDate();
  },

  /**
   * Get start of day in Turkish timezone
   * @param {Date|string} date - Date to get start of day
   * @returns {Date} Start of day
   */
  startOfDay(date = new Date()) {
    return moment.tz(date, 'Europe/Istanbul').startOf('day').toDate();
  },

  /**
   * Get end of day in Turkish timezone
   * @param {Date|string} date - Date to get end of day
   * @returns {Date} End of day
   */
  endOfDay(date = new Date()) {
    return moment.tz(date, 'Europe/Istanbul').endOf('day').toDate();
  },

  /**
   * Get date range for current month
   * @returns {Object} Start and end dates of current month
   */
  getCurrentMonth() {
    const now = moment.tz('Europe/Istanbul');
    return {
      start: now.clone().startOf('month').toDate(),
      end: now.clone().endOf('month').toDate()
    };
  },

  /**
   * Get date range for current year
   * @returns {Object} Start and end dates of current year
   */
  getCurrentYear() {
    const now = moment.tz('Europe/Istanbul');
    return {
      start: now.clone().startOf('year').toDate(),
      end: now.clone().endOf('year').toDate()
    };
  },

  /**
   * Check if date is today
   * @param {Date|string} date - Date to check
   * @returns {boolean} Is today
   */
  isToday(date) {
    return moment.tz(date, 'Europe/Istanbul').isSame(moment.tz('Europe/Istanbul'), 'day');
  },

  /**
   * Get days between two dates
   * @param {Date|string} startDate - Start date
   * @param {Date|string} endDate - End date
   * @returns {number} Number of days
   */
  daysBetween(startDate, endDate) {
    const start = moment.tz(startDate, 'Europe/Istanbul');
    const end = moment.tz(endDate, 'Europe/Istanbul');
    return end.diff(start, 'days');
  }
};

/**
 * Validation utilities
 */
const ValidationHelpers = {
  /**
   * Validate email address
   * @param {string} email - Email to validate
   * @returns {boolean} Is valid email
   */
  isValidEmail(email) {
    if (!email || typeof email !== 'string') return false;
    
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email.toLowerCase());
  },

  /**
   * Validate expense amount
   * @param {number|string} amount - Amount to validate
   * @returns {boolean} Is valid amount
   */
  isValidAmount(amount) {
    const parsed = typeof amount === 'string' ? TurkishHelpers.parseNumber(amount) : amount;
    return typeof parsed === 'number' && parsed > 0 && parsed < 1000000; // Max 1M TL
  },

  /**
   * Validate category name
   * @param {string} name - Category name to validate
   * @returns {boolean} Is valid category name
   */
  isValidCategoryName(name) {
    if (!name || typeof name !== 'string') return false;
    
    const trimmed = name.trim();
    return trimmed.length >= 2 && trimmed.length <= 50;
  },

  /**
   * Validate expense description
   * @param {string} description - Description to validate
   * @returns {boolean} Is valid description
   */
  isValidDescription(description) {
    if (!description) return true; // Description is optional
    if (typeof description !== 'string') return false;
    
    return description.trim().length <= 500;
  }
};

/**
 * Response formatting utilities
 */
const ResponseHelpers = {
  /**
   * Create success response
   * @param {any} data - Response data
   * @param {string} message - Success message
   * @returns {Object} Formatted success response
   */
  success(data, message = 'İşlem başarılı') {
    return {
      success: true,
      message,
      data,
      timestamp: new Date().toISOString()
    };
  },

  /**
   * Create error response
   * @param {string} code - Error code
   * @param {string} message - Error message
   * @param {any} details - Error details
   * @returns {Object} Formatted error response
   */
  error(code, message, details = null) {
    const response = {
      success: false,
      error: {
        code,
        message
      },
      timestamp: new Date().toISOString()
    };

    if (details) {
      response.error.details = details;
    }

    return response;
  },

  /**
   * Create paginated response
   * @param {Array} data - Data array
   * @param {number} page - Current page
   * @param {number} limit - Items per page
   * @param {number} total - Total items
   * @returns {Object} Formatted paginated response
   */
  paginated(data, page, limit, total) {
    const totalPages = Math.ceil(total / limit);
    
    return {
      success: true,
      data,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: parseInt(total),
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      },
      timestamp: new Date().toISOString()
    };
  }
};

module.exports = {
  TurkishHelpers,
  DateHelpers,
  ValidationHelpers,
  ResponseHelpers
};
