/**
 * Default Field Templates for Çiftçi Not Defterim
 * Templates and utilities for field creation
 */

const DEFAULT_FIELD_TEMPLATES = [
  {
    name: '<PERSON> Tarla',
    description: 'Varsayılan ana tarla alanı',
    size: {
      value: 10,
      unit: 'dekar'
    },
    soilType: 'other',
    irrigationType: 'none',
    notes: 'Otomatik oluşturulan varsayılan tarla'
  },
  {
    name: '<PERSON><PERSON>y Tarla',
    description: 'Kuzey yönündeki tarla alanı',
    size: {
      value: 5,
      unit: 'dekar'
    },
    soilType: 'loamy',
    irrigationType: 'drip',
    notes: '<PERSON><PERSON>y yönlü tarla şablonu'
  },
  {
    name: '<PERSON><PERSON>ney Tarla',
    description: 'Güney yönündeki tarla alanı',
    size: {
      value: 8,
      unit: 'dekar'
    },
    soilType: 'clay',
    irrigationType: 'sprinkler',
    notes: 'Güney yönlü tarla şablonu'
  },
  {
    name: '<PERSON><PERSON>',
    description: '<PERSON><PERSON><PERSON> sera üretim alanı',
    size: {
      value: 1,
      unit: 'dekar'
    },
    soilType: 'other',
    irrigationType: 'drip',
    notes: 'Sera üretimi için özel alan'
  },
  {
    name: 'Meyve Bahçesi',
    description: 'Meyve ağaçları için ayrılmış alan',
    size: {
      value: 15,
      unit: 'dekar'
    },
    soilType: 'loamy',
    irrigationType: 'drip',
    notes: 'Meyve ağaçları için uzun vadeli alan'
  }
];

const FIELD_SIZE_UNITS = [
  {
    code: 'dekar',
    name: 'Dekar',
    nameTr: 'Dekar',
    description: '1 dekar = 1000 m²',
    conversionToM2: 1000
  },
  {
    code: 'dönüm',
    name: 'Dönüm',
    nameTr: 'Dönüm',
    description: '1 dönüm = 919 m²',
    conversionToM2: 919
  },
  {
    code: 'hectare',
    name: 'Hectare',
    nameTr: 'Hektar',
    description: '1 hektar = 10000 m²',
    conversionToM2: 10000
  },
  {
    code: 'acre',
    name: 'Acre',
    nameTr: 'Akr',
    description: '1 akr = 4047 m²',
    conversionToM2: 4047
  }
];

const SOIL_TYPES = [
  {
    code: 'clay',
    name: 'Clay',
    nameTr: 'Killi Toprak',
    description: 'Su tutma kapasitesi yüksek, ağır toprak',
    characteristics: ['Yüksek su tutma', 'Ağır işleme', 'Besin tutma iyi']
  },
  {
    code: 'sandy',
    name: 'Sandy',
    nameTr: 'Kumlu Toprak',
    description: 'Hızlı drene olan, hafif toprak',
    characteristics: ['Hızlı drenaj', 'Kolay işleme', 'Düşük besin tutma']
  },
  {
    code: 'loamy',
    name: 'Loamy',
    nameTr: 'Balçık Toprak',
    description: 'İdeal tarım toprağı, dengeli yapı',
    characteristics: ['Dengeli drenaj', 'İyi havalanma', 'Optimal besin tutma']
  },
  {
    code: 'silty',
    name: 'Silty',
    nameTr: 'Siltli Toprak',
    description: 'İnce taneli, orta drene toprak',
    characteristics: ['Orta drenaj', 'İyi besin tutma', 'Kompaksiyon riski']
  },
  {
    code: 'peaty',
    name: 'Peaty',
    nameTr: 'Turbalı Toprak',
    description: 'Organik madde açısından zengin toprak',
    characteristics: ['Yüksek organik madde', 'Asidik pH', 'İyi su tutma']
  },
  {
    code: 'chalky',
    name: 'Chalky',
    nameTr: 'Kireçli Toprak',
    description: 'Kireç içeriği yüksek, alkali toprak',
    characteristics: ['Alkali pH', 'İyi drenaj', 'Kalsiyum zengin']
  },
  {
    code: 'other',
    name: 'Other',
    nameTr: 'Diğer',
    description: 'Belirtilmeyen toprak türü',
    characteristics: ['Genel kullanım']
  }
];

const IRRIGATION_TYPES = [
  {
    code: 'drip',
    name: 'Drip Irrigation',
    nameTr: 'Damla Sulama',
    description: 'Su tasarruflu, hassas sulama sistemi',
    efficiency: 'Yüksek',
    waterUsage: 'Düşük'
  },
  {
    code: 'sprinkler',
    name: 'Sprinkler Irrigation',
    nameTr: 'Yağmurlama Sulama',
    description: 'Geniş alan sulama sistemi',
    efficiency: 'Orta',
    waterUsage: 'Orta'
  },
  {
    code: 'flood',
    name: 'Flood Irrigation',
    nameTr: 'Salma Sulama',
    description: 'Geleneksel sulama yöntemi',
    efficiency: 'Düşük',
    waterUsage: 'Yüksek'
  },
  {
    code: 'manual',
    name: 'Manual Irrigation',
    nameTr: 'Elle Sulama',
    description: 'Manuel sulama yöntemi',
    efficiency: 'Değişken',
    waterUsage: 'Değişken'
  },
  {
    code: 'none',
    name: 'No Irrigation',
    nameTr: 'Sulamasız',
    description: 'Yağmur suyu ile üretim',
    efficiency: 'Doğal',
    waterUsage: 'Yok'
  }
];

/**
 * Create default field for user
 */
async function createDefaultField(userId, customName = null) {
  try {
    const Field = require('../models/Field');
    
    // Check if user already has a default field
    const existingDefault = await Field.findOne({ 
      userId, 
      isDefault: true, 
      isActive: true 
    });
    
    if (existingDefault) {
      return existingDefault;
    }
    
    const template = DEFAULT_FIELD_TEMPLATES[0]; // Ana Tarla template
    
    const fieldData = {
      userId,
      name: customName || template.name,
      size: template.size,
      soilType: template.soilType,
      irrigationType: template.irrigationType,
      notes: template.notes,
      isDefault: true,
      isActive: true
    };
    
    const field = new Field(fieldData);
    await field.save();
    
    console.log(`Default field created for user ${userId}: ${field.name}`);
    return field;
    
  } catch (error) {
    console.error('Error creating default field:', error);
    throw error;
  }
}

/**
 * Get field template by name
 */
function getFieldTemplate(templateName) {
  return DEFAULT_FIELD_TEMPLATES.find(template => 
    template.name.toLowerCase() === templateName.toLowerCase()
  );
}

/**
 * Convert field size between units
 */
function convertFieldSize(value, fromUnit, toUnit) {
  const fromUnitData = FIELD_SIZE_UNITS.find(unit => unit.code === fromUnit);
  const toUnitData = FIELD_SIZE_UNITS.find(unit => unit.code === toUnit);
  
  if (!fromUnitData || !toUnitData) {
    throw new Error('Invalid unit conversion');
  }
  
  // Convert to m² first, then to target unit
  const valueInM2 = value * fromUnitData.conversionToM2;
  const convertedValue = valueInM2 / toUnitData.conversionToM2;
  
  return Math.round(convertedValue * 100) / 100; // Round to 2 decimal places
}

/**
 * Get soil type information
 */
function getSoilTypeInfo(soilTypeCode) {
  return SOIL_TYPES.find(type => type.code === soilTypeCode);
}

/**
 * Get irrigation type information
 */
function getIrrigationTypeInfo(irrigationTypeCode) {
  return IRRIGATION_TYPES.find(type => type.code === irrigationTypeCode);
}

/**
 * Validate field data
 */
function validateFieldData(fieldData) {
  const errors = [];
  
  // Name validation
  if (!fieldData.name || fieldData.name.trim().length === 0) {
    errors.push('Tarla adı boş olamaz');
  }
  
  if (fieldData.name && fieldData.name.length > 50) {
    errors.push('Tarla adı 50 karakterden uzun olamaz');
  }
  
  // Size validation
  if (fieldData.size) {
    if (fieldData.size.value && fieldData.size.value < 0) {
      errors.push('Alan değeri negatif olamaz');
    }
    
    if (fieldData.size.unit && !FIELD_SIZE_UNITS.find(unit => unit.code === fieldData.size.unit)) {
      errors.push('Geçersiz alan birimi');
    }
  }
  
  // Soil type validation
  if (fieldData.soilType && !SOIL_TYPES.find(type => type.code === fieldData.soilType)) {
    errors.push('Geçersiz toprak türü');
  }
  
  // Irrigation type validation
  if (fieldData.irrigationType && !IRRIGATION_TYPES.find(type => type.code === fieldData.irrigationType)) {
    errors.push('Geçersiz sulama türü');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

module.exports = {
  DEFAULT_FIELD_TEMPLATES,
  FIELD_SIZE_UNITS,
  SOIL_TYPES,
  IRRIGATION_TYPES,
  createDefaultField,
  getFieldTemplate,
  convertFieldSize,
  getSoilTypeInfo,
  getIrrigationTypeInfo,
  validateFieldData
};
