/**
 * Performance Optimization Utilities
 * Database query optimization and caching for two-mode system
 */

const mongoose = require('mongoose');
const User = require('../models/User');
const Expense = require('../models/Expense');
const Field = require('../models/Field');
const Crop = require('../models/Crop');
const Category = require('../models/Category');

class PerformanceOptimizer {
  constructor() {
    this.queryCache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
  }

  /**
   * Optimize database indexes for two-mode system
   */
  async optimizeIndexes() {
    console.log('Starting database index optimization...');

    try {
      // User model indexes
      await User.collection.createIndex(
        { firebaseUid: 1 },
        { unique: true, background: true }
      );
      await User.collection.createIndex(
        { 'preferences.trackingMode': 1 },
        { background: true }
      );

      // Expense model indexes - optimized for two-mode queries
      await Expense.collection.createIndex(
        { userId: 1, date: -1 },
        { background: true }
      );
      await Expense.collection.createIndex(
        { userId: 1, trackingMode: 1, date: -1 },
        { background: true }
      );
      await Expense.collection.createIndex(
        { userId: 1, fieldId: 1, date: -1 },
        { background: true, partialFilterExpression: { fieldId: { $ne: null } } }
      );
      await Expense.collection.createIndex(
        { userId: 1, cropId: 1, date: -1 },
        { background: true, partialFilterExpression: { cropId: { $ne: null } } }
      );
      await Expense.collection.createIndex(
        { userId: 1, categoryId: 1, date: -1 },
        { background: true }
      );
      await Expense.collection.createIndex(
        { userId: 1, status: 1, trackingMode: 1 },
        { background: true }
      );

      // Field model indexes
      await Field.collection.createIndex(
        { userId: 1, isActive: 1 },
        { background: true }
      );
      await Field.collection.createIndex(
        { userId: 1, isDefault: 1 },
        { unique: true, background: true, partialFilterExpression: { isDefault: true } }
      );

      // Crop model indexes
      await Crop.collection.createIndex(
        { userId: 1, isActive: 1 },
        { background: true }
      );
      await Crop.collection.createIndex(
        { isDefault: 1, category: 1, isActive: 1 },
        { background: true }
      );
      await Crop.collection.createIndex(
        { productionType: 1, isActive: 1 },
        { background: true }
      );

      // Category model indexes
      await Category.collection.createIndex(
        { userId: 1, isActive: 1 },
        { background: true }
      );
      await Category.collection.createIndex(
        { isDefault: 1, isActive: 1 },
        { background: true }
      );

      console.log('✅ Database indexes optimized successfully');
      return true;

    } catch (error) {
      console.error('❌ Index optimization failed:', error);
      throw error;
    }
  }

  /**
   * Optimized expense query with intelligent field selection
   */
  async getOptimizedExpenses(userId, options = {}) {
    const {
      trackingMode,
      fieldId,
      cropId,
      categoryId,
      startDate,
      endDate,
      page = 1,
      limit = 20,
      sortBy = 'date',
      sortOrder = 'desc'
    } = options;

    // Build optimized query
    const query = { userId, status: 'active' };
    
    if (trackingMode) query.trackingMode = trackingMode;
    if (fieldId) query.fieldId = fieldId;
    if (cropId) query.cropId = cropId;
    if (categoryId) query.categoryId = categoryId;
    
    if (startDate || endDate) {
      query.date = {};
      if (startDate) query.date.$gte = new Date(startDate);
      if (endDate) query.date.$lte = new Date(endDate);
    }

    // Optimize sort
    const sortOptions = {};
    sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Build aggregation pipeline for better performance
    const pipeline = [
      { $match: query },
      { $sort: sortOptions },
      { $skip: skip },
      { $limit: limit },
      {
        $lookup: {
          from: 'categories',
          localField: 'categoryId',
          foreignField: '_id',
          as: 'category',
          pipeline: [{ $project: { name: 1, emoji: 1, color: 1 } }]
        }
      }
    ];

    // Add field lookup only for detailed mode
    if (trackingMode === 'detailed' || fieldId) {
      pipeline.push({
        $lookup: {
          from: 'fields',
          localField: 'fieldId',
          foreignField: '_id',
          as: 'field',
          pipeline: [{ $project: { name: 1, size: 1, isDefault: 1 } }]
        }
      });
    }

    // Add crop lookup only when needed
    if (cropId || trackingMode === 'detailed') {
      pipeline.push({
        $lookup: {
          from: 'crops',
          localField: 'cropId',
          foreignField: '_id',
          as: 'crop',
          pipeline: [{ $project: { name: 1, nameTr: 1, emoji: 1, category: 1 } }]
        }
      });
    }

    // Execute optimized query
    const [expenses, total] = await Promise.all([
      Expense.aggregate(pipeline),
      Expense.countDocuments(query)
    ]);

    return {
      expenses,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  }

  /**
   * Cached user mode lookup
   */
  async getUserTrackingMode(userId) {
    const cacheKey = `user_mode_${userId}`;
    const cached = this.queryCache.get(cacheKey);

    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }

    const user = await User.findById(userId).select('preferences.trackingMode').lean();
    const trackingMode = user?.preferences?.trackingMode || 'simple';

    this.queryCache.set(cacheKey, {
      data: trackingMode,
      timestamp: Date.now()
    });

    return trackingMode;
  }

  /**
   * Optimized field statistics calculation
   */
  async getFieldStatistics(userId, fieldId) {
    const cacheKey = `field_stats_${fieldId}`;
    const cached = this.queryCache.get(cacheKey);

    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }

    const stats = await Expense.aggregate([
      {
        $match: {
          userId: new mongoose.Types.ObjectId(userId),
          fieldId: new mongoose.Types.ObjectId(fieldId),
          status: 'active'
        }
      },
      {
        $group: {
          _id: null,
          totalExpenses: { $sum: 1 },
          totalAmount: { $sum: '$amount' },
          avgAmount: { $avg: '$amount' },
          lastExpenseDate: { $max: '$date' },
          minAmount: { $min: '$amount' },
          maxAmount: { $max: '$amount' }
        }
      }
    ]);

    const result = stats[0] || {
      totalExpenses: 0,
      totalAmount: 0,
      avgAmount: 0,
      lastExpenseDate: null,
      minAmount: 0,
      maxAmount: 0
    };

    this.queryCache.set(cacheKey, {
      data: result,
      timestamp: Date.now()
    });

    return result;
  }

  /**
   * Batch update field statistics
   */
  async updateAllFieldStatistics(userId) {
    const fields = await Field.find({ userId, isActive: true }).select('_id').lean();
    
    const updatePromises = fields.map(async (field) => {
      const stats = await this.getFieldStatistics(userId, field._id);
      
      return Field.updateOne(
        { _id: field._id },
        { $set: { stats } }
      );
    });

    await Promise.all(updatePromises);
    console.log(`Updated statistics for ${fields.length} fields`);
  }

  /**
   * Optimized crop usage statistics
   */
  async getCropUsageStatistics(userId, cropId) {
    const stats = await Expense.aggregate([
      {
        $match: {
          userId: new mongoose.Types.ObjectId(userId),
          cropId: new mongoose.Types.ObjectId(cropId),
          status: 'active'
        }
      },
      {
        $group: {
          _id: null,
          usageCount: { $sum: 1 },
          totalAmount: { $sum: '$amount' },
          avgAmount: { $avg: '$amount' },
          lastUsed: { $max: '$date' }
        }
      }
    ]);

    return stats[0] || {
      usageCount: 0,
      totalAmount: 0,
      avgAmount: 0,
      lastUsed: null
    };
  }

  /**
   * Database health check and optimization recommendations
   */
  async getDatabaseHealthReport() {
    const report = {
      timestamp: new Date(),
      collections: {},
      indexes: {},
      recommendations: []
    };

    try {
      // Collection statistics
      const collections = ['users', 'expenses', 'fields', 'crops', 'categories'];
      
      for (const collectionName of collections) {
        const stats = await mongoose.connection.db.collection(collectionName).stats();
        report.collections[collectionName] = {
          count: stats.count,
          size: stats.size,
          avgObjSize: stats.avgObjSize,
          indexSizes: stats.indexSizes
        };
      }

      // Index usage analysis
      const expenseIndexStats = await Expense.collection.getIndexes();
      report.indexes.expenses = Object.keys(expenseIndexStats);

      // Performance recommendations
      if (report.collections.expenses?.count > 10000) {
        report.recommendations.push('Consider implementing data archiving for old expenses');
      }

      if (report.collections.expenses?.avgObjSize > 1000) {
        report.recommendations.push('Consider optimizing expense document structure');
      }

      // Check for missing indexes
      const missingIndexes = await this.checkMissingIndexes();
      if (missingIndexes.length > 0) {
        report.recommendations.push(`Missing recommended indexes: ${missingIndexes.join(', ')}`);
      }

      return report;

    } catch (error) {
      console.error('Health report generation failed:', error);
      throw error;
    }
  }

  /**
   * Check for missing recommended indexes
   */
  async checkMissingIndexes() {
    const missing = [];
    
    try {
      const expenseIndexes = await Expense.collection.getIndexes();
      const indexNames = Object.keys(expenseIndexes);

      const recommendedIndexes = [
        'userId_1_trackingMode_1_date_-1',
        'userId_1_fieldId_1_date_-1',
        'userId_1_cropId_1_date_-1'
      ];

      for (const recommended of recommendedIndexes) {
        if (!indexNames.includes(recommended)) {
          missing.push(recommended);
        }
      }

    } catch (error) {
      console.error('Index check failed:', error);
    }

    return missing;
  }

  /**
   * Clear query cache
   */
  clearCache(pattern = null) {
    if (pattern) {
      for (const key of this.queryCache.keys()) {
        if (key.includes(pattern)) {
          this.queryCache.delete(key);
        }
      }
    } else {
      this.queryCache.clear();
    }
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    return {
      size: this.queryCache.size,
      keys: Array.from(this.queryCache.keys()),
      timeout: this.cacheTimeout
    };
  }
}

// Singleton instance
const performanceOptimizer = new PerformanceOptimizer();

module.exports = {
  PerformanceOptimizer,
  performanceOptimizer
};
