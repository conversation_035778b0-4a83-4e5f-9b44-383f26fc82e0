/**
 * Default Crops for Çiftçi Not Defterim
 * System-wide default crop definitions
 */

const DEFAULT_CROPS = [
  // Tahıl (Grains) - Seasonal
  {
    name: 'wheat',
    nameTr: 'Buğday',
    category: 'tahil',
    productionType: 'seasonal',
    emoji: '🌾',
    userId: null,
    isDefault: true,
    description: 'Temel tahıl ürünü, kış ve ilkbahar ekimi yapılır',
    growingSeason: {
      startMonth: 10,
      endMonth: 7,
      duration: 240
    },
    typicalExpenses: [
      {
        categoryName: 'Tohum',
        estimatedCost: { min: 500, max: 1000, currency: 'TRY' },
        timing: 'planting'
      },
      {
        categoryName: 'Gübre',
        estimatedCost: { min: 800, max: 1500, currency: 'TRY' },
        timing: 'growing'
      }
    ]
  },
  {
    name: 'corn',
    nameTr: 'Mısır',
    category: 'tahil',
    productionType: 'seasonal',
    emoji: '🌽',
    userId: null,
    isDefault: true,
    description: 'Yaz mevsimi tahıl ürün<PERSON>',
    growingSeason: {
      startMonth: 4,
      endMonth: 9,
      duration: 150
    }
  },
  {
    name: 'barley',
    nameTr: 'Arpa',
    category: 'tahil',
    productionType: 'seasonal',
    emoji: '🌾',
    userId: null,
    isDefault: true,
    description: 'Hayvan yemi ve malt üretimi için kullanılır'
  },
  {
    name: 'rice',
    nameTr: 'Pirinç',
    category: 'tahil',
    productionType: 'seasonal',
    emoji: '🌾',
    userId: null,
    isDefault: true,
    description: 'Su gerektiren tahıl ürünü'
  },

  // Sebze (Vegetables) - Mixed
  {
    name: 'tomato',
    nameTr: 'Domates',
    category: 'sebze',
    productionType: 'continuous',
    emoji: '🍅',
    userId: null,
    isDefault: true,
    description: 'Sera ve açık alan üretimi yapılabilir',
    growingSeason: {
      startMonth: 3,
      endMonth: 11,
      duration: 120
    }
  },
  {
    name: 'cucumber',
    nameTr: 'Salatalık',
    category: 'sebze',
    productionType: 'continuous',
    emoji: '🥒',
    userId: null,
    isDefault: true,
    description: 'Sera üretimi yaygın'
  },
  {
    name: 'pepper',
    nameTr: 'Biber',
    category: 'sebze',
    productionType: 'seasonal',
    emoji: '🌶️',
    userId: null,
    isDefault: true,
    description: 'Tatlı ve acı biber çeşitleri'
  },
  {
    name: 'lettuce',
    nameTr: 'Marul',
    category: 'sebze',
    productionType: 'continuous',
    emoji: '🥬',
    userId: null,
    isDefault: true,
    description: 'Kısa sürede hasat edilebilir'
  },
  {
    name: 'spinach',
    nameTr: 'Ispanak',
    category: 'sebze',
    productionType: 'continuous',
    emoji: '🥬',
    userId: null,
    isDefault: true,
    description: 'Soğuk havaya dayanıklı yapraklı sebze'
  },
  {
    name: 'onion',
    nameTr: 'Soğan',
    category: 'sebze',
    productionType: 'seasonal',
    emoji: '🧅',
    userId: null,
    isDefault: true,
    description: 'Uzun saklama süresi olan sebze'
  },
  {
    name: 'potato',
    nameTr: 'Patates',
    category: 'sebze',
    productionType: 'seasonal',
    emoji: '🥔',
    userId: null,
    isDefault: true,
    description: 'Temel besin kaynağı'
  },
  {
    name: 'carrot',
    nameTr: 'Havuç',
    category: 'sebze',
    productionType: 'seasonal',
    emoji: '🥕',
    userId: null,
    isDefault: true,
    description: 'Kök sebze türü'
  },

  // Meyve (Fruits) - Seasonal
  {
    name: 'apple',
    nameTr: 'Elma',
    category: 'meyve',
    productionType: 'seasonal',
    emoji: '🍎',
    userId: null,
    isDefault: true,
    description: 'Meyve ağacı, uzun vadeli yatırım',
    growingSeason: {
      startMonth: 8,
      endMonth: 11,
      duration: 90
    }
  },
  {
    name: 'grape',
    nameTr: 'Üzüm',
    category: 'meyve',
    productionType: 'seasonal',
    emoji: '🍇',
    userId: null,
    isDefault: true,
    description: 'Bağcılık, şarap ve sofralık üzüm'
  },
  {
    name: 'cherry',
    nameTr: 'Kiraz',
    category: 'meyve',
    productionType: 'seasonal',
    emoji: '🍒',
    userId: null,
    isDefault: true,
    description: 'Erken hasat meyve türü'
  },
  {
    name: 'peach',
    nameTr: 'Şeftali',
    category: 'meyve',
    productionType: 'seasonal',
    emoji: '🍑',
    userId: null,
    isDefault: true,
    description: 'Yaz meyvesi'
  },
  {
    name: 'orange',
    nameTr: 'Portakal',
    category: 'meyve',
    productionType: 'seasonal',
    emoji: '🍊',
    userId: null,
    isDefault: true,
    description: 'Akdeniz iklimi meyvesi'
  },

  // Baklagil (Legumes) - Seasonal
  {
    name: 'bean',
    nameTr: 'Fasulye',
    category: 'baklagil',
    productionType: 'seasonal',
    emoji: '🫘',
    userId: null,
    isDefault: true,
    description: 'Protein açısından zengin baklagil'
  },
  {
    name: 'chickpea',
    nameTr: 'Nohut',
    category: 'baklagil',
    productionType: 'seasonal',
    emoji: '🫛',
    userId: null,
    isDefault: true,
    description: 'Kuru baklagil türü'
  },
  {
    name: 'lentil',
    nameTr: 'Mercimek',
    category: 'baklagil',
    productionType: 'seasonal',
    emoji: '🫛',
    userId: null,
    isDefault: true,
    description: 'Kırmızı ve yeşil mercimek çeşitleri'
  },

  // Endüstriyel (Industrial) - Seasonal
  {
    name: 'cotton',
    nameTr: 'Pamuk',
    category: 'endüstriyel',
    productionType: 'seasonal',
    emoji: '🌾',
    userId: null,
    isDefault: true,
    description: 'Tekstil endüstrisi hammaddesi'
  },
  {
    name: 'sunflower',
    nameTr: 'Ayçiçeği',
    category: 'endüstriyel',
    productionType: 'seasonal',
    emoji: '🌻',
    userId: null,
    isDefault: true,
    description: 'Yağ üretimi için kullanılır'
  },
  {
    name: 'tobacco',
    nameTr: 'Tütün',
    category: 'endüstriyel',
    productionType: 'seasonal',
    emoji: '🌿',
    userId: null,
    isDefault: true,
    description: 'Tütün endüstrisi hammaddesi'
  },

  // Diğer (Other)
  {
    name: 'other',
    nameTr: 'Diğer',
    category: 'diğer',
    productionType: 'seasonal',
    emoji: '🌱',
    userId: null,
    isDefault: true,
    description: 'Belirtilmeyen diğer ürünler'
  }
];

/**
 * Initialize default crops in database
 */
async function initializeDefaultCrops() {
  try {
    const Crop = require('../models/Crop');
    
    // Check if default crops already exist
    const existingCrops = await Crop.find({ userId: null, isDefault: true });
    
    if (existingCrops.length === 0) {
      console.log('Initializing default crops...');
      
      for (const cropData of DEFAULT_CROPS) {
        const crop = new Crop(cropData);
        await crop.save();
      }
      
      console.log(`${DEFAULT_CROPS.length} default crops initialized successfully`);
    } else {
      console.log(`${existingCrops.length} default crops already exist`);
    }
    
    return true;
  } catch (error) {
    console.error('Error initializing default crops:', error);
    return false;
  }
}

/**
 * Get crops by category
 */
function getCropsByCategory(category) {
  return DEFAULT_CROPS.filter(crop => crop.category === category);
}

/**
 * Get crops by production type
 */
function getCropsByProductionType(productionType) {
  return DEFAULT_CROPS.filter(crop => crop.productionType === productionType);
}

module.exports = {
  DEFAULT_CROPS,
  initializeDefaultCrops,
  getCropsByCategory,
  getCropsByProductionType
};
