/**
 * Database Connection Utility for Çiftçi Not Defterim
 * Handles MongoDB connection with Mongoose
 */

const mongoose = require('mongoose');
const logger = require('./logger');

class DatabaseConnection {
  constructor() {
    this.connection = null;
    this.isConnected = false;
  }

  async connectDatabase() {
    try {
      // Connection options for MongoDB
      const options = {
        useNewUrlParser: true,
        useUnifiedTopology: true,
        maxPoolSize: 10, // Maintain up to 10 socket connections
        serverSelectionTimeoutMS: 5000, // Keep trying to send operations for 5 seconds
        socketTimeoutMS: 45000, // Close sockets after 45 seconds of inactivity
        family: 4, // Use IPv4, skip trying IPv6
        retryWrites: true,
        w: 'majority'
      };

      // Get MongoDB URI from environment
      const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/ciftci-notebook';
      
      logger.info('MongoDB bağlantısı kuruluyor...');
      
      // Connect to MongoDB
      this.connection = await mongoose.connect(mongoUri, options);
      this.isConnected = true;

      logger.info(`✅ MongoDB bağlantısı başarılı: ${this.connection.connection.host}:${this.connection.connection.port}`);
      logger.info(`📊 Veritabanı: ${this.connection.connection.name}`);

      // Connection event listeners
      mongoose.connection.on('connected', () => {
        logger.info('Mongoose MongoDB\'ye bağlandı');
      });

      mongoose.connection.on('error', (err) => {
        logger.error('Mongoose bağlantı hatası:', err);
        this.isConnected = false;
      });

      mongoose.connection.on('disconnected', () => {
        logger.warn('Mongoose MongoDB bağlantısı kesildi');
        this.isConnected = false;
      });

      // Handle application termination
      process.on('SIGINT', async () => {
        await this.closeConnection();
        process.exit(0);
      });

      return this.connection;
    } catch (error) {
      logger.error('MongoDB bağlantı hatası:', error);
      this.isConnected = false;

      // In development, continue without database
      if (process.env.NODE_ENV === 'development') {
        logger.warn('Development mode: MongoDB bağlantısı olmadan devam ediliyor');
        return null;
      }

      throw error;
    }
  }

  async closeConnection() {
    try {
      if (this.connection) {
        await mongoose.connection.close();
        this.isConnected = false;
        logger.info('MongoDB bağlantısı kapatıldı');
      }
    } catch (error) {
      logger.error('MongoDB bağlantısı kapatma hatası:', error);
      throw error;
    }
  }

  getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      readyState: mongoose.connection.readyState,
      host: mongoose.connection.host,
      port: mongoose.connection.port,
      name: mongoose.connection.name
    };
  }

  async healthCheck() {
    try {
      if (!this.isConnected) {
        return { status: 'disconnected', message: 'Veritabanı bağlantısı yok' };
      }

      // Simple ping to check connection
      await mongoose.connection.db.admin().ping();
      
      return {
        status: 'connected',
        message: 'Veritabanı bağlantısı aktif',
        details: this.getConnectionStatus()
      };
    } catch (error) {
      logger.error('Database health check hatası:', error);
      return {
        status: 'error',
        message: 'Veritabanı health check başarısız',
        error: error.message
      };
    }
  }
}

// Create singleton instance
const dbConnection = new DatabaseConnection();

// Export functions for backward compatibility
const connectDatabase = () => dbConnection.connectDatabase();
const closeDatabase = () => dbConnection.closeConnection();
const getDatabaseStatus = () => dbConnection.getConnectionStatus();
const databaseHealthCheck = () => dbConnection.healthCheck();

module.exports = {
  connectDatabase,
  closeDatabase,
  getDatabaseStatus,
  databaseHealthCheck,
  dbConnection
};
