/**
 * Main Application Class for Çiftçi Not Defterim Backend
 * Configures Express server with middleware, routes, and error handling
 */
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const path = require('path');
const { connectDatabase } = require('./utils/database');
const logger = require('./utils/logger');
const { errorHandler, notFoundHandler } = require('./middleware/errorHandler');
const rateLimiter = require('./middleware/rateLimiter');
const jobQueueService = require('./services/jobQueue');
const aiService = require('./services/aiService');
// Import routes
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/users');
const expenseRoutes = require('./routes/expenses');
const categoryRoutes = require('./routes/categories');
const seasonRoutes = require('./routes/seasons');
const syncRoutes = require('./routes/sync');
const uploadRoutes = require('./routes/upload');
class App {
  constructor() {
    this.app = express();
    this.setupMiddleware();
    this.setupRoutes();
    this.setupErrorHandling();
    this.initializeServices();
  }
  /**
   * Initialize background services
   */
  async initializeServices() {
    try {
      // Initialize job queue service
      await jobQueueService.initialize();
      // Initialize AI service
      await aiService.initialize();
      logger.info('Background services initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize background services:', error);
      // Continue without background services (fallback to sync processing)
    }
  }
  setupMiddleware() {
    // Security middleware
    this.app.use(helmet({
      crossOriginResourcePolicy: { policy: "cross-origin" }
    }));
    // CORS configuration
    const corsOptions = {
      origin: (origin, callback) => {
        const allowedOrigins = process.env.CORS_ORIGIN?.split(',') || ['*'];
        if (!origin || allowedOrigins.includes('*') || allowedOrigins.includes(origin)) {
          callback(null, true);
        } else {
          callback(new Error('CORS policy violation'));
        }
      },
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'Accept-Language']
    };
    this.app.use(cors(corsOptions));
    // Rate limiting
    this.app.use(rateLimiter.general);
    // Compression and parsing
    this.app.use(compression());
    // Disable ETag for development to prevent 304 responses
    this.app.set('etag', false);
    this.app.use(express.json({
      limit: '10mb',
      verify: (req, res, buf) => {
        try {
          JSON.parse(buf);
        } catch (e) {
          res.status(400).json({
            success: false,
            error: {
              code: 'INVALID_JSON',
              message: 'Geçersiz JSON formatı'
            }
          });
          return;
        }
      }
    }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));
    // Static file serving for uploads
    this.app.use('/uploads', express.static(path.join(__dirname, '../uploads')));
    // Detailed request/response logging middleware
    this.app.use((req, res, next) => {
      // Skip health check
      if (req.url === '/health') {
        return next();
      }
      // Log incoming request
      logger.info(`📥 INCOMING REQUEST: ${req.method} ${req.url}`, {
        headers: {
          authorization: req.headers.authorization ? 'Bearer ***' : 'None',
          'content-type': req.headers['content-type']
        },
        body: req.method !== 'GET' ? req.body : undefined,
        query: Object.keys(req.query).length > 0 ? req.query : undefined
      });
      // Capture original res.json to log response
      const originalJson = res.json;
      res.json = function(data) {
        const responseInfo = {
          success: data?.success,
          dataType: Array.isArray(data?.data) ? `Array(${data.data.length})` : typeof data?.data,
          dataLength: Array.isArray(data?.data) ? data.data.length : undefined,
          totalAmount: Array.isArray(data?.data) ? data.data.reduce((sum, item) => sum + (item.amount || 0), 0) : undefined,
          firstItem: Array.isArray(data?.data) && data.data.length > 0 ? {
            id: data.data[0]._id,
            amount: data.data[0].amount,
            categoryId: data.data[0].categoryId,
            date: data.data[0].date
          } : undefined,
          fullData: req.url.includes('expenses') ? data : undefined
        };
        logger.info(`📤 OUTGOING RESPONSE: ${req.method} ${req.url} [${res.statusCode}]`, responseInfo);
        return originalJson.call(this, data);
      };
      next();
    });
    // Logging middleware
    if (process.env.NODE_ENV !== 'test') {
      this.app.use(morgan('combined', {
        stream: {
          write: (message) => logger.info(message.trim())
        },
        skip: (req) => req.url === '/health'
      }));
    }
    // Request ID middleware
    this.app.use((req, res, next) => {
      req.requestId = require('uuid').v4();
      res.setHeader('X-Request-ID', req.requestId);
      next();
    });
    // Language detection middleware
    this.app.use((req, res, next) => {
      const acceptLanguage = req.headers['accept-language'];
      req.language = acceptLanguage?.startsWith('tr') ? 'tr' : 'tr'; // Default to Turkish
      next();
    });
  }
  setupRoutes() {
    const apiVersion = process.env.API_VERSION || 'v1';
    // Health check endpoint
    this.app.get('/health', (req, res) => {
      res.json({
        success: true,
        message: 'Çiftçi Not Defterim API çalışıyor',
        timestamp: new Date().toISOString(),
        version: apiVersion,
        environment: process.env.NODE_ENV,
        uptime: process.uptime()
      });
    });
    // API documentation endpoint (development only)
    if (process.env.NODE_ENV === 'development' && process.env.ENABLE_SWAGGER === 'true') {
      this.app.get('/api-docs', (req, res) => {
        res.json({
          message: 'API Documentation',
          documentation: '/api-docs.html',
          openapi: '/api/v1/openapi.json'
        });
      });
    }
    // API routes
    const apiRouter = express.Router();
    // Mount route modules
    apiRouter.use('/auth', authRoutes);
    apiRouter.use('/users', userRoutes);
    apiRouter.use('/expenses', expenseRoutes);
    apiRouter.use('/categories', categoryRoutes);
    apiRouter.use('/seasons', seasonRoutes);
    apiRouter.use('/sync', syncRoutes);
    apiRouter.use('/upload', uploadRoutes);
    // TWO-MODE SYSTEM: New routes
    apiRouter.use('/fields', require('./routes/fields'));
    apiRouter.use('/crops', require('./routes/crops'));
    // AI IMPORT/EXPORT SYSTEM: New routes
    apiRouter.use('/import', require('./routes/import'));
    // Mount API router
    this.app.use(`/api/${apiVersion}`, apiRouter);
    // Root endpoint
    this.app.get('/', (req, res) => {
      res.json({
        success: true,
        message: 'Çiftçi Not Defterim API',
        version: apiVersion,
        documentation: process.env.NODE_ENV === 'development' ? '/api-docs' : undefined,
        endpoints: {
          health: '/health',
          api: `/api/${apiVersion}`
        }
      });
    });
    // 404 handler for API routes
    this.app.use('/api/*', (req, res) => {
      res.status(404).json({
        success: false,
        error: {
          code: 'ENDPOINT_NOT_FOUND',
          message: 'İstenen API endpoint\'i bulunamadı',
          path: req.path,
          method: req.method
        },
        timestamp: new Date().toISOString(),
        requestId: req.requestId
      });
    });
    // 404 handler for all other routes
    this.app.use('*', (req, res) => {
      res.status(404).json({
        success: false,
        error: {
          code: 'ROUTE_NOT_FOUND',
          message: 'İstenen sayfa bulunamadı',
          path: req.path
        },
        timestamp: new Date().toISOString(),
        requestId: req.requestId
      });
    });
  }
  setupErrorHandling() {
    // 404 handler for unmatched routes
    this.app.use(notFoundHandler);
    // Global error handler
    this.app.use(errorHandler);
  }
  async start() {
    try {
      // Connect to database
      await connectDatabase();
      // Create uploads directory if it doesn't exist
      const fs = require('fs');
      const uploadsDir = path.join(__dirname, '../uploads');
      if (!fs.existsSync(uploadsDir)) {
        fs.mkdirSync(uploadsDir, { recursive: true });
        logger.info('Created uploads directory');
      }
      // Start server
      const port = process.env.PORT || 3000;
      const server = this.app.listen(port, () => {
        logger.info(`🚀 Çiftçi Not Defterim API sunucusu ${port} portunda çalışıyor`);
        logger.info(`📱 Environment: ${process.env.NODE_ENV}`);
        logger.info(`🌐 API Base URL: http://localhost:${port}/api/${process.env.API_VERSION || 'v1'}`);
        if (process.env.NODE_ENV === 'development') {
          logger.info(`📚 Health Check: http://localhost:${port}/health`);
          if (process.env.ENABLE_SWAGGER === 'true') {
            logger.info(`📖 API Docs: http://localhost:${port}/api-docs`);
          }
        }
      });
      // Graceful shutdown
      const gracefulShutdown = () => {
        logger.info('Sunucu kapatılıyor...');
        server.close(() => {
          logger.info('HTTP sunucusu kapatıldı');
          process.exit(0);
        });
      };
      process.on('SIGTERM', gracefulShutdown);
      process.on('SIGINT', gracefulShutdown);
      return server;
    } catch (error) {
      logger.error('Sunucu başlatma hatası:', error);
      throw error;
    }
  }
}
module.exports = App;
