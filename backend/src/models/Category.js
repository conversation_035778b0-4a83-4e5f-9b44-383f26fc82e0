/**
 * Category Model for Çiftçi Not Defterim
 * Represents expense categories (both default and user-created)
 */

const mongoose = require('mongoose');
const { DEFAULT_CATEGORIES } = require('../utils/constants');

const categorySchema = new mongoose.Schema({
  // Basic Information
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 50,
    index: true
  },
  
  emoji: {
    type: String,
    required: true,
    validate: {
      validator: function(v) {
        // Very flexible validation - just check if it's not empty
        return v && v.length > 0;
      },
      message: 'Emoji alanı boş olamaz'
    }
  },
  
  color: {
    type: String,
    required: true,
    validate: {
      validator: function(v) {
        return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(v);
      },
      message: 'Geçersiz renk formatı (hex renk kodu bekleniyor)'
    }
  },
  
  icon: {
    type: String,
    trim: true,
    maxlength: 50
  },
  
  description: {
    type: String,
    trim: true,
    maxlength: 200
  },
  
  // Ownership
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null, // null for default categories
    index: true
  },
  
  isDefault: {
    type: Boolean,
    default: false,
    index: true
  },
  
  // Category Status
  isActive: {
    type: Boolean,
    default: true
  },
  
  // Usage Statistics
  usageCount: {
    type: Number,
    default: 0,
    min: 0
  },
  
  totalAmount: {
    type: Number,
    default: 0,
    min: 0
  },
  
  lastUsedAt: {
    type: Date,
    default: null
  },
  
  // Agricultural Context
  seasonalRelevance: [{
    season: {
      type: String,
      enum: ['spring', 'summer', 'autumn', 'winter']
    },
    relevanceScore: {
      type: Number,
      min: 0,
      max: 10,
      default: 5
    }
  }],
  
  cropTypes: [{
    type: String,
    trim: true
  }],
  
  // Category Hierarchy (for subcategories)
  parentCategory: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category',
    default: null
  },
  
  subcategories: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category'
  }],
  
  // Display Order
  sortOrder: {
    type: Number,
    default: 0
  },
  
  // Sync Information
  lastSyncAt: {
    type: Date,
    default: Date.now
  },
  
  syncVersion: {
    type: Number,
    default: 1
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Compound indexes for performance
categorySchema.index({ userId: 1, isActive: 1 });
categorySchema.index({ isDefault: 1, isActive: 1 });
categorySchema.index({ usageCount: -1 });
categorySchema.index({ name: 'text', description: 'text' });
categorySchema.index({ 'seasonalRelevance.season': 1 });

// Unique constraint for user categories (user can't have duplicate category names)
categorySchema.index(
  { userId: 1, name: 1 },
  { 
    unique: true,
    partialFilterExpression: { userId: { $ne: null } }
  }
);

// Virtual for category type
categorySchema.virtual('type').get(function() {
  return this.isDefault ? 'default' : 'custom';
});

// Virtual for full display name
categorySchema.virtual('displayName').get(function() {
  return `${this.emoji} ${this.name}`;
});

// Virtual for usage frequency
categorySchema.virtual('usageFrequency').get(function() {
  if (this.usageCount === 0) return 'never';
  if (this.usageCount < 5) return 'low';
  if (this.usageCount < 20) return 'medium';
  return 'high';
});

// Virtual for seasonal relevance score
categorySchema.virtual('currentSeasonRelevance').get(function() {
  const currentMonth = new Date().getMonth() + 1;
  let currentSeason;

  if (currentMonth >= 3 && currentMonth <= 5) currentSeason = 'spring';
  else if (currentMonth >= 6 && currentMonth <= 8) currentSeason = 'summer';
  else if (currentMonth >= 9 && currentMonth <= 11) currentSeason = 'autumn';
  else currentSeason = 'winter';

  // Check if seasonalRelevance exists and is an array
  if (!this.seasonalRelevance || !Array.isArray(this.seasonalRelevance)) {
    return 5; // Default relevance score
  }

  const seasonalData = this.seasonalRelevance.find(s => s.season === currentSeason);
  return seasonalData ? seasonalData.relevanceScore : 5;
});

// Pre-save middleware
categorySchema.pre('save', function(next) {
  // Update sync version on changes
  if (this.isModified() && !this.isNew) {
    this.syncVersion += 1;
    this.lastSyncAt = new Date();
  }
  
  // Set default seasonal relevance if not provided
  if (this.isNew && (!this.seasonalRelevance || this.seasonalRelevance.length === 0)) {
    this.seasonalRelevance = [
      { season: 'spring', relevanceScore: 5 },
      { season: 'summer', relevanceScore: 5 },
      { season: 'autumn', relevanceScore: 5 },
      { season: 'winter', relevanceScore: 5 }
    ];
  }
  
  next();
});

// Instance methods
categorySchema.methods.incrementUsage = function(amount = 0) {
  this.usageCount += 1;
  this.totalAmount += amount;
  this.lastUsedAt = new Date();
  return this.save();
};

categorySchema.methods.updateSeasonalRelevance = function(season, score) {
  // Initialize seasonalRelevance if it doesn't exist
  if (!this.seasonalRelevance || !Array.isArray(this.seasonalRelevance)) {
    this.seasonalRelevance = [];
  }

  const seasonalData = this.seasonalRelevance.find(s => s.season === season);
  if (seasonalData) {
    seasonalData.relevanceScore = score;
  } else {
    this.seasonalRelevance.push({ season, relevanceScore: score });
  }
  return this.save();
};

categorySchema.methods.addSubcategory = function(subcategoryId) {
  if (!this.subcategories.includes(subcategoryId)) {
    this.subcategories.push(subcategoryId);
    return this.save();
  }
  return Promise.resolve(this);
};

categorySchema.methods.toPublicJSON = function() {
  const category = this.toObject();
  
  // Remove internal fields
  delete category.syncVersion;
  delete category.lastSyncAt;
  
  return category;
};

// Static methods
categorySchema.statics.getDefaultCategories = function() {
  return this.find({ isDefault: true, isActive: true }).sort({ sortOrder: 1, name: 1 });
};

categorySchema.statics.getUserCategories = function(userId) {
  return this.find({ 
    $or: [
      { isDefault: true },
      { userId: userId }
    ],
    isActive: true 
  }).sort({ isDefault: -1, usageCount: -1, name: 1 });
};

categorySchema.statics.getPopularCategories = function(limit = 10) {
  return this.find({ isActive: true })
    .sort({ usageCount: -1, totalAmount: -1 })
    .limit(limit);
};

categorySchema.statics.getSeasonalCategories = function(season, limit = 10) {
  return this.find({
    isActive: true,
    'seasonalRelevance.season': season
  })
  .sort({ 'seasonalRelevance.relevanceScore': -1 })
  .limit(limit);
};

categorySchema.statics.searchCategories = function(query, userId = null) {
  const searchQuery = {
    $text: { $search: query },
    isActive: true
  };
  
  if (userId) {
    searchQuery.$or = [
      { isDefault: true },
      { userId: userId }
    ];
  }
  
  return this.find(searchQuery, { score: { $meta: 'textScore' } })
    .sort({ score: { $meta: 'textScore' } });
};

categorySchema.statics.getCategoryStats = function(userId = null) {
  const matchQuery = { isActive: true };
  if (userId) {
    matchQuery.$or = [
      { isDefault: true },
      { userId: userId }
    ];
  }
  
  return this.aggregate([
    { $match: matchQuery },
    {
      $group: {
        _id: null,
        totalCategories: { $sum: 1 },
        defaultCategories: {
          $sum: { $cond: ['$isDefault', 1, 0] }
        },
        customCategories: {
          $sum: { $cond: ['$isDefault', 0, 1] }
        },
        totalUsage: { $sum: '$usageCount' },
        totalAmount: { $sum: '$totalAmount' },
        avgUsage: { $avg: '$usageCount' }
      }
    }
  ]);
};

// Create default categories if they don't exist
categorySchema.statics.ensureDefaultCategories = async function() {
  const existingDefaults = await this.find({ isDefault: true });
  
  if (existingDefaults.length === 0) {
    const defaultCategories = DEFAULT_CATEGORIES.map((cat, index) => ({
      ...cat,
      sortOrder: index,
      isDefault: true,
      isActive: true
    }));
    
    await this.insertMany(defaultCategories);
    console.log('Default categories created');
  }
};

module.exports = mongoose.model('Category', categorySchema);
