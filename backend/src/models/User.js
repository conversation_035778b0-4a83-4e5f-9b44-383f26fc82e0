/**
 * User Model for Çiftçi Not Defterim
 * Represents farmers and users of the application
 */

const mongoose = require('mongoose');
const { DEFAULT_CATEGORIES } = require('../utils/constants');

const userSchema = new mongoose.Schema({
  // Firebase Authentication
  firebaseUid: {
    type: String,
    required: true,
    unique: true
  },

  // Basic Information
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true
  },
  
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  
  // Authentication Details
  emailVerified: {
    type: Boolean,
    default: false
  },
  
  picture: {
    type: String,
    default: null
  },
  
  provider: {
    type: String,
    enum: ['google.com', 'facebook.com', 'apple.com', 'email'],
    default: 'google.com'
  },
  
  // User Preferences
  preferences: {
    currency: {
      type: String,
      enum: ['TRY', 'USD', 'EUR'],
      default: 'TRY'
    },
    
    language: {
      type: String,
      enum: ['tr', 'en'],
      default: 'tr'
    },
    
    defaultSeason: {
      type: String,
      enum: ['spring', 'summer', 'autumn', 'winter'],
      default: 'spring'
    },
    
    timezone: {
      type: String,
      default: 'Europe/Istanbul'
    },
    
    notifications: {
      email: {
        type: Boolean,
        default: true
      },
      push: {
        type: Boolean,
        default: true
      },
      weeklyReport: {
        type: Boolean,
        default: true
      },
      monthlyReport: {
        type: Boolean,
        default: true
      }
    },
    
    privacy: {
      shareData: {
        type: Boolean,
        default: false
      },
      analytics: {
        type: Boolean,
        default: true
      }
    },

    // TWO-MODE SYSTEM: Tracking Mode Preference
    trackingMode: {
      type: String,
      enum: ['simple', 'detailed'],
      default: 'simple'
    },

    // Detailed Mode Activation Date
    detailedModeActivatedAt: {
      type: Date,
      default: null
    }
  },
  
  // Farm Information (Optional)
  farmInfo: {
    name: {
      type: String,
      trim: true,
      maxlength: 100
    },
    
    location: {
      address: String,
      city: String,
      region: String,
      country: {
        type: String,
        default: 'Turkey'
      },
      coordinates: {
        latitude: Number,
        longitude: Number
      }
    },
    
    size: {
      value: Number,
      unit: {
        type: String,
        enum: ['dekar', 'dönüm', 'hectare', 'acre'],
        default: 'dekar'
      }
    },
    
    cropTypes: [{
      type: String,
      trim: true
    }],
    
    establishedYear: Number
  },
  
  // User Status
  status: {
    type: String,
    enum: ['active', 'inactive', 'suspended', 'deleted'],
    default: 'active'
  },
  
  type: {
    type: String,
    enum: ['free', 'premium', 'admin'],
    default: 'free'
  },
  
  isAdmin: {
    type: Boolean,
    default: false
  },
  
  // Usage Statistics
  stats: {
    totalExpenses: {
      type: Number,
      default: 0
    },
    
    totalAmount: {
      type: Number,
      default: 0
    },
    
    categoriesCreated: {
      type: Number,
      default: 0
    },
    
    lastExpenseDate: Date,
    
    loginCount: {
      type: Number,
      default: 0
    },
    
    lastLoginAt: Date,
    
    registrationCompleted: {
      type: Boolean,
      default: false
    },

    // AI IMPORT/EXPORT SYSTEM: Import/Export Statistics
    importStats: {
      totalImports: {
        type: Number,
        default: 0
      },

      successfulImports: {
        type: Number,
        default: 0
      },

      lastImportDate: Date,

      totalRecordsImported: {
        type: Number,
        default: 0
      },

      aiImportsUsed: {
        type: Number,
        default: 0
      }
    },

    exportStats: {
      totalExports: {
        type: Number,
        default: 0
      },

      lastExportDate: Date,

      totalRecordsExported: {
        type: Number,
        default: 0
      }
    }
  },
  
  // Subscription Information (for premium features)
  subscription: {
    plan: {
      type: String,
      enum: ['free', 'premium', 'enterprise'],
      default: 'free'
    },
    
    startDate: Date,
    endDate: Date,
    
    features: [{
      name: String,
      enabled: Boolean
    }],
    
    paymentMethod: String,
    customerId: String // Stripe/payment provider customer ID
  },
  
  // Data Management
  dataRetention: {
    deleteAfterInactive: {
      type: Number,
      default: 365 // days
    },
    
    lastDataExport: Date,
    
    gdprConsent: {
      given: Boolean,
      date: Date,
      version: String
    }
  },
  
  // Sync Information
  lastSyncAt: {
    type: Date,
    default: Date.now
  },
  
  syncVersion: {
    type: Number,
    default: 1
  },
  
  // Device Information
  devices: [{
    deviceId: String,
    platform: {
      type: String,
      enum: ['ios', 'android', 'web']
    },
    appVersion: String,
    lastSeen: Date,
    pushToken: String
  }]
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for performance
userSchema.index({ 'farmInfo.location.city': 1 });
userSchema.index({ status: 1, type: 1 });
userSchema.index({ lastLoginAt: -1 });
userSchema.index({ createdAt: -1 });

// Virtual for full name
userSchema.virtual('fullName').get(function() {
  return this.name;
});

// Virtual for farm display name
userSchema.virtual('farmDisplayName').get(function() {
  return this.farmInfo?.name || `${this.name}'in Çiftliği`;
});

// Virtual for subscription status
userSchema.virtual('isSubscriptionActive').get(function() {
  if (this.subscription.plan === 'free') return true;
  if (!this.subscription.endDate) return false;
  return new Date() < this.subscription.endDate;
});

// Pre-save middleware
userSchema.pre('save', function(next) {
  // Update login count and last login
  if (this.isModified('stats.lastLoginAt')) {
    this.stats.loginCount += 1;
  }
  
  // Set registration completed if basic info is filled
  if (!this.stats.registrationCompleted && this.name && this.email) {
    this.stats.registrationCompleted = true;
  }
  
  next();
});

// Instance methods
userSchema.methods.updateLoginInfo = function() {
  this.stats.lastLoginAt = new Date();
  this.stats.loginCount += 1;
  return this.save();
};

userSchema.methods.addDevice = function(deviceInfo) {
  // Remove existing device with same deviceId
  this.devices = this.devices.filter(d => d.deviceId !== deviceInfo.deviceId);
  
  // Add new device info
  this.devices.push({
    ...deviceInfo,
    lastSeen: new Date()
  });
  
  // Keep only last 5 devices
  if (this.devices.length > 5) {
    this.devices = this.devices.slice(-5);
  }
  
  return this.save();
};

userSchema.methods.updateStats = function(expenseData) {
  this.stats.totalExpenses += 1;
  this.stats.totalAmount += expenseData.amount;
  this.stats.lastExpenseDate = expenseData.date;
  return this.save();
};

userSchema.methods.toPublicJSON = function() {
  const user = this.toObject();
  
  // Remove sensitive information
  delete user.firebaseUid;
  delete user.devices;
  delete user.subscription.customerId;
  delete user.dataRetention;
  
  return user;
};

// Static methods
userSchema.statics.findByFirebaseUid = function(firebaseUid) {
  return this.findOne({ firebaseUid });
};

userSchema.statics.findActiveUsers = function() {
  return this.find({ status: 'active' });
};

userSchema.statics.getUserStats = function() {
  return this.aggregate([
    {
      $group: {
        _id: null,
        totalUsers: { $sum: 1 },
        activeUsers: {
          $sum: {
            $cond: [{ $eq: ['$status', 'active'] }, 1, 0]
          }
        },
        premiumUsers: {
          $sum: {
            $cond: [{ $eq: ['$type', 'premium'] }, 1, 0]
          }
        },
        avgExpenses: { $avg: '$stats.totalExpenses' },
        avgAmount: { $avg: '$stats.totalAmount' }
      }
    }
  ]);
};

module.exports = mongoose.model('User', userSchema);
