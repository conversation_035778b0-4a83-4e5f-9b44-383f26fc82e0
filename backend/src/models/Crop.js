/**
 * Crop Model for Çiftçi Not Defterim
 * Represents agricultural crops/products for detailed tracking
 */

const mongoose = require('mongoose');

const cropSchema = new mongoose.Schema({
  // Basic Crop Information
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 50,
    lowercase: true,
    validate: {
      validator: function(v) {
        return v && v.length > 0;
      },
      message: '<PERSON>rün adı boş olamaz'
    }
  },
  
  // Turkish Name
  nameTr: {
    type: String,
    required: true,
    trim: true,
    maxlength: 50,
    validate: {
      validator: function(v) {
        return v && v.length > 0;
      },
      message: 'Türkçe ürün adı boş olamaz'
    }
  },
  
  // Crop Category
  category: {
    type: String,
    enum: ['tahil', 'sebze', 'meyve', 'baklagil', 'endüstriyel', 'diğer'],
    required: true,
    default: 'diğer'
  },
  
  // IMPORTANT: Production Type
  productionType: {
    type: String,
    enum: ['seasonal', 'continuous'],
    required: true,
    validate: {
      validator: function(v) {
        return ['seasonal', 'continuous'].includes(v);
      },
      message: 'Üretim tipi seasonal veya continuous olmalıdır'
    }
  },
  
  // Visual
  emoji: {
    type: String,
    default: '🌱',
    maxlength: 10
  },
  
  // User Ownership (NEW)
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null, // null = system default crop
    index: true
  },
  
  // Is System Default?
  isDefault: {
    type: Boolean,
    default: true
  },
  
  // Is Active?
  isActive: {
    type: Boolean,
    default: true
  },
  
  // Additional Information
  description: {
    type: String,
    trim: true,
    maxlength: 200
  },
  
  // Growing Season Information
  growingSeason: {
    startMonth: {
      type: Number,
      min: 1,
      max: 12
    },
    endMonth: {
      type: Number,
      min: 1,
      max: 12
    },
    duration: {
      type: Number, // in days
      min: 1
    }
  },
  
  // Typical Expenses for this crop
  typicalExpenses: [{
    categoryName: String,
    estimatedCost: {
      min: Number,
      max: Number,
      currency: {
        type: String,
        default: 'TRY'
      }
    },
    timing: String // e.g., "planting", "growing", "harvest"
  }],
  
  // Statistics
  stats: {
    usageCount: {
      type: Number,
      default: 0
    },
    totalExpenses: {
      type: Number,
      default: 0
    },
    totalAmount: {
      type: Number,
      default: 0
    },
    avgExpenseAmount: {
      type: Number,
      default: 0
    },
    lastUsed: Date
  },
  
  // Sync Information
  lastSyncAt: {
    type: Date,
    default: Date.now
  },
  
  syncVersion: {
    type: Number,
    default: 1
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
cropSchema.index({ isDefault: 1, isActive: 1 });
cropSchema.index({ category: 1, isActive: 1 });
cropSchema.index({ productionType: 1, isActive: 1 });
cropSchema.index({ userId: 1, isActive: 1 });

// Unique crop name per user (for custom crops)
cropSchema.index(
  { userId: 1, name: 1 },
  {
    unique: true,
    partialFilterExpression: { userId: { $ne: null } }
  }
);

// Virtual for formatted production type
cropSchema.virtual('productionTypeDisplay').get(function() {
  return this.productionType === 'seasonal' ? 'Sezonluk' : 'Sürekli';
});

// Virtual for category display
cropSchema.virtual('categoryDisplay').get(function() {
  const categoryMap = {
    'tahil': 'Tahıl',
    'sebze': 'Sebze',
    'meyve': 'Meyve',
    'baklagil': 'Baklagil',
    'endüstriyel': 'Endüstriyel',
    'diğer': 'Diğer'
  };
  return categoryMap[this.category] || this.category;
});

// Virtual for expense count
cropSchema.virtual('expenseCount', {
  ref: 'Expense',
  localField: '_id',
  foreignField: 'cropId',
  count: true
});

// Pre-save middleware
cropSchema.pre('save', function(next) {
  // Update stats
  if (this.stats.totalExpenses > 0) {
    this.stats.avgExpenseAmount = this.stats.totalAmount / this.stats.totalExpenses;
  }
  
  next();
});

// Instance methods
cropSchema.methods.toPublicJSON = function() {
  const crop = this.toObject();
  
  // Remove internal fields
  delete crop.syncVersion;
  delete crop.lastSyncAt;
  
  return crop;
};

cropSchema.methods.updateStats = async function() {
  const Expense = mongoose.model('Expense');
  
  const stats = await Expense.aggregate([
    { $match: { cropId: this._id, status: 'active' } },
    {
      $group: {
        _id: null,
        totalExpenses: { $sum: 1 },
        totalAmount: { $sum: '$amount' },
        lastUsed: { $max: '$date' }
      }
    }
  ]);
  
  if (stats.length > 0) {
    this.stats.totalExpenses = stats[0].totalExpenses;
    this.stats.totalAmount = stats[0].totalAmount;
    this.stats.lastUsed = stats[0].lastUsed;
    this.stats.avgExpenseAmount = this.stats.totalAmount / this.stats.totalExpenses;
    this.stats.usageCount = stats[0].totalExpenses;
  } else {
    this.stats.totalExpenses = 0;
    this.stats.totalAmount = 0;
    this.stats.avgExpenseAmount = 0;
    this.stats.usageCount = 0;
    this.stats.lastUsed = null;
  }
  
  await this.save();
};

// Static methods
cropSchema.statics.getDefaultCrops = function() {
  return this.find({ 
    userId: null, 
    isDefault: true, 
    isActive: true 
  }).sort({ category: 1, nameTr: 1 });
};

cropSchema.statics.getUserCrops = function(userId) {
  return this.find({
    $or: [
      { userId: null, isDefault: true }, // System crops
      { userId: userId }                 // User crops
    ],
    isActive: true
  }).sort({ isDefault: -1, category: 1, nameTr: 1 });
};

cropSchema.statics.getPopularCrops = function(limit = 10) {
  return this.find({ isActive: true })
    .sort({ 'stats.usageCount': -1, 'stats.totalAmount': -1 })
    .limit(limit);
};

cropSchema.statics.getCropsByCategory = function(category, userId = null) {
  const query = { category, isActive: true };
  
  if (userId) {
    query.$or = [
      { userId: null, isDefault: true },
      { userId: userId }
    ];
  } else {
    query.userId = null;
    query.isDefault = true;
  }
  
  return this.find(query).sort({ nameTr: 1 });
};

cropSchema.statics.getCropsByProductionType = function(productionType, userId = null) {
  const query = { productionType, isActive: true };
  
  if (userId) {
    query.$or = [
      { userId: null, isDefault: true },
      { userId: userId }
    ];
  } else {
    query.userId = null;
    query.isDefault = true;
  }
  
  return this.find(query).sort({ category: 1, nameTr: 1 });
};

module.exports = mongoose.model('Crop', cropSchema);
