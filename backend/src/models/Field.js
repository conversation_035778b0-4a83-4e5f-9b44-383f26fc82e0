/**
 * Field Model for Çiftçi Not Defterim
 * Represents agricultural fields/plots for detailed tracking mode
 */

const mongoose = require('mongoose');

const fieldSchema = new mongoose.Schema({
  // Basic Information
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 50,
    validate: {
      validator: function(v) {
        return v && v.length > 0;
      },
      message: 'Tarla adı boş olamaz'
    }
  },
  
  // Location Information
  location: {
    address: {
      type: String,
      trim: true,
      maxlength: 200
    },
    coordinates: {
      latitude: {
        type: Number,
        min: -90,
        max: 90
      },
      longitude: {
        type: Number,
        min: -180,
        max: 180
      }
    }
  },
  
  // Size Information
  size: {
    value: {
      type: Number,
      min: 0,
      validate: {
        validator: function(v) {
          return v >= 0;
        },
        message: 'Alan değeri negatif olamaz'
      }
    },
    unit: {
      type: String,
      enum: ['dekar', 'dönüm', 'hectare', 'acre'],
      default: 'dekar'
    }
  },
  
  // Status
  isActive: {
    type: Boolean,
    default: true
  },
  
  // Default Field Marker
  isDefault: {
    type: Boolean,
    default: false
  },
  
  // Notes
  notes: {
    type: String,
    trim: true,
    maxlength: 200
  },
  
  // Soil Information (optional)
  soilType: {
    type: String,
    enum: ['clay', 'sandy', 'loamy', 'silty', 'peaty', 'chalky', 'other'],
    default: 'other'
  },
  
  // Irrigation Information
  irrigationType: {
    type: String,
    enum: ['drip', 'sprinkler', 'flood', 'manual', 'none'],
    default: 'none'
  },
  
  // Statistics
  stats: {
    totalExpenses: {
      type: Number,
      default: 0
    },
    totalAmount: {
      type: Number,
      default: 0
    },
    lastExpenseDate: Date,
    cropCount: {
      type: Number,
      default: 0
    }
  },
  
  // Sync Information
  lastSyncAt: {
    type: Date,
    default: Date.now
  },
  
  syncVersion: {
    type: Number,
    default: 1
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
fieldSchema.index({ userId: 1, isActive: 1 });
fieldSchema.index({ userId: 1, name: 1 });

// Ensure only one default field per user
fieldSchema.index(
  { userId: 1, isDefault: 1 },
  {
    unique: true,
    partialFilterExpression: { isDefault: true }
  }
);

// Virtual for formatted size
fieldSchema.virtual('formattedSize').get(function() {
  if (!this.size || !this.size.value) return null;
  return `${this.size.value} ${this.size.unit}`;
});

// Virtual for expense count
fieldSchema.virtual('expenseCount', {
  ref: 'Expense',
  localField: '_id',
  foreignField: 'fieldId',
  count: true
});

// Pre-save middleware
fieldSchema.pre('save', async function(next) {
  // If this is being set as default, unset other defaults for this user
  if (this.isDefault && this.isModified('isDefault')) {
    await this.constructor.updateMany(
      { 
        userId: this.userId, 
        _id: { $ne: this._id },
        isDefault: true 
      },
      { isDefault: false }
    );
  }
  
  // Ensure at least one field is default for the user
  if (!this.isDefault) {
    const defaultField = await this.constructor.findOne({
      userId: this.userId,
      isDefault: true,
      _id: { $ne: this._id }
    });
    
    if (!defaultField) {
      this.isDefault = true;
    }
  }
  
  next();
});

// Pre-remove middleware
fieldSchema.pre('remove', async function(next) {
  // If removing default field, set another field as default
  if (this.isDefault) {
    const anotherField = await this.constructor.findOne({
      userId: this.userId,
      _id: { $ne: this._id },
      isActive: true
    });
    
    if (anotherField) {
      anotherField.isDefault = true;
      await anotherField.save();
    }
  }
  
  next();
});

// Instance methods
fieldSchema.methods.toPublicJSON = function() {
  const field = this.toObject();
  
  // Remove internal fields
  delete field.syncVersion;
  delete field.lastSyncAt;
  
  return field;
};

fieldSchema.methods.updateStats = async function() {
  const Expense = mongoose.model('Expense');
  
  const stats = await Expense.aggregate([
    { $match: { fieldId: this._id, status: 'active' } },
    {
      $group: {
        _id: null,
        totalExpenses: { $sum: 1 },
        totalAmount: { $sum: '$amount' },
        lastExpenseDate: { $max: '$date' }
      }
    }
  ]);
  
  if (stats.length > 0) {
    this.stats.totalExpenses = stats[0].totalExpenses;
    this.stats.totalAmount = stats[0].totalAmount;
    this.stats.lastExpenseDate = stats[0].lastExpenseDate;
  } else {
    this.stats.totalExpenses = 0;
    this.stats.totalAmount = 0;
    this.stats.lastExpenseDate = null;
  }
  
  await this.save();
};

// Static methods
fieldSchema.statics.getUserFields = function(userId, includeInactive = false) {
  const query = { userId };
  if (!includeInactive) {
    query.isActive = true;
  }
  
  return this.find(query)
    .sort({ isDefault: -1, name: 1 })
    .populate('expenseCount');
};

fieldSchema.statics.getDefaultField = function(userId) {
  return this.findOne({ userId, isDefault: true, isActive: true });
};

fieldSchema.statics.createDefaultField = async function(userId) {
  const existingDefault = await this.findOne({ userId, isDefault: true });
  
  if (existingDefault) {
    return existingDefault;
  }
  
  const defaultField = new this({
    userId,
    name: 'Ana Tarla',
    isDefault: true,
    isActive: true,
    notes: 'Otomatik oluşturulan varsayılan tarla'
  });
  
  return await defaultField.save();
};

module.exports = mongoose.model('Field', fieldSchema);
