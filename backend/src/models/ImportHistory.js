/**
 * Import History Model for Çiftçi Not Defterim
 * Tracks import operations and their results
 */

const mongoose = require('mongoose');

const importHistorySchema = new mongoose.Schema({
  // User reference
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },

  // Import details
  fileName: {
    type: String,
    required: true,
    trim: true,
    maxlength: 255
  },

  originalFileName: {
    type: String,
    required: true,
    trim: true,
    maxlength: 255
  },

  fileSize: {
    type: Number,
    required: true,
    min: 0
  },

  filePath: {
    type: String,
    required: false, // File might be cleaned up
    trim: true
  },

  // Import type and method
  importType: {
    type: String,
    enum: ['excel', 'ai-chat', 'manual'],
    required: true,
    default: 'excel'
  },

  importMethod: {
    type: String,
    enum: ['template', 'custom', 'ai-assisted'],
    required: true,
    default: 'template'
  },

  // Processing status
  status: {
    type: String,
    enum: ['pending', 'processing', 'completed', 'failed', 'cancelled'],
    required: true,
    default: 'pending',
    index: true
  },

  // Processing results
  results: {
    totalRows: {
      type: Number,
      default: 0
    },
    
    validRows: {
      type: Number,
      default: 0
    },
    
    invalidRows: {
      type: Number,
      default: 0
    },
    
    recordsImported: {
      type: Number,
      default: 0
    },
    
    recordsSkipped: {
      type: Number,
      default: 0
    },
    
    recordsFailed: {
      type: Number,
      default: 0
    }
  },

  // Data summary
  summary: {
    totalAmount: {
      type: Number,
      default: 0
    },
    
    categoryBreakdown: {
      type: Map,
      of: Number,
      default: new Map()
    },
    
    dateRange: {
      startDate: Date,
      endDate: Date
    },
    
    averageAmount: {
      type: Number,
      default: 0
    }
  },

  // Error tracking
  errors: [{
    rowNumber: Number,
    field: String,
    error: String,
    originalValue: String,
    timestamp: {
      type: Date,
      default: Date.now
    }
  }],

  // Processing metadata
  processingTime: {
    type: Number, // in milliseconds
    default: 0
  },

  aiProcessingUsed: {
    type: Boolean,
    default: false
  },

  aiConfidenceScore: {
    type: Number,
    min: 0,
    max: 1,
    default: null
  },

  // Batch information
  batchId: {
    type: String,
    trim: true,
    index: true
  },

  // User feedback
  userFeedback: {
    rating: {
      type: Number,
      min: 1,
      max: 5
    },
    
    comment: {
      type: String,
      trim: true,
      maxlength: 1000
    },
    
    reportedIssues: [{
      type: String,
      enum: ['data-quality', 'performance', 'ui-issue', 'feature-request', 'other']
    }],
    
    feedbackDate: Date
  },

  // Timestamps
  startedAt: {
    type: Date,
    default: Date.now,
    index: true
  },

  completedAt: {
    type: Date,
    index: true
  },

  // System metadata
  systemInfo: {
    userAgent: String,
    ipAddress: String,
    apiVersion: String,
    clientVersion: String
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for performance
importHistorySchema.index({ userId: 1, createdAt: -1 });
importHistorySchema.index({ status: 1, createdAt: -1 });
importHistorySchema.index({ importType: 1, createdAt: -1 });
importHistorySchema.index({ batchId: 1 });

// Virtual for success rate
importHistorySchema.virtual('successRate').get(function() {
  if (this.results.totalRows === 0) return 0;
  return (this.results.validRows / this.results.totalRows) * 100;
});

// Virtual for processing duration
importHistorySchema.virtual('processingDuration').get(function() {
  if (!this.completedAt || !this.startedAt) return null;
  return this.completedAt.getTime() - this.startedAt.getTime();
});

// Instance methods
importHistorySchema.methods.markAsCompleted = function(results) {
  this.status = 'completed';
  this.completedAt = new Date();
  this.results = { ...this.results, ...results };
  return this.save();
};

importHistorySchema.methods.markAsFailed = function(error) {
  this.status = 'failed';
  this.completedAt = new Date();
  if (error) {
    this.errors.push({
      error: error.message || error,
      timestamp: new Date()
    });
  }
  return this.save();
};

importHistorySchema.methods.addError = function(rowNumber, field, error, originalValue) {
  this.errors.push({
    rowNumber,
    field,
    error,
    originalValue,
    timestamp: new Date()
  });
};

importHistorySchema.methods.updateProgress = function(status, partialResults) {
  this.status = status;
  if (partialResults) {
    this.results = { ...this.results, ...partialResults };
  }
  return this.save();
};

// Static methods
importHistorySchema.statics.getRecentImports = function(userId, limit = 10) {
  return this.find({ userId })
    .sort({ createdAt: -1 })
    .limit(limit)
    .populate('userId', 'name email')
    .lean();
};

importHistorySchema.statics.getImportStats = function(userId, dateRange) {
  const matchStage = { userId: new mongoose.Types.ObjectId(userId) };
  
  if (dateRange) {
    matchStage.createdAt = {
      $gte: new Date(dateRange.start),
      $lte: new Date(dateRange.end)
    };
  }

  return this.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: null,
        totalImports: { $sum: 1 },
        successfulImports: {
          $sum: { $cond: [{ $eq: ['$status', 'completed'] }, 1, 0] }
        },
        totalRecordsImported: { $sum: '$results.recordsImported' },
        totalAmount: { $sum: '$summary.totalAmount' },
        averageProcessingTime: { $avg: '$processingTime' },
        importTypes: {
          $push: '$importType'
        }
      }
    },
    {
      $project: {
        _id: 0,
        totalImports: 1,
        successfulImports: 1,
        successRate: {
          $multiply: [
            { $divide: ['$successfulImports', '$totalImports'] },
            100
          ]
        },
        totalRecordsImported: 1,
        totalAmount: 1,
        averageProcessingTime: 1,
        importTypeBreakdown: {
          $reduce: {
            input: '$importTypes',
            initialValue: {},
            in: {
              $mergeObjects: [
                '$$value',
                {
                  $arrayToObject: [[{
                    k: '$$this',
                    v: { $add: [{ $ifNull: [{ $getField: { field: '$$this', input: '$$value' } }, 0] }, 1] }
                  }]]
                }
              ]
            }
          }
        }
      }
    }
  ]);
};

// Pre-save middleware
importHistorySchema.pre('save', function(next) {
  // Calculate processing time if completed
  if (this.status === 'completed' && this.startedAt && this.completedAt) {
    this.processingTime = this.completedAt.getTime() - this.startedAt.getTime();
  }

  // Calculate average amount
  if (this.results.validRows > 0 && this.summary.totalAmount > 0) {
    this.summary.averageAmount = this.summary.totalAmount / this.results.validRows;
  }

  next();
});

// Post-save middleware for cleanup
importHistorySchema.post('save', async function(doc) {
  // Clean up old failed imports (older than 7 days)
  if (doc.status === 'failed') {
    const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    
    try {
      await this.constructor.deleteMany({
        userId: doc.userId,
        status: 'failed',
        createdAt: { $lt: sevenDaysAgo }
      });
    } catch (error) {
      console.error('Failed to cleanup old import records:', error);
    }
  }
});

module.exports = mongoose.model('ImportHistory', importHistorySchema);
