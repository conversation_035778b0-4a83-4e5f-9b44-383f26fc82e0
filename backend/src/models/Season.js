/**
 * Season Model for Çiftçi Not Defterim
 * User-defined seasons with flexible date ranges and active season management
 */

const mongoose = require('mongoose');

const seasonSchema = new mongoose.Schema({
  // User Relationship
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },

  // Basic Information
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },

  description: {
    type: String,
    trim: true,
    maxlength: 500
  },

  // Flexible Date Range (Optional)
  startDate: {
    type: Date,
    default: null // null = open start
  },

  endDate: {
    type: Date,
    default: null // null = open end
  },

  // Status Information
  isActive: {
    type: Boolean,
    default: false
  },

  isDefault: {
    type: Boolean,
    default: false
  },

  // Metadata
  color: {
    type: String,
    default: '#4CAF50',
    validate: {
      validator: function(v) {
        return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(v);
      },
      message: 'G<PERSON><PERSON><PERSON>iz renk formatı'
    }
  },

  emoji: {
    type: String,
    default: '🌱',
    validate: {
      validator: function(v) {
        // Simplified emoji validation - allow any Unicode character that looks like an emoji
        // This includes a much broader range of emojis and symbols
        if (!v || v.length === 0) return false;

        // Allow any character in the emoji ranges, including plant emojis like 🌱
        const emojiRegex = /[\u{1F000}-\u{1F9FF}]|[\u{2600}-\u{27BF}]|[\u{1F300}-\u{1F5FF}]|[\u{1F600}-\u{1F64F}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]/u;

        // Also allow common symbols and characters that might be used as icons
        const symbolRegex = /[\u{2000}-\u{2BFF}]|[\u{3000}-\u{303F}]|[\u{FE00}-\u{FE0F}]/u;

        return emojiRegex.test(v) || symbolRegex.test(v) || v.length <= 4;
      },
      message: 'Geçerli bir emoji veya simge seçiniz'
    }
  },

  // Timestamps
  createdAt: {
    type: Date,
    default: Date.now
  },

  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for Performance Optimization
seasonSchema.index({ userId: 1, isActive: 1 }); // Active season queries
seasonSchema.index({ userId: 1, createdAt: -1 }); // User seasons listing
seasonSchema.index({ userId: 1, startDate: 1, endDate: 1 }); // Date range queries
seasonSchema.index({ userId: 1, isDefault: 1 }); // Default season queries

// Sparse indexes for optional fields
seasonSchema.index({ startDate: 1 }, { sparse: true });
seasonSchema.index({ endDate: 1 }, { sparse: true });

// Text index for search functionality
seasonSchema.index({
  name: 'text',
  description: 'text'
}, {
  weights: { name: 10, description: 5 },
  name: 'season_text_index'
});

// Virtual for current status
seasonSchema.virtual('isCurrent').get(function() {
  const now = new Date();

  // If no date range specified, consider it always current
  if (!this.startDate && !this.endDate) {
    return true;
  }

  // If only start date specified
  if (this.startDate && !this.endDate) {
    return now >= this.startDate;
  }

  // If only end date specified
  if (!this.startDate && this.endDate) {
    return now <= this.endDate;
  }

  // If both dates specified
  return now >= this.startDate && now <= this.endDate;
});

// Virtual for season duration in days
seasonSchema.virtual('durationInDays').get(function() {
  if (!this.startDate || !this.endDate) {
    return null; // Open-ended season
  }

  const diffTime = this.endDate - this.startDate;
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
});

// Pre-save middleware
seasonSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Instance methods
seasonSchema.methods.activate = async function() {
  // Deactivate all other seasons for this user
  await this.constructor.updateMany(
    { userId: this.userId, _id: { $ne: this._id } },
    { isActive: false, updatedAt: new Date() }
  );

  // Activate this season
  this.isActive = true;
  this.updatedAt = new Date();
  return this.save();
};

seasonSchema.methods.isWithinDateRange = function(date) {
  const checkDate = new Date(date);

  // If no date range specified, always within range
  if (!this.startDate && !this.endDate) {
    return true;
  }

  // If only start date specified
  if (this.startDate && !this.endDate) {
    return checkDate >= this.startDate;
  }

  // If only end date specified
  if (!this.startDate && this.endDate) {
    return checkDate <= this.endDate;
  }

  // If both dates specified
  return checkDate >= this.startDate && checkDate <= this.endDate;
};

// Static methods
seasonSchema.statics.getActiveSeasonForUser = function(userId) {
  return this.findOne({
    userId: new mongoose.Types.ObjectId(userId),
    isActive: true
  }).lean();
};

seasonSchema.statics.getUserSeasons = function(userId) {
  return this.find({
    userId: new mongoose.Types.ObjectId(userId)
  }).sort({ createdAt: -1 });
};

seasonSchema.statics.createDefaultSeason = async function(userId) {
  const currentYear = new Date().getFullYear();
  const defaultSeason = new this({
    userId: new mongoose.Types.ObjectId(userId),
    name: `${currentYear} Sezonu`,
    description: 'Otomatik oluşturulan varsayılan sezon',
    startDate: new Date(),
    isActive: true,
    isDefault: true,
    color: '#4CAF50',
    emoji: '🌱'
  });

  return defaultSeason.save();
};

seasonSchema.statics.getSeasonSummary = function(seasonId) {
  return this.aggregate([
    {
      $match: {
        _id: new mongoose.Types.ObjectId(seasonId)
      }
    },
    {
      $lookup: {
        from: 'expenses',
        localField: '_id',
        foreignField: 'seasonId',
        as: 'expenses'
      }
    },
    {
      $addFields: {
        totalExpenses: { $size: '$expenses' },
        totalAmount: { $sum: '$expenses.amount' },
        avgAmount: { $avg: '$expenses.amount' }
      }
    }
  ]);
};

module.exports = mongoose.model('Season', seasonSchema);
