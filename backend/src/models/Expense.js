/**
 * Expense Model for Çiftçi Not Defterim
 * Represents agricultural expenses with seasonal tracking
 */

const mongoose = require('mongoose');
const { TurkishHelpers, DateHelpers } = require('../utils/helpers');

const expenseSchema = new mongoose.Schema({
  // Basic Information
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  
  categoryId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category',
    required: true,
    index: true
  },
  
  amount: {
    type: Number,
    required: true,
    min: 0,
    max: 1000000, // 1M TL max
    validate: {
      validator: function(v) {
        return v > 0 && v <= 1000000;
      },
      message: 'Tutar 0 ile 1.000.000 TL arasında olmalıdır'
    }
  },
  
  description: {
    type: String,
    trim: true,
    maxlength: 500
  },
  
  date: {
    type: Date,
    required: true,
    index: true,
    validate: {
      validator: function(v) {
        const now = new Date();
        const oneYearAgo = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());
        const oneYearFromNow = new Date(now.getFullYear() + 1, now.getMonth(), now.getDate());
        return v >= oneYearAgo && v <= oneYearFromNow;
      },
      message: 'Tarih son 1 yıl ile gelecek 1 yıl arasında olmalıdır'
    }
  },
  
  // Agricultural Context
  seasonId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Season',
    required: true,
    index: true
  },

  // Legacy season ID for migration support
  legacySeasonId: {
    type: String,
    enum: ['spring', 'summer', 'autumn', 'winter'],
    default: null
  },
  
  cropType: {
    type: String,
    trim: true,
    maxlength: 50
  },
  
  farmArea: {
    type: String,
    trim: true,
    maxlength: 100
  },
  
  // Location Information
  location: {
    latitude: {
      type: Number,
      min: -90,
      max: 90
    },
    longitude: {
      type: Number,
      min: -180,
      max: 180
    },
    address: {
      type: String,
      trim: true,
      maxlength: 200
    },
    accuracy: Number // GPS accuracy in meters
  },
  
  // File Attachments
  photos: [{
    id: {
      type: String,
      required: true
    },
    url: {
      type: String,
      required: true
    },
    thumbnail: String,
    filename: String,
    size: Number,
    mimeType: String,
    uploadedAt: {
      type: Date,
      default: Date.now
    }
  }],
  
  // Receipt Information
  receipt: {
    number: String,
    vendor: String,
    taxNumber: String,
    date: Date
  },
  
  // Payment Information
  paymentMethod: {
    type: String,
    enum: ['cash', 'card', 'transfer', 'check', 'other'],
    default: 'cash'
  },
  
  currency: {
    type: String,
    enum: ['TRY', 'USD', 'EUR'],
    default: 'TRY'
  },
  
  // Status and Flags
  status: {
    type: String,
    enum: ['active', 'deleted', 'archived'],
    default: 'active',
    index: true
  },
  
  isRecurring: {
    type: Boolean,
    default: false
  },
  
  recurringPattern: {
    frequency: {
      type: String,
      enum: ['daily', 'weekly', 'monthly', 'yearly']
    },
    interval: Number, // Every N periods
    endDate: Date
  },
  
  // Tags and Notes
  tags: [{
    type: String,
    trim: true,
    maxlength: 30
  }],

  notes: {
    type: String,
    trim: true,
    maxlength: 1000
  },

  // NEW FIELDS FOR TWO-MODE SYSTEM
  // Field Reference (for detailed mode)
  fieldId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Field',
    default: null,
    index: true
  },

  // Crop Reference (for detailed mode)
  cropId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Crop',
    default: null,
    index: true
  },

  // Tracking Mode Indicator
  trackingMode: {
    type: String,
    enum: ['simple', 'detailed'],
    default: 'simple',
    index: true
  },
  
  // Sync Information
  localId: {
    type: String,
    index: true // For offline sync
  },
  
  lastSyncAt: {
    type: Date,
    default: Date.now
  },
  
  syncVersion: {
    type: Number,
    default: 1
  },
  
  syncStatus: {
    type: String,
    enum: ['synced', 'pending', 'conflict'],
    default: 'synced'
  },
  
  // Audit Trail
  createdBy: {
    type: String,
    enum: ['user', 'import', 'recurring', 'api'],
    default: 'user'
  },
  
  lastModifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Compound indexes for performance
expenseSchema.index({ userId: 1, date: -1 });
expenseSchema.index({ userId: 1, categoryId: 1 });
expenseSchema.index({ userId: 1, seasonId: 1, date: -1 }); // Updated for season-based queries
expenseSchema.index({ seasonId: 1, categoryId: 1 }); // Season-category analysis
expenseSchema.index({ seasonId: 1, amount: -1 }); // Season total calculations
expenseSchema.index({ userId: 1, status: 1, date: -1 });
expenseSchema.index({ date: -1, amount: -1 });
expenseSchema.index({ localId: 1, userId: 1 });
expenseSchema.index({ syncStatus: 1, lastSyncAt: 1 });

// NEW INDEXES FOR TWO-MODE SYSTEM
expenseSchema.index({ userId: 1, fieldId: 1, date: -1 });
expenseSchema.index({ userId: 1, cropId: 1, date: -1 });
expenseSchema.index({ trackingMode: 1, userId: 1 });
expenseSchema.index({ userId: 1, trackingMode: 1, fieldId: 1 });
expenseSchema.index({ userId: 1, trackingMode: 1, cropId: 1 });

// Text index for search
expenseSchema.index({
  description: 'text',
  notes: 'text',
  'receipt.vendor': 'text',
  tags: 'text'
});

// Virtual for formatted amount
expenseSchema.virtual('formattedAmount').get(function() {
  return TurkishHelpers.formatCurrency(this.amount);
});

// Virtual for formatted date
expenseSchema.virtual('formattedDate').get(function() {
  return TurkishHelpers.formatDate(this.date);
});

// Virtual for season display name
expenseSchema.virtual('seasonDisplayName').get(function() {
  const seasonNames = {
    spring: 'Bahar',
    summer: 'Yaz',
    autumn: 'Sonbahar',
    winter: 'Kış'
  };
  return seasonNames[this.seasonId] || this.seasonId;
});

// Virtual for days since creation
expenseSchema.virtual('daysSinceCreated').get(function() {
  return DateHelpers.daysBetween(this.createdAt, new Date());
});

// Virtual for photo count
expenseSchema.virtual('photoCount').get(function() {
  return this.photos ? this.photos.length : 0;
});

// Pre-save middleware
expenseSchema.pre('save', async function(next) {
  // Auto-assign active season if not provided
  if (!this.seasonId && this.userId) {
    try {
      const Season = mongoose.model('Season');
      const activeSeason = await Season.findOne({
        userId: this.userId,
        isActive: true
      });

      if (activeSeason) {
        this.seasonId = activeSeason._id;
      }
    } catch (error) {
      console.warn('Failed to auto-assign season:', error);
    }
  }

  // TWO-MODE SYSTEM: Auto-detect tracking mode
  if (!this.fieldId && !this.cropId) {
    this.trackingMode = 'simple';
  } else {
    this.trackingMode = 'detailed';
  }

  // Update sync version on changes
  if (this.isModified() && !this.isNew) {
    this.syncVersion += 1;
    this.lastSyncAt = new Date();
    this.syncStatus = 'pending';
  }

  // Set lastModifiedBy
  if (this.isModified() && !this.isNew) {
    this.lastModifiedBy = this.userId;
  }

  next();
});

// TWO-MODE SYSTEM: Additional validation middleware
expenseSchema.pre('save', async function(next) {
  try {
    // Validate field and crop if provided
    if (this.fieldId) {
      const Field = mongoose.model('Field');
      const field = await Field.findOne({
        _id: this.fieldId,
        userId: this.userId,
        isActive: true
      });

      if (!field) {
        throw new Error('Geçersiz tarla seçimi');
      }
    }

    if (this.cropId) {
      const Crop = mongoose.model('Crop');
      const crop = await Crop.findOne({
        _id: this.cropId,
        $or: [
          { userId: this.userId },
          { userId: null, isDefault: true }
        ],
        isActive: true
      });

      if (!crop) {
        throw new Error('Geçersiz ürün seçimi');
      }
    }

    // Detailed mode validation
    if (this.trackingMode === 'detailed' && !this.fieldId) {
      throw new Error('Detaylı modda tarla seçimi zorunludur');
    }

    next();
  } catch (error) {
    next(error);
  }
});

// Post-save middleware to update category usage
expenseSchema.post('save', async function(doc) {
  try {
    const Category = mongoose.model('Category');
    await Category.findByIdAndUpdate(
      doc.categoryId,
      {
        $inc: { usageCount: 1, totalAmount: doc.amount },
        $set: { lastUsedAt: new Date() }
      }
    );
  } catch (error) {
    console.error('Error updating category usage:', error);
  }
});

// Instance methods
expenseSchema.methods.addPhoto = function(photoData) {
  this.photos.push({
    id: photoData.id,
    url: photoData.url,
    thumbnail: photoData.thumbnail,
    filename: photoData.filename,
    size: photoData.size,
    mimeType: photoData.mimeType,
    uploadedAt: new Date()
  });
  return this.save();
};

expenseSchema.methods.removePhoto = function(photoId) {
  this.photos = this.photos.filter(photo => photo.id !== photoId);
  return this.save();
};

expenseSchema.methods.addTag = function(tag) {
  if (!this.tags.includes(tag)) {
    this.tags.push(tag);
    return this.save();
  }
  return Promise.resolve(this);
};

expenseSchema.methods.removeTag = function(tag) {
  this.tags = this.tags.filter(t => t !== tag);
  return this.save();
};

expenseSchema.methods.markAsDeleted = function() {
  this.status = 'deleted';
  return this.save();
};

expenseSchema.methods.toPublicJSON = function() {
  const expense = this.toObject();
  
  // Remove internal sync fields
  delete expense.syncVersion;
  delete expense.syncStatus;
  delete expense.localId;
  
  return expense;
};

// Static methods
expenseSchema.statics.getUserExpenses = function(userId, options = {}) {
  const query = { userId, status: 'active' };
  
  // Add date range filter
  if (options.startDate || options.endDate) {
    query.date = {};
    if (options.startDate) query.date.$gte = new Date(options.startDate);
    if (options.endDate) query.date.$lte = new Date(options.endDate);
  }
  
  // Add category filter
  if (options.categoryId) {
    query.categoryId = options.categoryId;
  }
  
  // Add season filter
  if (options.seasonId) {
    query.seasonId = options.seasonId;
  }
  
  return this.find(query)
    .populate('categoryId', 'name emoji color icon')
    .sort({ date: -1, createdAt: -1 });
};

expenseSchema.statics.getExpenseStats = function(userId, options = {}) {
  const matchQuery = { userId, status: 'active' };
  
  // Add date range
  if (options.startDate || options.endDate) {
    matchQuery.date = {};
    if (options.startDate) matchQuery.date.$gte = new Date(options.startDate);
    if (options.endDate) matchQuery.date.$lte = new Date(options.endDate);
  }
  
  return this.aggregate([
    { $match: matchQuery },
    {
      $group: {
        _id: null,
        totalExpenses: { $sum: 1 },
        totalAmount: { $sum: '$amount' },
        avgAmount: { $avg: '$amount' },
        minAmount: { $min: '$amount' },
        maxAmount: { $max: '$amount' },
        categoriesUsed: { $addToSet: '$categoryId' },
        seasonsUsed: { $addToSet: '$seasonId' }
      }
    },
    {
      $project: {
        totalExpenses: 1,
        totalAmount: 1,
        avgAmount: { $round: ['$avgAmount', 2] },
        minAmount: 1,
        maxAmount: 1,
        uniqueCategories: { $size: '$categoriesUsed' },
        uniqueSeasons: { $size: '$seasonsUsed' }
      }
    }
  ]);
};

expenseSchema.statics.getSeasonalStats = function(userId, year = new Date().getFullYear()) {
  return this.aggregate([
    {
      $match: {
        userId: new mongoose.Types.ObjectId(userId),
        status: 'active',
        date: {
          $gte: new Date(year, 0, 1),
          $lt: new Date(year + 1, 0, 1)
        }
      }
    },
    {
      $group: {
        _id: '$seasonId',
        totalAmount: { $sum: '$amount' },
        expenseCount: { $sum: 1 },
        avgAmount: { $avg: '$amount' }
      }
    },
    {
      $sort: { totalAmount: -1 }
    }
  ]);
};

expenseSchema.statics.getCategoryStats = function(userId, options = {}) {
  const matchQuery = { userId, status: 'active' };
  
  if (options.startDate || options.endDate) {
    matchQuery.date = {};
    if (options.startDate) matchQuery.date.$gte = new Date(options.startDate);
    if (options.endDate) matchQuery.date.$lte = new Date(options.endDate);
  }
  
  return this.aggregate([
    { $match: matchQuery },
    {
      $group: {
        _id: '$categoryId',
        totalAmount: { $sum: '$amount' },
        expenseCount: { $sum: 1 },
        avgAmount: { $avg: '$amount' },
        lastExpense: { $max: '$date' }
      }
    },
    {
      $lookup: {
        from: 'categories',
        localField: '_id',
        foreignField: '_id',
        as: 'category'
      }
    },
    {
      $unwind: '$category'
    },
    {
      $project: {
        categoryName: '$category.name',
        categoryEmoji: '$category.emoji',
        categoryColor: '$category.color',
        totalAmount: 1,
        expenseCount: 1,
        avgAmount: { $round: ['$avgAmount', 2] },
        lastExpense: 1,
        percentage: 1
      }
    },
    {
      $sort: { totalAmount: -1 }
    }
  ]);
};

module.exports = mongoose.model('Expense', expenseSchema);
