/**
 * MongoDB Schema Validation Test
 * Tests if the updated schema accepts ObjectId format for seasonId
 */

const mongoose = require('mongoose');

const MONGODB_URI = '************************************************************************************************';

async function testSchema() {
  try {
    console.log('🔗 Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    const db = mongoose.connection.db;

    // Test 1: Check if expenses collection exists
    console.log('\n📋 Test 1: Checking expenses collection...');
    const collections = await db.listCollections({ name: 'expenses' }).toArray();
    if (collections.length === 0) {
      console.log('❌ Expenses collection does not exist');
      return;
    }
    console.log('✅ Expenses collection exists');

    // Test 2: Get collection validator
    console.log('\n📋 Test 2: Checking collection validator...');
    const collectionInfo = await db.listCollections({ name: 'expenses' }).toArray();
    const validator = collectionInfo[0]?.options?.validator;
    
    if (!validator) {
      console.log('❌ No validator found on expenses collection');
      return;
    }
    
    console.log('✅ Validator exists');
    console.log('Validator schema:', JSON.stringify(validator, null, 2));

    // Test 3: Test ObjectId validation for seasonId
    console.log('\n📋 Test 3: Testing ObjectId validation for seasonId...');
    
    const testDoc = {
      userId: new mongoose.Types.ObjectId(),
      categoryId: new mongoose.Types.ObjectId(),
      seasonId: new mongoose.Types.ObjectId(), // This should be accepted now
      amount: 100,
      date: new Date(),
      description: 'Test expense for schema validation'
    };

    try {
      await db.collection('expenses').insertOne(testDoc);
      console.log('✅ ObjectId seasonId accepted by validator');
      
      // Clean up test document
      await db.collection('expenses').deleteOne({ _id: testDoc._id });
      console.log('✅ Test document cleaned up');
      
    } catch (validationError) {
      console.log('❌ ObjectId seasonId rejected by validator:', validationError.message);
    }

    // Test 4: Test legacy seasonId (should still work)
    console.log('\n📋 Test 4: Testing legacy seasonId support...');
    
    const legacyTestDoc = {
      userId: new mongoose.Types.ObjectId(),
      categoryId: new mongoose.Types.ObjectId(),
      seasonId: new mongoose.Types.ObjectId(),
      legacySeasonId: 'spring', // Legacy format
      amount: 150,
      date: new Date(),
      description: 'Test expense for legacy season support'
    };

    try {
      await db.collection('expenses').insertOne(legacyTestDoc);
      console.log('✅ Legacy seasonId support working');
      
      // Clean up test document
      await db.collection('expenses').deleteOne({ _id: legacyTestDoc._id });
      console.log('✅ Legacy test document cleaned up');
      
    } catch (validationError) {
      console.log('❌ Legacy seasonId support failed:', validationError.message);
    }

    // Test 5: Test invalid data (should be rejected)
    console.log('\n📋 Test 5: Testing invalid data rejection...');
    
    const invalidDoc = {
      userId: new mongoose.Types.ObjectId(),
      categoryId: new mongoose.Types.ObjectId(),
      seasonId: 'invalid-season-id', // Invalid format - should be rejected
      amount: 100,
      date: new Date()
    };

    try {
      await db.collection('expenses').insertOne(invalidDoc);
      console.log('❌ Invalid seasonId was accepted (should have been rejected)');
    } catch (validationError) {
      console.log('✅ Invalid seasonId correctly rejected:', validationError.message);
    }

    console.log('\n🎉 Schema validation tests completed!');

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('📡 Disconnected from MongoDB');
  }
}

// Run the test
testSchema();
