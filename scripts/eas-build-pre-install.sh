#!/bin/bash

# EAS Build Pre-install Hook
# This script runs before dependencies are installed

echo "🔥 Setting up Firebase configuration files..."

# Create android directory if it doesn't exist
mkdir -p android

# Create google-services.json from environment variable
if [ -n "$GOOGLE_SERVICES_JSON" ]; then
    echo "📱 Creating android/google-services.json from environment variable..."
    echo "$GOOGLE_SERVICES_JSON" | base64 -d > android/google-services.json
    echo "✅ android/google-services.json created successfully"

    # Verify the file was created correctly
    if [ -f "android/google-services.json" ]; then
        echo "📋 File size: $(wc -c < android/google-services.json) bytes"
        echo "📋 First few lines:"
        head -3 android/google-services.json
    fi
else
    echo "❌ GOOGLE_SERVICES_JSON environment variable not found"
    echo "📋 Available environment variables:"
    env | grep -E "(GOOGLE|FIREBASE)" || echo "No Firebase-related environment variables found"
    exit 1
fi

# Create ios directory if it doesn't exist
mkdir -p ios

# Create GoogleService-Info.plist from environment variable (if exists)
if [ -n "$GOOGLE_SERVICE_INFO_PLIST" ]; then
    echo "🍎 Creating ios/GoogleService-Info.plist from environment variable..."
    echo "$GOOGLE_SERVICE_INFO_PLIST" | base64 -d > ios/GoogleService-Info.plist
    echo "✅ ios/GoogleService-Info.plist created successfully"
else
    echo "⚠️  GOOGLE_SERVICE_INFO_PLIST environment variable not found (iOS config will be skipped)"
fi

echo "🎉 Firebase configuration setup completed!"
