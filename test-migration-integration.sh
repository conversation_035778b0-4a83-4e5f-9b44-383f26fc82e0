#!/bin/bash

# Integration Test for Migration Process
# Tests the complete migration flow from guest user to Google authenticated user

API_BASE_URL="http://localhost:3000/api/v1"

echo "🧪 Migration Integration Test Başlıyor..."
echo ""

# Test 1: Simulate Migration Season Creation (Original Issue)
echo "📝 Test 1: Migration Season Creation (Original Issue Scenario)"
MOCK_TOKEN_1="Bearer dev-test-token-migration-1-$(date +%s)"

# This is the exact data structure that was causing the original error
MIGRATION_SEASON_DATA='{
  "name": "2025 Sezonu (Migration)",
  "description": "Guest verilerinden aktarılan sezon",
  "startDate": "'$(date -u +%Y-%m-%dT%H:%M:%S.000Z)'",
  "isActive": "true",
  "isDefault": "true",
  "color": "#4CAF50",
  "emoji": "🌱"
}'

echo "Gönderilen veri (Migration senaryosu):"
echo "$MIGRATION_SEASON_DATA"
echo ""

echo "API Çağrısı yapılıyor..."
RESPONSE_1=$(curl -s -w "\nHTTP_STATUS:%{http_code}" \
  -X POST \
  -H "Authorization: $MOCK_TOKEN_1" \
  -H "Content-Type: application/json" \
  -d "$MIGRATION_SEASON_DATA" \
  "$API_BASE_URL/seasons")

HTTP_STATUS_1=$(echo "$RESPONSE_1" | grep "HTTP_STATUS:" | cut -d: -f2)
RESPONSE_BODY_1=$(echo "$RESPONSE_1" | sed '/HTTP_STATUS:/d')

echo "HTTP Status: $HTTP_STATUS_1"
echo "Response:"
echo "$RESPONSE_BODY_1" | jq . 2>/dev/null || echo "$RESPONSE_BODY_1"

echo ""
echo "=================================================="
echo ""

# Test 2: Create Multiple Seasons (Simulating Migration with Multiple Data)
echo "📝 Test 2: Multiple Season Creation (Batch Migration Simulation)"

SEASONS=(
  '{"name": "2025 Bahar Sezonu", "description": "Bahar dönemi", "isActive": "false", "isDefault": "false", "color": "#8BC34A", "emoji": "🌸"}'
  '{"name": "2025 Yaz Sezonu", "description": "Yaz dönemi", "isActive": "false", "isDefault": "false", "color": "#FF9800", "emoji": "☀️"}'
  '{"name": "2025 Sonbahar Sezonu", "description": "Sonbahar dönemi", "isActive": "false", "isDefault": "false", "color": "#FF5722", "emoji": "🍂"}'
)

SUCCESS_COUNT=0
TOTAL_SEASONS=${#SEASONS[@]}

for i in "${!SEASONS[@]}"; do
  SEASON_NUM=$((i + 1))
  MOCK_TOKEN="Bearer dev-test-token-migration-batch-$SEASON_NUM-$(date +%s)"
  
  # Add common fields to season data
  SEASON_DATA=$(echo "${SEASONS[$i]}" | jq --arg startDate "$(date -u +%Y-%m-%dT%H:%M:%S.000Z)" '. + {startDate: $startDate}')
  
  echo "Sezon $SEASON_NUM oluşturuluyor..."
  
  RESPONSE=$(curl -s -w "\nHTTP_STATUS:%{http_code}" \
    -X POST \
    -H "Authorization: $MOCK_TOKEN" \
    -H "Content-Type: application/json" \
    -d "$SEASON_DATA" \
    "$API_BASE_URL/seasons")
  
  HTTP_STATUS=$(echo "$RESPONSE" | grep "HTTP_STATUS:" | cut -d: -f2)
  
  if [ "$HTTP_STATUS" = "201" ]; then
    echo "✅ Sezon $SEASON_NUM başarıyla oluşturuldu"
    SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
  else
    echo "❌ Sezon $SEASON_NUM oluşturulamadı (HTTP: $HTTP_STATUS)"
    RESPONSE_BODY=$(echo "$RESPONSE" | sed '/HTTP_STATUS:/d')
    echo "Hata detayı: $RESPONSE_BODY"
  fi
  
  # Small delay between requests
  sleep 0.5
done

echo ""
echo "📊 Batch Migration Sonucu: $SUCCESS_COUNT/$TOTAL_SEASONS sezon başarıyla oluşturuldu"

echo ""
echo "=================================================="
echo ""

# Test 3: Error Handling Test (Invalid Data)
echo "📝 Test 3: Error Handling Test (Invalid Migration Data)"
MOCK_TOKEN_3="Bearer dev-test-token-migration-error-$(date +%s)"

INVALID_MIGRATION_DATA='{
  "name": "",
  "description": "Geçersiz veri testi",
  "startDate": "invalid-date",
  "isActive": "invalid-boolean",
  "isDefault": "true",
  "color": "invalid-color",
  "emoji": "invalid_emoji_text"
}'

echo "Gönderilen veri (Geçersiz veri):"
echo "$INVALID_MIGRATION_DATA"
echo ""

RESPONSE_3=$(curl -s -w "\nHTTP_STATUS:%{http_code}" \
  -X POST \
  -H "Authorization: $MOCK_TOKEN_3" \
  -H "Content-Type: application/json" \
  -d "$INVALID_MIGRATION_DATA" \
  "$API_BASE_URL/seasons")

HTTP_STATUS_3=$(echo "$RESPONSE_3" | grep "HTTP_STATUS:" | cut -d: -f2)
RESPONSE_BODY_3=$(echo "$RESPONSE_3" | sed '/HTTP_STATUS:/d')

echo "HTTP Status: $HTTP_STATUS_3"
echo "Response:"
echo "$RESPONSE_BODY_3" | jq . 2>/dev/null || echo "$RESPONSE_BODY_3"

echo ""
echo "🏁 Integration Test tamamlandı!"

# Test sonuçlarını değerlendir
echo ""
echo "📊 Test Sonuçları:"

if [ "$HTTP_STATUS_1" = "201" ]; then
    echo "✅ Test 1 (Migration Season): BAŞARILI"
    TEST_1_SUCCESS=true
else
    echo "❌ Test 1 (Migration Season): BAŞARISIZ (HTTP: $HTTP_STATUS_1)"
    TEST_1_SUCCESS=false
fi

echo "✅ Test 2 (Batch Migration): $SUCCESS_COUNT/$TOTAL_SEASONS başarılı"

if [ "$HTTP_STATUS_3" = "400" ] || [ "$HTTP_STATUS_3" = "422" ]; then
    echo "✅ Test 3 (Error Handling): BAŞARILI (Geçersiz veri doğru şekilde reddedildi)"
    TEST_3_SUCCESS=true
else
    echo "❌ Test 3 (Error Handling): BAŞARISIZ (HTTP: $HTTP_STATUS_3)"
    TEST_3_SUCCESS=false
fi

echo ""
if [ "$TEST_1_SUCCESS" = true ] && [ "$SUCCESS_COUNT" -gt 0 ] && [ "$TEST_3_SUCCESS" = true ]; then
    echo "🎉 Tüm integration testleri başarılı!"
    echo "✅ Migration validation hatası çözüldü"
    echo "✅ Boolean sanitization çalışıyor"
    echo "✅ Emoji validation çalışıyor"
    echo "✅ Error handling çalışıyor"
    exit 0
else
    echo "⚠️  Bazı integration testleri başarısız"
    exit 1
fi
