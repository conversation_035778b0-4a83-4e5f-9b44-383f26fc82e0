version: "3.8"

services:
  # MongoDB Database
  mongodb:
    image: mongo:7.0
    container_name: ciftci-mongodb
    restart: unless-stopped
    ports:
      - "27018:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: ciftci-notebook
    volumes:
      - mongodb_data:/data/db
      - ./backend/scripts/init-mongo.js:/docker-entrypoint-initdb.d/init-mongo.js:ro
    networks:
      - ciftci-network

  # MongoDB Express (Web UI for MongoDB)
  mongo-express:
    image: mongo-express:1.0.0
    container_name: ciftci-mongo-express
    restart: unless-stopped
    ports:
      - "8082:8081"
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: admin
      ME_CONFIG_MONGODB_ADMINPASSWORD: password123
      ME_CONFIG_MONGODB_URL: *****************************************/
      ME_CONFIG_BASICAUTH_USERNAME: admin
      ME_CONFIG_BASICAUTH_PASSWORD: admin123
    depends_on:
      - mongodb
    networks:
      - ciftci-network

  # Redis (for caching and sessions)
  redis:
    image: redis:7.2-alpine
    container_name: ciftci-redis
    restart: unless-stopped
    ports:
      - "6380:6379"
    command: redis-server --appendonly yes --requirepass redis123
    volumes:
      - redis_data:/data
    networks:
      - ciftci-network

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: development
    container_name: ciftci-backend
    restart: unless-stopped
    ports:
      - "3000:3000"
    env_file:
      - ./backend/.env
    environment:
      NODE_ENV: development
      PORT: 3000
      MONGODB_URI: **********************************************************************************************
      REDIS_URL: redis://:redis123@redis:6379
      # Override specific settings for Docker
      CORS_ORIGIN: "*"
      TZ: Europe/Istanbul
    depends_on:
      - mongodb
      - redis
    volumes:
      - ./backend:/app
      - /app/node_modules
      - backend_uploads:/app/uploads
      - backend_logs:/app/logs
    networks:
      - ciftci-network
    healthcheck:
      test:
        [
          "CMD",
          "node",
          "-e",
          "require('http').get('http://localhost:3000/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })",
        ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local
  backend_uploads:
    driver: local
  backend_logs:
    driver: local

networks:
  ciftci-network:
    driver: bridge
