/**
 * Simple Backup System Test
 * Tests backup and rollback logic without React Native dependencies
 */

console.log('🧪 Starting Simple Backup System Tests...\n');

// Mock storage for testing
const mockStorage = {
  data: {},
  getItem: async (key) => {
    return mockStorage.data[key] || null;
  },
  setItem: async (key, value) => {
    mockStorage.data[key] = value;
  },
  removeItem: async (key) => {
    delete mockStorage.data[key];
  }
};

// Backup service logic
class SimpleBackupService {
  constructor() {
    this.BACKUP_KEY = 'migration_backup';
    this.BACKUP_METADATA_KEY = 'migration_backup_metadata';
  }

  generateBackupId() {
    return `backup_${Date.now()}_test-uuid`;
  }

  async createMigrationBackup() {
    try {
      const backupId = this.generateBackupId();
      const timestamp = new Date().toISOString();

      // Get guest data
      const [guestExpenses, guestFields] = await Promise.all([
        mockStorage.getItem('expenses_guest'),
        mockStorage.getItem('fields_guest')
      ]);

      // Create backup object
      const backup = {
        backupId,
        timestamp,
        version: '1.0.0',
        type: 'migration_backup',
        data: {
          expenses_guest: guestExpenses,
          fields_guest: guestFields
        },
        metadata: {
          expenseCount: guestExpenses ? JSON.parse(guestExpenses).length : 0,
          fieldCount: guestFields ? JSON.parse(guestFields).length : 0
        }
      };

      // Validate backup
      const validation = this.validateBackup(backup);
      if (!validation.isValid) {
        throw new Error(`Backup validation failed: ${validation.errors.join(', ')}`);
      }

      // Store backup
      await mockStorage.setItem(this.BACKUP_KEY, JSON.stringify(backup));
      
      // Store metadata
      const metadata = {
        backupId,
        timestamp,
        dataSize: JSON.stringify(backup).length,
        itemCounts: backup.metadata
      };
      await mockStorage.setItem(this.BACKUP_METADATA_KEY, JSON.stringify(metadata));

      return {
        success: true,
        backupId,
        metadata: backup.metadata,
        timestamp
      };

    } catch (error) {
      throw new Error(`Backup creation failed: ${error.message}`);
    }
  }

  validateBackup(backup) {
    const errors = [];

    if (!backup.backupId) errors.push('Missing backupId');
    if (!backup.timestamp) errors.push('Missing timestamp');
    if (!backup.data) errors.push('Missing data object');

    // Validate data structure
    if (backup.data) {
      for (const [key, value] of Object.entries(backup.data)) {
        if (value !== null) {
          try {
            JSON.parse(value);
          } catch (parseError) {
            errors.push(`Invalid JSON in ${key}`);
          }
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      size: JSON.stringify(backup).length
    };
  }

  async restoreFromBackup(backupId) {
    try {
      // Get backup data
      const backupData = await mockStorage.getItem(this.BACKUP_KEY);
      if (!backupData) {
        throw new Error('No backup found');
      }

      const backup = JSON.parse(backupData);
      
      // Verify backup ID
      if (backup.backupId !== backupId) {
        throw new Error(`Backup ID mismatch. Expected: ${backupId}, Found: ${backup.backupId}`);
      }

      // Validate backup
      const validation = this.validateBackup(backup);
      if (!validation.isValid) {
        throw new Error(`Backup validation failed: ${validation.errors.join(', ')}`);
      }

      // Restore data
      const restorePromises = [];
      for (const [key, value] of Object.entries(backup.data)) {
        if (value !== null) {
          restorePromises.push(mockStorage.setItem(key, value));
        }
      }

      await Promise.all(restorePromises);

      return {
        success: true,
        backupId,
        restoredItems: Object.keys(backup.data).length,
        metadata: backup.metadata
      };

    } catch (error) {
      throw new Error(`Restore failed: ${error.message}`);
    }
  }

  async hasBackup() {
    try {
      const backup = await mockStorage.getItem(this.BACKUP_KEY);
      return !!backup;
    } catch (error) {
      return false;
    }
  }

  async getBackupMetadata() {
    try {
      const metadataData = await mockStorage.getItem(this.BACKUP_METADATA_KEY);
      return metadataData ? JSON.parse(metadataData) : null;
    } catch (error) {
      return null;
    }
  }

  async cleanupBackup(backupId) {
    try {
      const metadata = await this.getBackupMetadata();
      if (metadata && metadata.backupId === backupId) {
        await Promise.all([
          mockStorage.removeItem(this.BACKUP_KEY),
          mockStorage.removeItem(this.BACKUP_METADATA_KEY)
        ]);
        return true;
      }
      return false;
    } catch (error) {
      return false;
    }
  }
}

// Run tests
async function runBackupTests() {
  const backupService = new SimpleBackupService();
  let passedTests = 0;
  let totalTests = 0;

  async function test(name, testFn) {
    totalTests++;
    try {
      const result = await testFn();
      if (result === true) {
        console.log(`✅ ${name}`);
        passedTests++;
      } else {
        console.log(`❌ ${name}: Test returned false`);
      }
    } catch (error) {
      console.log(`❌ ${name}: ${error.message}`);
    }
  }

  // Test 1: Backup creation with data
  await test('Backup creation with guest data', async () => {
    // Set up test data
    const testExpenses = [{ id: '1', amount: 100, description: 'Test expense' }];
    const testFields = [{ id: '1', name: 'Test field', area: 100 }];
    
    await mockStorage.setItem('expenses_guest', JSON.stringify(testExpenses));
    await mockStorage.setItem('fields_guest', JSON.stringify(testFields));

    const result = await backupService.createMigrationBackup();
    
    return result.success === true && 
           result.metadata.expenseCount === 1 && 
           result.metadata.fieldCount === 1;
  });

  // Test 2: Backup creation with empty data
  await test('Backup creation with empty data', async () => {
    // Clear test data
    await mockStorage.setItem('expenses_guest', JSON.stringify([]));
    await mockStorage.setItem('fields_guest', JSON.stringify([]));

    const result = await backupService.createMigrationBackup();
    
    return result.success === true && 
           result.metadata.expenseCount === 0 && 
           result.metadata.fieldCount === 0;
  });

  // Test 3: Backup validation
  await test('Backup validation - valid backup', async () => {
    const validBackup = {
      backupId: 'test-backup-id',
      timestamp: '2022-01-01T00:00:00.000Z',
      data: {
        expenses_guest: JSON.stringify([{ id: '1' }]),
        fields_guest: null
      },
      metadata: { expenseCount: 1 }
    };

    const result = backupService.validateBackup(validBackup);
    return result.isValid === true && result.errors.length === 0;
  });

  // Test 4: Backup validation - invalid backup
  await test('Backup validation - invalid backup', async () => {
    const invalidBackup = {
      // Missing required fields
      data: {
        expenses_guest: 'invalid-json-{{'
      }
    };

    const result = backupService.validateBackup(invalidBackup);
    return result.isValid === false && result.errors.length > 0;
  });

  // Test 5: Backup restore
  await test('Backup restore functionality', async () => {
    // Create a backup first
    const testExpenses = [{ id: '2', amount: 200, description: 'Test expense 2' }];
    await mockStorage.setItem('expenses_guest', JSON.stringify(testExpenses));
    
    const backupResult = await backupService.createMigrationBackup();
    const backupId = backupResult.backupId;

    // Modify original data
    await mockStorage.setItem('expenses_guest', JSON.stringify([]));

    // Restore from backup
    const restoreResult = await backupService.restoreFromBackup(backupId);
    
    // Check if data was restored
    const restoredData = await mockStorage.getItem('expenses_guest');
    const restoredExpenses = JSON.parse(restoredData);

    return restoreResult.success === true && 
           restoredExpenses.length === 1 && 
           restoredExpenses[0].id === '2';
  });

  // Test 6: Backup existence check
  await test('Backup existence check', async () => {
    const hasBackupBefore = await backupService.hasBackup();
    
    // Create backup
    await mockStorage.setItem('expenses_guest', JSON.stringify([{ id: '1' }]));
    await backupService.createMigrationBackup();
    
    const hasBackupAfter = await backupService.hasBackup();
    
    return hasBackupBefore === true && hasBackupAfter === true; // Should have backup from previous test
  });

  // Test 7: Backup metadata retrieval
  await test('Backup metadata retrieval', async () => {
    const metadata = await backupService.getBackupMetadata();
    
    return metadata !== null && 
           metadata.backupId && 
           metadata.timestamp && 
           metadata.itemCounts;
  });

  // Test 8: Backup cleanup
  await test('Backup cleanup', async () => {
    const metadata = await backupService.getBackupMetadata();
    const backupId = metadata.backupId;
    
    const cleanupResult = await backupService.cleanupBackup(backupId);
    const hasBackupAfterCleanup = await backupService.hasBackup();
    
    return cleanupResult === true && hasBackupAfterCleanup === false;
  });

  // Summary
  console.log('\n📊 Backup Test Results Summary:');
  console.log(`✅ Passed: ${passedTests}/${totalTests}`);
  console.log(`❌ Failed: ${totalTests - passedTests}/${totalTests}`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All backup tests passed!');
    return true;
  } else {
    console.log('⚠️ Some backup tests failed!');
    return false;
  }
}

// Run the tests
runBackupTests().then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('❌ Backup test suite failed:', error);
  process.exit(1);
});
