#!/usr/bin/env node

/**
 * Test script for boolean sanitization in season creation API
 * Tests the fix for migration validation errors
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3000/api/v1';

// Test data with string boolean values (as sent from frontend during migration)
const testSeasonData = {
  name: '2025 Test Sezonu (Boolean Test)',
  description: 'Boolean sanitization test için oluşturulan sezon',
  startDate: new Date().toISOString(),
  isActive: 'true',  // String boolean - should be sanitized to true
  isDefault: 'false', // String boolean - should be sanitized to false
  color: '#4CAF50',
  emoji: '🌱'
};

// Test data with actual boolean values
const testSeasonDataBoolean = {
  name: '2025 Test Sezonu (Real Boolean)',
  description: 'Gerçek boolean değerlerle test',
  startDate: new Date().toISOString(),
  isActive: true,  // Real boolean
  isDefault: false, // Real boolean
  color: '#4CAF50',
  emoji: '🌱'
};

async function testBooleanSanitization() {
  console.log('🧪 Boolean Sanitization Test Başlıyor...\n');

  // Development mode mock token (backend will auto-create a dev user)
  const mockToken = 'Bearer dev-test-token-' + Date.now();
  
  try {
    console.log('📝 Test 1: String Boolean Değerler');
    console.log('Gönderilen veri:', JSON.stringify(testSeasonData, null, 2));
    
    const response1 = await axios.post(`${API_BASE_URL}/seasons`, testSeasonData, {
      headers: {
        'Authorization': mockToken,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Test 1 Başarılı:', response1.status);
    console.log('Dönen veri:', JSON.stringify(response1.data, null, 2));
    
  } catch (error) {
    console.log('❌ Test 1 Hatası:', error.response?.status);
    console.log('Hata detayı:', error.response?.data);
  }

  console.log('\n' + '='.repeat(50) + '\n');

  try {
    console.log('📝 Test 2: Gerçek Boolean Değerler');
    console.log('Gönderilen veri:', JSON.stringify(testSeasonDataBoolean, null, 2));
    
    const response2 = await axios.post(`${API_BASE_URL}/seasons`, testSeasonDataBoolean, {
      headers: {
        'Authorization': mockToken,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Test 2 Başarılı:', response2.status);
    console.log('Dönen veri:', JSON.stringify(response2.data, null, 2));
    
  } catch (error) {
    console.log('❌ Test 2 Hatası:', error.response?.status);
    console.log('Hata detayı:', error.response?.data);
  }

  console.log('\n🏁 Test tamamlandı!');
}

// Test'i çalıştır
testBooleanSanitization().catch(console.error);
