[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[ ] NAME:Epic 1: Excel Template Sistemi DESCRIPTION:Sabit Excel template dosyası oluşturma, indirme ve yükleme sistemi. Kullanıcılar template'i indirip verilerini manuel olarak girip sisteme yükleyebilecek.
--[ ] NAME:Excel Template Dosyası Tasarımı DESCRIPTION:Tarla adı, gider kategorisi, miktar, tarih, açıklama kolonları ile standart Excel template dosyası oluşturma. Türk<PERSON>e başlıklar, veri validasyon kuralları ve örnek veriler dahil.
--[ ] NAME:Backend Template Download Endpoint DESCRIPTION:Excel template dosyasını indirmek için backend endpoint (/api/v1/import/template) oluşturma. Static file serving ve proper headers ile dosya indirme.
--[ ] NAME:Frontend Template Download UI DESCRIPTION:Settings ekranında template indirme butonu ve UI. Download progress indicator, success/error handling ve file save functionality.
--[ ] NAME:Excel File Upload Backend DESCRIPTION:Kullanıcıların doldurdukları Excel dosyalarını yüklemek için backend endpoint. File validation, Excel parsing ve veri extraction.
--[ ] NAME:Excel Data Validation Service DESCRIPTION:Yüklenen Excel dosyasındaki verilerin validasyonu. Tarih formatları, kategori kontrolü, miktar validasyonu ve hata raporlama.
--[ ] NAME:Frontend Excel Upload UI DESCRIPTION:Excel dosyası seçme, yükleme ve preview ekranı. File picker, upload progress, validation results ve import confirmation.
-[ ] NAME:Epic 2: Standart Excel Export Sistemi DESCRIPTION:Mevcut verileri Excel formatında export etme özelliği. Tüm gider kategorileri, tarla bilgileri, sezon verileri dahil.
--[ ] NAME:Backend Export Endpoint Tasarımı DESCRIPTION:Mevcut verileri Excel formatında export etmek için backend endpoint (/api/v1/export/excel) oluşturma. Query parameters ile tarih aralığı, kategori filtreleme.
--[ ] NAME:Excel Export Service DESCRIPTION:Veritabanından veri çekip Excel dosyası oluşturan service. Gider, kategori, tarla, sezon verilerini birleştirme ve formatting.
--[ ] NAME:Export Data Aggregation DESCRIPTION:Export edilecek verilerin toplanması ve birleştirilmesi. User-specific data filtering, date range queries ve related data joining.
--[ ] NAME:Frontend Export UI DESCRIPTION:Settings ekranında export butonu ve seçenekleri. Tarih aralığı seçimi, kategori filtreleme, export format seçimi ve download.
--[ ] NAME:Export Progress ve Feedback DESCRIPTION:Export işlemi sırasında progress indicator, success/error handling ve dosya indirme confirmation.
--[ ] NAME:Export File Management DESCRIPTION:Oluşturulan Excel dosyalarının geçici depolanması, cleanup ve security. File naming conventions ve download links.
-[ ] NAME:Epic 3: AI Destekli Chat Import Sistemi DESCRIPTION:Kullanıcıların düzensiz notlarını AI (Gemini) ile Excel template formatına dönüştürme sistemi. Chat arayüzü, veri dönüştürme, onay ve import süreçleri.
--[ ] NAME:Gemini AI API Entegrasyonu DESCRIPTION:Google Gemini API'sini backend'e entegre etme. API key configuration, request/response handling ve rate limiting.
--[ ] NAME:Chat Interface Component DESCRIPTION:Kullanıcıların düzensiz notlarını yapıştırabileceği chat arayüzü. Message bubbles, input field, send button ve conversation history.
--[ ] NAME:AI Text Processing Service DESCRIPTION:Kullanıcı notlarını AI ile analiz edip Excel template formatına dönüştüren service. Prompt engineering ve response parsing.
--[ ] NAME:Smart Question System DESCRIPTION:Eksik/belirsiz bilgiler için kullanıcıya otomatik soru sorma sistemi. Context-aware questions ve progressive data collection.
--[ ] NAME:Data Preview ve Edit UI DESCRIPTION:AI tarafından dönüştürülen verilerin kullanıcıya gösterilmesi ve düzenleme arayüzü. Editable table, validation ve correction tools.
--[ ] NAME:AI Import Confirmation Flow DESCRIPTION:Final onay süreci ve 'İçeri Aktar' butonu. Data summary, final validation ve import execution.
-[ ] NAME:Epic 4: Authentication ve Erişim Kontrolü DESCRIPTION:Import/Export özelliklerinin sadece Google Sign-In kullanıcıları için erişilebilir olması. Misafir kullanıcılar için kısıtlama ve uyarı sistemleri.
--[ ] NAME:Authentication Middleware Güncelleme DESCRIPTION:Mevcut authentication middleware'i import/export endpoints için güncelleme. Google Sign-In requirement ve guest user blocking.
--[ ] NAME:Frontend Auth Guard Component DESCRIPTION:Import/Export ekranlarına erişim kontrolü için component. Authentication status check ve redirect logic.
--[ ] NAME:Guest User Warning System DESCRIPTION:Misafir kullanıcılar için import/export özelliklerine erişim denemesinde uyarı mesajları ve Google Sign-In yönlendirmesi.
--[ ] NAME:Settings Screen Integration DESCRIPTION:Settings ekranına import/export seçeneklerinin eklenmesi. Authentication-based visibility ve feature toggles.
--[ ] NAME:Permission Error Handling DESCRIPTION:Yetkisiz erişim denemelerinde error handling ve user-friendly mesajlar. Backend error responses ve frontend error display.
-[ ] NAME:Epic 5: Backend Infrastructure DESCRIPTION:Excel dosya işleme, AI API entegrasyonu, file upload/download endpoints, veri validasyon ve error handling altyapısı.
--[ ] NAME:Excel Processing Library Setup DESCRIPTION:Backend'e Excel dosya işleme kütüphanesi (xlsx, exceljs) kurulumu ve konfigurasyonu. Read/write operations ve error handling.
--[ ] NAME:File Upload Infrastructure DESCRIPTION:Multer middleware setup, file size limits, allowed file types ve temporary file storage configuration.
--[ ] NAME:Database Schema Extensions DESCRIPTION:Import/export işlemleri için gerekli database schema genişletmeleri. Import history, batch operations ve metadata tracking.
--[ ] NAME:Error Handling Framework DESCRIPTION:Import/export işlemleri için kapsamlı error handling. Validation errors, file processing errors ve user-friendly error messages.
--[ ] NAME:Background Job System DESCRIPTION:Büyük dosya işlemleri için background job system. Queue management, progress tracking ve notification system.
--[ ] NAME:Security ve Validation DESCRIPTION:File upload security, malware scanning, data validation ve rate limiting. Input sanitization ve XSS protection.