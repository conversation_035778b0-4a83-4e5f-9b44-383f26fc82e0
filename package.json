{"name": "ciftcinot<PERSON><PERSON><PERSON>", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "build:dev:android": "eas build --platform android --profile development", "build:dev:ios": "eas build --platform ios --profile development", "build:dev:all": "eas build --platform all --profile development", "install:dev:android": "eas build:run --platform android", "install:dev:ios": "eas build:run --platform ios"}, "dependencies": {"@expo/metro-runtime": "~5.0.4", "@google/generative-ai": "^0.24.1", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "8.4.1", "@react-native-community/netinfo": "^11.4.1", "@react-native-firebase/app": "^22.2.1", "@react-native-firebase/auth": "^22.2.1", "@react-native-google-signin/google-signin": "^15.0.0", "@react-native-masked-view/masked-view": "^0.3.2", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.2", "bull": "^4.16.5", "exceljs": "^4.4.0", "expo": "53.0.19", "expo-build-properties": "~0.14.8", "expo-dev-client": "~5.2.4", "expo-linear-gradient": "~14.1.5", "expo-status-bar": "~2.2.3", "file-type": "^21.0.0", "node-cache": "^5.1.2", "prop-types": "^15.8.1", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-collapsible": "^1.6.2", "react-native-date-picker": "^5.0.13", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "~4.11.1", "react-native-sqlite-2": "^3.6.2", "react-native-sqlite-storage": "^6.0.1", "react-native-web": "^0.20.0", "redis": "^5.6.0", "expo-document-picker": "~13.1.6"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}