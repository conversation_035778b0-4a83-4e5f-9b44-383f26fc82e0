const { withDangerousMod } = require('@expo/config-plugins');
const fs = require('fs');
const path = require('path');

// Hardcoded google-services.json content as fallback
const GOOGLE_SERVICES_CONTENT = {
  "project_info": {
    "project_number": "680682523446",
    "project_id": "ciftcinotdefterim",
    "storage_bucket": "ciftcinotdefterim.firebasestorage.app"
  },
  "client": [
    {
      "client_info": {
        "mobilesdk_app_id": "1:680682523446:android:c2fbd9ac676466c989c197",
        "android_client_info": {
          "package_name": "com.ciftcinotdefterim.app"
        }
      },
      "oauth_client": [
        {
          "client_id": "680682523446-rd7a61p4qvp2r8nih9sv151dlj4mji0r.apps.googleusercontent.com",
          "client_type": 3
        }
      ],
      "api_key": [
        {
          "current_key": "AIzaSyCZvYEUEXUKbZrBdG4KvTfyVkfRu8xcCKY"
        }
      ],
      "services": {
        "appinvite_service": {
          "other_platform_oauth_client": [
            {
              "client_id": "680682523446-rd7a61p4qvp2r8nih9sv151dlj4mji0r.apps.googleusercontent.com",
              "client_type": 3
            }
          ]
        }
      }
    }
  ],
  "configuration_version": "1"
};

function withFirebaseConfig(config) {
  // Create the google-services.json file before other plugins process it
  return withDangerousMod(config, [
    'android',
    async (config) => {
      console.log('🔥 Setting up google-services.json...');

      const projectRoot = config.modRequest.projectRoot;
      const androidDir = path.join(projectRoot, 'android');
      const googleServicesPath = path.join(androidDir, 'google-services.json');

      // Ensure android directory exists
      if (!fs.existsSync(androidDir)) {
        fs.mkdirSync(androidDir, { recursive: true });
      }

      let contentToWrite = null;
      const googleServicesJson = process.env.GOOGLE_SERVICES_JSON;

      if (googleServicesJson) {
        console.log('📦 Using google-services.json from environment variable...');
        try {
          const decodedContent = Buffer.from(googleServicesJson, 'base64').toString('utf8');
          contentToWrite = decodedContent;

          // Verify the content is valid JSON
          const parsedJson = JSON.parse(decodedContent);
          console.log(`✅ Environment variable content is valid JSON`);
          console.log(`📋 Project ID: ${parsedJson.project_info?.project_id}`);
        } catch (error) {
          console.log('❌ Invalid environment variable content:', error.message);
          console.log('🔄 Falling back to hardcoded content...');
          contentToWrite = JSON.stringify(GOOGLE_SERVICES_CONTENT, null, 2);
        }
      } else {
        console.log('📁 Using hardcoded google-services.json content...');
        contentToWrite = JSON.stringify(GOOGLE_SERVICES_CONTENT, null, 2);
      }

      // Write the file
      fs.writeFileSync(googleServicesPath, contentToWrite);

      console.log('✅ google-services.json created successfully');
      console.log(`📍 File location: ${googleServicesPath}`);
      console.log(`📋 File size: ${fs.statSync(googleServicesPath).size} bytes`);

      return config;
    },
  ]);
}

module.exports = withFirebaseConfig;
