/**
 * Migration End-to-End Test Scenarios
 * Tests complete migration flow with different scenarios
 */

console.log('🧪 Starting Migration End-to-End Test Scenarios...\n');

// Mock storage and services for testing
const mockStorage = {
  data: {},
  getItem: async (key) => mockStorage.data[key] || null,
  setItem: async (key, value) => { mockStorage.data[key] = value; },
  removeItem: async (key) => { delete mockStorage.data[key]; }
};

// Mock API client
class MockAPIClient {
  constructor() {
    this.initialized = true;
    this.shouldFailSeasonCreation = false;
    this.shouldFailExpenseCreation = false;
    this.shouldFailNetworkRequest = false;
    this.requestCount = 0;
  }

  async makeRequest(endpoint, options = {}) {
    this.requestCount++;
    
    if (this.shouldFailNetworkRequest) {
      throw new Error('Network timeout');
    }

    if (endpoint === '/seasons' && options.method === 'POST') {
      if (this.shouldFailSeasonCreation) {
        throw new Error('Season creation failed');
      }
      return {
        data: {
          _id: '507f1f77bcf86cd799439011',
          name: '2024 Sezonu (Migration)',
          isActive: true
        }
      };
    }

    if (endpoint === '/seasons' && options.method === 'GET') {
      return {
        data: [{
          _id: '507f1f77bcf86cd799439011',
          name: '2024 Sezonu',
          isActive: true
        }]
      };
    }

    if (endpoint === '/categories') {
      return {
        data: [{
          _id: '507f1f77bcf86cd799439012',
          name: 'Gübre',
          isDefault: true
        }]
      };
    }

    if (endpoint === '/expenses' && options.method === 'POST') {
      if (this.shouldFailExpenseCreation) {
        throw new Error('Expense creation failed');
      }
      return {
        data: {
          _id: '507f1f77bcf86cd799439013',
          amount: 100,
          description: 'Migrated expense'
        }
      };
    }

    if (endpoint === '/users/clear-data' && options.method === 'DELETE') {
      return { success: true };
    }

    return { data: [] };
  }
}

// Simplified migration service
class SimpleMigrationService {
  constructor() {
    this.apiClient = new MockAPIClient();
    this.backupId = null;
  }

  async createBackup() {
    const backupId = `backup_${Date.now()}_test`;
    
    // Get guest data
    const [expenses, fields] = await Promise.all([
      mockStorage.getItem('expenses_guest'),
      mockStorage.getItem('fields_guest')
    ]);

    // Create backup
    const backup = {
      backupId,
      timestamp: new Date().toISOString(),
      data: { expenses_guest: expenses, fields_guest: fields }
    };

    await mockStorage.setItem('migration_backup', JSON.stringify(backup));
    this.backupId = backupId;
    
    return { success: true, backupId };
  }

  async rollback(backupId) {
    const backupData = await mockStorage.getItem('migration_backup');
    if (!backupData) throw new Error('No backup found');

    const backup = JSON.parse(backupData);
    if (backup.backupId !== backupId) throw new Error('Backup ID mismatch');

    // Restore data
    for (const [key, value] of Object.entries(backup.data)) {
      if (value) await mockStorage.setItem(key, value);
    }

    return { success: true };
  }

  async validateGuestData() {
    const expenses = await mockStorage.getItem('expenses_guest');
    const expenseData = expenses ? JSON.parse(expenses) : [];
    
    const errors = [];
    expenseData.forEach((expense, index) => {
      if (!expense.amount || expense.amount <= 0) {
        errors.push(`Expense ${index + 1}: Invalid amount`);
      }
      if (!expense.categoryId) {
        errors.push(`Expense ${index + 1}: Missing category`);
      }
    });

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  async migrateGuestDataToUser() {
    const migrationProgress = {
      step: 'starting',
      seasonCreated: false,
      expensesMigrated: 0,
      completed: false
    };

    try {
      // Step 1: Create backup
      console.log('🔄 Step 1: Creating backup...');
      const backupResult = await this.createBackup();
      console.log('✅ Backup created:', backupResult.backupId);

      // Step 2: Validate guest data
      console.log('🔄 Step 2: Validating guest data...');
      const validation = await this.validateGuestData();
      if (!validation.isValid) {
        throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
      }
      console.log('✅ Guest data validation passed');

      // Step 3: Create season
      console.log('🔄 Step 3: Creating season...');
      migrationProgress.step = 'season_creation';
      const seasonResponse = await this.apiClient.makeRequest('/seasons', {
        method: 'POST',
        body: JSON.stringify({
          name: '2024 Sezonu (Migration)',
          isActive: true
        })
      });
      const seasonId = seasonResponse.data._id;
      migrationProgress.seasonCreated = true;
      console.log('✅ Season created:', seasonId);

      // Step 4: Get categories
      console.log('🔄 Step 4: Getting categories...');
      const categoriesResponse = await this.apiClient.makeRequest('/categories');
      const defaultCategory = categoriesResponse.data[0];
      if (!defaultCategory || !defaultCategory._id) {
        throw new Error('No default category found');
      }
      console.log('✅ Categories loaded');

      // Step 5: Migrate expenses
      console.log('🔄 Step 5: Migrating expenses...');
      const expenses = await mockStorage.getItem('expenses_guest');
      const expenseData = expenses ? JSON.parse(expenses) : [];

      for (const expense of expenseData) {
        const migratedExpense = {
          categoryId: defaultCategory._id,
          seasonId: seasonId,
          amount: expense.amount,
          description: expense.description || '',
          date: expense.date || new Date().toISOString()
        };

        await this.apiClient.makeRequest('/expenses', {
          method: 'POST',
          body: JSON.stringify(migratedExpense)
        });

        migrationProgress.expensesMigrated++;
      }
      console.log(`✅ Migrated ${migrationProgress.expensesMigrated} expenses`);

      // Step 6: Clear guest data
      console.log('🔄 Step 6: Clearing guest data...');
      await Promise.all([
        mockStorage.removeItem('expenses_guest'),
        mockStorage.removeItem('fields_guest')
      ]);
      console.log('✅ Guest data cleared');

      migrationProgress.completed = true;
      migrationProgress.step = 'completed';

      return {
        success: true,
        migratedCount: migrationProgress.expensesMigrated,
        migrationProgress
      };

    } catch (error) {
      console.error('❌ Migration failed:', error.message);

      // Attempt rollback
      if (this.backupId) {
        try {
          console.log('🔄 Attempting rollback...');
          await this.rollback(this.backupId);
          console.log('✅ Rollback successful');
        } catch (rollbackError) {
          console.error('❌ Rollback failed:', rollbackError.message);
        }
      }

      throw error;
    }
  }
}

// Test scenarios
async function runMigrationScenarios() {
  let passedTests = 0;
  let totalTests = 0;

  async function testScenario(name, setupFn, expectedResult) {
    totalTests++;
    console.log(`\n📋 Testing: ${name}`);
    
    try {
      // Reset state
      mockStorage.data = {};
      const migrationService = new SimpleMigrationService();
      
      // Setup scenario
      await setupFn(migrationService);
      
      // Run migration
      const result = await migrationService.migrateGuestDataToUser();
      
      if (expectedResult === 'success' && result.success) {
        console.log(`✅ ${name}: SUCCESS (Expected)`);
        passedTests++;
      } else if (expectedResult === 'success' && !result.success) {
        console.log(`❌ ${name}: FAILED (Expected success)`);
      }
      
    } catch (error) {
      if (expectedResult === 'failure') {
        console.log(`✅ ${name}: FAILED (Expected)`);
        passedTests++;
      } else {
        console.log(`❌ ${name}: FAILED (Unexpected) - ${error.message}`);
      }
    }
  }

  // Scenario 1: Successful migration (happy path)
  await testScenario(
    'Successful migration with valid data',
    async (service) => {
      const validExpenses = [
        { amount: 100, description: 'Test expense 1', categoryId: 'gübre', date: new Date().toISOString() },
        { amount: 200, description: 'Test expense 2', categoryId: 'işçilik', date: new Date().toISOString() }
      ];
      await mockStorage.setItem('expenses_guest', JSON.stringify(validExpenses));
    },
    'success'
  );

  // Scenario 2: Validation error prevents migration
  await testScenario(
    'Validation error prevents migration',
    async (service) => {
      const invalidExpenses = [
        { amount: 0, description: 'Invalid expense', categoryId: '', date: new Date().toISOString() }
      ];
      await mockStorage.setItem('expenses_guest', JSON.stringify(invalidExpenses));
    },
    'failure'
  );

  // Scenario 3: Season creation failure triggers rollback
  await testScenario(
    'Season creation failure triggers rollback',
    async (service) => {
      service.apiClient.shouldFailSeasonCreation = true;
      const validExpenses = [
        { amount: 100, description: 'Test expense', categoryId: 'gübre', date: new Date().toISOString() }
      ];
      await mockStorage.setItem('expenses_guest', JSON.stringify(validExpenses));
    },
    'failure'
  );

  // Scenario 4: Network failure during migration
  await testScenario(
    'Network failure during migration',
    async (service) => {
      service.apiClient.shouldFailNetworkRequest = true;
      const validExpenses = [
        { amount: 100, description: 'Test expense', categoryId: 'gübre', date: new Date().toISOString() }
      ];
      await mockStorage.setItem('expenses_guest', JSON.stringify(validExpenses));
    },
    'failure'
  );

  // Scenario 5: Partial migration failure
  await testScenario(
    'Partial migration failure recovery',
    async (service) => {
      // Set up to fail after first expense
      let requestCount = 0;
      const originalMakeRequest = service.apiClient.makeRequest.bind(service.apiClient);
      service.apiClient.makeRequest = async (endpoint, options) => {
        if (endpoint === '/expenses' && options.method === 'POST') {
          requestCount++;
          if (requestCount > 1) {
            throw new Error('Partial failure simulation');
          }
        }
        return originalMakeRequest(endpoint, options);
      };

      const validExpenses = [
        { amount: 100, description: 'Test expense 1', categoryId: 'gübre', date: new Date().toISOString() },
        { amount: 200, description: 'Test expense 2', categoryId: 'gübre', date: new Date().toISOString() }
      ];
      await mockStorage.setItem('expenses_guest', JSON.stringify(validExpenses));
    },
    'failure'
  );

  // Summary
  console.log('\n📊 Migration Scenario Test Results:');
  console.log(`✅ Passed: ${passedTests}/${totalTests}`);
  console.log(`❌ Failed: ${totalTests - passedTests}/${totalTests}`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All migration scenarios passed!');
    return true;
  } else {
    console.log('⚠️ Some migration scenarios failed!');
    return false;
  }
}

// Run the scenarios
runMigrationScenarios().then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('❌ Migration scenario tests failed:', error);
  process.exit(1);
});
