/**
 * Simple Validation Test
 * Tests validation logic without ES modules
 */

console.log('🧪 Starting Simple Validation Tests...\n');

// Test 1: Field Validation Logic
console.log('📋 Test 1: Field Validation Logic');

function validateFieldName(name) {
  if (!name || name.trim().length === 0) {
    return { isValid: false, error: 'Tarla adı boş olamaz' };
  }
  if (name.length > 50) {
    return { isValid: false, error: 'Tarla adı en fazla 50 karakter olabilir' };
  }
  return { isValid: true };
}

function validateFieldSize(size) {
  if (size && size.value !== undefined) {
    if (typeof size.value !== 'number' || size.value < 0) {
      return { isValid: false, error: 'Alan değeri negatif olamaz' };
    }
  }
  if (size && size.unit) {
    const validUnits = ['dekar', 'dönüm', 'hectare', 'acre'];
    if (!validUnits.includes(size.unit)) {
      return { isValid: false, error: '<PERSON><PERSON><PERSON><PERSON><PERSON> alan bi<PERSON>' };
    }
  }
  return { isValid: true };
}

// Test field validation
const testFields = [
  { name: 'Valid Field', size: { value: 10, unit: 'dekar' } },
  { name: '', size: { value: 10, unit: 'dekar' } },
  { name: 'A'.repeat(51), size: { value: 10, unit: 'dekar' } },
  { name: 'Test Field', size: { value: -5, unit: 'dekar' } },
  { name: 'Test Field', size: { value: 10, unit: 'invalid' } }
];

let fieldTestsPassed = 0;
testFields.forEach((field, index) => {
  const nameValidation = validateFieldName(field.name);
  const sizeValidation = validateFieldSize(field.size);
  const isValid = nameValidation.isValid && sizeValidation.isValid;
  
  const expected = [true, false, false, false, false][index];
  if (isValid === expected) {
    console.log(`✅ Field test ${index + 1}: ${isValid ? 'Valid' : 'Invalid'} (Expected)`);
    fieldTestsPassed++;
  } else {
    console.log(`❌ Field test ${index + 1}: ${isValid ? 'Valid' : 'Invalid'} (Unexpected)`);
  }
});

console.log(`Field validation: ${fieldTestsPassed}/5 tests passed\n`);

// Test 2: Expense Validation Logic
console.log('📋 Test 2: Expense Validation Logic');

function validateExpenseAmount(amount) {
  if (amount === undefined || amount === null || amount === '') {
    return { isValid: false, error: 'Tutar zorunludur' };
  }
  const numAmount = parseFloat(amount);
  if (isNaN(numAmount)) {
    return { isValid: false, error: 'Tutar sayı olmalıdır' };
  }
  if (numAmount <= 0) {
    return { isValid: false, error: 'Tutar 0\'dan büyük olmalıdır' };
  }
  if (numAmount > 1000000) {
    return { isValid: false, error: 'Tutar en fazla 1.000.000 TL olabilir' };
  }
  return { isValid: true };
}

function validateExpenseDate(date) {
  if (!date) {
    return { isValid: false, error: 'Tarih zorunludur' };
  }
  const dateObj = new Date(date);
  if (isNaN(dateObj.getTime())) {
    return { isValid: false, error: 'Geçerli bir tarih giriniz' };
  }
  
  // Check date range (1 year ago to 1 year from now)
  const now = new Date();
  const oneYearAgo = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());
  const oneYearFromNow = new Date(now.getFullYear() + 1, now.getMonth(), now.getDate());
  
  if (dateObj < oneYearAgo || dateObj > oneYearFromNow) {
    return { isValid: false, error: 'Tarih son 1 yıl ile gelecek 1 yıl arasında olmalıdır' };
  }
  
  return { isValid: true };
}

function validateObjectId(id) {
  if (!id || typeof id !== 'string') return false;
  const objectIdRegex = /^[0-9a-fA-F]{24}$/;
  return objectIdRegex.test(id);
}

// Test expense validation
const testExpenses = [
  { amount: 100, date: new Date().toISOString(), categoryId: '507f1f77bcf86cd799439011', seasonId: '507f1f77bcf86cd799439012' },
  { amount: 0, date: new Date().toISOString(), categoryId: '507f1f77bcf86cd799439011', seasonId: '507f1f77bcf86cd799439012' },
  { amount: 2000000, date: new Date().toISOString(), categoryId: '507f1f77bcf86cd799439011', seasonId: '507f1f77bcf86cd799439012' },
  { amount: 100, date: 'invalid-date', categoryId: '507f1f77bcf86cd799439011', seasonId: '507f1f77bcf86cd799439012' },
  { amount: 100, date: new Date().toISOString(), categoryId: 'invalid-id', seasonId: '507f1f77bcf86cd799439012' }
];

let expenseTestsPassed = 0;
testExpenses.forEach((expense, index) => {
  const amountValidation = validateExpenseAmount(expense.amount);
  const dateValidation = validateExpenseDate(expense.date);
  const categoryValidation = validateObjectId(expense.categoryId);
  const seasonValidation = validateObjectId(expense.seasonId);
  
  const isValid = amountValidation.isValid && dateValidation.isValid && categoryValidation && seasonValidation;
  const expected = [true, false, false, false, false][index];
  
  if (isValid === expected) {
    console.log(`✅ Expense test ${index + 1}: ${isValid ? 'Valid' : 'Invalid'} (Expected)`);
    expenseTestsPassed++;
  } else {
    console.log(`❌ Expense test ${index + 1}: ${isValid ? 'Valid' : 'Invalid'} (Unexpected)`);
    if (!isValid) {
      console.log(`   Errors: ${[amountValidation.error, dateValidation.error].filter(e => e).join(', ')}`);
    }
  }
});

console.log(`Expense validation: ${expenseTestsPassed}/5 tests passed\n`);

// Test 3: ObjectId Validation
console.log('📋 Test 3: ObjectId Validation');

const testObjectIds = [
  '507f1f77bcf86cd799439011', // Valid
  'invalid-id',                // Invalid
  '507f1f77bcf86cd79943901',   // Too short
  '507f1f77bcf86cd799439011x', // Too long
  ''                           // Empty
];

let objectIdTestsPassed = 0;
testObjectIds.forEach((id, index) => {
  const isValid = validateObjectId(id);
  const expected = [true, false, false, false, false][index];
  
  if (isValid === expected) {
    console.log(`✅ ObjectId test ${index + 1}: ${isValid ? 'Valid' : 'Invalid'} (Expected)`);
    objectIdTestsPassed++;
  } else {
    console.log(`❌ ObjectId test ${index + 1}: ${isValid ? 'Valid' : 'Invalid'} (Unexpected)`);
  }
});

console.log(`ObjectId validation: ${objectIdTestsPassed}/5 tests passed\n`);

// Test 4: Auto-fix Logic
console.log('📋 Test 4: Auto-fix Logic');

function autoFixField(field) {
  const fixed = { ...field };
  let fixCount = 0;
  
  // Fix negative area
  if (fixed.size && fixed.size.value < 0) {
    fixed.size.value = Math.abs(fixed.size.value);
    fixCount++;
  }
  
  // Fix long name
  if (fixed.name && fixed.name.length > 50) {
    fixed.name = fixed.name.substring(0, 50);
    fixCount++;
  }
  
  // Fix invalid unit
  if (fixed.size && fixed.size.unit && !['dekar', 'dönüm', 'hectare', 'acre'].includes(fixed.size.unit)) {
    fixed.size.unit = 'dekar';
    fixCount++;
  }
  
  return { fixed, fixCount };
}

const testFixFields = [
  { name: 'Test Field', size: { value: -10, unit: 'dekar' } },
  { name: 'A'.repeat(60), size: { value: 10, unit: 'invalid' } }
];

let autoFixTestsPassed = 0;
testFixFields.forEach((field, index) => {
  const { fixed, fixCount } = autoFixField(field);
  const expectedFixCount = [1, 2][index];
  
  if (fixCount === expectedFixCount) {
    console.log(`✅ Auto-fix test ${index + 1}: ${fixCount} fixes applied (Expected)`);
    autoFixTestsPassed++;
  } else {
    console.log(`❌ Auto-fix test ${index + 1}: ${fixCount} fixes applied (Expected ${expectedFixCount})`);
  }
});

console.log(`Auto-fix logic: ${autoFixTestsPassed}/2 tests passed\n`);

// Summary
const totalPassed = fieldTestsPassed + expenseTestsPassed + objectIdTestsPassed + autoFixTestsPassed;
const totalTests = 5 + 5 + 5 + 2;

console.log('📊 Test Results Summary:');
console.log(`✅ Total Passed: ${totalPassed}/${totalTests}`);
console.log(`❌ Total Failed: ${totalTests - totalPassed}/${totalTests}`);

if (totalPassed === totalTests) {
  console.log('🎉 All validation logic tests passed!');
  process.exit(0);
} else {
  console.log('⚠️ Some validation logic tests failed!');
  process.exit(1);
}
