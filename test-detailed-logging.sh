#!/bin/bash

# Test script for detailed logging in season creation API
# Tests the enhanced logging for debugging migration issues

API_BASE_URL="http://localhost:3000/api/v1"
MOCK_TOKEN="Bearer dev-test-token-logging-$(date +%s)"

echo "🧪 Detailed Logging Test Başlıyor..."
echo ""

# Test: Season creation with detailed logging
echo "📝 Test: Season Creation with Detailed Logging"

TEST_DATA='{
  "name": "Test Season Detailed Logging",
  "description": "Detaylı logging test için oluşturulan sezon",
  "startDate": "'$(date -u +%Y-%m-%dT%H:%M:%S.000Z)'",
  "isActive": "true",
  "isDefault": "false",
  "color": "#4CAF50",
  "emoji": "🌱"
}'

echo "Gönderilen veri:"
echo "$TEST_DATA"
echo ""

echo "API Çağrısı yapılıyor..."
RESPONSE=$(curl -s -w "\nHTTP_STATUS:%{http_code}" \
  -X POST \
  -H "Authorization: $MOCK_TOKEN" \
  -H "Content-Type: application/json" \
  -d "$TEST_DATA" \
  "$API_BASE_URL/seasons")

HTTP_STATUS=$(echo "$RESPONSE" | grep "HTTP_STATUS:" | cut -d: -f2)
RESPONSE_BODY=$(echo "$RESPONSE" | sed '/HTTP_STATUS:/d')

echo "HTTP Status: $HTTP_STATUS"
echo "Response:"
echo "$RESPONSE_BODY" | jq . 2>/dev/null || echo "$RESPONSE_BODY"

echo ""
echo "🔍 Backend loglarını kontrol etmek için:"
echo "docker logs ciftci-backend --tail 50"

echo ""
echo "🏁 Test tamamlandı!"

if [ "$HTTP_STATUS" = "201" ]; then
    echo "✅ Test başarılı! Detaylı loglar backend'de görünmelidir."
    exit 0
else
    echo "❌ Test başarısız! HTTP Status: $HTTP_STATUS"
    exit 1
fi
