/**
 * Validation Services Test Suite
 * Tests FieldValidationService, ExpenseValidationService, and ValidationService
 */

// Mock AsyncStorage for testing
const mockAsyncStorage = {
  data: {},
  getItem: async (key) => {
    return mockAsyncStorage.data[key] || null;
  },
  setItem: async (key, value) => {
    mockAsyncStorage.data[key] = value;
  },
  removeItem: async (key) => {
    delete mockAsyncStorage.data[key];
  }
};

// Mock react-native-uuid
const mockUuid = {
  v4: () => 'test-uuid-1234'
};

// Set up global mocks
global.AsyncStorage = mockAsyncStorage;
global.require = (moduleName) => {
  if (moduleName === '@react-native-async-storage/async-storage') {
    return mockAsyncStorage;
  }
  if (moduleName === 'react-native-uuid') {
    return mockUuid;
  }
  return require(moduleName);
};

// Import services
const FieldValidationService = require('./src/services/FieldValidationService.js').default;
const ExpenseValidationService = require('./src/services/ExpenseValidationService.js').default;
const ValidationService = require('./src/services/ValidationService.js').default;

async function runTests() {
  console.log('🧪 Starting Validation Services Test Suite...\n');

  let passedTests = 0;
  let totalTests = 0;

  function test(name, testFn) {
    totalTests++;
    try {
      const result = testFn();
      if (result === true || (result && result.then)) {
        console.log(`✅ ${name}`);
        passedTests++;
        return result;
      } else {
        console.log(`❌ ${name}: Test returned false`);
      }
    } catch (error) {
      console.log(`❌ ${name}: ${error.message}`);
    }
  }

  async function asyncTest(name, testFn) {
    totalTests++;
    try {
      const result = await testFn();
      if (result === true) {
        console.log(`✅ ${name}`);
        passedTests++;
      } else {
        console.log(`❌ ${name}: Test returned false`);
      }
    } catch (error) {
      console.log(`❌ ${name}: ${error.message}`);
    }
  }

  // FieldValidationService Tests
  console.log('📋 FieldValidationService Tests:');
  
  test('Field name validation - valid name', () => {
    const result = FieldValidationService.validateField({ name: 'Test Field' });
    return result.isValid === true;
  });

  test('Field name validation - empty name', () => {
    const result = FieldValidationService.validateField({ name: '' });
    return result.isValid === false && result.errors.some(e => e.includes('boş olamaz'));
  });

  test('Field name validation - too long name', () => {
    const longName = 'A'.repeat(51);
    const result = FieldValidationService.validateField({ name: longName });
    return result.isValid === false && result.errors.some(e => e.includes('50 karakter'));
  });

  test('Field size validation - negative value', () => {
    const result = FieldValidationService.validateField({ 
      name: 'Test', 
      size: { value: -10, unit: 'dekar' } 
    });
    return result.isValid === false && result.errors.some(e => e.includes('negatif olamaz'));
  });

  test('Field size validation - invalid unit', () => {
    const result = FieldValidationService.validateField({ 
      name: 'Test', 
      size: { value: 10, unit: 'invalid-unit' } 
    });
    return result.isValid === false && result.errors.some(e => e.includes('Geçersiz alan birimi'));
  });

  test('Field migration readiness - valid field', () => {
    const result = FieldValidationService.isMigrationReady({ 
      name: 'Test Field',
      size: { value: 10, unit: 'dekar' }
    });
    return result.isReady === true;
  });

  // ExpenseValidationService Tests
  console.log('\n📋 ExpenseValidationService Tests:');

  test('Expense amount validation - valid amount', () => {
    const result = ExpenseValidationService.validateExpense({
      amount: 100,
      date: new Date(),
      categoryId: '507f1f77bcf86cd799439011',
      seasonId: '507f1f77bcf86cd799439012'
    });
    return result.isValid === true;
  });

  test('Expense amount validation - zero amount', () => {
    const result = ExpenseValidationService.validateExpense({
      amount: 0,
      date: new Date(),
      categoryId: '507f1f77bcf86cd799439011',
      seasonId: '507f1f77bcf86cd799439012'
    });
    return result.isValid === false && result.errors.some(e => e.includes('0\'dan büyük'));
  });

  test('Expense amount validation - too high amount', () => {
    const result = ExpenseValidationService.validateExpense({
      amount: 2000000,
      date: new Date(),
      categoryId: '507f1f77bcf86cd799439011',
      seasonId: '507f1f77bcf86cd799439012'
    });
    return result.isValid === false && result.errors.some(e => e.includes('1.000.000'));
  });

  test('Expense date validation - valid date', () => {
    const result = ExpenseValidationService.validateExpense({
      amount: 100,
      date: new Date(),
      categoryId: '507f1f77bcf86cd799439011',
      seasonId: '507f1f77bcf86cd799439012'
    });
    return result.isValid === true;
  });

  test('Expense date validation - old date', () => {
    const oldDate = new Date();
    oldDate.setFullYear(oldDate.getFullYear() - 2);
    const result = ExpenseValidationService.validateExpense({
      amount: 100,
      date: oldDate,
      categoryId: '507f1f77bcf86cd799439011',
      seasonId: '507f1f77bcf86cd799439012'
    });
    return result.isValid === false && result.errors.some(e => e.includes('1 yıl'));
  });

  test('Expense ObjectId validation - valid ObjectId', () => {
    const result = ExpenseValidationService.isValidObjectId('507f1f77bcf86cd799439011');
    return result === true;
  });

  test('Expense ObjectId validation - invalid ObjectId', () => {
    const result = ExpenseValidationService.isValidObjectId('invalid-id');
    return result === false;
  });

  test('Expense batch validation - mixed valid/invalid', () => {
    const expenses = [
      { amount: 100, date: new Date(), categoryId: '507f1f77bcf86cd799439011', seasonId: '507f1f77bcf86cd799439012' },
      { amount: 0, date: new Date(), categoryId: '507f1f77bcf86cd799439011', seasonId: '507f1f77bcf86cd799439012' }
    ];
    const result = ExpenseValidationService.validateExpenses(expenses);
    return result.validCount === 1 && result.invalidCount === 1;
  });

  // ValidationService Integration Tests
  console.log('\n📋 ValidationService Integration Tests:');

  await asyncTest('ValidationService - empty data validation', async () => {
    // Set up empty test data
    await mockAsyncStorage.setItem('expenses_guest', JSON.stringify([]));
    await mockAsyncStorage.setItem('fields_guest', JSON.stringify([]));
    await mockAsyncStorage.setItem('categories_guest', JSON.stringify([]));

    const result = await ValidationService.validateAllGuestData();
    return result.isValid === true && result.summary.expenses.total === 0;
  });

  await asyncTest('ValidationService - valid data validation', async () => {
    // Set up valid test data
    const validExpenses = [
      { amount: 100, date: new Date().toISOString(), categoryId: '507f1f77bcf86cd799439011', seasonId: '507f1f77bcf86cd799439012' }
    ];
    const validFields = [
      { name: 'Test Field', size: { value: 10, unit: 'dekar' } }
    ];

    await mockAsyncStorage.setItem('expenses_guest', JSON.stringify(validExpenses));
    await mockAsyncStorage.setItem('fields_guest', JSON.stringify(validFields));

    const result = await ValidationService.validateAllGuestData();
    return result.isValid === true && result.summary.expenses.valid === 1 && result.summary.fields.valid === 1;
  });

  await asyncTest('ValidationService - invalid data validation', async () => {
    // Set up invalid test data
    const invalidExpenses = [
      { amount: 0, date: new Date().toISOString(), categoryId: '', seasonId: '' }
    ];
    const invalidFields = [
      { name: '', size: { value: -10, unit: 'invalid' } }
    ];

    await mockAsyncStorage.setItem('expenses_guest', JSON.stringify(invalidExpenses));
    await mockAsyncStorage.setItem('fields_guest', JSON.stringify(invalidFields));

    const result = await ValidationService.validateAllGuestData();
    return result.isValid === false && result.errors.length > 0;
  });

  await asyncTest('ValidationService - fix suggestions generation', async () => {
    // Set up data that can be auto-fixed
    const fixableExpenses = [
      { amount: 100, date: new Date().toISOString(), categoryId: '507f1f77bcf86cd799439011', seasonId: '507f1f77bcf86cd799439012', description: 'A'.repeat(600) }
    ];
    const fixableFields = [
      { name: 'Test Field', size: { value: -10, unit: 'dekar' } }
    ];

    await mockAsyncStorage.setItem('expenses_guest', JSON.stringify(fixableExpenses));
    await mockAsyncStorage.setItem('fields_guest', JSON.stringify(fixableFields));

    const validationResult = await ValidationService.validateAllGuestData();
    const suggestions = ValidationService.generateFixSuggestions(validationResult);
    
    return suggestions.canAutoFix === true && suggestions.autoFixCount > 0;
  });

  await asyncTest('ValidationService - auto-fix application', async () => {
    // Set up data that can be auto-fixed
    const fixableFields = [
      { name: 'Test Field', size: { value: -10, unit: 'dekar' } }
    ];

    await mockAsyncStorage.setItem('fields_guest', JSON.stringify(fixableFields));

    const validationResult = await ValidationService.validateAllGuestData();
    const fixResult = await ValidationService.applyAutoFixes(validationResult);
    
    return fixResult.success === true && fixResult.fixedCount > 0;
  });

  await asyncTest('ValidationService - performance optimization', async () => {
    // Test optimized validation
    const result = await ValidationService.validateAllGuestDataOptimized({ skipDetailedValidation: true });
    return result.performance && result.performance.optimized === true;
  });

  // Summary
  console.log('\n📊 Test Results Summary:');
  console.log(`✅ Passed: ${passedTests}/${totalTests}`);
  console.log(`❌ Failed: ${totalTests - passedTests}/${totalTests}`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All validation tests passed!');
    return true;
  } else {
    console.log('⚠️ Some validation tests failed!');
    return false;
  }
}

// Run the tests
runTests().then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('❌ Test suite failed:', error);
  process.exit(1);
});
