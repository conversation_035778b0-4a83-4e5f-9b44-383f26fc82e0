const { getDefaultConfig } = require('expo/metro-config');

const config = getDefaultConfig(__dirname);

// Disable symbolication to prevent InternalBytecode.js errors
config.symbolicator = {
  customizeFrame: () => null,
};

// Disable source maps in development to prevent symbolication issues
config.transformer = {
  ...config.transformer,
  minifierConfig: {
    ...config.transformer.minifierConfig,
    keep_fnames: true,
  },
};

// Add resolver configuration
config.resolver = {
  ...config.resolver,
  alias: {
    // Add any aliases if needed
  },
};

module.exports = config;
