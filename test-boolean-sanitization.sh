#!/bin/bash

# Test script for boolean sanitization in season creation API
# Tests the fix for migration validation errors

API_BASE_URL="http://localhost:3000/api/v1"
MOCK_TOKEN_1="Bearer dev-test-token-1-$(date +%s)"
MOCK_TOKEN_2="Bearer dev-test-token-2-$(date +%s)"

echo "🧪 Boolean Sanitization Test Başlıyor..."
echo ""

# Test 1: String Boolean De<PERSON>erler
echo "📝 Test 1: String Boolean De<PERSON>erler"
echo "Gönderilen veri:"

TEST_DATA_1='{
  "name": "2025 Test Sezonu (String Boolean)",
  "description": "String boolean test için oluşturulan sezon",
  "startDate": "'$(date -u +%Y-%m-%dT%H:%M:%S.000Z)'",
  "isActive": "true",
  "isDefault": "false",
  "color": "#4CAF50",
  "emoji": "🌱"
}'

echo "$TEST_DATA_1"
echo ""

echo "API Çağrısı yapılıyor..."
RESPONSE_1=$(curl -s -w "\nHTTP_STATUS:%{http_code}" \
  -X POST \
  -H "Authorization: $MOCK_TOKEN_1" \
  -H "Content-Type: application/json" \
  -d "$TEST_DATA_1" \
  "$API_BASE_URL/seasons")

HTTP_STATUS_1=$(echo "$RESPONSE_1" | grep "HTTP_STATUS:" | cut -d: -f2)
RESPONSE_BODY_1=$(echo "$RESPONSE_1" | sed '/HTTP_STATUS:/d')

echo "HTTP Status: $HTTP_STATUS_1"
echo "Response:"
echo "$RESPONSE_BODY_1" | jq . 2>/dev/null || echo "$RESPONSE_BODY_1"

echo ""
echo "=================================================="
echo ""

# Test 2: Gerçek Boolean Değerler
echo "📝 Test 2: Gerçek Boolean Değerler"
echo "Gönderilen veri:"

TEST_DATA_2='{
  "name": "2025 Test Sezonu (Real Boolean)",
  "description": "Gerçek boolean test için oluşturulan sezon",
  "startDate": "'$(date -u +%Y-%m-%dT%H:%M:%S.000Z)'",
  "isActive": true,
  "isDefault": false,
  "color": "#4CAF50",
  "emoji": "🌱"
}'

echo "$TEST_DATA_2"
echo ""

echo "API Çağrısı yapılıyor..."
RESPONSE_2=$(curl -s -w "\nHTTP_STATUS:%{http_code}" \
  -X POST \
  -H "Authorization: $MOCK_TOKEN_2" \
  -H "Content-Type: application/json" \
  -d "$TEST_DATA_2" \
  "$API_BASE_URL/seasons")

HTTP_STATUS_2=$(echo "$RESPONSE_2" | grep "HTTP_STATUS:" | cut -d: -f2)
RESPONSE_BODY_2=$(echo "$RESPONSE_2" | sed '/HTTP_STATUS:/d')

echo "HTTP Status: $HTTP_STATUS_2"
echo "Response:"
echo "$RESPONSE_BODY_2" | jq . 2>/dev/null || echo "$RESPONSE_BODY_2"

echo ""
echo "🏁 Test tamamlandı!"

# Test sonuçlarını değerlendir
if [ "$HTTP_STATUS_1" = "201" ] && [ "$HTTP_STATUS_2" = "201" ]; then
    echo "✅ Tüm testler başarılı!"
    exit 0
elif [ "$HTTP_STATUS_1" = "201" ]; then
    echo "⚠️  Test 1 başarılı, Test 2 başarısız"
    exit 1
elif [ "$HTTP_STATUS_2" = "201" ]; then
    echo "⚠️  Test 2 başarılı, Test 1 başarısız"
    exit 1
else
    echo "❌ Her iki test de başarısız"
    exit 1
fi
