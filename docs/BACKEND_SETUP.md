# Backend Setup Guide - <PERSON><PERSON><PERSON><PERSON> Not Defterim

## Quick Start

This guide provides instructions for setting up and running the Node.js backend service for the Çiftçi Not Defterim mobile application.

## Prerequisites

- **Node.js 18+** installed
- **MongoDB** or **PostgreSQL** database
- **Firebase project** with Admin SDK credentials
- **Git** for version control

## Installation

### 1. Navigate to Backend Directory
```bash
cd backend
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Environment Configuration
```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your configuration
nano .env
```

### 4. Required Environment Variables

#### Database (Choose one)
```env
# MongoDB
MONGODB_URI=mongodb://localhost:27017/ciftci-notebook

# OR PostgreSQL
DATABASE_URL=postgresql://username:password@localhost:5432/ciftci_notebook
```

#### Firebase Configuration
```env
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_PRIVATE_KEY_ID=your-private-key-id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_CLIENT_ID=your-client-id
```

### 5. Database Setup

#### For MongoDB
```bash
# Start MongoDB service
sudo systemctl start mongod

# Or using Docker
docker run -d -p 27017:27017 --name mongodb mongo:latest
```

#### For PostgreSQL
```bash
# Create database
createdb ciftci_notebook

# Or using Docker
docker run -d -p 5432:5432 --name postgres -e POSTGRES_DB=ciftci_notebook -e POSTGRES_PASSWORD=password postgres:latest
```

## Running the Server

### Development Mode
```bash
npm run dev
```

### Production Mode
```bash
npm start
```

### Testing
```bash
# Run all tests
npm test

# Run with coverage
npm run test:coverage

# Run specific test types
npm run test:unit
npm run test:integration
npm run test:e2e
```

## API Endpoints

Once running, the API will be available at:
- **Base URL:** `http://localhost:3000/api/v1`
- **Health Check:** `http://localhost:3000/health`
- **Documentation:** `http://localhost:3000/api-docs` (development only)

### Core Endpoints
- `POST /api/v1/auth/validate` - Validate Firebase token
- `GET /api/v1/users/profile` - Get user profile
- `GET/POST/PUT/DELETE /api/v1/expenses` - Expense management
- `GET/POST /api/v1/categories` - Category management
- `GET /api/v1/seasons` - Agricultural seasons
- `POST /api/v1/sync/push` - Push local changes
- `GET /api/v1/sync/pull` - Pull server changes
- `POST /api/v1/upload/photos` - Upload receipt photos

## Integration with Mobile App

### Update Mobile App Configuration

1. **Update SyncService.js** to use real API endpoints:
```javascript
// Replace simulated cloud operations
async sendToCloud(method, endpoint, data = null) {
  const baseURL = 'http://localhost:3000/api/v1'; // or your production URL
  
  const response = await fetch(`${baseURL}${endpoint}`, {
    method,
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${this.firebaseToken}`,
    },
    body: data ? JSON.stringify(data) : null,
  });
  
  return await response.json();
}
```

2. **Update Firebase Configuration** in mobile app to match backend settings

3. **Test Sync Functionality** between mobile app and backend

## Development Workflow

### 1. Code Structure
```
backend/
├── src/
│   ├── controllers/     # Request handlers
│   ├── middleware/      # Express middleware
│   ├── models/         # Database models
│   ├── routes/         # Route definitions
│   ├── services/       # Business logic
│   └── utils/          # Helper functions
├── tests/              # Test files
├── uploads/            # File uploads
└── logs/              # Application logs
```

### 2. Adding New Features

1. **Create Model** (if needed):
```bash
touch src/models/NewModel.js
```

2. **Create Controller**:
```bash
touch src/controllers/newController.js
```

3. **Create Routes**:
```bash
touch src/routes/new.js
```

4. **Add Tests**:
```bash
touch tests/unit/controllers/newController.test.js
touch tests/integration/new.test.js
```

5. **Update App.js** to include new routes

### 3. Database Migrations

For schema changes, create migration scripts:
```bash
touch scripts/migrations/001_add_new_field.js
```

## Monitoring and Logging

### Log Files
- **Location:** `logs/app.log`
- **Rotation:** Daily, kept for 14 days
- **Levels:** error, warn, info, debug

### Health Monitoring
```bash
# Check server health
curl http://localhost:3000/health

# Check specific endpoint
curl -H "Authorization: Bearer YOUR_TOKEN" http://localhost:3000/api/v1/expenses
```

## Production Deployment

### Environment Variables for Production
```env
NODE_ENV=production
PORT=3000
LOG_LEVEL=warn
ENABLE_DEBUG_LOGS=false
ENABLE_SWAGGER=false
```

### Security Considerations
- Use HTTPS in production
- Set strong JWT_SECRET
- Configure proper CORS origins
- Enable rate limiting
- Use environment-specific Firebase projects

### Docker Deployment (Optional)
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3000
CMD ["npm", "start"]
```

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check database service is running
   - Verify connection string in .env
   - Check firewall settings

2. **Firebase Authentication Error**
   - Verify Firebase credentials in .env
   - Check Firebase project configuration
   - Ensure service account has proper permissions

3. **File Upload Issues**
   - Check uploads directory permissions
   - Verify UPLOAD_MAX_SIZE setting
   - Check available disk space

4. **Rate Limiting Errors**
   - Adjust rate limit settings in .env
   - Check if client is making too many requests

### Debug Mode
```bash
# Enable debug logging
LOG_LEVEL=debug npm run dev

# Enable all debug logs
ENABLE_DEBUG_LOGS=true npm run dev
```

## Support

### Documentation
- **API Documentation:** See `API_DOCUMENTATION.md`
- **Implementation Guide:** See `BACKEND_IMPLEMENTATION_GUIDE.md`
- **Testing Strategy:** See `TEST_STRATEGY.md`

### Agricultural Features
- **Seasonal Detection:** Automatic season assignment based on date
- **Turkish Language:** All error messages in Turkish
- **Currency Support:** Turkish Lira formatting
- **Agricultural Categories:** Pre-defined farming expense categories

### Performance Optimization
- **Database Indexing:** Optimized queries for expense retrieval
- **Caching:** Response caching for static data
- **Compression:** Gzip compression enabled
- **Rate Limiting:** Prevents API abuse

## Next Steps

1. **Complete Implementation:** Follow the implementation guide to build all controllers and services
2. **Write Tests:** Implement the comprehensive test suite
3. **Mobile Integration:** Update mobile app to use real backend
4. **Production Deployment:** Deploy to cloud platform
5. **Monitoring Setup:** Configure application monitoring and alerts

This backend service provides a solid foundation for the Çiftçi Not Defterim mobile application with agricultural-specific features and Turkish language support.
