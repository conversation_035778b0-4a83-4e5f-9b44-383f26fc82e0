# Çiftçi Not Defterim - Misafir Modu Offline Test Senaryoları ve Migration Analizi

## 📋 Test Planı Özeti

**Amaç:** Misafir modunun tamamen offline çalışabildiğini ve hiçbir backend API isteği yapmadığını doğrulamak.

**Kapsam:** DataManager, SyncService, APIClient, AuthService iyileştirmeleri ve Guest User Migration sistemi

**Test Ortamı:**
- Android/iOS emülatör
- Network bağlantısı kapalı
- Guest mode aktif

---

## 🚨 Guest User Migration Sistemi - Kritik Bulgular

### Executive Summary

Guest user data migration sisteminin kapsamlı analizinde **8 kritik sorun** ve **12 yüksek riskli alan** tespit edilmiştir. Migration sistemi mevcut haliyle **%60 data kaybı riski** taşımaktadır ve acil müdahale gerektirmektedir.

### Kritik Bulgular
- ❌ **MongoDB schema validation uyumsuzluğu** - Season ID format çelişkisi
- ❌ **Güvenli olmayan migration sırası** - Data loss riski
- ❌ **Eksik rollback mekanizması** - Recovery imkansızlığı
- ❌ **Yetersiz validation** - Backend-frontend uyumsuzluğu
- ⚠️ **Test coverage eksikliği** - Migration endpoint'leri test edilmemiş

### Etki Analizi
- **Kullanıcı Deneyimi:** Kritik - Migration başarısızlığında tüm data kaybı
- **Sistem Kararlılığı:** Yüksek Risk - Validation hataları sistem çökmesine neden olabilir
- **Data Güvenliği:** Kritik Risk - Rollback mekanizması eksikliği

---

## 🧪 Test Senaryoları

### 1. **Guest Mode Initialization Tests**

#### Test 1.1: Guest Mode Activation
**Amaç:** Misafir moduna geçişin doğru çalıştığını test et
**Adımlar:**
1. Uygulamayı başlat
2. "Verileri burada tut" seçeneğini seç
3. AuthService.isInGuestMode() = true olduğunu kontrol et
4. DataManager.isGuestMode() = true olduğunu kontrol et

**Beklenen Sonuç:**
- ✅ Guest mode aktif
- ✅ Backend istekleri yapılmıyor
- ✅ Local storage kullanılıyor

#### Test 1.2: Service Initialization in Guest Mode
**Amaç:** Servislerin guest mode'da doğru initialize olduğunu test et
**Adımlar:**
1. Guest mode'da uygulama başlat
2. DataManager.initialize() çağır
3. SyncService.initialize() çağır
4. APIClient.initialize() çağır

**Beklenen Sonuç:**
- ✅ DataManager: Local data yüklendi
- ✅ SyncService: Initialization skipped
- ✅ APIClient: Initialization skipped

### 2. **Expense Management Tests**

#### Test 2.1: Add Expense in Guest Mode
**Amaç:** Gider eklemenin tamamen offline çalıştığını test et
**Adımlar:**
1. Guest mode aktif
2. Network bağlantısını kapat
3. Yeni gider ekle (kategori: Gübre, miktar: 500 TL)
4. Network trafiğini monitor et

**Beklenen Sonuç:**
- ✅ Gider başarıyla eklendi
- ✅ Local storage'a kaydedildi
- ✅ Hiçbir API isteği yapılmadı
- ✅ syncStatus = 'local'

#### Test 2.2: Get Expenses in Guest Mode
**Amaç:** Gider listesinin local storage'dan geldiğini test et
**Adımlar:**
1. Guest mode'da birkaç gider ekle
2. Network bağlantısını kapat
3. Gider listesini getir
4. Network trafiğini monitor et

**Beklenen Sonuç:**
- ✅ Tüm giderler listelendi
- ✅ Local storage'dan geldi
- ✅ Hiçbir API isteği yapılmadı

#### Test 2.3: Update Expense in Guest Mode
**Adımlar:**
1. Guest mode'da gider ekle
2. Network bağlantısını kapat
3. Gideri güncelle
4. Network trafiğini monitor et

**Beklenen Sonuç:**
- ✅ Gider başarıyla güncellendi
- ✅ Local storage'da güncellendi
- ✅ Hiçbir API isteği yapılmadı

#### Test 2.4: Delete Expense in Guest Mode
**Adımlar:**
1. Guest mode'da gider ekle
2. Network bağlantısını kapat
3. Gideri sil
4. Network trafiğini monitor et

**Beklenen Sonuç:**
- ✅ Gider başarıyla silindi
- ✅ Local storage'dan silindi
- ✅ Hiçbir API isteği yapılmadı

### 3. **Category Management Tests**

#### Test 3.1: Get Categories in Guest Mode
**Adımlar:**
1. Guest mode aktif
2. Network bağlantısını kapat
3. Kategori listesini getir

**Beklenen Sonuç:**
- ✅ Default kategoriler listelendi
- ✅ Local storage'dan geldi
- ✅ Hiçbir API isteği yapılmadı

### 4. **Field Management Tests**

#### Test 4.1: Add Field in Guest Mode
**Adımlar:**
1. Guest mode aktif
2. Network bağlantısını kapat
3. Yeni tarla ekle

**Beklenen Sonuç:**
- ✅ Tarla başarıyla eklendi
- ✅ Local storage'a kaydedildi
- ✅ Hiçbir API isteği yapılmadı

#### Test 4.2: Field Operations in Guest Mode
**Adımlar:**
1. Guest mode'da tarla ekle
2. Tarlayı güncelle
3. Tarlayı sil (default değilse)

**Beklenen Sonuç:**
- ✅ Tüm operasyonlar local çalıştı
- ✅ Hiçbir API isteği yapılmadı

### 5. **Sync Service Tests**

#### Test 5.1: Sync Queue Bypass in Guest Mode
**Adımlar:**
1. Guest mode aktif
2. SyncService.addToSyncQueue() çağır
3. SyncService.startSync() çağır

**Beklenen Sonuç:**
- ✅ Sync queue'ya ekleme yapılmadı
- ✅ Sync process başlatılmadı
- ✅ Guest mode log mesajları görüldü

#### Test 5.2: Force Sync in Guest Mode
**Adımlar:**
1. Guest mode aktif
2. SyncService.forceSync() çağır

**Beklenen Sonuç:**
- ✅ Sync başlatılmadı
- ✅ Guest mode log mesajı görüldü

### 6. **API Client Tests**

#### Test 6.1: API Request Blocking in Guest Mode
**Adımlar:**
1. Guest mode aktif
2. APIClient.makeRequest('/expenses') çağır

**Beklenen Sonuç:**
- ✅ Request bloklandı
- ✅ "API requests not available in guest mode" hatası
- ✅ Hiçbir network isteği yapılmadı

### 7. **Integration Tests**

#### Test 7.1: Complete Offline Workflow
**Adımlar:**
1. Guest mode'da uygulama başlat
2. Network bağlantısını tamamen kapat
3. Kategori listesini görüntüle
4. Yeni gider ekle
5. Gider listesini görüntüle
6. Gideri düzenle
7. Gideri sil
8. Tarla ekle
9. Tarla düzenle

**Beklenen Sonuç:**
- ✅ Tüm işlemler başarıyla tamamlandı
- ✅ Hiçbir network hatası oluşmadı
- ✅ Tüm veriler local storage'da

#### Test 7.2: Data Isolation Test
**Adımlar:**
1. Guest mode'da veri ekle
2. Google ile giriş yap
3. Guest verilerinin authenticated user'a karışmadığını kontrol et

**Beklenen Sonuç:**
- ✅ Guest verileri ayrı storage'da
- ✅ Authenticated user verileri ayrı storage'da
- ✅ Data leakage yok

### 8. **Error Handling Tests**

#### Test 8.1: Network Error Resilience
**Adımlar:**
1. Guest mode aktif
2. Network bağlantısını kapat
3. Tüm CRUD operasyonları yap
4. Network hatası oluşup oluşmadığını kontrol et

**Beklenen Sonuç:**
- ✅ Hiçbir network hatası oluşmadı
- ✅ Tüm operasyonlar local çalıştı

---

## 🔍 Test Execution Checklist

### Pre-Test Setup
- [ ] Guest mode aktif
- [ ] Network monitoring tool hazır
- [ ] Console log monitoring aktif
- [ ] Local storage inspector hazır

### During Test
- [ ] Network trafiği monitor ediliyor
- [ ] Console log'ları kontrol ediliyor
- [ ] Error handling test ediliyor
- [ ] Performance ölçülüyor

### Post-Test Validation
- [ ] Hiçbir API isteği yapılmadı
- [ ] Tüm veriler local storage'da
- [ ] Guest mode log mesajları görüldü
- [ ] Data isolation korundu

---

## 📊 Success Criteria

**✅ PASS Kriterleri:**
1. Guest mode'da hiçbir backend API isteği yapılmıyor
2. Tüm CRUD operasyonları local storage'da çalışıyor
3. Sync service guest mode'da devre dışı
4. API client guest mode'da request'leri blokluyor
5. Data isolation korunuyor
6. Network hatası oluşmuyor
7. Performance kabul edilebilir seviyede

**❌ FAIL Kriterleri:**
1. Guest mode'da API isteği yapılıyor
2. Network hatası oluşuyor
3. Data leakage var
4. Sync service guest mode'da çalışıyor
5. Performance kabul edilemez

---

## 🚨 Tespit Edilen Migration Sorunları

### 1. MongoDB Schema Validation Uyumsuzluğu
**Risk Seviyesi:** 🔴 Kritik

**Sorun Açıklaması:**
MongoDB collection validator hala eski enum season ID'leri bekliyor, ancak yeni sistem ObjectId kullanıyor.

```javascript
// backend/scripts/init-mongo.js - HATALI SCHEMA
seasonId: {
  enum: ['spring', 'summer', 'autumn', 'winter'], // ❌ Eski format
  description: 'Season must be one of the enum values'
}

// backend/src/models/Expense.js - YENİ FORMAT
seasonId: {
  type: mongoose.Schema.Types.ObjectId, // ✅ Yeni format
  ref: 'Season',
  required: true
}
```

**Etki Analizi:**
- Migration sırasında tüm expense creation'lar başarısız olur
- Kullanıcı data'sı tamamen kaybolur
- Backend validation error'ları

### 2. Güvenli Olmayan Migration Sırası
**Risk Seviyesi:** 🔴 Kritik

**Sorun Açıklaması:**
Migration sürecinde önce backend data siliniyor, sonra yeni data ekleniyor. Herhangi bir adımda hata olursa data kaybı oluşur.

```javascript
// src/services/DataManager.js - RİSKLİ SIRALAMA
// STEP 2: Delete ALL existing user data from backend ❌
const deleteResponse = await this.apiClient.makeRequest('/users/clear-data', {
  method: 'DELETE'
});

// STEP 3: Create season ❌ (Başarısız olursa data kaybolur)
const response = await this.apiClient.makeRequest('/seasons', {
  method: 'POST',
  body: JSON.stringify(seasonData)
});
```

### 3. Eksik Rollback Mekanizması
**Risk Seviyesi:** 🔴 Kritik

**Sorun Açıklaması:**
Migration başarısız olduğunda guest data'yı geri yükleme mekanizması yok.

```javascript
// src/services/DataManager.js - EKSİK ERROR HANDLING
} catch (error) {
  console.error('Migration failed:', error);
  // ❌ Rollback mekanizması yok
  // ❌ Guest data restore edilmiyor
  // ❌ User bilgilendirilmiyor
}
```

## 💡 Çözüm Önerileri

### 1. MongoDB Schema Düzeltme
```javascript
// backend/scripts/init-mongo.js - DÜZELTME
db.createCollection('expenses', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['userId', 'categoryId', 'amount', 'date', 'seasonId'],
      properties: {
        seasonId: {
          bsonType: 'objectId', // ✅ ObjectId format
          description: 'Season ObjectId is required'
        }
      }
    }
  }
});
```

### 2. Güvenli Migration Sırası
```javascript
// Önerilen güvenli sıralama:
async safeMigrateGuestData() {
  // 1. Create backup
  const backup = await this.createMigrationBackup();

  // 2. Validate all data
  const validation = await this.validateAllGuestData();
  if (!validation.isValid) throw new Error(validation.errors);

  // 3. Create season first
  const season = await this.createSeasonForMigration();

  // 4. Migrate data
  const migrationResult = await this.migrateDataWithSeason(season.id);

  // 5. Only if successful, clear backend data
  if (migrationResult.success) {
    await this.clearBackendData();
    await this.clearGuestData();
  } else {
    await this.rollbackMigration(backup);
  }
}
```

### 3. Rollback Mekanizması
```javascript
// src/services/DataManager.js - YENİ ROLLBACK SİSTEMİ
class MigrationBackupService {
  async createBackup() {
    const backup = {
      guestExpenses: await AsyncStorage.getItem('expenses_guest'),
      guestFields: await AsyncStorage.getItem('fields_guest'),
      guestCategories: await AsyncStorage.getItem('categories_guest'),
      timestamp: new Date().toISOString(),
      backupId: this.generateBackupId()
    };

    await AsyncStorage.setItem('migration_backup', JSON.stringify(backup));
    return backup;
  }

  async rollback(backupId) {
    const backup = await AsyncStorage.getItem('migration_backup');
    if (backup && backup.backupId === backupId) {
      // Restore guest data
      await AsyncStorage.setItem('expenses_guest', backup.guestExpenses);
      await AsyncStorage.setItem('fields_guest', backup.guestFields);
      await AsyncStorage.setItem('categories_guest', backup.guestCategories);

      // Clean up failed migration data
      await this.cleanupFailedMigration();

      return { success: true, message: 'Data restored successfully' };
    }
    throw new Error('Backup not found or invalid');
  }
}
```

---

## 🐛 Known Issues & Workarounds

### Migration Issues
- **Issue:** MongoDB schema validation conflicts
- **Workaround:** Update schema before migration
- **Status:** Critical - needs immediate fix

### Performance Issues
- **Issue:** Large dataset migration takes too long
- **Workaround:** Implement batch processing
- **Status:** High priority

---

## 📝 Test Execution Log

*Test sonuçları burada dokumentlanacak*

| Test ID | Test Name | Status | Notes |
|---------|-----------|--------|-------|
| 1.1 | Guest Mode Activation | ⏳ | Pending |
| 1.2 | Service Initialization | ⏳ | Pending |
| 2.1 | Add Expense | ⏳ | Pending |
| MIG.1 | MongoDB Schema Validation | ❌ | Critical issue found |
| MIG.2 | Migration Rollback | ❌ | Not implemented |
| MIG.3 | Data Backup | ❌ | Missing functionality |
| ... | ... | ... | ... |
