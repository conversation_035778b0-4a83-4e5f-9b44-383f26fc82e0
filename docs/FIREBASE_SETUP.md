# 🔥 Firebase & Google Sign-In Setup Guide

PC emülatörlerinde Google Sign-In'i test etmek için aşağıdaki adımları takip edin.

## 📋 Gereksinimler

1. **Firebase Console** hesabı
2. **Google Cloud Console** erişimi
3. **EAS CLI** kurulu olması
4. **Android Studio** (Android emülatör için)
5. **Xcode** (iOS simulator için - sadece macOS)

## 🚀 Kurulum Adımları

### 1. Firebase Projesi Oluşturma

1. [Firebase Console](https://console.firebase.google.com/)'a gidin
2. "Add project" ile yeni proje oluşturun
3. Proje adı: `ciftci-not-defterim-test`
4. Analytics'i etkinleştirin (isteğe bağlı)

### 2. Firebase Authentication Kurulumu

1. Firebase Console'da **Authentication** > **Sign-in method**'a gidin
2. **Google** provider'ını etkinleştirin
3. **Project support email** ayarlayın

### 3. Android App Ekleme

1. Firebase Console'da **Project Overview** > **Add app** > **Android**
2. **Package name**: `com.ciftcinotdefterim.app`
3. **App nickname**: `Çiftçi Not Defterim Android`
4. **google-services.json** dosyasını indirin
5. İndirilen dosyayı `android/google-services.json` olarak kaydedin

### 4. iOS App Ekleme

1. Firebase Console'da **Add app** > **iOS**
2. **Bundle ID**: `com.ciftcinotdefterim.app`
3. **App nickname**: `Çiftçi Not Defterim iOS`
4. **GoogleService-Info.plist** dosyasını indirin
5. İndirilen dosyayı `ios/GoogleService-Info.plist` olarak kaydedin

### 5. Google Cloud Console Konfigürasyonu

1. [Google Cloud Console](https://console.cloud.google.com/)'a gidin
2. Firebase projenizi seçin
3. **APIs & Services** > **Credentials**'a gidin
4. **Web client** ID'sini kopyalayın
5. `src/config/firebase.js`'te `webClientId` değerini güncelleyin

### 6. Firebase Config Güncelleme

`src/config/firebase.js` dosyasındaki değerleri Firebase Console'dan alın:

```javascript
export const firebaseConfig = {
  apiKey: "YOUR_API_KEY",
  authDomain: "YOUR_PROJECT_ID.firebaseapp.com",
  projectId: "YOUR_PROJECT_ID",
  storageBucket: "YOUR_PROJECT_ID.appspot.com",
  messagingSenderId: "YOUR_SENDER_ID",
  appId: "YOUR_APP_ID",
  measurementId: "YOUR_MEASUREMENT_ID"
};

export const googleSignInConfig = {
  webClientId: "YOUR_WEB_CLIENT_ID.apps.googleusercontent.com",
  offlineAccess: true,
  hostedDomain: '',
  forceCodeForRefreshToken: true,
};
```

## 🛠️ Development Build Oluşturma

### EAS CLI Kurulumu
```bash
npm install -g @expo/eas-cli
eas login
```

### Android Development Build
```bash
npm run build:dev:android
```

### iOS Development Build (sadece macOS)
```bash
npm run build:dev:ios
```

### Build'i Emülatöre Yükleme

**Android:**
```bash
# Build tamamlandıktan sonra
npm run install:dev:android
```

**iOS:**
```bash
# Build tamamlandıktan sonra
npm run install:dev:ios
```

## 🧪 Test Etme

1. **Development build** yüklendikten sonra
2. **Expo Dev Client** açılacak
3. **Development server**'ı başlatın: `npm start`
4. **Scan QR code** ile bağlanın
5. **AuthChoice** ekranında **Google ile Giriş Yap**'ı test edin

## 🔧 Troubleshooting

### Google Sign-In Hatası
- Web Client ID'nin doğru olduğundan emin olun
- SHA-1 fingerprint'i Android app'e ekleyin
- Bundle ID/Package name'lerin eşleştiğinden emin olun

### Build Hatası
- Firebase config dosyalarının doğru konumda olduğundan emin olun
- EAS CLI'nin güncel olduğundan emin olun

### Emülatör Bağlantı Sorunu
- Emülatörün internet bağlantısı olduğundan emin olun
- Google Play Services'in yüklü olduğundan emin olun (Android)

## 📱 Test Senaryoları

1. **İlk Giriş**: AuthChoice ekranı görünmeli
2. **Google Sign-In**: Google hesap seçimi açılmalı
3. **Başarılı Giriş**: Ana uygulamaya yönlendirmeli
4. **Tekrar Açma**: Direkt ana uygulamaya gitmeli
5. **Çıkış Yapma**: Tekrar AuthChoice ekranına dönmeli

## 🎯 Sonraki Adımlar

1. **Production Build** için ayrı Firebase projesi oluşturun
2. **App Store/Play Store** için release build'ler hazırlayın
3. **Analytics** ve **Crashlytics** entegrasyonu ekleyin
