# 🚀 Çiftçi Not Defterim Production Deployment Rehberi

## 📋 <PERSON><PERSON>k Kontrol Listesi

### <PERSON><PERSON><PERSON><PERSON>mler
- [ ] Linux server (Ubuntu 20.04+ önerilen)
- [ ] Domain adı (örn: ciftcinotdefterim.com)
- [ ] GitHub repository erişimi
- [ ] Firebase projesi hazır
- [ ] Server'da Docker ve Docker Compose kurulu

---

## 🏗️ ADIM 1: Server Hazırlığı

### 1.1 Server Bağlantısı
```bash
# Server'a SSH ile bağlan
ssh root@YOUR_SERVER_IP
```

### 1.2 Sistem Güncellemesi
```bash
# Sistem paketlerini güncelle
apt update && apt upgrade -y

# Gerekli paketleri kur
apt install -y curl wget git nano htop
```

### 1.3 Docker Kurulumu
```bash
# Docker kurulum scripti çalıştır
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# Docker Compose kur
curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# Docker servisini başlat
systemctl enable docker
systemctl start docker
```

### 1.4 Proje Dizini Oluştur
```bash
# Ana proje dizini
mkdir -p /opt/ciftci-not-defterim
cd /opt/ciftci-not-defterim

# Git repository'yi klonla
git clone https://github.com/YOUR_USERNAME/ciftci-not-defterim.git .
```

---

## 🌐 ADIM 2: Domain ve DNS Ayarları

### 2.1 Domain Satın Al
- **Önerilen sağlayıcılar**: Namecheap, GoDaddy, Turhost
- **Domain önerileri**: ciftcinotdefterim.com

### 2.2 DNS Kayıtları Ekle
Domain sağlayıcınızın DNS panelinde:
```
Type    Host    Value               TTL
A       @       YOUR_SERVER_IP      300
A       api     YOUR_SERVER_IP      300
CNAME   www     ciftcinotdefterim.com   300
```

### 2.3 DNS Propagation Kontrol
```bash
# DNS'in yayılmasını kontrol et (24 saat sürebilir)
dig api.ciftcinotdefterim.com
nslookup api.ciftcinotdefterim.com
```

---

## 🔐 ADIM 3: Environment Variables Ayarları

### 3.1 Production Environment Dosyası Oluştur
```bash
# Backend dizininde .env dosyası oluştur
cd /opt/ciftci-not-defterim/backend
cp .env.example .env
nano .env
```

### 3.2 Gerekli Değişkenleri Doldur
```env
# Server
NODE_ENV=production
PORT=3000

# Database
MONGODB_URI=**********************************************************************************************

# Firebase (Firebase Console'dan al)
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>

# Security
JWT_SECRET=GENERATE_STRONG_256_BIT_KEY
BCRYPT_ROUNDS=12
CORS_ORIGIN=https://api.ciftcinotdefterim.com

# Rate Limiting
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100
```

### 3.3 Docker Compose Environment
```bash
# Ana dizinde .env dosyası oluştur
cd /opt/ciftci-not-defterim
nano .env
```

```env
# MongoDB
MONGO_ROOT_USERNAME=admin
MONGO_ROOT_PASSWORD=STRONG_ADMIN_PASSWORD
MONGO_USERNAME=ciftci-user
MONGO_PASSWORD=STRONG_USER_PASSWORD
MONGO_DATABASE=ciftci-notebook

# Redis
REDIS_PASSWORD=STRONG_REDIS_PASSWORD

# GitHub
GITHUB_REPOSITORY_OWNER=YOUR_GITHUB_USERNAME
```

---

## 🐳 ADIM 4: Docker Production Setup

### 4.1 Production Docker Compose Çalıştır
```bash
cd /opt/ciftci-not-defterim

# Production modunda başlat
docker-compose -f docker-compose.prod.yml up -d

# Logları kontrol et
docker-compose -f docker-compose.prod.yml logs -f
```

### 4.2 Servislerin Durumunu Kontrol Et
```bash
# Container'ların durumu
docker ps

# Sağlık kontrolü
curl http://localhost:3000/health
```

---

## 🔒 ADIM 5: SSL Sertifikası (Let's Encrypt)

### 5.1 Certbot Kur
```bash
# Snapd kur
apt install snapd

# Certbot kur
snap install core; snap refresh core
snap install --classic certbot
ln -s /snap/bin/certbot /usr/bin/certbot
```

### 5.2 SSL Sertifikası Al
```bash
# Nginx'i geçici olarak durdur
docker-compose -f docker-compose.prod.yml stop nginx

# Sertifika al
certbot certonly --standalone \
  --email <EMAIL> \
  --agree-tos \
  --domains api.ciftcinotdefterim.com

# Nginx'i tekrar başlat
docker-compose -f docker-compose.prod.yml start nginx
```

### 5.3 Otomatik Yenileme Ayarla
```bash
# Cron job ekle
crontab -e

# Bu satırı ekle:
0 2,14 * * * certbot renew --quiet && docker-compose -f /opt/ciftci-not-defterim/docker-compose.prod.yml exec nginx nginx -s reload
```

---

## ⚙️ ADIM 6: GitHub Actions CI/CD

### 6.1 GitHub Secrets Ekle
GitHub repository > Settings > Secrets and variables > Actions:

```
PRODUCTION_HOST=YOUR_SERVER_IP
PRODUCTION_USER=root
PRODUCTION_SSH_KEY=YOUR_PRIVATE_SSH_KEY
PRODUCTION_PORT=22
```

### 6.2 SSH Key Oluştur (Eğer yoksa)
```bash
# Local makinende
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"

# Public key'i server'a ekle
ssh-copy-id -i ~/.ssh/id_rsa.pub root@YOUR_SERVER_IP
```

### 6.3 Deployment Test Et
```bash
# GitHub'a push yap
git add .
git commit -m "Production deployment setup"
git push origin main

# GitHub Actions'da deployment'ı izle
```

---

## 📱 ADIM 7: Mobile App API URL Güncelleme

### 7.1 APIClient.js Dosyasını Güncelle
```javascript
// src/services/APIClient.js
const API_BASE_URL = __DEV__ 
  ? 'http://localhost:3000/api/v1' 
  : 'https://api.ciftcinotdefterim.com/api/v1';
```

### 7.2 App Build ve Test
```bash
# Development'da test et
npm start

# Production build test et
eas build --platform android --profile production
```

---

## 🔍 ADIM 8: Monitoring ve Test

### 8.1 API Endpoint'lerini Test Et
```bash
# Health check
curl https://api.ciftcinotdefterim.com/health

# API test
curl -X POST https://api.ciftcinotdefterim.com/api/v1/auth/validate \
  -H "Content-Type: application/json" \
  -d '{"firebaseToken":"test"}'
```

### 8.2 SSL Test
```bash
# SSL sertifikası kontrol
openssl s_client -connect api.ciftcinotdefterim.com:443 -servername api.ciftcinotdefterim.com

# Online SSL test: https://www.ssllabs.com/ssltest/
```

### 8.3 Performance Test
```bash
# Load test (opsiyonel)
apt install apache2-utils
ab -n 100 -c 10 https://api.ciftcinotdefterim.com/health
```

---

## 🛡️ ADIM 9: Güvenlik Sıkılaştırma

### 9.1 Firewall Ayarları
```bash
# UFW firewall kur ve ayarla
ufw allow ssh
ufw allow 80
ufw allow 443
ufw enable
```

### 9.2 Fail2Ban Kur
```bash
# Brute force saldırılarına karşı koruma
apt install fail2ban
systemctl enable fail2ban
systemctl start fail2ban
```

### 9.3 Otomatik Güvenlik Güncellemeleri
```bash
# Unattended upgrades kur
apt install unattended-upgrades
dpkg-reconfigure -plow unattended-upgrades
```

---

## 📊 ADIM 10: Backup ve Monitoring

### 10.1 Database Backup Script
```bash
# Backup scripti oluştur
nano /opt/ciftci-not-defterim/scripts/backup.sh

# Cron job ekle
0 3 * * * /opt/ciftci-not-defterim/scripts/backup.sh
```

### 10.2 Log Monitoring
```bash
# Logları kontrol et
docker-compose -f docker-compose.prod.yml logs backend
tail -f /var/log/nginx/access.log
```

---

## 🔧 ADIM 11: Önemli Komutlar ve Troubleshooting

### 11.1 Sık Kullanılan Komutlar
```bash
# Servisleri yeniden başlat
docker-compose -f docker-compose.prod.yml restart

# Logları izle
docker-compose -f docker-compose.prod.yml logs -f backend

# Database'e bağlan
docker-compose -f docker-compose.prod.yml exec mongodb mongosh -u admin -p

# SSL sertifikası yenile (manuel)
certbot renew --force-renewal

# Nginx konfigürasyonu test et
docker-compose -f docker-compose.prod.yml exec nginx nginx -t

# Disk kullanımı kontrol et
df -h
docker system df
```

### 11.2 Yaygın Sorunlar ve Çözümleri

**Problem: API'ye erişilemiyor**
```bash
# Çözüm adımları:
1. curl http://localhost:3000/health  # Local erişim test et
2. docker ps  # Container'lar çalışıyor mu?
3. docker-compose logs backend  # Backend loglarını kontrol et
4. ufw status  # Firewall ayarları
```

**Problem: SSL sertifikası hatası**
```bash
# Çözüm:
1. certbot certificates  # Sertifika durumu
2. nginx -t  # Nginx config test
3. systemctl status nginx  # Nginx durumu
```

**Problem: Database bağlantı hatası**
```bash
# Çözüm:
1. docker-compose logs mongodb  # MongoDB logları
2. docker exec -it mongodb mongosh  # Database'e bağlan
3. .env dosyasındaki MONGODB_URI kontrol et
```

---

## 📋 Maintenance Checklist (Aylık)

### Güvenlik Güncellemeleri
- [ ] `apt update && apt upgrade`
- [ ] Docker image'ları güncelle
- [ ] SSL sertifikası durumu kontrol et
- [ ] Backup'ların çalıştığını kontrol et

### Performance Monitoring
- [ ] Disk kullanımı kontrol et
- [ ] Memory kullanımı kontrol et
- [ ] API response time'ları kontrol et
- [ ] Error log'ları incele

### Database Maintenance
- [ ] Database boyutu kontrol et
- [ ] Index performance kontrol et
- [ ] Backup restore test et

---

## 🆘 Acil Durum Prosedürleri

### Server Çöktüğünde
```bash
# 1. Server'a bağlan
ssh root@YOUR_SERVER_IP

# 2. Servisleri kontrol et
docker ps -a

# 3. Servisleri yeniden başlat
cd /opt/ciftci-not-defterim
docker-compose -f docker-compose.prod.yml up -d

# 4. Logları kontrol et
docker-compose logs --tail=100
```

### Database Restore
```bash
# Backup'tan restore et
docker-compose exec mongodb mongorestore --drop /backup/latest/
```

### SSL Sertifikası Acil Yenileme
```bash
# Manuel yenileme
certbot renew --force-renewal
docker-compose exec nginx nginx -s reload
```

---

## ✅ Deployment Tamamlandı!

### Son Kontroller
- [ ] API health check çalışıyor: `curl https://api.ciftcinotdefterim.com/health`
- [ ] HTTPS düzgün çalışıyor: SSL Labs test
- [ ] Mobile app production API'ye bağlanıyor
- [ ] SSL sertifikası otomatik yenileniyor: `certbot certificates`
- [ ] GitHub Actions deployment çalışıyor
- [ ] Backup sistemi kurulu ve test edildi
- [ ] Monitoring aktif ve alertler çalışıyor
- [ ] Firewall ayarları doğru: `ufw status`
- [ ] DNS kayıtları doğru: `dig api.ciftcinotdefterim.com`

### İletişim Bilgileri
- **API URL**: https://api.ciftcinotdefterim.com
- **Health Check**: https://api.ciftcinotdefterim.com/health
- **SSL Test**: https://www.ssllabs.com/ssltest/

### Sonraki Adımlar
1. **Monitoring Dashboard** kurulumu (Grafana/Prometheus)
2. **Error Tracking** (Sentry) entegrasyonu
3. **CDN** kurulumu (Cloudflare)
4. **Load Balancer** kurulumu (birden fazla server için)

**🎉 Tebrikler! Production deployment'ınız hazır ve güvenli!**

---

## 📞 Destek

Bu rehberde herhangi bir adımda takılırsanız:
1. İlgili log dosyalarını kontrol edin
2. GitHub Issues'da sorun bildirin
3. Dokümantasyonu tekrar gözden geçirin
4. Community forumlarından yardım alın

**Başarılı deployment'lar dileriz! 🚀**
