# Çiftçi Not Defterim - Kullanıcı Senaryoları ve Navigasyon Analizi

## 🚨 Tespit Edilen Teknik Sorunlar

### Ana Sorun: Yanlış App.js Dosyası Çalışıyor

**Kritik Durum:**
1. **index.js** dosyası `App.js`'i import ediyor (satır 3)
2. **App.js** dosyası basit bir demo ekranı - navigasyon yok
3. **src/navigation/AppNavigator.js** dosyası hiç kullanılmıyor
4. Tüm gelişmiş screen'ler (HomeScreen, ExpensesScreen, etc.) hiç çalışmıyor

### Navigasyon Sorunları Detayı:

**Şu Anda Çalışan Uygulama (App.js):**
```javascript
// Sadece Alert gösteriyor - gerçek navigasyon yok
handleAddExpense() → Alert: "Gider ekleme ekranı yakında eklenecek!"
handleViewExpenses() → Alert: "Gider listesi ekranı yakında eklenecek!"
handleViewReports() → Alert: "Raporlar ekranı yakında eklenecek!"
```

**Sorunlu Navigasyon Çağrıları:**
- HomeScreen.js: `navigation.navigate('AddExpense')` ❌ (Tanımsız ekran)
- ExpensesScreen.js: `navigation.navigate('AddExpense')` ❌ (Tanımsız ekran)
- CategorySelectionScreen.js: `navigation.navigate('CategoryCreation')` ❌ (Tanımsız ekran)

### Çözüm Önerileri:

**1. Acil Düzeltme - index.js Değişikliği:**
```javascript
// Mevcut:
import App from './App';

// Olması gereken:
import App from './src/navigation/AppNavigator';
```

**2. AppNavigator.js'e Eksik Ekranları Ekleme:**
```javascript
// Eksik ekranlar:
- AddExpense → EnhancedExpenseCreationScreen.js
- CategorySelection → CategorySelectionScreen.js
- CategoryCreation → CategoryCreationScreen.js
- ExpenseEdit → ExpenseEditScreen.js
```

**3. Öncelik Sırası:**
1. **YÜKSEK:** index.js'i düzelt (5 dakika)
2. **YÜKSEK:** AddExpense ekranını Navigator'a ekle (15 dakika)
3. **ORTA:** Diğer eksik ekranları ekle (30 dakika)
4. **DÜŞÜK:** Settings özelliklerini tamamla (2-3 saat)

---

## 🍇 Üzüm Bağı Çiftçisi Kullanıcı Senaryoları

### Çiftçi Profili: Mehmet Bey
- **Yaş:** 45
- **Lokasyon:** Manisa, Ege Bölgesi
- **Arazi:** 10 dönüm üzüm bağı
- **Deneyim:** 20 yıllık çiftçi
- **Teknoloji Kullanımı:** Orta seviye (akıllı telefon kullanıyor)

---

## 📅 Yıllık İş Akışı ve Uygulama Kullanımı

### 1. KIŞ DÖNEMİ (Aralık - Şubat)
**Dönem Karakteristiği:** Planlama, Bakım ve Hazırlık

#### Senaryo 1.1: Yıllık Planlama (Ocak)
**Durum:** Mehmet Bey yeni yıl için bütçe planlıyor
**Uygulama Kullanımı:**
1. **Raporlar** sekmesine girer
2. Geçen yılın toplam giderlerini inceler
3. Kategori bazında harcama analizini görür
4. Bu yılın bütçesini planlar
5. **Ayarlar**'dan hedef bütçe belirler

#### Senaryo 1.2: Makine Bakımı (Şubat)
**Durum:** Traktör ve sulama sisteminin bakımını yaptırıyor
**Uygulama Kullanımı:**
1. **Gider Ekle** butonuna basar
2. **Kategori:** Ekipman seçer
3. **Tutar:** 2.500 TL girer
4. **Açıklama:** "Traktör yıllık bakımı - motor yağı, filtre değişimi"
5. **Tarih:** Bakım tarihini seçer
6. Gideri kaydeder

### 2. İLKBAHAR DÖNEMİ (Mart - Mayıs)
**Dönem Karakteristiği:** Budama, Toprak Hazırlığı, İlk Bakım

#### Senaryo 2.1: Budama İşçiliği (Mart)
**Durum:** Asmaların budanması için işçi tutuyor
**Uygulama Kullanımı:**
1. **Ana Sayfa**'dan "Gider Ekle" seçer
2. **Kategori:** İşçilik
3. **Tutar:** 1.800 TL
4. **Açıklama:** "Budama işçiliği - 3 işçi, 2 gün"
5. Gideri kaydeder
6. **Ana Sayfa**'da bu ayın toplam giderini kontrol eder

#### Senaryo 2.2: Gübre Alımı (Nisan)
**Durum:** İlkbahar gübreleme için gübre satın alıyor
**Uygulama Kullanımı:**
1. **Gider Ekle** → **Kategori:** Gübre
2. **Tutar:** 3.200 TL
3. **Açıklama:** "NPK gübresi 20-20-0, 10 çuval"
4. **Tarih:** Satın alma tarihi
5. Gideri kaydeder

#### Senaryo 2.3: Haftalık Gider Kontrolü
**Durum:** Haftanın sonunda giderlerini kontrol ediyor
**Uygulama Kullanımı:**
1. **Giderler** sekmesine girer
2. Bu haftaki tüm giderleri listeler
3. Kategorilere göre filtreler
4. Toplam harcamayı görür
5. Gerekirse gider detaylarını düzenler

### 3. YAZ DÖNEMİ (Haziran - Ağustos)
**Dönem Karakteristiği:** Yoğun Bakım, Sulama, İlaçlama

#### Senaryo 3.1: Günlük Sulama Gideri (Haziran)
**Durum:** Her gün sulama yapıyor, elektrik faturası geliyor
**Uygulama Kullanımı:**
1. **Gider Ekle** → **Kategori:** Su
2. **Tutar:** 450 TL
3. **Açıklama:** "Aylık sulama elektrik faturası"
4. Gideri kaydeder

#### Senaryo 3.2: İlaçlama (Temmuz)
**Durum:** Hastalık önleme için ilaçlama yapıyor
**Uygulama Kullanımı:**
1. **Gider Ekle** → **Kategori:** İlaç
2. **Tutar:** 680 TL
3. **Açıklama:** "Külleme önleme ilacı + spreader"
4. Gideri kaydeder
5. **Ana Sayfa**'da bu ayın toplam giderini kontrol eder

#### Senaryo 3.3: Aylık Rapor İnceleme
**Durum:** Ay sonunda giderlerini analiz ediyor
**Uygulama Kullanımı:**
1. **Raporlar** sekmesine girer
2. Bu ayın kategori bazında giderlerini görür
3. Geçen aya göre karşılaştırma yapar
4. En çok harcama yaptığı kategorileri tespit eder
5. Gelecek ay için plan yapar

### 4. SONBAHAR DÖNEMİ (Eylül - Kasım)
**Dönem Karakteristiği:** Hasat, Satış, Kış Hazırlığı

#### Senaryo 4.1: Hasat İşçiliği (Eylül)
**Durum:** Üzüm hasadı için işçi tutuyor
**Uygulama Kullanımı:**
1. **Gider Ekle** → **Kategori:** İşçilik
2. **Tutar:** 4.500 TL
3. **Açıklama:** "Hasat işçiliği - 8 işçi, 5 gün"
4. Gideri kaydeder

#### Senaryo 4.2: Nakliye Gideri (Eylül)
**Durum:** Hasadı fabrikaya taşıyor
**Uygulama Kullanımı:**
1. **Gider Ekle** → **Kategori:** Diğer
2. **Tutar:** 800 TL
3. **Açıklama:** "Hasat nakliye - fabrikaya taşıma"
4. Gideri kaydeder

#### Senaryo 4.3: Yıllık Değerlendirme (Kasım)
**Durum:** Yıl sonu değerlendirmesi yapıyor
**Uygulama Kullanımı:**
1. **Raporlar** → Yıllık rapor görür
2. Toplam giderleri inceler
3. En çok harcama yaptığı kategorileri tespit eder
4. Gelir-gider dengesini hesaplar
5. Gelecek yıl için notlar alır

---

## 📱 Günlük Kullanım Senaryoları

### Sabah Rutini
1. **Ana Sayfa**'yı açar
2. Dünkü giderleri kontrol eder
3. Bu ayın toplam giderini görür
4. Günlük planını yapar

### Gider Ekleme Rutini
1. Harcama yaptığında hemen uygulamayı açar
2. **Gider Ekle** butonuna basar
3. Kategori seçer (genelde aynı kategoriler)
4. Tutarı girer
5. Kısa açıklama yazar
6. Kaydeder

### Haftalık Kontrol
1. **Giderler** sekmesine girer
2. Bu haftaki giderleri filtreler
3. Beklenmedik giderleri kontrol eder
4. Gerekirse düzeltme yapar

---

## 🎯 Kullanıcı İhtiyaçları ve Beklentiler

### Temel İhtiyaçlar
- ✅ Hızlı gider ekleme
- ✅ Kategori bazında takip
- ✅ Aylık/yıllık raporlar
- ✅ Basit ve anlaşılır arayüz

### Gelişmiş İhtiyaçlar
- 📊 Sezonluk analiz
- 📈 Trend grafikleri
- 💰 Bütçe uyarıları
- 📱 Offline çalışma
- 🔄 Yedekleme

### Kullanıcı Deneyimi Beklentileri
- **Hız:** 3 dokunuşta gider ekleme
- **Basitlik:** Karmaşık menüler olmasın
- **Güvenilirlik:** Veriler kaybolmasın
- **Erişilebilirlik:** Her yerden ulaşabilme

---

## 🔄 Tipik Kullanım Akışları

### Akış 1: Hızlı Gider Ekleme
```
Ana Sayfa → Gider Ekle → Kategori Seç → Tutar Gir → Kaydet
(Süre: 30 saniye)
```

### Akış 2: Gider Kontrolü
```
Ana Sayfa → Son Giderler → Detay Görüntüle → Düzenle (isteğe bağlı)
(Süre: 1 dakika)
```

### Akış 3: Aylık Rapor
```
Raporlar → Bu Ay → Kategori Filtresi → Detay İnceleme
(Süre: 2-3 dakika)
```

---

## 📋 Önerilen İyileştirmeler

### Navigasyon Düzeltmeleri
1. **AddExpense** ekranını AppNavigator.js'e ekle
2. EnhancedExpenseCreationScreen'i doğru şekilde bağla

### Kullanıcı Deneyimi İyileştirmeleri
1. **Hızlı Kategori Butonları:** Ana sayfada sık kullanılan kategoriler
2. **Sesli Gider Ekleme:** Tarlada kolay kullanım için
3. **Fotoğraf Ekleme:** Fiş/fatura fotoğrafı
4. **Konum Bilgisi:** Hangi tarlada harcama yapıldığı
5. **Hatırlatıcılar:** Düzenli giderler için

### Sezonluk Özellikler
1. **Sezon Bazında Dashboard:** Mevcut sezona göre öneriler
2. **Hava Durumu Entegrasyonu:** İlaçlama/sulama önerileri
3. **Takvim Entegrasyonu:** Tarımsal takvim ile senkronizasyon

Bu senaryolar, gerçek bir üzüm bağı çiftçisinin yıl boyunca uygulamayı nasıl kullanacağını detaylı bir şekilde göstermektedir.
