# Testing Strategy - Çiftçi Not Defterim Backend

## Overview

This document outlines a comprehensive testing strategy for the Çiftçi Not Defterim backend service, including unit tests, integration tests, and end-to-end testing approaches.

## Testing Framework Setup

### Dependencies
```bash
npm install -D jest supertest
npm install -D @types/jest @types/supertest # if using TypeScript
npm install -D mongodb-memory-server # for MongoDB testing
npm install -D nock # for mocking external APIs
```

### Jest Configuration
```javascript
// jest.config.js
module.exports = {
  testEnvironment: 'node',
  setupFilesAfterEnv: ['<rootDir>/tests/setup.js'],
  testMatch: [
    '<rootDir>/tests/**/*.test.js',
    '<rootDir>/tests/**/*.spec.js'
  ],
  collectCoverageFrom: [
    'src/**/*.js',
    '!src/app.js',
    '!src/server.js',
    '!src/utils/logger.js'
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  },
  testTimeout: 10000
};
```

## Test Structure

### Directory Organization
```
tests/
├── setup.js                 # Test setup and teardown
├── helpers/                 # Test helper functions
│   ├── auth.js             # Authentication helpers
│   ├── database.js         # Database helpers
│   └── fixtures.js         # Test data fixtures
├── unit/                   # Unit tests
│   ├── controllers/
│   ├── middleware/
│   ├── models/
│   ├── services/
│   └── utils/
├── integration/            # Integration tests
│   ├── auth.test.js
│   ├── expenses.test.js
│   ├── categories.test.js
│   ├── sync.test.js
│   └── upload.test.js
└── e2e/                   # End-to-end tests
    ├── user-journey.test.js
    └── sync-flow.test.js
```

## Test Setup and Configuration

### Global Test Setup
```javascript
// tests/setup.js
const { MongoMemoryServer } = require('mongodb-memory-server');
const mongoose = require('mongoose');
const { connectDatabase } = require('../src/utils/database');

let mongoServer;

beforeAll(async () => {
  // Start in-memory MongoDB instance
  mongoServer = await MongoMemoryServer.create();
  const mongoUri = mongoServer.getUri();
  
  // Override database URI for testing
  process.env.MONGODB_URI = mongoUri;
  process.env.NODE_ENV = 'test';
  
  // Connect to test database
  await connectDatabase();
});

afterAll(async () => {
  // Clean up
  await mongoose.connection.dropDatabase();
  await mongoose.connection.close();
  await mongoServer.stop();
});

beforeEach(async () => {
  // Clear all collections before each test
  const collections = mongoose.connection.collections;
  for (const key in collections) {
    await collections[key].deleteMany({});
  }
});
```

### Test Helpers
```javascript
// tests/helpers/auth.js
const firebaseService = require('../../src/services/firebaseService');
const User = require('../../src/models/User');

// Mock Firebase service for testing
jest.mock('../../src/services/firebaseService');

const createTestUser = async (userData = {}) => {
  const defaultUser = {
    firebaseUid: 'test-uid-123',
    email: '<EMAIL>',
    name: 'Test Çiftçi',
    ...userData
  };
  
  return await User.create(defaultUser);
};

const mockFirebaseToken = (user) => {
  firebaseService.verifyToken.mockResolvedValue({
    success: true,
    user: {
      uid: user.firebaseUid,
      email: user.email,
      name: user.name
    }
  });
};

const getAuthHeaders = (token = 'mock-token') => ({
  'Authorization': `Bearer ${token}`,
  'Content-Type': 'application/json'
});

module.exports = {
  createTestUser,
  mockFirebaseToken,
  getAuthHeaders
};
```

```javascript
// tests/helpers/fixtures.js
const expenseFixtures = {
  validExpense: {
    categoryId: '507f1f77bcf86cd799439011',
    amount: 1250.50,
    description: 'Organik gübre alımı',
    date: '2024-01-15'
  },
  
  invalidExpense: {
    categoryId: 'invalid-id',
    amount: -100,
    description: '',
    date: 'invalid-date'
  }
};

const categoryFixtures = {
  validCategory: {
    name: 'Test Kategori',
    emoji: '🌱',
    color: '#4CAF50',
    icon: 'fertilizer'
  },
  
  invalidCategory: {
    name: '', // Empty name
    emoji: '🌱🌿', // Multiple emojis
    color: 'invalid-color',
    icon: 'test'
  }
};

module.exports = {
  expenseFixtures,
  categoryFixtures
};
```

## Unit Tests

### Controller Tests
```javascript
// tests/unit/controllers/expenseController.test.js
const expenseController = require('../../../src/controllers/expenseController');
const Expense = require('../../../src/models/Expense');
const { expenseFixtures } = require('../../helpers/fixtures');

// Mock the Expense model
jest.mock('../../../src/models/Expense');

describe('ExpenseController', () => {
  let req, res, next;

  beforeEach(() => {
    req = {
      user: { _id: 'user123' },
      body: {},
      params: {},
      query: {}
    };
    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    };
    next = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createExpense', () => {
    it('should create expense successfully', async () => {
      const expenseData = expenseFixtures.validExpense;
      req.body = expenseData;

      const mockExpense = {
        _id: 'expense123',
        ...expenseData,
        userId: req.user._id
      };

      Expense.create.mockResolvedValue(mockExpense);

      await expenseController.createExpense(req, res, next);

      expect(Expense.create).toHaveBeenCalledWith({
        ...expenseData,
        userId: req.user._id
      });
      expect(res.status).toHaveBeenCalledWith(201);
      expect(res.json).toHaveBeenCalledWith({
        success: true,
        data: mockExpense
      });
    });

    it('should handle validation errors', async () => {
      req.body = expenseFixtures.invalidExpense;

      const validationError = new Error('Validation failed');
      validationError.name = 'ValidationError';
      Expense.create.mockRejectedValue(validationError);

      await expenseController.createExpense(req, res, next);

      expect(next).toHaveBeenCalledWith(validationError);
    });
  });

  describe('getExpenses', () => {
    it('should return paginated expenses', async () => {
      const mockExpenses = [
        { _id: 'exp1', amount: 100 },
        { _id: 'exp2', amount: 200 }
      ];

      Expense.find.mockReturnValue({
        populate: jest.fn().mockReturnValue({
          sort: jest.fn().mockReturnValue({
            limit: jest.fn().mockReturnValue({
              skip: jest.fn().mockResolvedValue(mockExpenses)
            })
          })
        })
      });

      Expense.countDocuments.mockResolvedValue(2);

      req.query = { page: 1, limit: 10 };

      await expenseController.getExpenses(req, res, next);

      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({
        success: true,
        data: {
          expenses: mockExpenses,
          pagination: expect.objectContaining({
            page: 1,
            limit: 10,
            total: 2
          })
        }
      });
    });
  });
});
```

### Service Tests
```javascript
// tests/unit/services/seasonService.test.js
const seasonService = require('../../../src/services/seasonService');

describe('SeasonService', () => {
  describe('getCurrentSeason', () => {
    it('should return spring for March', () => {
      const marchDate = new Date('2024-03-15');
      const season = seasonService.getCurrentSeason(marchDate);
      expect(season).toBe('spring');
    });

    it('should return summer for July', () => {
      const julyDate = new Date('2024-07-15');
      const season = seasonService.getCurrentSeason(julyDate);
      expect(season).toBe('summer');
    });

    it('should return autumn for October', () => {
      const octoberDate = new Date('2024-10-15');
      const season = seasonService.getCurrentSeason(octoberDate);
      expect(season).toBe('autumn');
    });

    it('should return winter for January', () => {
      const januaryDate = new Date('2024-01-15');
      const season = seasonService.getCurrentSeason(januaryDate);
      expect(season).toBe('winter');
    });
  });

  describe('getSeasonalRecommendations', () => {
    it('should return spring recommendations', () => {
      const recommendations = seasonService.getSeasonalRecommendations('spring');
      
      expect(recommendations).toHaveProperty('typicalExpenses');
      expect(recommendations.typicalExpenses).toContain('tohum');
      expect(recommendations).toHaveProperty('budgetRecommendations');
    });
  });
});
```

## Integration Tests

### API Endpoint Tests
```javascript
// tests/integration/expenses.test.js
const request = require('supertest');
const App = require('../../src/app');
const { createTestUser, mockFirebaseToken, getAuthHeaders } = require('../helpers/auth');
const { expenseFixtures } = require('../helpers/fixtures');
const Category = require('../../src/models/Category');

describe('Expenses API', () => {
  let app;
  let testUser;
  let testCategory;

  beforeAll(async () => {
    const appInstance = new App();
    app = appInstance.app;
  });

  beforeEach(async () => {
    testUser = await createTestUser();
    mockFirebaseToken(testUser);

    // Create test category
    testCategory = await Category.create({
      name: 'Test Kategori',
      emoji: '🌱',
      color: '#4CAF50',
      isDefault: true
    });
  });

  describe('POST /api/v1/expenses', () => {
    it('should create expense successfully', async () => {
      const expenseData = {
        ...expenseFixtures.validExpense,
        categoryId: testCategory._id.toString()
      };

      const response = await request(app)
        .post('/api/v1/expenses')
        .set(getAuthHeaders())
        .send(expenseData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('id');
      expect(response.body.data.amount).toBe(expenseData.amount);
      expect(response.body.data.description).toBe(expenseData.description);
    });

    it('should return 400 for invalid data', async () => {
      const response = await request(app)
        .post('/api/v1/expenses')
        .set(getAuthHeaders())
        .send(expenseFixtures.invalidExpense)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toHaveProperty('code');
    });

    it('should return 401 without authentication', async () => {
      await request(app)
        .post('/api/v1/expenses')
        .send(expenseFixtures.validExpense)
        .expect(401);
    });
  });

  describe('GET /api/v1/expenses', () => {
    beforeEach(async () => {
      // Create test expenses
      const Expense = require('../../src/models/Expense');
      await Expense.create([
        {
          userId: testUser._id,
          categoryId: testCategory._id,
          amount: 100,
          description: 'Test expense 1',
          date: new Date('2024-01-15'),
          seasonId: 'winter'
        },
        {
          userId: testUser._id,
          categoryId: testCategory._id,
          amount: 200,
          description: 'Test expense 2',
          date: new Date('2024-01-16'),
          seasonId: 'winter'
        }
      ]);
    });

    it('should return user expenses', async () => {
      const response = await request(app)
        .get('/api/v1/expenses')
        .set(getAuthHeaders())
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.expenses).toHaveLength(2);
      expect(response.body.data.pagination).toHaveProperty('total', 2);
    });

    it('should filter by date range', async () => {
      const response = await request(app)
        .get('/api/v1/expenses')
        .query({
          startDate: '2024-01-15',
          endDate: '2024-01-15'
        })
        .set(getAuthHeaders())
        .expect(200);

      expect(response.body.data.expenses).toHaveLength(1);
    });

    it('should paginate results', async () => {
      const response = await request(app)
        .get('/api/v1/expenses')
        .query({ page: 1, limit: 1 })
        .set(getAuthHeaders())
        .expect(200);

      expect(response.body.data.expenses).toHaveLength(1);
      expect(response.body.data.pagination.hasNext).toBe(true);
    });
  });
});
```

### Sync Integration Tests
```javascript
// tests/integration/sync.test.js
const request = require('supertest');
const App = require('../../src/app');
const { createTestUser, mockFirebaseToken, getAuthHeaders } = require('../helpers/auth');

describe('Sync API', () => {
  let app;
  let testUser;

  beforeAll(async () => {
    const appInstance = new App();
    app = appInstance.app;
  });

  beforeEach(async () => {
    testUser = await createTestUser();
    mockFirebaseToken(testUser);
  });

  describe('POST /api/v1/sync/push', () => {
    it('should process sync changes successfully', async () => {
      const syncData = {
        changes: [
          {
            id: 'sync_123',
            operation: 'create',
            table: 'expenses',
            localId: 'local_exp_456',
            data: {
              categoryId: '507f1f77bcf86cd799439011',
              amount: 1250.50,
              description: 'Organik gübre alımı',
              date: '2024-01-15'
            },
            timestamp: Date.now()
          }
        ],
        lastSyncTime: '2024-01-15T09:00:00Z'
      };

      const response = await request(app)
        .post('/api/v1/sync/push')
        .set(getAuthHeaders())
        .send(syncData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.results).toHaveLength(1);
      expect(response.body.data.results[0].status).toBe('success');
    });
  });

  describe('GET /api/v1/sync/pull', () => {
    it('should return changes since last sync', async () => {
      const response = await request(app)
        .get('/api/v1/sync/pull')
        .query({ since: '2024-01-15T09:00:00Z' })
        .set(getAuthHeaders())
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('changes');
      expect(response.body.data).toHaveProperty('serverTime');
    });
  });
});
```

## End-to-End Tests

### User Journey Tests
```javascript
// tests/e2e/user-journey.test.js
const request = require('supertest');
const App = require('../../src/app');
const { createTestUser, mockFirebaseToken, getAuthHeaders } = require('../helpers/auth');

describe('User Journey E2E', () => {
  let app;
  let testUser;

  beforeAll(async () => {
    const appInstance = new App();
    app = appInstance.app;
  });

  beforeEach(async () => {
    testUser = await createTestUser();
    mockFirebaseToken(testUser);
  });

  it('should complete full expense creation journey', async () => {
    // 1. Get categories
    const categoriesResponse = await request(app)
      .get('/api/v1/categories')
      .set(getAuthHeaders())
      .expect(200);

    const category = categoriesResponse.body.data[0];

    // 2. Create expense
    const expenseData = {
      categoryId: category.id,
      amount: 1250.50,
      description: 'E2E test expense',
      date: '2024-01-15'
    };

    const createResponse = await request(app)
      .post('/api/v1/expenses')
      .set(getAuthHeaders())
      .send(expenseData)
      .expect(201);

    const expenseId = createResponse.body.data.id;

    // 3. Get expenses and verify
    const expensesResponse = await request(app)
      .get('/api/v1/expenses')
      .set(getAuthHeaders())
      .expect(200);

    expect(expensesResponse.body.data.expenses).toHaveLength(1);
    expect(expensesResponse.body.data.expenses[0].id).toBe(expenseId);

    // 4. Update expense
    const updateData = {
      amount: 1500.00,
      description: 'Updated E2E test expense'
    };

    await request(app)
      .put(`/api/v1/expenses/${expenseId}`)
      .set(getAuthHeaders())
      .send(updateData)
      .expect(200);

    // 5. Verify update
    const updatedResponse = await request(app)
      .get('/api/v1/expenses')
      .set(getAuthHeaders())
      .expect(200);

    const updatedExpense = updatedResponse.body.data.expenses[0];
    expect(updatedExpense.amount).toBe(1500.00);
    expect(updatedExpense.description).toBe('Updated E2E test expense');

    // 6. Delete expense
    await request(app)
      .delete(`/api/v1/expenses/${expenseId}`)
      .set(getAuthHeaders())
      .expect(200);

    // 7. Verify deletion
    const finalResponse = await request(app)
      .get('/api/v1/expenses')
      .set(getAuthHeaders())
      .expect(200);

    expect(finalResponse.body.data.expenses).toHaveLength(0);
  });
});
```

## Test Execution Commands

### Package.json Scripts
```json
{
  "scripts": {
    "test": "jest",
    "test:unit": "jest tests/unit",
    "test:integration": "jest tests/integration",
    "test:e2e": "jest tests/e2e",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "test:ci": "jest --ci --coverage --watchAll=false"
  }
}
```

## Coverage Requirements

### Minimum Coverage Targets
- **Lines:** 80%
- **Functions:** 80%
- **Branches:** 75%
- **Statements:** 80%

### Critical Areas (95%+ Coverage Required)
- Authentication middleware
- Data validation
- Sync operations
- Error handling

## Agricultural-Specific Test Cases

### Seasonal Logic Tests
- Season detection accuracy
- Seasonal expense categorization
- Agricultural calendar integration

### Turkish Language Tests
- Error message localization
- Turkish character handling
- Number formatting (comma as decimal separator)

### Data Validation Tests
- Agricultural expense categories
- Turkish Lira amount validation
- Date format handling

This comprehensive testing strategy ensures the Çiftçi Not Defterim backend service is thoroughly tested with agricultural-specific considerations and Turkish language support.

---

## Tarla Yönetimi Test Senaryoları

### Ön Koşullar
1. ✅ Backend servisleri çalışır durumda olmalı
2. ✅ Kullanıcı Google ile giriş yapmış olmalı
3. ✅ Mobil uygulama çalışır durumda olmalı

### 1. Tarla Listesi Görüntüleme

**Amaç:** Tarla yönetimi ekranının doğru çalıştığını doğrulamak

**Adımlar:**
1. Ana sayfadan "Detaylı Takip" seçeneğine tıklayın
2. "Tarla Yönetimi" seçeneğine tıklayın
3. Tarla listesi ekranının açıldığını doğrulayın

**Beklenen Sonuç:**
- Tarla listesi ekranı açılır
- Eğer tarla yoksa "Henüz tarla yok" mesajı görünür
- "İlk Tarla Ekle" butonu görünür
- Header'da "+" butonu görünür

### 2. Yeni Tarla Ekleme - Başarılı Senaryo

**Amaç:** Geçerli verilerle tarla ekleme işleminin başarılı olduğunu doğrulamak

**Adımlar:**
1. Tarla listesi ekranında "+" butonuna tıklayın
2. "Yeni Tarla Ekle" ekranının açıldığını doğrulayın
3. Aşağıdaki bilgileri girin:
   - **Tarla Adı:** "Test Tarlası"
   - **Alan Büyüklüğü:** "15"
   - **Birim:** "Dekar" (varsayılan)
   - **Konum:** "Ankara, Çankaya" (isteğe bağlı)
   - **Toprak Tipi:** "Balçıklı" seçin
   - **Sulama Sistemi:** "Damla Sulama" seçin
   - **Notlar:** "Test için oluşturulan tarla" (isteğe bağlı)
4. "Kaydet" butonuna tıklayın

**Beklenen Sonuç:**
- "Tarla başarıyla eklendi" mesajı görünür
- Tarla listesi ekranına geri dönülür
- Yeni eklenen tarla listede görünür

### 3. Yeni Tarla Ekleme - Validasyon Hataları

**Amaç:** Geçersiz verilerle tarla ekleme işleminin hata verdiğini doğrulamak

**Test Senaryoları:**

#### 3.1 Boş Tarla Adı
**Adımlar:**
1. "Yeni Tarla Ekle" ekranını açın
2. Tarla adı alanını boş bırakın
3. Diğer alanları doldurun
4. "Kaydet" butonuna tıklayın

**Beklenen Sonuç:**
- "Tarla adı gereklidir" hata mesajı görünür
- Form submit edilmez

#### 3.2 Geçersiz Alan Büyüklüğü
**Adımlar:**
1. "Yeni Tarla Ekle" ekranını açın
2. Alan büyüklüğü alanına "0" veya negatif değer girin
3. Diğer alanları doldurun
4. "Kaydet" butonuna tıklayın

**Beklenen Sonuç:**
- "Alan büyüklüğü pozitif bir sayı olmalıdır" hata mesajı görünür
- Form submit edilmez

---

## Tarla ve Ürün Bilgilerinin Görüntülenmesi Test Senaryoları

### Gereksinimler Özeti

1. **Basit Mod Kullanıcısı:** Tarla ve ürün alanları hiç gösterilmemeli
2. **Detaylı Tarla Modu - Basit Modda Eklenen Veriler:** "Bilinmiyor" veya "Seçilmedi" gösterilmeli
3. **Detaylı Tarla Modu - Detaylı Modda Eklenen Veriler:** Tam bilgiler gösterilmeli
4. **Mod Değişikliği:** Veri kaybı olmamalı

### Senaryo 1: Basit Modda Eklenen Gider - Basit Modda Görüntüleme

**Ön Koşullar:**
- Kullanıcı basit modda
- Gider basit modda eklenmiş (fieldId ve cropId null)

**Test Adımları:**
1. Gider listesinden bir gider seç
2. Gider düzenleme ekranını aç

**Beklenen Sonuç:**
- Kategori, sezon, tutar, tarih, açıklama alanları görünür
- Tarla ve ürün alanları hiç görünmez

### Senaryo 2: Basit Modda Eklenen Gider - Detaylı Moda Geçiş Sonrası Görüntüleme

**Ön Koşullar:**
- Gider basit modda eklenmiş (fieldId ve cropId null)
- Kullanıcı detaylı moda geçmiş

**Test Adımları:**
1. Gider listesinden basit modda eklenen bir gider seç
2. Gider düzenleme ekranını aç

**Beklenen Sonuç:**
- Kategori, sezon, tutar, tarih, açıklama alanları görünür
- Tarla alanı görünmez (çünkü fieldId null)
- Ürün alanı görünmez (çünkü cropId null)

### Senaryo 3: Detaylı Modda Eklenen Gider - Detaylı Modda Görüntüleme

**Ön Koşullar:**
- Kullanıcı detaylı modda
- Gider detaylı modda eklenmiş (fieldId ve cropId dolu)

**Test Adımları:**
1. Gider listesinden detaylı modda eklenen bir gider seç
2. Gider düzenleme ekranını aç

**Beklenen Sonuç:**
- Tüm alanlar görünür (kategori, sezon, tutar, tarih, açıklama, tarla, ürün)
- Tarla ve ürün bilgileri doğru şekilde gösterilir

### Senaryo 4: Mod Değişikliği Sonrası Veri Bütünlüğü

**Ön Koşullar:**
- Kullanıcı detaylı modda
- Tarla ve ürün bilgileri olan giderler mevcut

**Test Adımları:**
1. Basit moda geç
2. Gider listesini kontrol et
3. Tekrar detaylı moda geç
4. Aynı giderleri kontrol et

**Beklenen Sonuç:**
- Basit modda tarla/ürün bilgileri görünmez ama kaybolmaz
- Detaylı moda geri dönüldüğünde tüm bilgiler korunmuş olur
