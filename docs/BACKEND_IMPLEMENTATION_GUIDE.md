# Backend Development & Maintenance Guide - Çiftç<PERSON> Not Defterim
## Overview
This guide provides instructions for developing, maintaining, and extending the existing Node.js backend service for the Çiftçi Not Defterim mobile application. The backend is fully implemented and operational.
## Prerequisites
- Node.js 18+ installed
- MongoDB database (currently implemented)
- Firebase project with Admin SDK credentials
- Git for version control
- Docker (for containerized deployment)
## Current Backend Status
The backend is fully implemented and operational. This guide covers maintenance and extension tasks.
### 1. Existing Project Structure
```bash
backend/
├── src/
│   ├── controllers/     # Request handlers
│   ├── middleware/      # Authentication, validation, error handling
│   ├── models/         # MongoDB models (User, Expense, Category, etc.)
│   ├── routes/         # API route definitions
│   ├── services/       # Business logic services
│   ├── utils/          # Utility functions and database connection
│   └── app.js          # Express application setup
├── tests/              # Test suites
├── scripts/            # Database initialization scripts
├── package.json        # Dependencies and scripts
└── server.js           # Application entry point
```
### 2. Project Structure
```
ciftci-backend/
├── src/
│   ├── controllers/
│   │   ├── authController.js
│   │   ├── expenseController.js
│   │   ├── categoryController.js
│   │   ├── syncController.js
│   │   └── uploadController.js
│   ├── middleware/
│   │   ├── auth.js
│   │   ├── validation.js
│   │   ├── errorHandler.js
│   │   └── rateLimiter.js
│   ├── models/
│   │   ├── User.js
│   │   ├── Expense.js
│   │   ├── Category.js
│   │   └── Season.js
│   ├── routes/
│   │   ├── auth.js
│   │   ├── expenses.js
│   │   ├── categories.js
│   │   ├── sync.js
│   │   └── upload.js
│   ├── services/
│   │   ├── firebaseService.js
│   │   ├── seasonService.js
│   │   ├── syncService.js
│   │   └── uploadService.js
│   ├── utils/
│   │   ├── database.js
│   │   ├── logger.js
│   │   ├── constants.js
│   │   └── helpers.js
│   └── app.js
├── tests/
│   ├── unit/
│   ├── integration/
│   └── e2e/
├── uploads/
├── .env.example
├── .gitignore
├── package.json
└── server.js
```
### 3. Environment Configuration
Create `.env` file:
```env
# Server Configuration
NODE_ENV=development
PORT=3000
API_VERSION=v1
# Database Configuration (MongoDB)
MONGODB_URI=mongodb://localhost:27017/ciftci-notebook
# OR PostgreSQL
# DATABASE_URL=postgresql://username:password@localhost:5432/ciftci_notebook
# Firebase Configuration
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_PRIVATE_KEY_ID=your-private-key-id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_CLIENT_ID=your-client-id
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token
# File Upload Configuration
UPLOAD_MAX_SIZE=5242880
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/webp
# Security
JWT_SECRET=your-super-secret-jwt-key
BCRYPT_ROUNDS=12
# Rate Limiting
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100
# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log
```
## Core Implementation
### 1. Database Setup (MongoDB Example)
```javascript
// src/utils/database.js
const mongoose = require('mongoose');
const logger = require('./logger');
const connectDatabase = async () => {
  try {
    const conn = await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    logger.info(`MongoDB Connected: ${conn.connection.host}`);
    return conn;
  } catch (error) {
    logger.error('Database connection failed:', error);
    process.exit(1);
  }
};
module.exports = { connectDatabase };
```
### 2. Firebase Service Setup
```javascript
// src/services/firebaseService.js
const admin = require('firebase-admin');
const logger = require('../utils/logger');
class FirebaseService {
  constructor() {
    this.initializeFirebase();
  }
  initializeFirebase() {
    try {
      const serviceAccount = {
        type: 'service_account',
        project_id: process.env.FIREBASE_PROJECT_ID,
        private_key_id: process.env.FIREBASE_PRIVATE_KEY_ID,
        private_key: process.env.FIREBASE_PRIVATE_KEY.replace(/\\n/g, '\n'),
        client_email: process.env.FIREBASE_CLIENT_EMAIL,
        client_id: process.env.FIREBASE_CLIENT_ID,
        auth_uri: process.env.FIREBASE_AUTH_URI,
        token_uri: process.env.FIREBASE_TOKEN_URI,
      };
      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
        projectId: process.env.FIREBASE_PROJECT_ID,
      });
      logger.info('Firebase Admin SDK initialized successfully');
    } catch (error) {
      logger.error('Firebase initialization failed:', error);
      throw error;
    }
  }
  async verifyToken(token) {
    try {
      const decodedToken = await admin.auth().verifyIdToken(token);
      return {
        success: true,
        user: {
          uid: decodedToken.uid,
          email: decodedToken.email,
          name: decodedToken.name,
        },
      };
    } catch (error) {
      logger.error('Token verification failed:', error);
      return {
        success: false,
        error: 'Geçersiz kimlik doğrulama token\'ı',
      };
    }
  }
}
module.exports = new FirebaseService();
```
### 3. Authentication Middleware
```javascript
// src/middleware/auth.js
const firebaseService = require('../services/firebaseService');
const User = require('../models/User');
const logger = require('../utils/logger');
const authenticateUser = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'MISSING_TOKEN',
          message: 'Kimlik doğrulama token\'ı gerekli',
        },
      });
    }
    const token = authHeader.substring(7);
    const verificationResult = await firebaseService.verifyToken(token);
    if (!verificationResult.success) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'INVALID_TOKEN',
          message: verificationResult.error,
        },
      });
    }
    // Find or create user
    let user = await User.findOne({ firebaseUid: verificationResult.user.uid });
    if (!user) {
      user = await User.create({
        firebaseUid: verificationResult.user.uid,
        email: verificationResult.user.email,
        name: verificationResult.user.name,
      });
      logger.info(`New user created: ${user.email}`);
    }
    req.user = user;
    next();
  } catch (error) {
    logger.error('Authentication middleware error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'AUTH_ERROR',
        message: 'Kimlik doğrulama hatası',
      },
    });
  }
};
module.exports = { authenticateUser };
```
### 4. Data Models (MongoDB/Mongoose Example)
```javascript
// src/models/User.js
const mongoose = require('mongoose');
const userSchema = new mongoose.Schema({
  firebaseUid: {
    type: String,
    required: true,
    unique: true,
  },
  email: {
    type: String,
    required: true,
    unique: true,
  },
  name: {
    type: String,
    required: true,
  },
  preferences: {
    currency: {
      type: String,
      default: 'TRY',
    },
    language: {
      type: String,
      default: 'tr',
    },
    defaultSeason: {
      type: String,
      default: 'spring',
    },
  },
  lastLoginAt: {
    type: Date,
    default: Date.now,
  },
}, {
  timestamps: true,
});
module.exports = mongoose.model('User', userSchema);
```
```javascript
// src/models/Expense.js
const mongoose = require('mongoose');
const expenseSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  categoryId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category',
    required: true,
  },
  seasonId: {
    type: String,
    required: true,
  },
  amount: {
    type: Number,
    required: true,
    min: 0,
  },
  description: {
    type: String,
    maxlength: 500,
  },
  date: {
    type: Date,
    required: true,
  },
  location: {
    latitude: Number,
    longitude: Number,
    address: String,
  },
  photos: [{
    id: String,
    url: String,
    thumbnail: String,
    filename: String,
    size: Number,
    mimeType: String,
  }],
  lastSynced: {
    type: Date,
    default: Date.now,
  },
}, {
  timestamps: true,
});
// Indexes for performance
expenseSchema.index({ userId: 1, date: -1 });
expenseSchema.index({ userId: 1, categoryId: 1 });
expenseSchema.index({ userId: 1, seasonId: 1 });
module.exports = mongoose.model('Expense', expenseSchema);
```
### 5. Express App Setup
```javascript
// src/app.js
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const { connectDatabase } = require('./utils/database');
const logger = require('./utils/logger');
const errorHandler = require('./middleware/errorHandler');
// Import routes
const authRoutes = require('./routes/auth');
const expenseRoutes = require('./routes/expenses');
const categoryRoutes = require('./routes/categories');
const syncRoutes = require('./routes/sync');
const uploadRoutes = require('./routes/upload');
class App {
  constructor() {
    this.app = express();
    this.setupMiddleware();
    this.setupRoutes();
    this.setupErrorHandling();
  }
  setupMiddleware() {
    // Security middleware
    this.app.use(helmet());
    this.app.use(cors({
      origin: process.env.ALLOWED_ORIGINS?.split(',') || '*',
      credentials: true,
    }));
    // Rate limiting
    const limiter = rateLimit({
      windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 60000,
      max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
      message: {
        success: false,
        error: {
          code: 'RATE_LIMIT_EXCEEDED',
          message: 'Çok fazla istek gönderildi. Lütfen daha sonra tekrar deneyin.',
        },
      },
    });
    this.app.use(limiter);
    // Compression and parsing
    this.app.use(compression());
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));
    // Logging
    this.app.use(morgan('combined', {
      stream: { write: (message) => logger.info(message.trim()) },
    }));
  }
  setupRoutes() {
    const apiVersion = process.env.API_VERSION || 'v1';
    // Health check
    this.app.get('/health', (req, res) => {
      res.json({
        success: true,
        message: 'Çiftçi Not Defterim API çalışıyor',
        timestamp: new Date().toISOString(),
        version: apiVersion,
      });
    });
    // API routes
    this.app.use(`/api/${apiVersion}/auth`, authRoutes);
    this.app.use(`/api/${apiVersion}/expenses`, expenseRoutes);
    this.app.use(`/api/${apiVersion}/categories`, categoryRoutes);
    this.app.use(`/api/${apiVersion}/sync`, syncRoutes);
    this.app.use(`/api/${apiVersion}/upload`, uploadRoutes);
    // 404 handler
    this.app.use('*', (req, res) => {
      res.status(404).json({
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: 'İstenen kaynak bulunamadı',
        },
      });
    });
  }
  setupErrorHandling() {
    this.app.use(errorHandler);
  }
  async start() {
    try {
      // Connect to database
      await connectDatabase();
      const port = process.env.PORT || 3000;
      this.app.listen(port, () => {
        logger.info(`Çiftçi Not Defterim API sunucusu ${port} portunda çalışıyor`);
      });
    } catch (error) {
      logger.error('Sunucu başlatma hatası:', error);
      process.exit(1);
    }
  }
}
module.exports = App;
```
### 6. Server Entry Point
```javascript
// server.js
require('dotenv').config();
const App = require('./src/app');
const app = new App();
app.start();
```
## Package.json Scripts
```json
{
  "scripts": {
    "start": "node server.js",
    "dev": "nodemon server.js",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "lint": "eslint src/",
    "lint:fix": "eslint src/ --fix",
    "format": "prettier --write src/"
  }
}
```
## Next Steps
1. **Implement Controllers:** Create controller files for each endpoint
2. **Add Validation:** Implement request validation middleware
3. **Create Tests:** Write comprehensive test suite
4. **Set up Logging:** Configure proper logging system
5. **Deploy:** Set up production deployment
## Agricultural-Specific Considerations
### Season Detection Logic
```javascript
// src/services/seasonService.js
const getCurrentSeason = (date = new Date()) => {
  const month = date.getMonth() + 1; // 1-12
  if (month >= 3 && month <= 5) return 'spring';
  if (month >= 6 && month <= 8) return 'summer';
  if (month >= 9 && month <= 11) return 'autumn';
  return 'winter';
};
```
### Turkish Language Support
- Use Turkish error messages throughout
- Support Turkish number formatting
- Handle Turkish characters in text fields
This implementation guide provides a solid foundation for building the Çiftçi Not Defterim backend service with agricultural-specific features and Turkish language support.
