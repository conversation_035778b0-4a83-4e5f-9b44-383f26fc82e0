# API Documentation - Çiftçi Not Defterim Backend

## Overview

This document provides comprehensive API documentation for the Çiftçi Not Defterim (Farmer's Notebook) backend service. The API supports agricultural expense tracking with seasonal features, offline synchronization, and multi-device access.

## Base URL
```
Development: http://localhost:3000/api/v1
Production: https://api.ciftcinotdefterim.com/api/v1
```

## Authentication

All API endpoints (except public ones) require Firebase Authentication token.

### Headers
```http
Authorization: Bearer <firebase_token>
Content-Type: application/json
Accept-Language: tr-TR (optional, defaults to Turkish)
```

### Authentication Flow
1. Client authenticates with Firebase Auth
2. Client sends Firebase token in Authorization header
3. Server validates token with Firebase Admin SDK
4. Server returns user-specific data

## Error Responses

### Standard Error Format
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Geçersiz veri formatı",
    "details": {
      "field": "amount",
      "reason": "Tutar pozitif bir sayı olmalıdır"
    }
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### HTTP Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request (validation errors)
- `401` - Unauthorized (invalid/missing token)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found
- `409` - Conflict (sync conflicts)
- `429` - Too Many Requests (rate limiting)
- `500` - Internal Server Error

## API Endpoints

### Authentication

#### POST /auth/validate
Validate Firebase token and get user info.

**Request:**
```json
{
  "firebaseToken": "eyJhbGciOiJSUzI1NiIs..."
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "userId": "user_123",
    "email": "<EMAIL>",
    "name": "Ahmet Çiftçi",
    "isNewUser": false,
    "lastLoginAt": "2024-01-15T10:30:00Z"
  }
}
```

### User Management

#### GET /users/profile
Get current user profile.

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "user_123",
    "email": "<EMAIL>",
    "name": "Ahmet Çiftçi",
    "preferences": {
      "currency": "TRY",
      "language": "tr",
      "defaultSeason": "spring"
    },
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-15T10:30:00Z"
  }
}
```

#### PUT /users/profile
Update user profile.

**Request:**
```json
{
  "name": "Ahmet Çiftçi",
  "preferences": {
    "currency": "TRY",
    "language": "tr",
    "defaultSeason": "spring"
  }
}
```

### Expenses

#### GET /expenses
Get user expenses with filtering and pagination.

**Query Parameters:**
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 20, max: 100)
- `startDate` (string): Start date (ISO format)
- `endDate` (string): End date (ISO format)
- `categoryId` (string): Filter by category
- `seasonId` (string): Filter by season
- `search` (string): Search in description

**Response:**
```json
{
  "success": true,
  "data": {
    "expenses": [
      {
        "id": "exp_123",
        "categoryId": "cat_456",
        "categoryName": "Gübre",
        "categoryEmoji": "🌱",
        "categoryColor": "#4CAF50",
        "amount": 1250.50,
        "description": "Organik gübre alımı",
        "date": "2024-01-15",
        "seasonId": "season_winter",
        "seasonName": "Kış Hazırlığı",
        "location": {
          "latitude": 39.9334,
          "longitude": 32.8597,
          "address": "Ankara, Türkiye"
        },
        "photos": [
          {
            "id": "photo_789",
            "url": "https://storage.example.com/receipts/photo_789.jpg",
            "thumbnail": "https://storage.example.com/receipts/thumb_photo_789.jpg"
          }
        ],
        "createdAt": "2024-01-15T10:30:00Z",
        "updatedAt": "2024-01-15T10:30:00Z",
        "lastSynced": "2024-01-15T10:30:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 150,
      "totalPages": 8,
      "hasNext": true,
      "hasPrev": false
    },
    "summary": {
      "totalAmount": 45750.25,
      "expenseCount": 150,
      "averageAmount": 305.00
    }
  }
}
```

#### POST /expenses
Create a new expense.

**Request:**
```json
{
  "categoryId": "cat_456",
  "amount": 1250.50,
  "description": "Organik gübre alımı",
  "date": "2024-01-15",
  "location": {
    "latitude": 39.9334,
    "longitude": 32.8597
  },
  "photos": ["photo_789"]
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "exp_123",
    "categoryId": "cat_456",
    "amount": 1250.50,
    "description": "Organik gübre alımı",
    "date": "2024-01-15",
    "seasonId": "season_winter",
    "location": {
      "latitude": 39.9334,
      "longitude": 32.8597,
      "address": "Ankara, Türkiye"
    },
    "photos": [
      {
        "id": "photo_789",
        "url": "https://storage.example.com/receipts/photo_789.jpg"
      }
    ],
    "createdAt": "2024-01-15T10:30:00Z",
    "updatedAt": "2024-01-15T10:30:00Z"
  }
}
```

#### PUT /expenses/:id
Update an existing expense.

#### DELETE /expenses/:id
Delete an expense.

### Categories

#### GET /categories
Get user categories (default + custom).

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "cat_456",
      "name": "Gübre",
      "emoji": "🌱",
      "color": "#4CAF50",
      "icon": "fertilizer",
      "isDefault": true,
      "usageCount": 25,
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-15T10:30:00Z"
    }
  ]
}
```

#### POST /categories
Create a custom category.

**Request:**
```json
{
  "name": "Özel Gübre",
  "emoji": "🌿",
  "color": "#8BC34A",
  "icon": "custom-fertilizer"
}
```

### Seasons

#### GET /seasons
Get agricultural seasons.

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "season_spring",
      "name": "Bahar Ekimi",
      "startMonth": 3,
      "endMonth": 5,
      "description": "Bahar dönemi ekim ve bakım çalışmaları",
      "typicalCrops": ["buğday", "arpa", "mısır"],
      "typicalExpenses": ["tohum", "gübre", "ilaç"],
      "budgetRecommendations": {
        "seedPercentage": 30,
        "fertilizerPercentage": 25,
        "pesticidePercentage": 20,
        "laborPercentage": 25
      }
    }
  ]
}
```

### Synchronization

#### POST /sync/push
Push local changes to server.

**Request:**
```json
{
  "changes": [
    {
      "id": "sync_123",
      "operation": "create",
      "table": "expenses",
      "localId": "local_exp_456",
      "data": {
        "categoryId": "cat_456",
        "amount": 1250.50,
        "description": "Organik gübre alımı",
        "date": "2024-01-15"
      },
      "timestamp": 1705312200000
    }
  ],
  "lastSyncTime": "2024-01-15T09:00:00Z"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "results": [
      {
        "localId": "local_exp_456",
        "serverId": "exp_789",
        "status": "success",
        "conflicts": null
      }
    ],
    "serverTime": "2024-01-15T10:30:00Z"
  }
}
```

#### GET /sync/pull
Pull server changes since last sync.

**Query Parameters:**
- `since` (string): Last sync timestamp (ISO format)

**Response:**
```json
{
  "success": true,
  "data": {
    "changes": [
      {
        "id": "exp_789",
        "operation": "update",
        "table": "expenses",
        "data": {
          "id": "exp_789",
          "amount": 1300.00,
          "updatedAt": "2024-01-15T10:15:00Z"
        },
        "timestamp": 1705312500000
      }
    ],
    "serverTime": "2024-01-15T10:30:00Z",
    "hasMore": false
  }
}
```

### File Upload

#### POST /upload/photos
Upload expense receipt photos.

**Request:** Multipart form data
- `file`: Image file (max 5MB, jpg/png)
- `expenseId`: Associated expense ID (optional)

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "photo_789",
    "url": "https://storage.example.com/receipts/photo_789.jpg",
    "thumbnail": "https://storage.example.com/receipts/thumb_photo_789.jpg",
    "filename": "receipt_20240115.jpg",
    "size": 2048576,
    "mimeType": "image/jpeg",
    "uploadedAt": "2024-01-15T10:30:00Z"
  }
}
```

## Rate Limiting

- **General endpoints:** 100 requests per minute per user
- **Upload endpoints:** 10 requests per minute per user
- **Sync endpoints:** 30 requests per minute per user

## Agricultural-Specific Features

### Seasonal Detection
The API automatically detects the current agricultural season based on:
- Current date
- User's geographic location
- Local agricultural calendar

### Turkish Language Support
- All error messages in Turkish
- Agricultural terminology in Turkish
- Support for Turkish number formatting (comma as decimal separator)

### Currency Support
- Primary currency: Turkish Lira (TRY)
- Support for Turkish Lira formatting
- Historical exchange rates for other currencies

## Data Models

### Expense Schema
```json
{
  "id": "string (required)",
  "userId": "string (required)",
  "categoryId": "string (required)",
  "seasonId": "string (auto-detected)",
  "amount": "number (required, positive)",
  "description": "string (optional, max 500 chars)",
  "date": "string (required, ISO date)",
  "location": {
    "latitude": "number (optional)",
    "longitude": "number (optional)",
    "address": "string (optional)"
  },
  "photos": ["string (photo IDs)"],
  "createdAt": "string (ISO datetime)",
  "updatedAt": "string (ISO datetime)",
  "lastSynced": "string (ISO datetime)"
}
```

### Category Schema
```json
{
  "id": "string (required)",
  "userId": "string (null for default categories)",
  "name": "string (required, max 50 chars)",
  "emoji": "string (required, single emoji)",
  "color": "string (required, hex color)",
  "icon": "string (optional, icon name)",
  "isDefault": "boolean (default: false)",
  "usageCount": "number (default: 0)",
  "createdAt": "string (ISO datetime)",
  "updatedAt": "string (ISO datetime)"
}
```

This API documentation provides a complete specification for implementing the Çiftçi Not Defterim backend service with agricultural-specific features and Turkish language support.
