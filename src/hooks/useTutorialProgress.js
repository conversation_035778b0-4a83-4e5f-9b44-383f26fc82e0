/**
 * Tutorial Progress Hook for Çiftçi Not Defterim
 * Manages tutorial progress tracking and state management
 */

import { useState, useEffect, useCallback } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import TutorialService, { TutorialType } from '../services/TutorialService';
import Logger, { LogCategory } from '../utils/Logger';
import Analytics, { AnalyticsEvent } from '../utils/Analytics';

export const useTutorialProgress = (tutorialType) => {
  const [progress, setProgress] = useState({
    currentStep: 0,
    totalSteps: 0,
    completed: false,
    skipped: false,
    startTime: null,
    completionTime: null,
    timeSpent: 0,
    stepsViewed: [],
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Load progress from storage
  const loadProgress = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const tutorial = TutorialService.getTutorial(tutorialType);
      if (!tutorial) {
        throw new Error(`Tutorial not found: ${tutorialType}`);
      }

      // Get stored progress
      const storedProgress = await AsyncStorage.getItem(`tutorial_progress_${tutorialType}`);
      const parsedProgress = storedProgress ? JSON.parse(storedProgress) : {};

      // Check if tutorial is completed
      const isCompleted = TutorialService.isTutorialCompleted(tutorialType);

      setProgress({
        currentStep: parsedProgress.currentStep || 0,
        totalSteps: tutorial.steps.length,
        completed: isCompleted,
        skipped: parsedProgress.skipped || false,
        startTime: parsedProgress.startTime || null,
        completionTime: parsedProgress.completionTime || null,
        timeSpent: parsedProgress.timeSpent || 0,
        stepsViewed: parsedProgress.stepsViewed || [],
      });

      Logger.debug(LogCategory.USER_ACTION, 'Tutorial progress loaded', {
        tutorialType,
        progress: parsedProgress,
      });
    } catch (err) {
      Logger.error(LogCategory.USER_ACTION, 'Failed to load tutorial progress', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, [tutorialType]);

  // Save progress to storage
  const saveProgress = useCallback(async (newProgress) => {
    try {
      const progressToSave = {
        ...progress,
        ...newProgress,
        lastUpdated: new Date().toISOString(),
      };

      await AsyncStorage.setItem(
        `tutorial_progress_${tutorialType}`,
        JSON.stringify(progressToSave)
      );

      setProgress(progressToSave);

      Logger.debug(LogCategory.USER_ACTION, 'Tutorial progress saved', {
        tutorialType,
        progress: progressToSave,
      });
    } catch (err) {
      Logger.error(LogCategory.USER_ACTION, 'Failed to save tutorial progress', err);
      setError(err.message);
    }
  }, [tutorialType, progress]);

  // Start tutorial
  const startTutorial = useCallback(async () => {
    try {
      const startTime = new Date().toISOString();
      
      await saveProgress({
        currentStep: 0,
        startTime,
        stepsViewed: [0],
      });

      Analytics.trackEvent(AnalyticsEvent.TUTORIAL_STARTED, {
        tutorialType,
        startTime,
      });

      Logger.info(LogCategory.USER_ACTION, 'Tutorial started', { tutorialType });
    } catch (err) {
      Logger.error(LogCategory.USER_ACTION, 'Failed to start tutorial', err);
      setError(err.message);
    }
  }, [tutorialType, saveProgress]);

  // Go to next step
  const nextStep = useCallback(async () => {
    try {
      const newStep = Math.min(progress.currentStep + 1, progress.totalSteps - 1);
      const newStepsViewed = [...new Set([...progress.stepsViewed, newStep])];

      await saveProgress({
        currentStep: newStep,
        stepsViewed: newStepsViewed,
      });

      Analytics.trackEvent(AnalyticsEvent.TUTORIAL_STEP_VIEWED, {
        tutorialType,
        stepNumber: newStep,
        totalSteps: progress.totalSteps,
      });

      Logger.debug(LogCategory.USER_ACTION, 'Tutorial step advanced', {
        tutorialType,
        step: newStep,
      });
    } catch (err) {
      Logger.error(LogCategory.USER_ACTION, 'Failed to advance tutorial step', err);
      setError(err.message);
    }
  }, [tutorialType, progress, saveProgress]);

  // Go to previous step
  const previousStep = useCallback(async () => {
    try {
      const newStep = Math.max(progress.currentStep - 1, 0);

      await saveProgress({
        currentStep: newStep,
      });

      Logger.debug(LogCategory.USER_ACTION, 'Tutorial step went back', {
        tutorialType,
        step: newStep,
      });
    } catch (err) {
      Logger.error(LogCategory.USER_ACTION, 'Failed to go back tutorial step', err);
      setError(err.message);
    }
  }, [tutorialType, progress, saveProgress]);

  // Go to specific step
  const goToStep = useCallback(async (stepIndex) => {
    try {
      if (stepIndex < 0 || stepIndex >= progress.totalSteps) {
        throw new Error('Invalid step index');
      }

      const newStepsViewed = [...new Set([...progress.stepsViewed, stepIndex])];

      await saveProgress({
        currentStep: stepIndex,
        stepsViewed: newStepsViewed,
      });

      Analytics.trackEvent(AnalyticsEvent.TUTORIAL_STEP_VIEWED, {
        tutorialType,
        stepNumber: stepIndex,
        totalSteps: progress.totalSteps,
        isJump: true,
      });

      Logger.debug(LogCategory.USER_ACTION, 'Tutorial jumped to step', {
        tutorialType,
        step: stepIndex,
      });
    } catch (err) {
      Logger.error(LogCategory.USER_ACTION, 'Failed to jump to tutorial step', err);
      setError(err.message);
    }
  }, [tutorialType, progress, saveProgress]);

  // Complete tutorial
  const completeTutorial = useCallback(async () => {
    try {
      const completionTime = new Date().toISOString();
      const timeSpent = progress.startTime 
        ? new Date(completionTime).getTime() - new Date(progress.startTime).getTime()
        : 0;

      await saveProgress({
        completed: true,
        completionTime,
        timeSpent,
        currentStep: progress.totalSteps - 1,
      });

      // Mark as completed in TutorialService
      await TutorialService.markTutorialCompleted(tutorialType);

      Analytics.trackEvent(AnalyticsEvent.TUTORIAL_COMPLETED, {
        tutorialType,
        timeSpent,
        stepsViewed: progress.stepsViewed.length,
        totalSteps: progress.totalSteps,
        completionRate: (progress.stepsViewed.length / progress.totalSteps) * 100,
      });

      Logger.info(LogCategory.USER_ACTION, 'Tutorial completed', {
        tutorialType,
        timeSpent,
        stepsViewed: progress.stepsViewed.length,
      });
    } catch (err) {
      Logger.error(LogCategory.USER_ACTION, 'Failed to complete tutorial', err);
      setError(err.message);
    }
  }, [tutorialType, progress, saveProgress]);

  // Skip tutorial
  const skipTutorial = useCallback(async () => {
    try {
      const skipTime = new Date().toISOString();
      const timeSpent = progress.startTime 
        ? new Date(skipTime).getTime() - new Date(progress.startTime).getTime()
        : 0;

      await saveProgress({
        skipped: true,
        completionTime: skipTime,
        timeSpent,
      });

      // Mark as skipped in TutorialService
      await TutorialService.markTutorialSkipped(tutorialType, progress.currentStep);

      Analytics.trackEvent(AnalyticsEvent.TUTORIAL_SKIPPED, {
        tutorialType,
        currentStep: progress.currentStep,
        timeSpent,
        stepsViewed: progress.stepsViewed.length,
        totalSteps: progress.totalSteps,
        completionRate: (progress.stepsViewed.length / progress.totalSteps) * 100,
      });

      Logger.info(LogCategory.USER_ACTION, 'Tutorial skipped', {
        tutorialType,
        currentStep: progress.currentStep,
        timeSpent,
      });
    } catch (err) {
      Logger.error(LogCategory.USER_ACTION, 'Failed to skip tutorial', err);
      setError(err.message);
    }
  }, [tutorialType, progress, saveProgress]);

  // Reset tutorial progress
  const resetProgress = useCallback(async () => {
    try {
      await AsyncStorage.removeItem(`tutorial_progress_${tutorialType}`);
      await TutorialService.resetTutorial(tutorialType);

      setProgress({
        currentStep: 0,
        totalSteps: progress.totalSteps,
        completed: false,
        skipped: false,
        startTime: null,
        completionTime: null,
        timeSpent: 0,
        stepsViewed: [],
      });

      Logger.info(LogCategory.USER_ACTION, 'Tutorial progress reset', { tutorialType });
    } catch (err) {
      Logger.error(LogCategory.USER_ACTION, 'Failed to reset tutorial progress', err);
      setError(err.message);
    }
  }, [tutorialType, progress.totalSteps]);

  // Get progress percentage
  const getProgressPercentage = useCallback(() => {
    if (progress.completed) return 100;
    if (progress.totalSteps === 0) return 0;
    return Math.round((progress.currentStep / progress.totalSteps) * 100);
  }, [progress]);

  // Check if step is viewed
  const isStepViewed = useCallback((stepIndex) => {
    return progress.stepsViewed.includes(stepIndex);
  }, [progress.stepsViewed]);

  // Get time spent in readable format
  const getTimeSpentFormatted = useCallback(() => {
    if (!progress.timeSpent) return '0 saniye';
    
    const seconds = Math.floor(progress.timeSpent / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours} saat ${minutes % 60} dakika`;
    } else if (minutes > 0) {
      return `${minutes} dakika ${seconds % 60} saniye`;
    } else {
      return `${seconds} saniye`;
    }
  }, [progress.timeSpent]);

  // Load progress on mount
  useEffect(() => {
    loadProgress();
  }, [loadProgress]);

  return {
    // State
    progress,
    loading,
    error,
    
    // Actions
    startTutorial,
    nextStep,
    previousStep,
    goToStep,
    completeTutorial,
    skipTutorial,
    resetProgress,
    
    // Computed values
    progressPercentage: getProgressPercentage(),
    timeSpentFormatted: getTimeSpentFormatted(),
    isStepViewed,
    
    // Utilities
    reload: loadProgress,
  };
};

// Hook for managing all tutorial progress
export const useAllTutorialProgress = () => {
  const [allProgress, setAllProgress] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const loadAllProgress = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const tutorialTypes = Object.values(TutorialType);
      const progressData = {};

      for (const type of tutorialTypes) {
        try {
          const storedProgress = await AsyncStorage.getItem(`tutorial_progress_${type}`);
          const progress = storedProgress ? JSON.parse(storedProgress) : null;
          const isCompleted = TutorialService.isTutorialCompleted(type);
          
          progressData[type] = {
            ...progress,
            completed: isCompleted,
            tutorialType: type,
          };
        } catch (err) {
          Logger.warn(LogCategory.USER_ACTION, `Failed to load progress for ${type}`, err);
          progressData[type] = null;
        }
      }

      setAllProgress(progressData);
      Logger.debug(LogCategory.USER_ACTION, 'All tutorial progress loaded', progressData);
    } catch (err) {
      Logger.error(LogCategory.USER_ACTION, 'Failed to load all tutorial progress', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, []);

  const getOverallStatistics = useCallback(() => {
    const tutorialTypes = Object.values(TutorialType);
    const completed = tutorialTypes.filter(type => 
      allProgress[type]?.completed || TutorialService.isTutorialCompleted(type)
    ).length;
    
    const skipped = tutorialTypes.filter(type => 
      allProgress[type]?.skipped
    ).length;
    
    const inProgress = tutorialTypes.filter(type => {
      const progress = allProgress[type];
      return progress && progress.startTime && !progress.completed && !progress.skipped;
    }).length;

    return {
      total: tutorialTypes.length,
      completed,
      skipped,
      inProgress,
      notStarted: tutorialTypes.length - completed - skipped - inProgress,
      completionRate: tutorialTypes.length > 0 ? (completed / tutorialTypes.length) * 100 : 0,
    };
  }, [allProgress]);

  useEffect(() => {
    loadAllProgress();
  }, [loadAllProgress]);

  return {
    allProgress,
    loading,
    error,
    statistics: getOverallStatistics(),
    reload: loadAllProgress,
  };
};

export default useTutorialProgress;
