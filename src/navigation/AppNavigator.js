import React, { useState, useEffect, useRef } from 'react';
import { View, Text, ActivityIndicator } from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useAuth } from '../context/AuthContext';
import DataConflictModal from '../components/DataConflictModal';
import { DataManager } from '../services/DataManager';
import { Alert } from 'react-native';

// Güvenli Google Sign-In import (Expo Go uyumlu)
let GoogleSignin = null;
try {
  GoogleSignin = require('@react-native-google-signin/google-signin').GoogleSignin;
} catch (error) {
  console.warn('Google Sign-In not available (Expo Go)', error.message);
}

// Screens
import AuthChoiceScreen from '../screens/AuthChoiceScreen';
import HomeScreen from '../screens/HomeScreen';
import ExpensesScreen from '../screens/ExpensesScreen';
import ReportsScreen from '../screens/ReportsScreen';
import SettingsScreen from '../screens/SettingsScreen';
import EnhancedExpenseCreationScreen from '../screens/EnhancedExpenseCreationScreen';
import ExpenseEditScreen from '../screens/ExpenseEditScreen';

// TWO-MODE SYSTEM: New screens
import FieldManagement from '../screens/FieldManagement';
import AddFieldScreen from '../screens/AddFieldScreen';
import EditFieldScreen from '../screens/EditFieldScreen';
import CropManagement from '../screens/CropManagement';
import CategoryManagement from '../screens/CategoryManagement';

// AI IMPORT/EXPORT SYSTEM: New screens
import AIChatImportScreen from '../screens/AIChatImportScreen';
import AIDataPreviewScreen from '../screens/AIDataPreviewScreen';

// SEASON MANAGEMENT: New screens
import SeasonManagementScreen from '../screens/SeasonManagementScreen';
import AddSeasonScreen from '../screens/AddSeasonScreen';
import EditSeasonScreen from '../screens/EditSeasonScreen';
import SeasonDetailScreen from '../screens/SeasonDetailScreen';

// Constants
import { Colors } from '../constants/Colors';
import { FontSize, FontWeight } from '../constants/Dimensions';
// Güvenli Firebase config import
let googleSignInConfig = null;
try {
  googleSignInConfig = require('../config/firebase').googleSignInConfig;
} catch (error) {
  console.warn('Firebase config not available', error.message);
}

const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();

const TabNavigator = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        headerShown: false,
        tabBarIcon: ({ focused, color, size }) => {
          let iconName;

          if (route.name === 'Home') {
            iconName = focused ? 'home' : 'home-outline';
          } else if (route.name === 'Expenses') {
            iconName = focused ? 'list' : 'list-outline';
          } else if (route.name === 'Reports') {
            iconName = focused ? 'bar-chart' : 'bar-chart-outline';
          } else if (route.name === 'Settings') {
            iconName = focused ? 'settings' : 'settings-outline';
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: Colors.primary,
        tabBarInactiveTintColor: Colors.textSecondary,
        tabBarStyle: {
          backgroundColor: Colors.surface,
          borderTopColor: Colors.border,
          borderTopWidth: 1,
          paddingBottom: 5,
          paddingTop: 5,
          height: 60,
          paddingHorizontal: 20,
        },
        tabBarItemStyle: {
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
          paddingHorizontal: 5,
        },
        tabBarLabelStyle: {
          fontSize: FontSize.xs,
          fontWeight: FontWeight.medium,
        },
      })}
    >
      <Tab.Screen
        name="Home"
        component={HomeScreen}
        options={{
          title: 'Ana Sayfa',
          tabBarLabel: 'Ana Sayfa',
        }}
      />
      <Tab.Screen
        name="Expenses"
        component={ExpensesScreen}
        options={{
          title: 'Giderler',
          tabBarLabel: 'Giderler',
        }}
      />
      <Tab.Screen
        name="Reports"
        component={ReportsScreen}
        options={{
          title: 'Raporlar',
          tabBarLabel: 'Raporlar',
        }}
      />
      <Tab.Screen
        name="Settings"
        component={SettingsScreen}
        options={{
          title: 'Ayarlar',
          tabBarLabel: 'Ayarlar',
        }}
      />

    </Tab.Navigator>
  );
};

const MainStackNavigator = () => {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="MainTabs" component={TabNavigator} />
      <Stack.Screen
        name="AddExpense"
        component={EnhancedExpenseCreationScreen}
        options={{
          headerShown: true,
          title: 'Gider Ekle',
          headerStyle: { backgroundColor: '#2E7D32' },
          headerTintColor: '#FFFFFF',
          presentation: 'modal',
        }}
      />
      <Stack.Screen
        name="EditExpense"
        component={ExpenseEditScreen}
        options={{
          headerShown: true,
          title: 'Gideri Düzenle',
          headerStyle: { backgroundColor: '#2E7D32' },
          headerTintColor: '#FFFFFF',
          presentation: 'modal',
        }}
      />

      {/* TWO-MODE SYSTEM: New screens */}
      <Stack.Screen
        name="FieldManagement"
        component={FieldManagement}
        options={{
          headerShown: false,
          presentation: 'card',
        }}
      />
      <Stack.Screen
        name="AddField"
        component={AddFieldScreen}
        options={{
          headerShown: false,
          presentation: 'modal',
        }}
      />
      <Stack.Screen
        name="EditField"
        component={EditFieldScreen}
        options={{
          headerShown: false,
          presentation: 'modal',
        }}
      />
      <Stack.Screen
        name="CropManagement"
        component={CropManagement}
        options={{
          headerShown: false,
          presentation: 'card',
        }}
      />
      <Stack.Screen
        name="CategoryManagement"
        component={CategoryManagement}
        options={{
          headerShown: false,
          presentation: 'card',
        }}
      />

      {/* SEASON MANAGEMENT: New screens */}
      <Stack.Screen
        name="SeasonManagement"
        component={SeasonManagementScreen}
        options={{
          headerShown: false,
          presentation: 'card',
        }}
      />
      <Stack.Screen
        name="AddSeason"
        component={AddSeasonScreen}
        options={{
          headerShown: false,
          presentation: 'modal',
        }}
      />
      <Stack.Screen
        name="EditSeason"
        component={EditSeasonScreen}
        options={{
          headerShown: false,
          presentation: 'modal',
        }}
      />
      <Stack.Screen
        name="SeasonDetail"
        component={SeasonDetailScreen}
        options={{
          headerShown: false,
          presentation: 'card',
        }}
      />

      {/* AI IMPORT/EXPORT SYSTEM: New screens */}
      <Stack.Screen
        name="AIChatImport"
        component={AIChatImportScreen}
        options={{
          headerShown: false,
          presentation: 'card',
        }}
      />
      <Stack.Screen
        name="AIDataPreview"
        component={AIDataPreviewScreen}
        options={{
          headerShown: false,
          presentation: 'modal',
        }}
      />
    </Stack.Navigator>
  );
};

export default function AppNavigator() {
  const {
    isFirstTime,
    loading,
    setNavigationRef,
    showDataConflictModal,
    setShowDataConflictModal,
    conflictData,
    signOut
  } = useAuth();
  const navigationRef = React.useRef();

  useEffect(() => {
    initializeApp();
  }, []);

  useEffect(() => {
    // Set navigation ref to AuthContext for programmatic navigation
    if (navigationRef.current) {
      setNavigationRef(navigationRef);
    }
  }, [setNavigationRef]);

  // Data conflict modal callbacks
  const handleDataConflictContinue = async () => {
    try {
      setShowDataConflictModal(false);
      console.log('AppNavigator: User chose to replace Google data with guest data');

      // Perform the migration (replace Google data with guest data)
      const migrationResult = await DataManager.migrateGuestDataToUser();
      console.log('AppNavigator: Data migration completed:', migrationResult);

      // Force refresh all data after migration
      console.log('AppNavigator: Refreshing data after migration...');
      await DataManager.syncWithBackend();

      // Show success message
      if (migrationResult && migrationResult.success && migrationResult.migratedCount > 0) {
        Alert.alert(
          'Veriler Değiştirildi!',
          `Google hesabınızdaki veriler silindi ve ${migrationResult.migratedCount} adet misafir gideriniz Google hesabınıza yüklendi. Artık verileriniz güvende ve diğer cihazlarınızdan erişebilirsiniz.`,
          [{ text: 'Tamam' }]
        );
      } else if (migrationResult && migrationResult.success && migrationResult.migratedCount === 0) {
        Alert.alert(
          'Migration Tamamlandı',
          'Veriler işlendi ancak hiçbir gider aktarılamadı. Lütfen giderlerinizi kontrol edin.',
          [{ text: 'Tamam' }]
        );
      }
    } catch (error) {
      console.error('AppNavigator: Continue with data replacement error:', error);
      Alert.alert('Hata', 'Veri değiştirme işlemi sırasında hata oluştu: ' + (error?.message || 'Bilinmeyen hata'));
    }
  };



  const handleDataConflictCancel = async () => {
    try {
      setShowDataConflictModal(false);
      // User cancelled - sign out and return to guest mode
      await signOut();
      console.log('AppNavigator: User cancelled data conflict resolution');
    } catch (error) {
      console.error('AppNavigator: Cancel error:', error);
    }
  };

  const initializeApp = async () => {
    try {
      // Google Sign-In'i initialize et (sadece development build'de çalışır)
      if (GoogleSignin && googleSignInConfig) {
        try {
          GoogleSignin.configure(googleSignInConfig);
          console.log('Google Sign-In initialized successfully');
        } catch (googleError) {
          console.warn('Google Sign-In initialization failed', googleError.message);
        }
      } else {
        console.log('Google Sign-In not available (Expo Go mode)');
      }

      // Initialize DataManager
      await DataManager.initialize();

      // Check if existing user needs season migration
      const needsMigration = await DataManager.needsSeasonMigration();
      if (needsMigration) {
        console.log('Existing user detected, starting season migration...');
        const migrationResult = await DataManager.migrateExistingUser();

        if (migrationResult.success) {
          console.log('Season migration completed successfully:', migrationResult.results);

          // Show migration success message to user
          setTimeout(() => {
            Alert.alert(
              'Sistem Güncellendi! 🎉',
              `Çiftçi Not Defterim artık sezon yönetimi sistemi ile çalışıyor. ${migrationResult.results?.expensesMigrated || 0} gideriniz "${migrationResult.results?.defaultSeason?.name}" sezonuna aktarıldı.`,
              [{ text: 'Tamam' }]
            );
          }, 1000);
        } else {
          console.error('Season migration failed:', migrationResult.error);
        }
      }
    } catch (error) {
      console.error('Error initializing app:', error);
    }
  };

  console.log('AppNavigator render - loading:', loading, 'isFirstTime:', isFirstTime);

  if (loading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#2E7D32' }}>
        <ActivityIndicator size="large" color="#FFFFFF" />
        <Text style={{ color: '#FFFFFF', marginTop: 16, fontSize: 16 }}>
          Çiftçi Not Defterim Yükleniyor...
        </Text>
      </View>
    );
  }

  return (
    <>
      <NavigationContainer ref={navigationRef}>
        <Stack.Navigator screenOptions={{ headerShown: false }}>
          {isFirstTime ? (
            <>
              <Stack.Screen name="AuthChoice" component={AuthChoiceScreen} />
              <Stack.Screen name="MainApp" component={MainStackNavigator} />
            </>
          ) : (
            <>
              <Stack.Screen name="MainApp" component={MainStackNavigator} />
            </>
          )}
        </Stack.Navigator>
      </NavigationContainer>

      {/* Global Data Conflict Modal */}
      <DataConflictModal
        visible={showDataConflictModal}
        onClose={handleDataConflictCancel}
        onContinue={handleDataConflictContinue}
        guestDataCount={conflictData.guestDataCount}
        guestFieldCount={conflictData.guestFieldCount}
        guestCategoryCount={conflictData.guestCategoryCount}
        userDataCount={conflictData.userDataCount}
      />
    </>
  );
}
