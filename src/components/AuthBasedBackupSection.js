import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { Colors } from '../constants/Colors';
import { Spacing, FontSize, FontWeight, BorderRadius } from '../constants/Dimensions';
import { useAuth } from '../context/AuthContext';
import { DataManager } from '../services/DataManager';

export default function AuthBasedBackupSection() {
  const {
    isAuthenticated,
    isGuestMode,
    signInWithGoogle,
    checkDataConflictAndSignIn
  } = useAuth();
  const [showGoogleSignInModal, setShowGoogleSignInModal] = useState(false);

  const handleBackupPress = () => {
    if (isAuthenticated && !isGuestMode) {
      // User is authenticated - show that data is safe
      return;
    } else {
      // User is in guest mode - show modal to encourage Google sign-in
      setShowGoogleSignInModal(true);
    }
  };

  const handleGoogleSignIn = async () => {
    try {
      console.log('AuthBasedBackupSection: Starting Google Sign-In...');
      setShowGoogleSignInModal(false);

      // First check for data conflicts
      const result = await signInWithGoogle();

      console.log('AuthBasedBackupSection: Google Sign-In result:', {
        success: result?.success,
        needsDataConflictCheck: result?.needsDataConflictCheck,
        guestDataCount: result?.guestDataCount,
        error: result?.error
      });

      if (result && result.needsDataConflictCheck) {
        // Guest has data, need to check for conflicts
        console.log('AuthBasedBackupSection: Data conflict check needed');
        await handleDataConflictCheck(result.guestDataCount);
      } else if (result && result.success) {
        // No conflicts, proceed with normal sign-in
        showSuccessMessage();
      } else if (result && result.cancelled) {
        // User cancelled - don't show error, just log it
        console.log('AuthBasedBackupSection: User cancelled Google Sign-In');
      } else if (result && result.error) {
        console.log('AuthBasedBackupSection: Showing error alert:', result.error);
        Alert.alert('Hata', result.error);
      } else {
        console.log('AuthBasedBackupSection: Unexpected result format:', result);
        Alert.alert('Hata', 'Google ile giriş yapılırken beklenmeyen bir hata oluştu.');
      }
    } catch (error) {
      console.error('AuthBasedBackupSection: Google sign-in error:', {
        error,
        errorType: typeof error,
        errorMessage: error?.message,
        errorCode: error?.code
      });
      Alert.alert('Hata', 'Google ile giriş yapılırken bir hata oluştu.');
    }
  };

  const handleDataConflictCheck = async (guestDataCount) => {
    try {
      console.log('AuthBasedBackupSection: Checking for data conflicts...');

      // Sign in and check user's existing data
      console.log('AuthBasedBackupSection: Calling checkDataConflictAndSignIn...');

      // Add timeout to prevent hanging
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Timeout: checkDataConflictAndSignIn took too long')), 30000);
      });

      const conflictResult = await Promise.race([
        checkDataConflictAndSignIn(guestDataCount),
        timeoutPromise
      ]);

      console.log('AuthBasedBackupSection: checkDataConflictAndSignIn returned:', conflictResult);

      console.log('AuthBasedBackupSection: Conflict result:', {
        success: conflictResult?.success,
        hasExistingData: conflictResult?.hasExistingData,
        userDataCount: conflictResult?.userDataCount,
        error: conflictResult?.error
      });

      if (conflictResult && conflictResult.success && conflictResult.hasExistingData) {
        // User has existing data - modal will be shown globally by AppNavigator
        console.log('AuthBasedBackupSection: Data conflict detected, modal will be shown globally');
      } else if (conflictResult && conflictResult.success) {
        // User has no existing data - proceed with migration
        console.log('AuthBasedBackupSection: No user data conflict, proceeding with migration');
        await performDataMigration();
      } else {
        console.log('AuthBasedBackupSection: Conflict check failed:', conflictResult?.error);
        Alert.alert('Hata', conflictResult?.error || 'Veri kontrolü sırasında hata oluştu');
      }
    } catch (error) {
      console.error('AuthBasedBackupSection: Data conflict check error:', error);
      Alert.alert('Hata', 'Veri kontrolü sırasında hata oluştu: ' + (error?.message || 'Bilinmeyen hata'));
    }
  };

  const performDataMigration = async () => {
    try {
      console.log('AuthBasedBackupSection: Starting data migration...');

      // Perform the migration
      const migrationResult = await DataManager.migrateGuestDataToUser();
      console.log('AuthBasedBackupSection: Migration result:', migrationResult);

      if (migrationResult && migrationResult.success && migrationResult.migratedCount > 0) {
        Alert.alert(
          'Veriler Yedeklendi!',
          `${migrationResult.migratedCount} adet gider kaydınız başarıyla Google hesabınıza yedeklendi. Artık verileriniz güvende ve diğer cihazlarınızdan erişebilirsiniz.`,
          [{ text: 'Tamam' }]
        );
      } else {
        // Even if no data was migrated, show success for sign-in
        showSuccessMessage();
      }
    } catch (error) {
      console.error('AuthBasedBackupSection: Migration failed:', error);
      Alert.alert(
        'Uyarı',
        'Google ile giriş başarılı ancak veri yedekleme sırasında bir sorun oluştu. Verileriniz yerel olarak korundu.',
        [{ text: 'Tamam' }]
      );
    }
  };

  const showSuccessMessage = () => {
    Alert.alert(
      'Başarılı!',
      'Google ile giriş yapıldı. Verileriniz artık güvenli bir şekilde yedeklendi ve diğer cihazlarınızdan erişebilirsiniz.',
      [{ text: 'Tamam' }]
    );
  };



  const renderBackupItem = () => {
    if (isAuthenticated && !isGuestMode) {
      // Authenticated user - show safe message
      return (
        <TouchableOpacity style={[styles.settingItem, styles.disabledItem]} disabled>
          <View style={[styles.settingIcon, { backgroundColor: Colors.success }]}>
            <Ionicons name="cloud-done-outline" size={24} color={Colors.surface} />
          </View>
          <View style={styles.settingContent}>
            <Text style={styles.settingTitle}>Google ile giriş yaptınız</Text>
            <Text style={styles.settingSubtitle}>Verileriniz güvende</Text>
          </View>
          <Ionicons name="checkmark-circle" size={24} color={Colors.success} />
        </TouchableOpacity>
      );
    } else {
      // Guest user - show backup option
      return (
        <TouchableOpacity style={styles.settingItem} onPress={handleBackupPress}>
          <View style={[styles.settingIcon, { backgroundColor: Colors.secondary }]}>
            <Ionicons name="cloud-outline" size={24} color={Colors.surface} />
          </View>
          <View style={styles.settingContent}>
            <Text style={styles.settingTitle}>Yedekleme</Text>
            <Text style={styles.settingSubtitle}>Verilerinizi bulutta yedekleyin</Text>
          </View>
          <Ionicons name="chevron-forward" size={20} color={Colors.textSecondary} />
        </TouchableOpacity>
      );
    }
  };

  // Modal state logging removed - modal now only handled in AuthChoiceScreen

  return (
    <View>
      {renderBackupItem()}

      {/* Google Sign-In Modal */}
      <Modal
        visible={showGoogleSignInModal}
        transparent
        animationType="fade"
        onRequestClose={() => setShowGoogleSignInModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Ionicons name="cloud-upload-outline" size={48} color={Colors.primary} />
              <Text style={styles.modalTitle}>Verilerinizi Yedekleyin</Text>
            </View>

            <Text style={styles.modalMessage}>
              Google ile giriş yaparsanız verileriniz yedeklenecektir
            </Text>

            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={styles.modalCancelButton}
                onPress={() => setShowGoogleSignInModal(false)}
              >
                <Text style={styles.modalCancelText}>İptal</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.modalSignInButton}
                onPress={handleGoogleSignIn}
              >
                <Ionicons name="logo-google" size={20} color={Colors.surface} />
                <Text style={styles.modalSignInText}>Google ile giriş yap</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

    </View>
  );
}

const styles = StyleSheet.create({
  settingItem: {
    backgroundColor: Colors.surface,
    borderRadius: 12,
    padding: Spacing.md,
    marginBottom: Spacing.sm,
    flexDirection: 'row',
    alignItems: 'center',
    elevation: 2,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  disabledItem: {
    opacity: 0.8,
  },
  settingIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.md,
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    fontSize: FontSize.md,
    fontWeight: FontWeight.medium,
    color: Colors.text,
    marginBottom: 2,
  },
  settingSubtitle: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: Spacing.lg,
  },
  modalContainer: {
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.lg,
    padding: Spacing.xl,
    width: '100%',
    maxWidth: 400,
    alignItems: 'center',
  },
  modalHeader: {
    alignItems: 'center',
    marginBottom: Spacing.lg,
  },
  modalTitle: {
    fontSize: FontSize.lg,
    fontWeight: FontWeight.bold,
    color: Colors.text,
    marginTop: Spacing.md,
    textAlign: 'center',
  },
  modalMessage: {
    fontSize: FontSize.md,
    color: Colors.textSecondary,
    textAlign: 'center',
    marginBottom: Spacing.xl,
    lineHeight: 22,
  },
  modalButtons: {
    flexDirection: 'row',
    gap: Spacing.md,
    width: '100%',
  },
  modalCancelButton: {
    flex: 1,
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.lg,
    borderRadius: BorderRadius.md,
    borderWidth: 1,
    borderColor: Colors.border,
    alignItems: 'center',
  },
  modalCancelText: {
    fontSize: FontSize.md,
    color: Colors.textSecondary,
    fontWeight: FontWeight.medium,
  },
  modalSignInButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.lg,
    borderRadius: BorderRadius.md,
    backgroundColor: Colors.primary,
    gap: Spacing.sm,
  },
  modalSignInText: {
    fontSize: FontSize.md,
    color: Colors.surface,
    fontWeight: FontWeight.medium,
  },
});
