import React, { useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import PropTypes from 'prop-types';

import { Colors } from '../constants/Colors';
import { Spacing, FontSize, FontWeight } from '../constants/Dimensions';

/**
 * Ye<PERSON>den kullanılabilir view toggle component
 * Aylık/sezonluk görünüm geçişi ve diğer toggle ihtiyaçları için
 * 
 * @param {string} mode - Aktif mod ('monthly' | 'seasonal' | custom)
 * @param {Function} onToggle - Mode değiştiğinde çalışacak callback
 * @param {Array} options - Toggle seçenekleri [{key, label, icon}]
 * @param {boolean} disabled - Component disabled durumu
 * @param {Object} style - Ek stil tanımları
 * @param {boolean} showIcons - Icon'lar<PERSON> gö<PERSON>ilsin mi
 * @param {string} size - Component boyutu ('small' | 'medium' | 'large')
 */
const ViewToggle = ({
  mode,
  onToggle,
  options = [
    { key: 'monthly', label: 'Aylık', icon: 'calendar' },
    { key: 'seasonal', label: 'Sezonluk', icon: 'leaf' }
  ],
  disabled = false,
  style = {},
  showIcons = true,
  size = 'medium'
}) => {
  const slideAnimation = useRef(new Animated.Value(0)).current;
  const { width: screenWidth } = Dimensions.get('window');

  // Calculate toggle width based on screen size and options count
  const toggleWidth = Math.min(screenWidth * 0.8, 300);
  const optionWidth = toggleWidth / options.length;

  useEffect(() => {
    const activeIndex = options.findIndex(option => option.key === mode);
    if (activeIndex !== -1) {
      Animated.spring(slideAnimation, {
        toValue: activeIndex * optionWidth,
        useNativeDriver: false,
        tension: 100,
        friction: 8,
      }).start();
    }
  }, [mode, optionWidth, options]);

  const handleToggle = (optionKey) => {
    if (!disabled && onToggle && optionKey !== mode) {
      onToggle(optionKey);
    }
  };

  const getSizeStyles = () => {
    switch (size) {
      case 'small':
        return {
          container: { height: 36 },
          text: { fontSize: FontSize.sm },
          icon: 16,
          padding: Spacing.xs,
        };
      case 'large':
        return {
          container: { height: 52 },
          text: { fontSize: FontSize.lg },
          icon: 24,
          padding: Spacing.md,
        };
      default: // medium
        return {
          container: { height: 44 },
          text: { fontSize: FontSize.md },
          icon: 20,
          padding: Spacing.sm,
        };
    }
  };

  const sizeStyles = getSizeStyles();

  const renderOption = (option, index) => {
    const isActive = option.key === mode;
    
    return (
      <TouchableOpacity
        key={option.key}
        style={[
          styles.option,
          { width: optionWidth },
          sizeStyles.container,
          disabled && styles.optionDisabled
        ]}
        onPress={() => handleToggle(option.key)}
        disabled={disabled}
        accessibilityRole="button"
        accessibilityLabel={option.label}
        accessibilityState={{ 
          selected: isActive,
          disabled 
        }}
        accessibilityHint={`${option.label} görünümüne geç`}
      >
        <View style={styles.optionContent}>
          {showIcons && option.icon && (
            <Ionicons
              name={option.icon}
              size={sizeStyles.icon}
              color={isActive ? Colors.surface : (disabled ? Colors.textSecondary : Colors.text)}
              style={styles.optionIcon}
            />
          )}
          <Text
            style={[
              styles.optionText,
              sizeStyles.text,
              isActive && styles.activeOptionText,
              disabled && styles.disabledOptionText
            ]}
            numberOfLines={1}
          >
            {option.label}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={[styles.container, { width: toggleWidth }, style]}>
      <View style={[
        styles.toggleContainer,
        sizeStyles.container,
        disabled && styles.toggleContainerDisabled
      ]}>
        {/* Animated Background Slider */}
        <Animated.View
          style={[
            styles.slider,
            sizeStyles.container,
            {
              width: optionWidth,
              transform: [{ translateX: slideAnimation }],
            },
            disabled && styles.sliderDisabled
          ]}
        />
        
        {/* Options */}
        {options.map((option, index) => renderOption(option, index))}
      </View>
    </View>
  );
};

ViewToggle.propTypes = {
  mode: PropTypes.string.isRequired,
  onToggle: PropTypes.func.isRequired,
  options: PropTypes.arrayOf(
    PropTypes.shape({
      key: PropTypes.string.isRequired,
      label: PropTypes.string.isRequired,
      icon: PropTypes.string,
    })
  ),
  disabled: PropTypes.bool,
  style: PropTypes.object,
  showIcons: PropTypes.bool,
  size: PropTypes.oneOf(['small', 'medium', 'large']),
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
  },
  toggleContainer: {
    backgroundColor: Colors.background,
    borderRadius: 8,
    flexDirection: 'row',
    position: 'relative',
    borderWidth: 1,
    borderColor: Colors.border,
    overflow: 'hidden',
  },
  toggleContainerDisabled: {
    opacity: 0.6,
  },
  slider: {
    position: 'absolute',
    backgroundColor: Colors.primary,
    borderRadius: 6,
    margin: 2,
    elevation: 2,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  sliderDisabled: {
    backgroundColor: Colors.textSecondary,
  },
  option: {
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  optionDisabled: {
    opacity: 0.6,
  },
  optionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  optionIcon: {
    marginRight: Spacing.xs,
  },
  optionText: {
    fontWeight: FontWeight.medium,
    color: Colors.text,
    textAlign: 'center',
  },
  activeOptionText: {
    color: Colors.surface,
    fontWeight: FontWeight.semibold,
  },
  disabledOptionText: {
    color: Colors.textSecondary,
  },
});

export default ViewToggle;
