import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Modal,
  ScrollView,
  SafeAreaView,
  StatusBar,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { Colors } from '../constants/Colors';
import { Spacing, FontSize, FontWeight, BorderRadius } from '../constants/Dimensions';
import EditableDatePicker from './EditableDatePicker';
import Logger, { LogCategory } from '../utils/Logger';
import Analytics, { AnalyticsEvent } from '../utils/Analytics';

export default function ExpenseSearchFilter({
  visible,
  onClose,
  onApplyFilters,
  categories = [],
  initialFilters = {},
}) {
  const [searchQuery, setSearchQuery] = useState(initialFilters.searchQuery || '');
  const [selectedCategories, setSelectedCategories] = useState(
    new Set(initialFilters.categories || [])
  );
  const [dateRange, setDateRange] = useState({
    startDate: initialFilters.startDate || '',
    endDate: initialFilters.endDate || '',
  });
  const [amountRange, setAmountRange] = useState({
    minAmount: initialFilters.minAmount || '',
    maxAmount: initialFilters.maxAmount || '',
  });
  const [sortBy, setSortBy] = useState(initialFilters.sortBy || 'date');
  const [sortOrder, setSortOrder] = useState(initialFilters.sortOrder || 'desc');

  const slideAnim = useRef(new Animated.Value(300)).current;

  useEffect(() => {
    if (visible) {
      Animated.spring(slideAnim, {
        toValue: 0,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }).start();
    } else {
      Animated.timing(slideAnim, {
        toValue: 300,
        duration: 200,
        useNativeDriver: true,
      }).start();
    }
  }, [visible]);

  const handleApplyFilters = () => {
    const filters = {
      searchQuery: searchQuery.trim(),
      categories: Array.from(selectedCategories),
      startDate: dateRange.startDate,
      endDate: dateRange.endDate,
      minAmount: amountRange.minAmount ? parseFloat(amountRange.minAmount) : null,
      maxAmount: amountRange.maxAmount ? parseFloat(amountRange.maxAmount) : null,
      sortBy,
      sortOrder,
    };

    Analytics.trackEvent(AnalyticsEvent.EXPENSE_FILTER_APPLIED, {
      hasSearchQuery: !!filters.searchQuery,
      categoryCount: filters.categories.length,
      hasDateRange: !!(filters.startDate || filters.endDate),
      hasAmountRange: !!(filters.minAmount || filters.maxAmount),
      sortBy: filters.sortBy,
      sortOrder: filters.sortOrder,
    });

    Logger.info(LogCategory.USER_ACTION, 'Expense filters applied', filters);

    onApplyFilters(filters);
    onClose();
  };

  const handleClearFilters = () => {
    setSearchQuery('');
    setSelectedCategories(new Set());
    setDateRange({ startDate: '', endDate: '' });
    setAmountRange({ minAmount: '', maxAmount: '' });
    setSortBy('date');
    setSortOrder('desc');

    Analytics.trackEvent(AnalyticsEvent.EXPENSE_FILTER_CLEARED);
    Logger.info(LogCategory.USER_ACTION, 'Expense filters cleared');
  };

  const toggleCategory = (categoryId) => {
    const newSelected = new Set(selectedCategories);
    if (newSelected.has(categoryId)) {
      newSelected.delete(categoryId);
    } else {
      newSelected.add(categoryId);
    }
    setSelectedCategories(newSelected);
  };

  const getActiveFilterCount = () => {
    let count = 0;
    if (searchQuery.trim()) count++;
    if (selectedCategories.size > 0) count++;
    if (dateRange.startDate || dateRange.endDate) count++;
    if (amountRange.minAmount || amountRange.maxAmount) count++;
    return count;
  };

  const renderSearchSection = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Arama</Text>
      <View style={styles.searchContainer}>
        <Ionicons name="search-outline" size={20} color={Colors.textSecondary} />
        <TextInput
          style={styles.searchInput}
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholder="Açıklama veya kategori ara..."
          placeholderTextColor={Colors.textSecondary}
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity onPress={() => setSearchQuery('')}>
            <Ionicons name="close-circle" size={20} color={Colors.textSecondary} />
          </TouchableOpacity>
        )}
      </View>
    </View>
  );

  const renderCategorySection = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>
        Kategoriler {selectedCategories.size > 0 && `(${selectedCategories.size})`}
      </Text>
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.categoriesScroll}>
        {categories.map((category) => (
          <TouchableOpacity
            key={category.id}
            style={[
              styles.categoryChip,
              { borderColor: category.color },
              selectedCategories.has(category.id) && [
                styles.categoryChipSelected,
                { backgroundColor: category.color }
              ],
            ]}
            onPress={() => toggleCategory(category.id)}
          >
            <Text style={styles.categoryEmoji}>{category.emoji}</Text>
            <Text
              style={[
                styles.categoryChipText,
                selectedCategories.has(category.id) && styles.categoryChipTextSelected,
              ]}
            >
              {category.name}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  const renderDateSection = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Tarih Aralığı</Text>
      <View style={styles.dateRangeContainer}>
        <View style={styles.dateInputContainer}>
          <Text style={styles.dateLabel}>Başlangıç</Text>
          <EditableDatePicker
            date={dateRange.startDate}
            onDateChange={(date) => setDateRange(prev => ({ ...prev, startDate: date }))}
            placeholder="Başlangıç tarihi"
            style={styles.dateInput}
          />
        </View>
        <View style={styles.dateInputContainer}>
          <Text style={styles.dateLabel}>Bitiş</Text>
          <EditableDatePicker
            date={dateRange.endDate}
            onDateChange={(date) => setDateRange(prev => ({ ...prev, endDate: date }))}
            placeholder="Bitiş tarihi"
            style={styles.dateInput}
          />
        </View>
      </View>
    </View>
  );

  const renderAmountSection = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Tutar Aralığı</Text>
      <View style={styles.amountRangeContainer}>
        <View style={styles.amountInputContainer}>
          <Text style={styles.amountLabel}>Min ₺</Text>
          <TextInput
            style={styles.amountInput}
            value={amountRange.minAmount}
            onChangeText={(text) => setAmountRange(prev => ({ ...prev, minAmount: text }))}
            placeholder="0"
            keyboardType="numeric"
          />
        </View>
        <View style={styles.amountInputContainer}>
          <Text style={styles.amountLabel}>Max ₺</Text>
          <TextInput
            style={styles.amountInput}
            value={amountRange.maxAmount}
            onChangeText={(text) => setAmountRange(prev => ({ ...prev, maxAmount: text }))}
            placeholder="∞"
            keyboardType="numeric"
          />
        </View>
      </View>
    </View>
  );

  const renderSortSection = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Sıralama</Text>
      <View style={styles.sortContainer}>
        <View style={styles.sortByContainer}>
          <Text style={styles.sortLabel}>Sırala:</Text>
          <View style={styles.sortOptions}>
            {[
              { value: 'date', label: 'Tarih' },
              { value: 'amount', label: 'Tutar' },
              { value: 'category', label: 'Kategori' },
            ].map((option) => (
              <TouchableOpacity
                key={option.value}
                style={[
                  styles.sortOption,
                  sortBy === option.value && styles.sortOptionSelected,
                ]}
                onPress={() => setSortBy(option.value)}
              >
                <Text
                  style={[
                    styles.sortOptionText,
                    sortBy === option.value && styles.sortOptionTextSelected,
                  ]}
                >
                  {option.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
        
        <View style={styles.sortOrderContainer}>
          <TouchableOpacity
            style={[
              styles.sortOrderButton,
              sortOrder === 'desc' && styles.sortOrderButtonSelected,
            ]}
            onPress={() => setSortOrder(sortOrder === 'desc' ? 'asc' : 'desc')}
          >
            <Ionicons
              name={sortOrder === 'desc' ? 'arrow-down' : 'arrow-up'}
              size={20}
              color={sortOrder === 'desc' ? Colors.surface : Colors.textSecondary}
            />
            <Text
              style={[
                styles.sortOrderText,
                sortOrder === 'desc' && styles.sortOrderTextSelected,
              ]}
            >
              {sortOrder === 'desc' ? 'Azalan' : 'Artan'}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <SafeAreaView style={styles.modalContainer}>
          <StatusBar backgroundColor="rgba(0,0,0,0.5)" barStyle="light-content" />
          
          <Animated.View
            style={[
              styles.filterPanel,
              {
                transform: [{ translateY: slideAnim }],
              },
            ]}
          >
            {/* Header */}
            <View style={styles.header}>
              <TouchableOpacity style={styles.headerButton} onPress={onClose}>
                <Ionicons name="close" size={24} color={Colors.text} />
              </TouchableOpacity>
              
              <Text style={styles.headerTitle}>
                Filtrele {getActiveFilterCount() > 0 && `(${getActiveFilterCount()})`}
              </Text>
              
              <TouchableOpacity style={styles.headerButton} onPress={handleClearFilters}>
                <Text style={styles.clearButtonText}>Temizle</Text>
              </TouchableOpacity>
            </View>

            {/* Content */}
            <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
              {renderSearchSection()}
              {renderCategorySection()}
              {renderDateSection()}
              {renderAmountSection()}
              {renderSortSection()}
            </ScrollView>

            {/* Footer */}
            <View style={styles.footer}>
              <TouchableOpacity style={styles.applyButton} onPress={handleApplyFilters}>
                <Text style={styles.applyButtonText}>Filtreleri Uygula</Text>
              </TouchableOpacity>
            </View>
          </Animated.View>
        </SafeAreaView>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  filterPanel: {
    backgroundColor: Colors.background,
    borderTopLeftRadius: BorderRadius.xl,
    borderTopRightRadius: BorderRadius.xl,
    maxHeight: '90%',
    minHeight: '60%',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  headerButton: {
    padding: Spacing.sm,
    minWidth: 60,
  },
  headerTitle: {
    fontSize: FontSize.lg,
    fontWeight: FontWeight.bold,
    color: Colors.text,
  },
  clearButtonText: {
    fontSize: FontSize.md,
    color: Colors.primary,
    fontWeight: FontWeight.medium,
  },
  content: {
    flex: 1,
    paddingHorizontal: Spacing.lg,
  },
  section: {
    marginVertical: Spacing.lg,
  },
  sectionTitle: {
    fontSize: FontSize.md,
    fontWeight: FontWeight.bold,
    color: Colors.text,
    marginBottom: Spacing.md,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.lg,
    borderWidth: 1,
    borderColor: Colors.border,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
  },
  searchInput: {
    flex: 1,
    fontSize: FontSize.md,
    color: Colors.text,
    marginLeft: Spacing.sm,
  },
  categoriesScroll: {
    marginTop: Spacing.sm,
  },
  categoryChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.md,
    borderRadius: BorderRadius.lg,
    borderWidth: 1,
    marginRight: Spacing.sm,
    backgroundColor: Colors.surface,
  },
  categoryChipSelected: {
    borderWidth: 2,
  },
  categoryEmoji: {
    fontSize: 16,
    marginRight: Spacing.xs,
  },
  categoryChipText: {
    fontSize: FontSize.sm,
    color: Colors.text,
  },
  categoryChipTextSelected: {
    color: Colors.surface,
    fontWeight: FontWeight.bold,
  },
  dateRangeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  dateInputContainer: {
    flex: 1,
    marginHorizontal: Spacing.xs,
  },
  dateLabel: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
    marginBottom: Spacing.xs,
  },
  dateInput: {
    marginVertical: 0,
  },
  amountRangeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  amountInputContainer: {
    flex: 1,
    marginHorizontal: Spacing.xs,
  },
  amountLabel: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
    marginBottom: Spacing.xs,
  },
  amountInput: {
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.lg,
    borderWidth: 1,
    borderColor: Colors.border,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.md,
    fontSize: FontSize.md,
    color: Colors.text,
  },
  sortContainer: {
    gap: Spacing.md,
  },
  sortByContainer: {
    marginBottom: Spacing.md,
  },
  sortLabel: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
    marginBottom: Spacing.sm,
  },
  sortOptions: {
    flexDirection: 'row',
    gap: Spacing.sm,
  },
  sortOption: {
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.md,
    borderRadius: BorderRadius.md,
    borderWidth: 1,
    borderColor: Colors.border,
    backgroundColor: Colors.surface,
  },
  sortOptionSelected: {
    borderColor: Colors.primary,
    backgroundColor: Colors.primary,
  },
  sortOptionText: {
    fontSize: FontSize.sm,
    color: Colors.text,
  },
  sortOptionTextSelected: {
    color: Colors.surface,
    fontWeight: FontWeight.bold,
  },
  sortOrderContainer: {
    alignItems: 'flex-start',
  },
  sortOrderButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.md,
    borderRadius: BorderRadius.md,
    borderWidth: 1,
    borderColor: Colors.border,
    backgroundColor: Colors.surface,
  },
  sortOrderButtonSelected: {
    borderColor: Colors.primary,
    backgroundColor: Colors.primary,
  },
  sortOrderText: {
    fontSize: FontSize.sm,
    color: Colors.text,
    marginLeft: Spacing.xs,
  },
  sortOrderTextSelected: {
    color: Colors.surface,
    fontWeight: FontWeight.bold,
  },
  footer: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.lg,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  applyButton: {
    backgroundColor: Colors.primary,
    borderRadius: BorderRadius.lg,
    paddingVertical: Spacing.md,
    alignItems: 'center',
  },
  applyButtonText: {
    fontSize: FontSize.md,
    color: Colors.surface,
    fontWeight: FontWeight.bold,
  },
});
