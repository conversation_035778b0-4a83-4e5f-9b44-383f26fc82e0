import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import PropTypes from 'prop-types';

import { Colors } from '../constants/Colors';
import { Spacing, FontSize, FontWeight, BorderRadius } from '../constants/Dimensions';

/**
 * Reusable expense card component
 * 
 * @param {Object} expense - Expense object
 * @param {Object} season - Season object (optional)
 * @param {Function} onPress - Card press handler
 * @param {Function} onEdit - Edit button press handler
 * @param {boolean} showSeason - Whether to show season information
 * @param {boolean} compact - Compact mode for smaller cards
 * @param {Object} style - Additional styles
 */
const ExpenseCard = ({
  expense,
  season,
  onPress,
  onEdit,
  showSeason = false,
  compact = false,
  style = {}
}) => {
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
    }).format(amount);
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('tr-TR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  return (
    <TouchableOpacity
      style={[
        styles.container,
        compact && styles.containerCompact,
        style
      ]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      {/* Season Indicator - SEASON INTEGRATION */}
      {showSeason && season && (
        <View style={[
          styles.seasonIndicator,
          { backgroundColor: season.color || Colors.primary }
        ]}>
          <Text style={styles.seasonEmoji}>{season.emoji || '🌱'}</Text>
          {!compact && (
            <Text style={styles.seasonName} numberOfLines={1}>
              {season.name}
            </Text>
          )}
          {season.isActive && (
            <View style={styles.activeSeasonBadge}>
              <Text style={styles.activeSeasonBadgeText}>Aktif</Text>
            </View>
          )}
        </View>
      )}

      <View style={styles.content}>
        {/* Main Content */}
        <View style={styles.mainContent}>
          <View style={styles.categorySection}>
            <View style={styles.categoryIcon}>
              <Text style={styles.categoryEmoji}>
                {expense.categoryEmoji || '📝'}
              </Text>
            </View>
            
            <View style={styles.expenseInfo}>
              <Text style={styles.categoryName} numberOfLines={1}>
                {expense.categoryName || 'Bilinmeyen'}
              </Text>
              {!compact && expense.description && (
                <Text style={styles.description} numberOfLines={2}>
                  {expense.description}
                </Text>
              )}
              <Text style={styles.date}>
                {formatDate(expense.date)}
              </Text>
            </View>
          </View>

          <View style={styles.amountSection}>
            <Text style={styles.amount}>
              {formatCurrency(expense.amount)}
            </Text>
            {onEdit && (
              <TouchableOpacity
                style={styles.editButton}
                onPress={(e) => {
                  e.stopPropagation();
                  onEdit();
                }}
              >
                <Ionicons name="pencil" size={14} color={Colors.primary} />
              </TouchableOpacity>
            )}
          </View>
        </View>

        {/* Compact Season Info */}
        {showSeason && season && compact && (
          <View style={styles.compactSeasonInfo}>
            <View style={[
              styles.compactSeasonIndicator,
              { backgroundColor: season.color || Colors.primary }
            ]} />
            <Text style={styles.compactSeasonText} numberOfLines={1}>
              {season.name}
            </Text>
            {season.isActive && (
              <Text style={styles.compactActiveText}>Aktif</Text>
            )}
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
};

ExpenseCard.propTypes = {
  expense: PropTypes.object.isRequired,
  season: PropTypes.object,
  onPress: PropTypes.func,
  onEdit: PropTypes.func,
  showSeason: PropTypes.bool,
  compact: PropTypes.bool,
  style: PropTypes.object,
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.md,
    marginVertical: Spacing.xs,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  containerCompact: {
    marginVertical: Spacing.xs / 2,
  },
  // SEASON INTEGRATION: Season indicator styles
  seasonIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderTopLeftRadius: BorderRadius.md,
    borderTopRightRadius: BorderRadius.md,
  },
  seasonEmoji: {
    fontSize: 16,
    marginRight: Spacing.xs,
  },
  seasonName: {
    flex: 1,
    fontSize: FontSize.sm,
    fontWeight: FontWeight.medium,
    color: Colors.surface,
  },
  activeSeasonBadge: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    paddingHorizontal: Spacing.xs,
    paddingVertical: 2,
    borderRadius: BorderRadius.sm,
  },
  activeSeasonBadgeText: {
    fontSize: FontSize.xs,
    fontWeight: FontWeight.medium,
    color: Colors.surface,
  },
  content: {
    padding: Spacing.md,
  },
  mainContent: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  categorySection: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  categoryIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.background,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.sm,
  },
  categoryEmoji: {
    fontSize: 20,
  },
  expenseInfo: {
    flex: 1,
  },
  categoryName: {
    fontSize: FontSize.md,
    fontWeight: FontWeight.semibold,
    color: Colors.text,
    marginBottom: 2,
  },
  description: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
    lineHeight: 18,
    marginBottom: Spacing.xs,
  },
  date: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
  },
  amountSection: {
    alignItems: 'flex-end',
  },
  amount: {
    fontSize: FontSize.lg,
    fontWeight: FontWeight.bold,
    color: Colors.text,
    marginBottom: Spacing.xs,
  },
  editButton: {
    padding: Spacing.xs,
    borderRadius: BorderRadius.sm,
    backgroundColor: Colors.background,
  },
  // Compact season info styles
  compactSeasonInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: Spacing.sm,
    paddingTop: Spacing.sm,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  compactSeasonIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: Spacing.xs,
  },
  compactSeasonText: {
    flex: 1,
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
  },
  compactActiveText: {
    fontSize: FontSize.xs,
    fontWeight: FontWeight.medium,
    color: Colors.success,
  },
});

export default ExpenseCard;
