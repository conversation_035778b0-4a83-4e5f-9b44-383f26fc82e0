import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  PanGestureHandler,
  Animated,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { Colors } from '../constants/Colors';
import { Spacing, FontSize, FontWeight, BorderRadius } from '../constants/Dimensions';
import Logger, { LogCategory } from '../utils/Logger';
import Analytics, { AnalyticsEvent } from '../utils/Analytics';

export default function CategoryReorderList({
  categories,
  onReorder,
  onSave,
  showUsageStats = true,
}) {
  const [reorderedCategories, setReorderedCategories] = useState([...categories]);
  const [draggedIndex, setDraggedIndex] = useState(-1);
  const [hasChanges, setHasChanges] = useState(false);
  
  const dragY = useRef(new Animated.Value(0)).current;
  const itemHeight = 80;

  const handleDragStart = (index) => {
    setDraggedIndex(index);
    Analytics.trackEvent(AnalyticsEvent.CATEGORY_REORDER_STARTED, {
      categoryId: reorderedCategories[index].id,
      originalPosition: index,
    });
  };

  const handleDragEnd = () => {
    if (draggedIndex >= 0) {
      Analytics.trackEvent(AnalyticsEvent.CATEGORY_REORDER_COMPLETED, {
        categoryId: reorderedCategories[draggedIndex].id,
        originalPosition: draggedIndex,
        newPosition: draggedIndex, // This would be updated with actual new position
      });
    }
    setDraggedIndex(-1);
    dragY.setValue(0);
  };

  const moveCategory = (fromIndex, toIndex) => {
    if (fromIndex === toIndex) return;

    const newCategories = [...reorderedCategories];
    const [movedCategory] = newCategories.splice(fromIndex, 1);
    newCategories.splice(toIndex, 0, movedCategory);
    
    setReorderedCategories(newCategories);
    setHasChanges(true);
    
    if (onReorder) {
      onReorder(newCategories);
    }

    Logger.info(LogCategory.USER_ACTION, 'Category reordered', {
      categoryId: movedCategory.id,
      fromIndex,
      toIndex,
    });
  };

  const handleSave = async () => {
    try {
      if (!hasChanges) {
        Alert.alert('Bilgi', 'Herhangi bir değişiklik yapılmadı.');
        return;
      }

      // Save new order
      if (onSave) {
        await onSave(reorderedCategories);
      }

      Analytics.trackEvent(AnalyticsEvent.CATEGORY_ORDER_SAVED, {
        categoryCount: reorderedCategories.length,
        changesCount: getChangesCount(),
      });

      setHasChanges(false);
      Alert.alert('Başarılı', 'Kategori sıralaması kaydedildi.');
    } catch (error) {
      Logger.error(LogCategory.USER_ACTION, 'Failed to save category order', error);
      Alert.alert('Hata', 'Sıralama kaydedilirken bir hata oluştu.');
    }
  };

  const handleReset = () => {
    Alert.alert(
      'Sıralamayı Sıfırla',
      'Kategori sıralamasını orijinal haline döndürmek istiyor musunuz?',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sıfırla',
          onPress: () => {
            setReorderedCategories([...categories]);
            setHasChanges(false);
            Analytics.trackEvent(AnalyticsEvent.CATEGORY_ORDER_RESET);
          },
        },
      ]
    );
  };

  const handleAutoSort = () => {
    Alert.alert(
      'Otomatik Sırala',
      'Kategorileri kullanım sıklığına göre otomatik sıralamak istiyor musunuz?',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sırala',
          onPress: () => {
            const sortedCategories = [...reorderedCategories].sort((a, b) => {
              // Sort by usage count (descending) then by name
              const usageA = a.usageCount || 0;
              const usageB = b.usageCount || 0;
              
              if (usageA !== usageB) {
                return usageB - usageA;
              }
              
              return a.name.localeCompare(b.name, 'tr');
            });
            
            setReorderedCategories(sortedCategories);
            setHasChanges(true);
            
            Analytics.trackEvent(AnalyticsEvent.CATEGORY_AUTO_SORTED, {
              sortMethod: 'usage_frequency',
            });
          },
        },
      ]
    );
  };

  const getChangesCount = () => {
    let changes = 0;
    for (let i = 0; i < categories.length; i++) {
      if (categories[i].id !== reorderedCategories[i].id) {
        changes++;
      }
    }
    return changes;
  };

  const renderCategoryItem = (category, index) => {
    const isDragged = draggedIndex === index;
    
    return (
      <Animated.View
        key={category.id}
        style={[
          styles.categoryItem,
          { borderColor: category.color },
          isDragged && styles.categoryItemDragged,
        ]}
      >
        <View style={styles.categoryContent}>
          {/* Drag Handle */}
          <PanGestureHandler
            onGestureEvent={Animated.event(
              [{ nativeEvent: { translationY: dragY } }],
              { useNativeDriver: false }
            )}
            onHandlerStateChange={(event) => {
              if (event.nativeEvent.state === 4) { // BEGAN
                handleDragStart(index);
              } else if (event.nativeEvent.state === 5) { // END
                handleDragEnd();
              }
            }}
          >
            <View style={styles.dragHandle}>
              <Ionicons name="reorder-three-outline" size={24} color={Colors.textSecondary} />
            </View>
          </PanGestureHandler>

          {/* Category Info */}
          <View style={styles.categoryInfo}>
            <View style={styles.categoryHeader}>
              <Text style={styles.categoryEmoji}>{category.emoji}</Text>
              <View style={styles.categoryDetails}>
                <Text style={styles.categoryName}>{category.name}</Text>
                {category.description && (
                  <Text style={styles.categoryDescription}>{category.description}</Text>
                )}
              </View>
            </View>
            
            {showUsageStats && (
              <View style={styles.usageStats}>
                <Text style={styles.usageText}>
                  {category.usageCount || 0} kullanım
                </Text>
                {category.lastUsed && (
                  <Text style={styles.lastUsedText}>
                    Son: {new Date(category.lastUsed).toLocaleDateString('tr-TR')}
                  </Text>
                )}
              </View>
            )}
          </View>

          {/* Position Indicator */}
          <View style={styles.positionIndicator}>
            <Text style={styles.positionText}>{index + 1}</Text>
          </View>

          {/* Move Buttons */}
          <View style={styles.moveButtons}>
            <TouchableOpacity
              style={[styles.moveButton, index === 0 && styles.moveButtonDisabled]}
              onPress={() => moveCategory(index, index - 1)}
              disabled={index === 0}
            >
              <Ionicons 
                name="chevron-up" 
                size={20} 
                color={index === 0 ? Colors.border : Colors.textSecondary} 
              />
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.moveButton, 
                index === reorderedCategories.length - 1 && styles.moveButtonDisabled
              ]}
              onPress={() => moveCategory(index, index + 1)}
              disabled={index === reorderedCategories.length - 1}
            >
              <Ionicons 
                name="chevron-down" 
                size={20} 
                color={index === reorderedCategories.length - 1 ? Colors.border : Colors.textSecondary} 
              />
            </TouchableOpacity>
          </View>
        </View>
      </Animated.View>
    );
  };

  const renderHeader = () => (
    <View style={styles.header}>
      <Text style={styles.headerTitle}>Kategori Sıralaması</Text>
      <Text style={styles.headerDescription}>
        Kategorileri sürükleyerek veya ok tuşlarıyla yeniden sıralayın
      </Text>
      
      <View style={styles.headerActions}>
        <TouchableOpacity style={styles.actionButton} onPress={handleAutoSort}>
          <Ionicons name="analytics-outline" size={20} color={Colors.primary} />
          <Text style={styles.actionButtonText}>Otomatik Sırala</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.actionButton} onPress={handleReset}>
          <Ionicons name="refresh-outline" size={20} color={Colors.textSecondary} />
          <Text style={styles.actionButtonText}>Sıfırla</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderFooter = () => (
    <View style={styles.footer}>
      {hasChanges && (
        <View style={styles.changesIndicator}>
          <Ionicons name="information-circle-outline" size={16} color={Colors.warning} />
          <Text style={styles.changesText}>
            {getChangesCount()} kategori yeniden sıralandı
          </Text>
        </View>
      )}
      
      <TouchableOpacity
        style={[
          styles.saveButton,
          !hasChanges && styles.saveButtonDisabled,
        ]}
        onPress={handleSave}
        disabled={!hasChanges}
      >
        <Text style={styles.saveButtonText}>Sıralamayı Kaydet</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={styles.container}>
      {renderHeader()}
      
      <ScrollView style={styles.categoriesList} showsVerticalScrollIndicator={false}>
        {reorderedCategories.map((category, index) => renderCategoryItem(category, index))}
      </ScrollView>
      
      {renderFooter()}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    padding: Spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  headerTitle: {
    fontSize: FontSize.xl,
    fontWeight: FontWeight.bold,
    color: Colors.text,
    marginBottom: Spacing.sm,
  },
  headerDescription: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
    marginBottom: Spacing.lg,
  },
  headerActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.md,
    borderRadius: BorderRadius.md,
    backgroundColor: Colors.surface,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  actionButtonText: {
    fontSize: FontSize.sm,
    color: Colors.text,
    marginLeft: Spacing.xs,
  },
  categoriesList: {
    flex: 1,
    paddingHorizontal: Spacing.lg,
  },
  categoryItem: {
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.lg,
    borderWidth: 1,
    marginVertical: Spacing.sm,
    overflow: 'hidden',
  },
  categoryItemDragged: {
    elevation: 8,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  categoryContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: Spacing.md,
  },
  dragHandle: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.md,
  },
  categoryInfo: {
    flex: 1,
    marginLeft: Spacing.sm,
  },
  categoryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.xs,
  },
  categoryEmoji: {
    fontSize: 24,
    marginRight: Spacing.sm,
  },
  categoryDetails: {
    flex: 1,
  },
  categoryName: {
    fontSize: FontSize.md,
    fontWeight: FontWeight.medium,
    color: Colors.text,
  },
  categoryDescription: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
    marginTop: Spacing.xs,
  },
  usageStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  usageText: {
    fontSize: FontSize.xs,
    color: Colors.primary,
    fontWeight: FontWeight.medium,
  },
  lastUsedText: {
    fontSize: FontSize.xs,
    color: Colors.textSecondary,
  },
  positionIndicator: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: Colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: Spacing.sm,
  },
  positionText: {
    fontSize: FontSize.sm,
    color: Colors.surface,
    fontWeight: FontWeight.bold,
  },
  moveButtons: {
    justifyContent: 'space-between',
    height: 50,
  },
  moveButton: {
    padding: Spacing.xs,
  },
  moveButtonDisabled: {
    opacity: 0.3,
  },
  footer: {
    padding: Spacing.lg,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  changesIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.md,
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.md,
    backgroundColor: Colors.warningLight,
    borderRadius: BorderRadius.md,
  },
  changesText: {
    fontSize: FontSize.sm,
    color: Colors.warning,
    marginLeft: Spacing.sm,
  },
  saveButton: {
    backgroundColor: Colors.primary,
    borderRadius: BorderRadius.lg,
    paddingVertical: Spacing.md,
    alignItems: 'center',
  },
  saveButtonDisabled: {
    backgroundColor: Colors.border,
  },
  saveButtonText: {
    fontSize: FontSize.md,
    color: Colors.surface,
    fontWeight: FontWeight.bold,
  },
});
