import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import DatePicker from 'react-native-date-picker';
import { Ionicons } from '@expo/vector-icons';

import { Colors } from '../constants/Colors';
import { Spacing, FontSize, FontWeight, BorderRadius } from '../constants/Dimensions';

export default function EditableDatePicker({
  date,
  onDateChange,
  minimumDate,
  maximumDate,
  mode = 'date',
  placeholder = 'Tarih seçin',
  style,
  disabled = false,
  error,
}) {
  const [isPickerVisible, setIsPickerVisible] = useState(false);
  const [selectedDate, setSelectedDate] = useState(date ? new Date(date) : new Date());

  const handleDateConfirm = () => {
    onDateChange(selectedDate.toISOString().split('T')[0]);
    setIsPickerVisible(false);
  };

  const handleDateCancel = () => {
    setSelectedDate(date ? new Date(date) : new Date());
    setIsPickerVisible(false);
  };

  const formatDate = (dateString) => {
    if (!dateString) return placeholder;
    
    const dateObj = new Date(dateString);
    return dateObj.toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const getDateDisplayStyle = () => {
    if (!date) return styles.placeholderText;
    if (error) return styles.errorDateText;
    return styles.dateText;
  };

  return (
    <View style={[styles.container, style]}>
      <TouchableOpacity
        style={[
          styles.dateSelector,
          error && styles.dateSelectorError,
          disabled && styles.dateSelectorDisabled,
        ]}
        onPress={() => !disabled && setIsPickerVisible(true)}
        disabled={disabled}
      >
        <Ionicons 
          name="calendar-outline" 
          size={24} 
          color={error ? Colors.error : Colors.primary} 
        />
        <Text style={getDateDisplayStyle()}>
          {formatDate(date)}
        </Text>
        <Ionicons 
          name="chevron-down" 
          size={20} 
          color={disabled ? Colors.border : Colors.textSecondary} 
        />
      </TouchableOpacity>

      {error && (
        <Text style={styles.errorText}>{error}</Text>
      )}

      <Modal
        visible={isPickerVisible}
        transparent
        animationType="slide"
        onRequestClose={handleDateCancel}
      >
        <View style={styles.modalOverlay}>
          <SafeAreaView style={styles.modalContainer}>
            <StatusBar backgroundColor="rgba(0,0,0,0.5)" barStyle="light-content" />
            
            {/* Header */}
            <View style={styles.modalHeader}>
              <TouchableOpacity style={styles.modalButton} onPress={handleDateCancel}>
                <Text style={styles.modalCancelText}>İptal</Text>
              </TouchableOpacity>
              
              <Text style={styles.modalTitle}>Tarih Seç</Text>
              
              <TouchableOpacity style={styles.modalButton} onPress={handleDateConfirm}>
                <Text style={styles.modalConfirmText}>Tamam</Text>
              </TouchableOpacity>
            </View>

            {/* Date Picker */}
            <View style={styles.pickerContainer}>
              <DatePicker
                date={selectedDate}
                onDateChange={setSelectedDate}
                mode={mode}
                minimumDate={minimumDate}
                maximumDate={maximumDate}
                locale="tr"
                textColor={Colors.text}
                fadeToColor={Colors.background}
                style={styles.datePicker}
              />
            </View>

            {/* Quick Date Options */}
            <View style={styles.quickOptionsContainer}>
              <Text style={styles.quickOptionsTitle}>Hızlı Seçim</Text>
              <View style={styles.quickOptions}>
                <TouchableOpacity
                  style={styles.quickOption}
                  onPress={() => {
                    const weekAgo = new Date();
                    weekAgo.setDate(weekAgo.getDate() - 7);
                    setSelectedDate(weekAgo);
                    onDateChange(weekAgo.toISOString().split('T')[0]);
                    setIsPickerVisible(false);
                  }}
                >
                  <Text style={styles.quickOptionText}>1 Hafta Önce</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.quickOption}
                  onPress={() => {
                    const yesterday = new Date();
                    yesterday.setDate(yesterday.getDate() - 1);
                    setSelectedDate(yesterday);
                    onDateChange(yesterday.toISOString().split('T')[0]);
                    setIsPickerVisible(false);
                  }}
                >
                  <Text style={styles.quickOptionText}>Dün</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.quickOption}
                  onPress={() => {
                    const today = new Date();
                    setSelectedDate(today);
                    onDateChange(today.toISOString().split('T')[0]);
                    setIsPickerVisible(false);
                  }}
                >
                  <Text style={styles.quickOptionText}>Bugün</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.quickOption}
                  onPress={() => {
                    const weekLater = new Date();
                    weekLater.setDate(weekLater.getDate() + 7);
                    setSelectedDate(weekLater);
                    onDateChange(weekLater.toISOString().split('T')[0]);
                    setIsPickerVisible(false);
                  }}
                >
                  <Text style={styles.quickOptionText}>1 Hafta Sonra</Text>
                </TouchableOpacity>
              </View>
            </View>
          </SafeAreaView>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginVertical: Spacing.sm,
  },
  dateSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: Spacing.lg,
    borderRadius: BorderRadius.lg,
    borderWidth: 1,
    borderColor: Colors.border,
    backgroundColor: Colors.surface,
    minHeight: 56,
  },
  dateSelectorError: {
    borderColor: Colors.error,
  },
  dateSelectorDisabled: {
    backgroundColor: Colors.disabled,
    opacity: 0.6,
  },
  dateText: {
    fontSize: FontSize.md,
    color: Colors.text,
    flex: 1,
    marginLeft: Spacing.md,
    fontWeight: FontWeight.medium,
  },
  placeholderText: {
    fontSize: FontSize.md,
    color: Colors.textSecondary,
    flex: 1,
    marginLeft: Spacing.md,
  },
  errorDateText: {
    fontSize: FontSize.md,
    color: Colors.error,
    flex: 1,
    marginLeft: Spacing.md,
    fontWeight: FontWeight.medium,
  },
  errorText: {
    fontSize: FontSize.sm,
    color: Colors.error,
    marginTop: Spacing.xs,
    marginLeft: Spacing.sm,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContainer: {
    backgroundColor: Colors.background,
    borderTopLeftRadius: BorderRadius.xl,
    borderTopRightRadius: BorderRadius.xl,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  modalButton: {
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.md,
    minWidth: 60,
  },
  modalCancelText: {
    fontSize: FontSize.md,
    color: Colors.textSecondary,
  },
  modalTitle: {
    fontSize: FontSize.lg,
    fontWeight: FontWeight.bold,
    color: Colors.text,
  },
  modalConfirmText: {
    fontSize: FontSize.md,
    color: Colors.primary,
    fontWeight: FontWeight.medium,
    textAlign: 'right',
  },
  pickerContainer: {
    alignItems: 'center',
    paddingVertical: Spacing.xl,
  },
  datePicker: {
    height: 200,
  },
  quickOptionsContainer: {
    paddingHorizontal: Spacing.lg,
    paddingBottom: Spacing.xl,
  },
  quickOptionsTitle: {
    fontSize: FontSize.md,
    fontWeight: FontWeight.medium,
    color: Colors.text,
    marginBottom: Spacing.md,
  },
  quickOptions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  quickOption: {
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.md,
    borderRadius: BorderRadius.md,
    borderWidth: 1,
    borderColor: Colors.border,
    backgroundColor: Colors.surface,
    minWidth: 80,
    alignItems: 'center',
  },
  quickOptionText: {
    fontSize: FontSize.sm,
    color: Colors.text,
    textAlign: 'center',
  },
});

// Enhanced Date Picker with more features
export const EnhancedDatePicker = ({
  date,
  onDateChange,
  label,
  required = false,
  showTime = false,
  allowFuture = true,
  allowPast = true,
  style,
  error,
}) => {
  const getMinimumDate = () => {
    if (!allowPast) return new Date();
    // Allow up to 10 years in the past
    const minDate = new Date();
    minDate.setFullYear(minDate.getFullYear() - 10);
    return minDate;
  };

  const getMaximumDate = () => {
    if (!allowFuture) return new Date();
    // Allow up to 1 year in the future
    const maxDate = new Date();
    maxDate.setFullYear(maxDate.getFullYear() + 1);
    return maxDate;
  };

  return (
    <View style={[styles.enhancedContainer, style]}>
      {label && (
        <Text style={styles.label}>
          {label}
          {required && <Text style={styles.required}> *</Text>}
        </Text>
      )}
      
      <EditableDatePicker
        date={date}
        onDateChange={onDateChange}
        mode={showTime ? 'datetime' : 'date'}
        minimumDate={getMinimumDate()}
        maximumDate={getMaximumDate()}
        error={error}
      />
    </View>
  );
};

const enhancedStyles = StyleSheet.create({
  enhancedContainer: {
    marginVertical: Spacing.md,
  },
  label: {
    fontSize: FontSize.md,
    fontWeight: FontWeight.medium,
    color: Colors.text,
    marginBottom: Spacing.sm,
  },
  required: {
    color: Colors.error,
  },
});
