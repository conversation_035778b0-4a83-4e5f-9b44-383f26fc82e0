import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { Colors } from '../constants/Colors';
import { Spacing, FontSize, FontWeight, BorderRadius } from '../constants/Dimensions';
import { formatAmount } from '../utils/validation';
import { getCurrentSeason, getSeasonProgress } from '../utils/SeasonDetection';
import { AgriculturalSeasons, SeasonalHelpers } from '../models/SeasonalModels';
import Logger, { LogCategory } from '../utils/Logger';
import Analytics, { AnalyticsEvent } from '../utils/Analytics';

const { width: screenWidth } = Dimensions.get('window');

export default function SeasonalDashboard({
  expenses = [],
  budgets = [],
  onSeasonChange,
  onViewDetails,
}) {
  const [currentSeasonInfo, setCurrentSeasonInfo] = useState(null);
  const [seasonalData, setSeasonalData] = useState({});
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadSeasonalData();
    Analytics.trackEvent(AnalyticsEvent.SEASONAL_DASHBOARD_VIEWED, {
      year: selectedYear,
    });
  }, [selectedYear, expenses, budgets]);

  const loadSeasonalData = async () => {
    try {
      setLoading(true);
      
      // Get current season information
      const seasonInfo = getCurrentSeason();
      setCurrentSeasonInfo(seasonInfo);
      
      // Calculate seasonal data
      const data = calculateSeasonalData();
      setSeasonalData(data);
      
      Logger.debug(LogCategory.DASHBOARD, 'Seasonal data loaded', {
        currentSeason: seasonInfo.season.id,
        year: selectedYear,
        dataKeys: Object.keys(data),
      });
    } catch (error) {
      Logger.error(LogCategory.DASHBOARD, 'Failed to load seasonal data', error);
    } finally {
      setLoading(false);
    }
  };

  const calculateSeasonalData = () => {
    const seasons = SeasonalHelpers.getAllSeasons();
    const data = {};

    seasons.forEach(season => {
      const seasonExpenses = getSeasonExpenses(season.id, selectedYear);
      const seasonBudget = getSeasonBudget(season.id, selectedYear);
      
      data[season.id] = {
        season,
        expenses: seasonExpenses,
        totalAmount: seasonExpenses.reduce((sum, exp) => sum + exp.amount, 0),
        expenseCount: seasonExpenses.length,
        budget: seasonBudget,
        budgetUsed: seasonBudget ? (seasonExpenses.reduce((sum, exp) => sum + exp.amount, 0) / seasonBudget.amount) * 100 : 0,
        averageExpense: seasonExpenses.length > 0 ? seasonExpenses.reduce((sum, exp) => sum + exp.amount, 0) / seasonExpenses.length : 0,
        topCategory: getTopCategory(seasonExpenses),
        progress: season.id === currentSeasonInfo?.season.id ? getSeasonProgress() : 100,
      };
    });

    return data;
  };

  const getSeasonExpenses = (seasonId, year) => {
    return expenses.filter(expense => {
      const expenseDate = new Date(expense.date);
      const expenseYear = expenseDate.getFullYear();
      const expenseSeason = SeasonalHelpers.getSeasonByDate(expense.date);
      
      return expenseYear === year && expenseSeason.id === seasonId;
    });
  };

  const getSeasonBudget = (seasonId, year) => {
    return budgets.find(budget => 
      budget.seasonId === seasonId && budget.year === year
    );
  };

  const getTopCategory = (seasonExpenses) => {
    if (seasonExpenses.length === 0) return null;
    
    const categoryTotals = {};
    seasonExpenses.forEach(expense => {
      categoryTotals[expense.categoryId] = (categoryTotals[expense.categoryId] || 0) + expense.amount;
    });
    
    const topCategoryId = Object.keys(categoryTotals).reduce((a, b) => 
      categoryTotals[a] > categoryTotals[b] ? a : b
    );
    
    return {
      categoryId: topCategoryId,
      amount: categoryTotals[topCategoryId],
    };
  };

  const renderCurrentSeasonCard = () => {
    if (!currentSeasonInfo) return null;
    
    const { season, progress, recommendations } = currentSeasonInfo;
    const seasonData = seasonalData[season.id];
    
    return (
      <View style={[styles.currentSeasonCard, { borderColor: season.color }]}>
        <View style={styles.currentSeasonHeader}>
          <View style={styles.seasonInfo}>
            <Text style={styles.currentSeasonEmoji}>{season.emoji}</Text>
            <View>
              <Text style={styles.currentSeasonName}>{season.name}</Text>
              <Text style={styles.currentSeasonDescription}>{season.description}</Text>
            </View>
          </View>
          <View style={styles.seasonProgress}>
            <Text style={styles.progressText}>{progress}%</Text>
            <View style={styles.progressBar}>
              <View 
                style={[
                  styles.progressFill, 
                  { width: `${progress}%`, backgroundColor: season.color }
                ]} 
              />
            </View>
          </View>
        </View>
        
        {seasonData && (
          <View style={styles.currentSeasonStats}>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{formatAmount(seasonData.totalAmount)}</Text>
              <Text style={styles.statLabel}>Bu Sezon Harcama</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{seasonData.expenseCount}</Text>
              <Text style={styles.statLabel}>Gider Sayısı</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={[styles.statValue, { color: season.color }]}>
                {seasonData.budgetUsed.toFixed(0)}%
              </Text>
              <Text style={styles.statLabel}>Bütçe Kullanımı</Text>
            </View>
          </View>
        )}
        
        <TouchableOpacity 
          style={styles.viewDetailsButton}
          onPress={() => onViewDetails?.(season.id)}
        >
          <Text style={styles.viewDetailsText}>Detayları Görüntüle</Text>
          <Ionicons name="chevron-forward" size={16} color={Colors.primary} />
        </TouchableOpacity>
      </View>
    );
  };

  const renderSeasonCard = (seasonId) => {
    const data = seasonalData[seasonId];
    if (!data) return null;
    
    const { season, totalAmount, expenseCount, budgetUsed, topCategory } = data;
    const isCurrentSeason = currentSeasonInfo?.season.id === seasonId;
    
    return (
      <TouchableOpacity
        key={seasonId}
        style={[
          styles.seasonCard,
          { borderColor: season.color },
          isCurrentSeason && styles.currentSeasonHighlight,
        ]}
        onPress={() => onSeasonChange?.(seasonId)}
      >
        <View style={styles.seasonCardHeader}>
          <Text style={styles.seasonEmoji}>{season.emoji}</Text>
          <View style={styles.seasonCardInfo}>
            <Text style={styles.seasonName}>{season.shortName}</Text>
            <Text style={styles.seasonMonths}>
              {season.months.map(m => {
                const monthNames = ['Oca', 'Şub', 'Mar', 'Nis', 'May', 'Haz', 
                                 'Tem', 'Ağu', 'Eyl', 'Eki', 'Kas', 'Ara'];
                return monthNames[m - 1];
              }).join(' - ')}
            </Text>
          </View>
          {isCurrentSeason && (
            <View style={styles.currentBadge}>
              <Text style={styles.currentBadgeText}>Aktif</Text>
            </View>
          )}
        </View>
        
        <View style={styles.seasonCardStats}>
          <View style={styles.seasonStat}>
            <Text style={styles.seasonStatValue}>{formatAmount(totalAmount)}</Text>
            <Text style={styles.seasonStatLabel}>Toplam</Text>
          </View>
          <View style={styles.seasonStat}>
            <Text style={styles.seasonStatValue}>{expenseCount}</Text>
            <Text style={styles.seasonStatLabel}>Gider</Text>
          </View>
          <View style={styles.seasonStat}>
            <Text style={[styles.seasonStatValue, { color: season.color }]}>
              {budgetUsed.toFixed(0)}%
            </Text>
            <Text style={styles.seasonStatLabel}>Bütçe</Text>
          </View>
        </View>
        
        {topCategory && (
          <View style={styles.topCategory}>
            <Text style={styles.topCategoryText}>
              En çok: {topCategory.categoryId} ({formatAmount(topCategory.amount)})
            </Text>
          </View>
        )}
      </TouchableOpacity>
    );
  };

  const renderYearSelector = () => {
    const currentYear = new Date().getFullYear();
    const years = [currentYear - 2, currentYear - 1, currentYear, currentYear + 1];
    
    return (
      <View style={styles.yearSelector}>
        <Text style={styles.yearSelectorTitle}>Yıl Seçin</Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {years.map(year => (
            <TouchableOpacity
              key={year}
              style={[
                styles.yearButton,
                selectedYear === year && styles.yearButtonActive,
              ]}
              onPress={() => setSelectedYear(year)}
            >
              <Text
                style={[
                  styles.yearButtonText,
                  selectedYear === year && styles.yearButtonTextActive,
                ]}
              >
                {year}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
    );
  };

  const renderSeasonalComparison = () => {
    const seasons = SeasonalHelpers.getAllSeasons();
    const maxAmount = Math.max(...seasons.map(s => seasonalData[s.id]?.totalAmount || 0));
    
    return (
      <View style={styles.comparisonSection}>
        <Text style={styles.sectionTitle}>Sezonsal Karşılaştırma</Text>
        {seasons.map(season => {
          const data = seasonalData[season.id];
          if (!data) return null;
          
          const percentage = maxAmount > 0 ? (data.totalAmount / maxAmount) * 100 : 0;
          
          return (
            <View key={season.id} style={styles.comparisonItem}>
              <View style={styles.comparisonHeader}>
                <Text style={styles.comparisonEmoji}>{season.emoji}</Text>
                <Text style={styles.comparisonName}>{season.shortName}</Text>
                <Text style={styles.comparisonAmount}>{formatAmount(data.totalAmount)}</Text>
              </View>
              <View style={styles.comparisonBar}>
                <View
                  style={[
                    styles.comparisonBarFill,
                    { 
                      width: `${percentage}%`,
                      backgroundColor: season.color,
                    }
                  ]}
                />
              </View>
            </View>
          );
        })}
      </View>
    );
  };

  const renderInsights = () => {
    const insights = generateSeasonalInsights();
    
    return (
      <View style={styles.insightsSection}>
        <Text style={styles.sectionTitle}>Sezonsal İçgörüler</Text>
        {insights.map((insight, index) => (
          <View key={index} style={styles.insightCard}>
            <Ionicons name={insight.icon} size={20} color={Colors.primary} />
            <Text style={styles.insightText}>{insight.text}</Text>
          </View>
        ))}
      </View>
    );
  };

  const generateSeasonalInsights = () => {
    const insights = [];
    const seasons = SeasonalHelpers.getAllSeasons();
    
    // Find most expensive season
    const mostExpensiveSeason = seasons.reduce((max, season) => {
      const seasonData = seasonalData[season.id];
      const maxData = seasonalData[max.id];
      return (seasonData?.totalAmount || 0) > (maxData?.totalAmount || 0) ? season : max;
    });
    
    insights.push({
      icon: 'trending-up-outline',
      text: `En yüksek harcama ${mostExpensiveSeason.name} döneminde yapıldı`,
    });
    
    // Find most active season
    const mostActiveSeason = seasons.reduce((max, season) => {
      const seasonData = seasonalData[season.id];
      const maxData = seasonalData[max.id];
      return (seasonData?.expenseCount || 0) > (maxData?.expenseCount || 0) ? season : max;
    });
    
    insights.push({
      icon: 'pulse-outline',
      text: `En aktif dönem ${mostActiveSeason.name} (${seasonalData[mostActiveSeason.id]?.expenseCount || 0} gider)`,
    });
    
    // Budget efficiency
    const efficientSeasons = seasons.filter(season => {
      const data = seasonalData[season.id];
      return data && data.budgetUsed > 0 && data.budgetUsed < 90;
    });
    
    if (efficientSeasons.length > 0) {
      insights.push({
        icon: 'checkmark-circle-outline',
        text: `${efficientSeasons.length} sezonda bütçe hedeflerinize ulaştınız`,
      });
    }
    
    return insights;
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Sezonsal veriler yükleniyor...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {renderYearSelector()}
      {renderCurrentSeasonCard()}
      
      <View style={styles.seasonsGrid}>
        {SeasonalHelpers.getAllSeasons().map(season => renderSeasonCard(season.id))}
      </View>
      
      {renderSeasonalComparison()}
      {renderInsights()}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Spacing.xl,
  },
  loadingText: {
    fontSize: FontSize.md,
    color: Colors.textSecondary,
  },
  yearSelector: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  yearSelectorTitle: {
    fontSize: FontSize.md,
    fontWeight: FontWeight.medium,
    color: Colors.text,
    marginBottom: Spacing.sm,
  },
  yearButton: {
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.lg,
    borderRadius: BorderRadius.md,
    marginRight: Spacing.sm,
    backgroundColor: Colors.surface,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  yearButtonActive: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  yearButtonText: {
    fontSize: FontSize.sm,
    color: Colors.text,
    fontWeight: FontWeight.medium,
  },
  yearButtonTextActive: {
    color: Colors.surface,
    fontWeight: FontWeight.bold,
  },
  currentSeasonCard: {
    margin: Spacing.lg,
    padding: Spacing.lg,
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.lg,
    borderWidth: 2,
  },
  currentSeasonHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  seasonInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  currentSeasonEmoji: {
    fontSize: 32,
    marginRight: Spacing.md,
  },
  currentSeasonName: {
    fontSize: FontSize.lg,
    fontWeight: FontWeight.bold,
    color: Colors.text,
  },
  currentSeasonDescription: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
    marginTop: Spacing.xs,
  },
  seasonProgress: {
    alignItems: 'center',
    minWidth: 60,
  },
  progressText: {
    fontSize: FontSize.sm,
    fontWeight: FontWeight.bold,
    color: Colors.text,
    marginBottom: Spacing.xs,
  },
  progressBar: {
    width: 50,
    height: 6,
    backgroundColor: Colors.border,
    borderRadius: 3,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  currentSeasonStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: Spacing.md,
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: FontSize.md,
    fontWeight: FontWeight.bold,
    color: Colors.text,
  },
  statLabel: {
    fontSize: FontSize.xs,
    color: Colors.textSecondary,
    marginTop: Spacing.xs,
  },
  viewDetailsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Spacing.sm,
  },
  viewDetailsText: {
    fontSize: FontSize.sm,
    color: Colors.primary,
    fontWeight: FontWeight.medium,
    marginRight: Spacing.xs,
  },
  seasonsGrid: {
    paddingHorizontal: Spacing.lg,
  },
  seasonCard: {
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.lg,
    borderWidth: 1,
    padding: Spacing.md,
    marginBottom: Spacing.md,
  },
  currentSeasonHighlight: {
    borderWidth: 2,
  },
  seasonCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  seasonEmoji: {
    fontSize: 24,
    marginRight: Spacing.sm,
  },
  seasonCardInfo: {
    flex: 1,
  },
  seasonName: {
    fontSize: FontSize.md,
    fontWeight: FontWeight.bold,
    color: Colors.text,
  },
  seasonMonths: {
    fontSize: FontSize.xs,
    color: Colors.textSecondary,
    marginTop: Spacing.xs,
  },
  currentBadge: {
    backgroundColor: Colors.success,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.sm,
  },
  currentBadgeText: {
    fontSize: FontSize.xs,
    color: Colors.surface,
    fontWeight: FontWeight.bold,
  },
  seasonCardStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: Spacing.sm,
  },
  seasonStat: {
    alignItems: 'center',
  },
  seasonStatValue: {
    fontSize: FontSize.sm,
    fontWeight: FontWeight.bold,
    color: Colors.text,
  },
  seasonStatLabel: {
    fontSize: FontSize.xs,
    color: Colors.textSecondary,
    marginTop: Spacing.xs,
  },
  topCategory: {
    alignItems: 'center',
  },
  topCategoryText: {
    fontSize: FontSize.xs,
    color: Colors.textSecondary,
    fontStyle: 'italic',
  },
  sectionTitle: {
    fontSize: FontSize.lg,
    fontWeight: FontWeight.bold,
    color: Colors.text,
    marginBottom: Spacing.md,
  },
  comparisonSection: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.lg,
  },
  comparisonItem: {
    marginBottom: Spacing.md,
  },
  comparisonHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  comparisonEmoji: {
    fontSize: 20,
    marginRight: Spacing.sm,
  },
  comparisonName: {
    fontSize: FontSize.sm,
    color: Colors.text,
    flex: 1,
  },
  comparisonAmount: {
    fontSize: FontSize.sm,
    fontWeight: FontWeight.bold,
    color: Colors.text,
  },
  comparisonBar: {
    height: 8,
    backgroundColor: Colors.border,
    borderRadius: 4,
    overflow: 'hidden',
  },
  comparisonBarFill: {
    height: '100%',
    borderRadius: 4,
  },
  insightsSection: {
    paddingHorizontal: Spacing.lg,
    paddingBottom: Spacing.xl,
  },
  insightCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
    marginBottom: Spacing.sm,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  insightText: {
    fontSize: FontSize.sm,
    color: Colors.text,
    marginLeft: Spacing.md,
    flex: 1,
  },
});
