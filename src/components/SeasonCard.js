import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import PropTypes from 'prop-types';

import { Colors } from '../constants/Colors';
import { Spacing, FontSize, FontWeight } from '../constants/Dimensions';

/**
 * Yeniden kullanılabilir sezon bilgisi gösterim component'i
 * 
 * @param {Object} season - Sezon objesi
 * @param {Function} onPress - Kart tıklandığında çalışacak fonksiyon
 * @param {boolean} showDetails - Detay bilgileri gösterilsin mi
 * @param {boolean} compact - Kompakt görünüm modu
 * @param {Object} style - Ek stil tanımları
 */
const SeasonCard = ({ 
  season, 
  onPress, 
  showDetails = true, 
  compact = false,
  style = {} 
}) => {
  if (!season) {
    return null;
  }

  const formatSeasonDate = (dateString) => {
    if (!dateString) return null;
    return new Date(dateString).toLocaleDateString('tr-TR');
  };

  const getDateRangeText = () => {
    const startText = season.startDate 
      ? formatSeasonDate(season.startDate)
      : 'Başlangıç: Açık';
    
    const endText = season.endDate 
      ? formatSeasonDate(season.endDate)
      : 'Bitiş: Açık';
    
    return `${startText} - ${endText}`;
  };

  const getDurationText = () => {
    if (!season.startDate || !season.endDate) {
      return 'Açık dönem';
    }
    
    const start = new Date(season.startDate);
    const end = new Date(season.endDate);
    const diffTime = Math.abs(end - start);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays < 30) {
      return `${diffDays} gün`;
    } else if (diffDays < 365) {
      const months = Math.round(diffDays / 30);
      return `${months} ay`;
    } else {
      const years = Math.round(diffDays / 365);
      return `${years} yıl`;
    }
  };

  const CardContent = () => (
    <View style={[
      styles.container, 
      compact && styles.compactContainer,
      style
    ]}>
      <View style={styles.header}>
        <Text style={[styles.emoji, compact && styles.compactEmoji]}>
          {season.emoji || '🌱'}
        </Text>
        <View style={styles.info}>
          <Text style={[styles.name, compact && styles.compactName]} numberOfLines={1}>
            {season.name}
          </Text>
          {season.isActive && (
            <View style={styles.activeIndicator}>
              <View style={styles.activeDot} />
              <Text style={styles.activeText}>Aktif Sezon</Text>
            </View>
          )}
          {season.description && !compact && (
            <Text style={styles.description} numberOfLines={2}>
              {season.description}
            </Text>
          )}
        </View>
        {onPress && (
          <Ionicons 
            name="chevron-forward" 
            size={compact ? 16 : 20} 
            color={Colors.textSecondary} 
          />
        )}
      </View>
      
      {showDetails && !compact && (
        <View style={styles.details}>
          <View style={styles.detailRow}>
            <Ionicons name="calendar-outline" size={16} color={Colors.textSecondary} />
            <Text style={styles.detailText}>{getDateRangeText()}</Text>
          </View>
          <View style={styles.detailRow}>
            <Ionicons name="time-outline" size={16} color={Colors.textSecondary} />
            <Text style={styles.detailText}>{getDurationText()}</Text>
          </View>
        </View>
      )}
      
      {showDetails && compact && (
        <Text style={styles.compactDetails} numberOfLines={1}>
          {getDateRangeText()}
        </Text>
      )}
    </View>
  );

  if (onPress) {
    return (
      <TouchableOpacity 
        onPress={() => onPress(season)}
        style={[styles.touchable, compact && styles.compactTouchable]}
        accessibilityRole="button"
        accessibilityLabel={`${season.name} sezonunu görüntüle`}
        accessibilityHint="Sezon detaylarını görmek için dokunun"
      >
        <CardContent />
      </TouchableOpacity>
    );
  }

  return <CardContent />;
};

SeasonCard.propTypes = {
  season: PropTypes.shape({
    id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    _id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    name: PropTypes.string.isRequired,
    description: PropTypes.string,
    emoji: PropTypes.string,
    startDate: PropTypes.string,
    endDate: PropTypes.string,
    isActive: PropTypes.bool,
    color: PropTypes.string,
  }),
  onPress: PropTypes.func,
  showDetails: PropTypes.bool,
  compact: PropTypes.bool,
  style: PropTypes.object,
};

const styles = StyleSheet.create({
  touchable: {
    backgroundColor: Colors.surface,
    borderRadius: 12,
    elevation: 2,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  compactTouchable: {
    borderRadius: 8,
    elevation: 1,
  },
  container: {
    padding: Spacing.md,
  },
  compactContainer: {
    padding: Spacing.sm,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  emoji: {
    fontSize: 24,
    marginRight: Spacing.sm,
  },
  compactEmoji: {
    fontSize: 20,
    marginRight: Spacing.xs,
  },
  info: {
    flex: 1,
  },
  name: {
    fontSize: FontSize.lg,
    fontWeight: FontWeight.semibold,
    color: Colors.text,
    marginBottom: Spacing.xs,
  },
  compactName: {
    fontSize: FontSize.md,
    marginBottom: 2,
  },
  description: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
    lineHeight: 18,
  },
  activeIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.xs,
  },
  activeDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: Colors.primary,
    marginRight: Spacing.xs,
  },
  activeText: {
    fontSize: FontSize.sm,
    color: Colors.primary,
    fontWeight: FontWeight.medium,
  },
  details: {
    marginTop: Spacing.sm,
    paddingTop: Spacing.sm,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.xs,
  },
  detailText: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
    marginLeft: Spacing.xs,
    flex: 1,
  },
  compactDetails: {
    fontSize: FontSize.xs,
    color: Colors.textSecondary,
    marginTop: Spacing.xs,
    marginLeft: 28, // emoji + margin
  },
});

export default SeasonCard;
