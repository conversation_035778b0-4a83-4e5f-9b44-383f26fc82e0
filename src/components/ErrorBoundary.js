/**
 * Error Boundary Component for Çiftçi Not Defterim
 * Catches JavaScript errors anywhere in the component tree and displays fallback UI
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { Colors } from '../constants/Colors';
import { Spacing, FontSize, FontWeight, BorderRadius } from '../constants/Dimensions';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
    };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      errorId: Date.now().toString(),
    };
  }

  componentDidCatch(error, errorInfo) {
    // Log error details
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo,
    });

    // Log to crash reporting service (if available)
    this.logErrorToService(error, errorInfo);
  }

  logErrorToService = (error, errorInfo) => {
    try {
      // Here you would typically send the error to a crash reporting service
      // like Crashlytics, Sentry, or Bugsnag
      
      const errorReport = {
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        timestamp: new Date().toISOString(),
        errorId: this.state.errorId,
        userAgent: navigator.userAgent,
        url: window.location?.href,
      };

      console.log('Error report:', errorReport);
      
      // Example: Send to your error reporting service
      // crashReportingService.recordError(errorReport);
    } catch (reportingError) {
      console.error('Failed to log error to service:', reportingError);
    }
  };

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
    });
  };

  handleRestart = () => {
    // In a real app, you might want to restart the app or navigate to home
    Alert.alert(
      'Uygulamayı Yeniden Başlat',
      'Uygulamayı tamamen yeniden başlatmak istiyor musunuz?',
      [
        {
          text: 'İptal',
          style: 'cancel',
        },
        {
          text: 'Yeniden Başlat',
          onPress: () => {
            // Reset to initial state and navigate to home
            this.handleRetry();
            if (this.props.onRestart) {
              this.props.onRestart();
            }
          },
        },
      ]
    );
  };

  handleReportError = () => {
    const { error, errorInfo, errorId } = this.state;
    
    Alert.alert(
      'Hata Raporu Gönder',
      'Bu hatayı geliştiricilere bildirmek istiyor musunuz? Bu, uygulamayı iyileştirmemize yardımcı olur.',
      [
        {
          text: 'Hayır',
          style: 'cancel',
        },
        {
          text: 'Gönder',
          onPress: () => {
            // Send error report
            this.sendErrorReport(error, errorInfo, errorId);
          },
        },
      ]
    );
  };

  sendErrorReport = async (error, errorInfo, errorId) => {
    try {
      // Here you would send the error report to your backend
      // or error reporting service
      
      Alert.alert(
        'Teşekkürler',
        'Hata raporu başarıyla gönderildi. Geri bildiriminiz için teşekkür ederiz.'
      );
    } catch (sendError) {
      console.error('Failed to send error report:', sendError);
      Alert.alert(
        'Hata',
        'Hata raporu gönderilemedi. Lütfen daha sonra tekrar deneyin.'
      );
    }
  };

  render() {
    if (this.state.hasError) {
      const { error, errorInfo } = this.state;
      const { fallback: CustomFallback } = this.props;

      // If a custom fallback component is provided, use it
      if (CustomFallback) {
        return (
          <CustomFallback
            error={error}
            errorInfo={errorInfo}
            onRetry={this.handleRetry}
            onRestart={this.handleRestart}
          />
        );
      }

      // Default error UI
      return (
        <SafeAreaView style={styles.container}>
          <ScrollView contentContainerStyle={styles.content}>
            <View style={styles.iconContainer}>
              <Ionicons name="warning-outline" size={64} color={Colors.error} />
            </View>

            <Text style={styles.title}>Bir Hata Oluştu</Text>
            
            <Text style={styles.description}>
              Üzgünüz, beklenmeyen bir hata oluştu. Lütfen uygulamayı yeniden başlatmayı deneyin.
            </Text>

            {__DEV__ && error && (
              <View style={styles.errorDetails}>
                <Text style={styles.errorTitle}>Hata Detayları (Geliştirici Modu):</Text>
                <Text style={styles.errorText}>{error.message}</Text>
                {error.stack && (
                  <Text style={styles.errorStack}>{error.stack}</Text>
                )}
              </View>
            )}

            <View style={styles.buttonContainer}>
              <TouchableOpacity style={styles.primaryButton} onPress={this.handleRetry}>
                <Ionicons name="refresh-outline" size={20} color={Colors.surface} />
                <Text style={styles.primaryButtonText}>Tekrar Dene</Text>
              </TouchableOpacity>

              <TouchableOpacity style={styles.secondaryButton} onPress={this.handleRestart}>
                <Ionicons name="home-outline" size={20} color={Colors.primary} />
                <Text style={styles.secondaryButtonText}>Ana Sayfaya Dön</Text>
              </TouchableOpacity>

              <TouchableOpacity style={styles.tertiaryButton} onPress={this.handleReportError}>
                <Ionicons name="bug-outline" size={20} color={Colors.textSecondary} />
                <Text style={styles.tertiaryButtonText}>Hata Bildir</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.footer}>
              <Text style={styles.footerText}>
                Hata ID: {this.state.errorId}
              </Text>
              <Text style={styles.footerText}>
                Bu sorunu yaşamaya devam ederseniz, lütfen destek ekibiyle iletişime geçin.
              </Text>
            </View>
          </ScrollView>
        </SafeAreaView>
      );
    }

    return this.props.children;
  }
}

// Simple Error Boundary for specific components
export class SimpleErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    console.error('SimpleErrorBoundary caught an error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <View style={styles.simpleErrorContainer}>
          <Ionicons name="alert-circle-outline" size={24} color={Colors.error} />
          <Text style={styles.simpleErrorText}>
            {this.props.fallbackText || 'Bu bölüm yüklenirken bir hata oluştu'}
          </Text>
          <TouchableOpacity
            style={styles.simpleRetryButton}
            onPress={() => this.setState({ hasError: false })}
          >
            <Text style={styles.simpleRetryText}>Tekrar Dene</Text>
          </TouchableOpacity>
        </View>
      );
    }

    return this.props.children;
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  content: {
    flexGrow: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.xl,
  },
  iconContainer: {
    marginBottom: Spacing.xl,
  },
  title: {
    fontSize: FontSize.xxl,
    fontWeight: FontWeight.bold,
    color: Colors.text,
    textAlign: 'center',
    marginBottom: Spacing.md,
  },
  description: {
    fontSize: FontSize.md,
    color: Colors.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: Spacing.xl,
  },
  errorDetails: {
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.md,
    padding: Spacing.md,
    marginBottom: Spacing.xl,
    width: '100%',
    borderLeftWidth: 4,
    borderLeftColor: Colors.error,
  },
  errorTitle: {
    fontSize: FontSize.sm,
    fontWeight: FontWeight.bold,
    color: Colors.error,
    marginBottom: Spacing.sm,
  },
  errorText: {
    fontSize: FontSize.sm,
    color: Colors.text,
    marginBottom: Spacing.sm,
  },
  errorStack: {
    fontSize: FontSize.xs,
    color: Colors.textSecondary,
    fontFamily: 'monospace',
  },
  buttonContainer: {
    width: '100%',
    marginBottom: Spacing.xl,
  },
  primaryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.primary,
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.lg,
    borderRadius: BorderRadius.lg,
    marginBottom: Spacing.md,
  },
  primaryButtonText: {
    fontSize: FontSize.md,
    fontWeight: FontWeight.medium,
    color: Colors.surface,
    marginLeft: Spacing.sm,
  },
  secondaryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.surface,
    borderWidth: 1,
    borderColor: Colors.primary,
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.lg,
    borderRadius: BorderRadius.lg,
    marginBottom: Spacing.md,
  },
  secondaryButtonText: {
    fontSize: FontSize.md,
    fontWeight: FontWeight.medium,
    color: Colors.primary,
    marginLeft: Spacing.sm,
  },
  tertiaryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.md,
  },
  tertiaryButtonText: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
    marginLeft: Spacing.sm,
  },
  footer: {
    alignItems: 'center',
  },
  footerText: {
    fontSize: FontSize.xs,
    color: Colors.textSecondary,
    textAlign: 'center',
    marginBottom: Spacing.xs,
  },
  // Simple Error Boundary Styles
  simpleErrorContainer: {
    padding: Spacing.md,
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.md,
    borderWidth: 1,
    borderColor: Colors.error,
    alignItems: 'center',
    margin: Spacing.md,
  },
  simpleErrorText: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
    textAlign: 'center',
    marginVertical: Spacing.sm,
  },
  simpleRetryButton: {
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.md,
    backgroundColor: Colors.primary,
    borderRadius: BorderRadius.sm,
  },
  simpleRetryText: {
    fontSize: FontSize.sm,
    color: Colors.surface,
    fontWeight: FontWeight.medium,
  },
});

export default ErrorBoundary;
