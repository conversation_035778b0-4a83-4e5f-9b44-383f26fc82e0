import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Modal,
  SafeAreaView,
  StatusBar,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { Colors } from '../constants/Colors';
import { Spacing, FontSize, FontWeight, BorderRadius } from '../constants/Dimensions';

// Emoji categories with relevant emojis for agricultural expenses
const EMOJI_CATEGORIES = {
  agriculture: {
    name: 'Tarım',
    emojis: ['🌾', '🌱', '🌿', '🌳', '🌲', '🌴', '🌵', '🌻', '🌺', '🌸', '🌼', '🌷', '🥀', '🌹', '🌽', '🥕', '🥔', '🍅', '🥒', '🥬', '🥦', '🧄', '🧅', '🍄', '🥜', '🌰'],
  },
  tools: {
    name: '<PERSON><PERSON><PERSON>',
    emojis: ['🚜', '⚒️', '🔨', '🔧', '🪓', '⛏️', '🪚', '🔩', '⚙️', '🛠️', '⚡', '🔋', '🪣', '🧰', '🪜', '⚖️', '📏', '📐', '🧲', '🔗', '⛓️', '🪝', '📎', '📌'],
  },
  nature: {
    name: 'Doğa',
    emojis: ['☀️', '🌤️', '⛅', '🌦️', '🌧️', '⛈️', '🌩️', '❄️', '☃️', '⛄', '🌊', '💧', '💦', '🌈', '🔥', '💨', '🌪️', '🌍', '🌎', '🌏', '🗻', '⛰️', '🏔️', '🌋'],
  },
  animals: {
    name: 'Hayvanlar',
    emojis: ['🐄', '🐂', '🐃', '🐎', '🐴', '🐷', '🐖', '🐗', '🐑', '🐐', '🐪', '🐫', '🦙', '🐔', '🐓', '🐣', '🐤', '🐥', '🦆', '🦅', '🦉', '🐝', '🐛', '🦋', '🐌', '🐞'],
  },
  objects: {
    name: 'Nesneler',
    emojis: ['📦', '📋', '📊', '📈', '📉', '💰', '💵', '💴', '💶', '💷', '💳', '💎', '⚖️', '🏷️', '🔖', '📝', '✏️', '📌', '📍', '🗂️', '📁', '📄', '📃', '📑', '🧾', '💼'],
  },
  transport: {
    name: 'Ulaşım',
    emojis: ['🚛', '🚚', '🚐', '🚗', '🚙', '🛻', '🚌', '🚎', '🏍️', '🛵', '🚲', '🛴', '🚁', '✈️', '🚀', '⛽', '🛣️', '🛤️', '🚧', '⚠️', '🚥', '🚦', '🛑', '⛔'],
  },
  symbols: {
    name: 'Semboller',
    emojis: ['✅', '❌', '⭐', '🌟', '💫', '⚡', '🔥', '💥', '💢', '💯', '🎯', '🏆', '🥇', '🥈', '🥉', '🏅', '🎖️', '🏵️', '🎗️', '🎀', '🎁', '🎊', '🎉', '🎈'],
  },
};

export default function EmojiPicker({
  visible,
  onClose,
  onEmojiSelect,
  selectedEmoji = '',
  title = 'Emoji Seç',
}) {
  const [activeCategory, setActiveCategory] = useState('agriculture');
  const [searchQuery, setSearchQuery] = useState('');

  const handleEmojiSelect = (emoji) => {
    onEmojiSelect(emoji);
    onClose();
  };

  const getFilteredEmojis = () => {
    if (!searchQuery) {
      return EMOJI_CATEGORIES[activeCategory]?.emojis || [];
    }

    // Search across all categories
    const allEmojis = Object.values(EMOJI_CATEGORIES).flatMap(category => category.emojis);
    return allEmojis.filter(emoji => 
      emoji.includes(searchQuery) || 
      // You could add emoji name matching here if needed
      false
    );
  };

  const renderCategoryTabs = () => (
    <ScrollView 
      horizontal 
      showsHorizontalScrollIndicator={false} 
      style={styles.categoryTabs}
      contentContainerStyle={styles.categoryTabsContent}
    >
      {Object.entries(EMOJI_CATEGORIES).map(([key, category]) => (
        <TouchableOpacity
          key={key}
          style={[
            styles.categoryTab,
            activeCategory === key && styles.categoryTabActive,
          ]}
          onPress={() => setActiveCategory(key)}
        >
          <Text
            style={[
              styles.categoryTabText,
              activeCategory === key && styles.categoryTabTextActive,
            ]}
          >
            {category.name}
          </Text>
        </TouchableOpacity>
      ))}
    </ScrollView>
  );

  const renderEmojiGrid = () => {
    const emojis = getFilteredEmojis();
    
    if (emojis.length === 0) {
      return (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>
            {searchQuery ? 'Emoji bulunamadı' : 'Bu kategoride emoji yok'}
          </Text>
        </View>
      );
    }

    return (
      <ScrollView style={styles.emojiGrid} showsVerticalScrollIndicator={false}>
        <View style={styles.emojiContainer}>
          {emojis.map((emoji, index) => (
            <TouchableOpacity
              key={`${emoji}-${index}`}
              style={[
                styles.emojiButton,
                selectedEmoji === emoji && styles.emojiButtonSelected,
              ]}
              onPress={() => handleEmojiSelect(emoji)}
            >
              <Text style={styles.emoji}>{emoji}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
    );
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <SafeAreaView style={styles.modalContainer}>
          <StatusBar backgroundColor="rgba(0,0,0,0.5)" barStyle="light-content" />
          
          <View style={styles.pickerContainer}>
            {/* Header */}
            <View style={styles.header}>
              <TouchableOpacity style={styles.headerButton} onPress={onClose}>
                <Ionicons name="close" size={24} color={Colors.text} />
              </TouchableOpacity>
              
              <Text style={styles.headerTitle}>{title}</Text>
              
              <View style={styles.headerButton} />
            </View>

            {/* Search */}
            <View style={styles.searchContainer}>
              <Ionicons name="search-outline" size={20} color={Colors.textSecondary} />
              <TextInput
                style={styles.searchInput}
                value={searchQuery}
                onChangeText={setSearchQuery}
                placeholder="Emoji ara..."
                placeholderTextColor={Colors.textSecondary}
              />
              {searchQuery.length > 0 && (
                <TouchableOpacity onPress={() => setSearchQuery('')}>
                  <Ionicons name="close-circle" size={20} color={Colors.textSecondary} />
                </TouchableOpacity>
              )}
            </View>

            {/* Category Tabs */}
            {!searchQuery && renderCategoryTabs()}

            {/* Selected Emoji Preview */}
            {selectedEmoji && (
              <View style={styles.selectedPreview}>
                <Text style={styles.selectedEmoji}>{selectedEmoji}</Text>
                <Text style={styles.selectedText}>Seçili Emoji</Text>
              </View>
            )}

            {/* Emoji Grid */}
            {renderEmojiGrid()}

            {/* Quick Actions */}
            <View style={styles.quickActions}>
              <TouchableOpacity
                style={styles.quickActionButton}
                onPress={() => handleEmojiSelect('📝')}
              >
                <Text style={styles.quickActionEmoji}>📝</Text>
                <Text style={styles.quickActionText}>Varsayılan</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={styles.quickActionButton}
                onPress={() => handleEmojiSelect('')}
              >
                <Ionicons name="close-outline" size={24} color={Colors.textSecondary} />
                <Text style={styles.quickActionText}>Emoji Yok</Text>
              </TouchableOpacity>
            </View>
          </View>
        </SafeAreaView>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  pickerContainer: {
    backgroundColor: Colors.background,
    borderTopLeftRadius: BorderRadius.xl,
    borderTopRightRadius: BorderRadius.xl,
    maxHeight: '80%',
    minHeight: '60%',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  headerButton: {
    padding: Spacing.sm,
    minWidth: 40,
  },
  headerTitle: {
    fontSize: FontSize.lg,
    fontWeight: FontWeight.bold,
    color: Colors.text,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.lg,
    borderWidth: 1,
    borderColor: Colors.border,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    margin: Spacing.lg,
  },
  searchInput: {
    flex: 1,
    fontSize: FontSize.md,
    color: Colors.text,
    marginLeft: Spacing.sm,
  },
  categoryTabs: {
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  categoryTabsContent: {
    paddingHorizontal: Spacing.lg,
    paddingBottom: Spacing.md,
  },
  categoryTab: {
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.md,
    borderRadius: BorderRadius.md,
    marginRight: Spacing.sm,
    backgroundColor: Colors.surface,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  categoryTabActive: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  categoryTabText: {
    fontSize: FontSize.sm,
    color: Colors.text,
    fontWeight: FontWeight.medium,
  },
  categoryTabTextActive: {
    color: Colors.surface,
    fontWeight: FontWeight.bold,
  },
  selectedPreview: {
    alignItems: 'center',
    paddingVertical: Spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  selectedEmoji: {
    fontSize: 48,
    marginBottom: Spacing.sm,
  },
  selectedText: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
  },
  emojiGrid: {
    flex: 1,
    paddingHorizontal: Spacing.lg,
  },
  emojiContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingVertical: Spacing.lg,
  },
  emojiButton: {
    width: '12%',
    aspectRatio: 1,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: BorderRadius.md,
    marginBottom: Spacing.sm,
  },
  emojiButtonSelected: {
    backgroundColor: Colors.primaryLight,
    borderWidth: 2,
    borderColor: Colors.primary,
  },
  emoji: {
    fontSize: 24,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: Spacing.xl,
  },
  emptyText: {
    fontSize: FontSize.md,
    color: Colors.textSecondary,
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.lg,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  quickActionButton: {
    alignItems: 'center',
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.md,
    borderRadius: BorderRadius.md,
    backgroundColor: Colors.surface,
    borderWidth: 1,
    borderColor: Colors.border,
    minWidth: 80,
  },
  quickActionEmoji: {
    fontSize: 24,
    marginBottom: Spacing.xs,
  },
  quickActionText: {
    fontSize: FontSize.xs,
    color: Colors.textSecondary,
    textAlign: 'center',
  },
});
