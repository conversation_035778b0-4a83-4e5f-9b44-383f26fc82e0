import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import PropTypes from 'prop-types';

import { DataManager } from '../services/DataManager';
import { Colors } from '../constants/Colors';
import { Spacing, FontSize, FontWeight } from '../constants/Dimensions';

/**
 * Sezon özet bilgilerini gösteren widget component
 * 
 * @param {Object} season - <PERSON>zon objesi
 * @param {Array} expenses - Sezon giderleri (opsiyonel, otomatik yüklenebilir)
 * @param {boolean} compact - Kompakt görünüm modu
 * @param {boolean} showProgress - Progress indicator gösterilsin mi
 * @param {Function} onPress - Widget'a tıklandığında çalışacak callback
 * @param {Object} style - Ek stil tanımları
 * @param {boolean} showCategoryBreakdown - Kategori breakdown gösterilsin mi
 */
const SeasonSummary = ({
  season,
  expenses: propExpenses,
  compact = false,
  showProgress = false,
  onPress,
  style = {},
  showCategoryBreakdown = true
}) => {
  const [expenses, setExpenses] = useState(propExpenses || []);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (season && !propExpenses) {
      loadSeasonData();
    } else if (propExpenses) {
      setExpenses(propExpenses);
      loadCategories();
    }
  }, [season, propExpenses]);

  const loadSeasonData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [seasonExpenses, allCategories] = await Promise.all([
        getSeasonExpenses(),
        DataManager.getCategories()
      ]);

      setExpenses(seasonExpenses || []);
      setCategories(allCategories || []);
    } catch (err) {
      console.error('Error loading season data:', err);
      setError('Sezon verileri yüklenirken hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  const loadCategories = async () => {
    try {
      const allCategories = await DataManager.getCategories();
      setCategories(allCategories || []);
    } catch (err) {
      console.error('Error loading categories:', err);
    }
  };

  const getSeasonExpenses = async () => {
    if (!season) return [];

    const allExpenses = await DataManager.getExpenses();
    
    // Filter expenses by season
    return allExpenses.filter(expense => {
      // Check if expense belongs to this season
      if (expense.seasonId === season.id || expense.seasonId === season._id) {
        return true;
      }

      // If no seasonId, check date range (for legacy expenses)
      if (!expense.seasonId && season.startDate) {
        const expenseDate = new Date(expense.date);
        const seasonStart = new Date(season.startDate);
        const seasonEnd = season.endDate ? new Date(season.endDate) : new Date();
        
        return expenseDate >= seasonStart && expenseDate <= seasonEnd;
      }

      return false;
    });
  };

  const calculateSummary = () => {
    if (!expenses || expenses.length === 0) {
      return {
        totalAmount: 0,
        expenseCount: 0,
        avgAmount: 0,
        categoryBreakdown: []
      };
    }

    const totalAmount = expenses.reduce((sum, expense) => sum + expense.amount, 0);
    const expenseCount = expenses.length;
    const avgAmount = totalAmount / expenseCount;

    // Calculate category breakdown
    const categoryMap = new Map();
    expenses.forEach(expense => {
      const categoryId = expense.categoryId;
      if (categoryMap.has(categoryId)) {
        const existing = categoryMap.get(categoryId);
        categoryMap.set(categoryId, {
          ...existing,
          amount: existing.amount + expense.amount,
          count: existing.count + 1
        });
      } else {
        const category = categories.find(cat => cat.id === categoryId || cat._id === categoryId);
        categoryMap.set(categoryId, {
          categoryId,
          categoryName: category?.name || 'Bilinmeyen',
          categoryEmoji: category?.emoji || '📝',
          amount: expense.amount,
          count: 1
        });
      }
    });

    const categoryBreakdown = Array.from(categoryMap.values())
      .sort((a, b) => b.amount - a.amount)
      .slice(0, 3); // Top 3 categories

    return {
      totalAmount,
      expenseCount,
      avgAmount,
      categoryBreakdown
    };
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
    }).format(amount);
  };

  const formatDate = (dateString) => {
    if (!dateString) return null;
    return new Date(dateString).toLocaleDateString('tr-TR');
  };

  const getDateRangeText = () => {
    if (!season) return '';
    
    const startText = season.startDate 
      ? formatDate(season.startDate)
      : 'Başlangıç: Açık';
    
    const endText = season.endDate 
      ? formatDate(season.endDate)
      : 'Bitiş: Açık';
    
    return `${startText} - ${endText}`;
  };

  const getDurationDays = () => {
    if (!season?.startDate || !season?.endDate) return null;
    
    const start = new Date(season.startDate);
    const end = new Date(season.endDate);
    const diffTime = Math.abs(end - start);
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  const summary = calculateSummary();

  if (loading) {
    return (
      <View style={[styles.container, styles.loadingContainer, style]}>
        <ActivityIndicator size="small" color={Colors.primary} />
        <Text style={styles.loadingText}>Yükleniyor...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={[styles.container, styles.errorContainer, style]}>
        <Ionicons name="alert-circle" size={24} color={Colors.error} />
        <Text style={styles.errorText}>{error}</Text>
      </View>
    );
  }

  if (!season) {
    return (
      <View style={[styles.container, styles.emptyContainer, style]}>
        <Text style={styles.emptyText}>Sezon bilgisi bulunamadı</Text>
      </View>
    );
  }

  const Content = () => (
    <View style={[
      styles.container,
      compact && styles.compactContainer,
      style
    ]}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.seasonInfo}>
          <Text style={styles.seasonEmoji}>{season.emoji || '🌱'}</Text>
          <View style={styles.seasonDetails}>
            <Text style={[styles.seasonName, compact && styles.compactSeasonName]}>
              {season.name}
            </Text>
            {!compact && (
              <Text style={styles.dateRange}>{getDateRangeText()}</Text>
            )}
          </View>
        </View>
        {season.isActive && (
          <View style={styles.activeBadge}>
            <Text style={styles.activeBadgeText}>Aktif</Text>
          </View>
        )}
      </View>

      {/* Summary Stats */}
      <View style={[styles.statsContainer, compact && styles.compactStatsContainer]}>
        <View style={styles.statItem}>
          <Text style={[styles.statValue, compact && styles.compactStatValue]}>
            {formatCurrency(summary.totalAmount)}
          </Text>
          <Text style={styles.statLabel}>Toplam Gider</Text>
        </View>
        
        <View style={styles.statDivider} />
        
        <View style={styles.statItem}>
          <Text style={[styles.statValue, compact && styles.compactStatValue]}>
            {summary.expenseCount}
          </Text>
          <Text style={styles.statLabel}>Gider Sayısı</Text>
        </View>

        {!compact && summary.expenseCount > 0 && (
          <>
            <View style={styles.statDivider} />
            <View style={styles.statItem}>
              <Text style={styles.statValue}>
                {formatCurrency(summary.avgAmount)}
              </Text>
              <Text style={styles.statLabel}>Ortalama</Text>
            </View>
          </>
        )}
      </View>

      {/* Category Breakdown */}
      {!compact && showCategoryBreakdown && summary.categoryBreakdown.length > 0 && (
        <View style={styles.categorySection}>
          <Text style={styles.categoryTitle}>En Çok Harcanan</Text>
          {summary.categoryBreakdown.map((category, index) => (
            <View key={category.categoryId} style={styles.categoryItem}>
              <Text style={styles.categoryEmoji}>{category.categoryEmoji}</Text>
              <Text style={styles.categoryName}>{category.categoryName}</Text>
              <Text style={styles.categoryAmount}>
                {formatCurrency(category.amount)}
              </Text>
            </View>
          ))}
        </View>
      )}

      {/* Progress Indicator */}
      {showProgress && getDurationDays() && (
        <View style={styles.progressSection}>
          <Text style={styles.progressLabel}>
            Sezon Süresi: {getDurationDays()} gün
          </Text>
        </View>
      )}
    </View>
  );

  if (onPress) {
    return (
      <TouchableOpacity
        onPress={() => onPress(season)}
        accessibilityRole="button"
        accessibilityLabel={`${season.name} sezon özetini görüntüle`}
      >
        <Content />
      </TouchableOpacity>
    );
  }

  return <Content />;
};

SeasonSummary.propTypes = {
  season: PropTypes.shape({
    id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    _id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    name: PropTypes.string.isRequired,
    emoji: PropTypes.string,
    startDate: PropTypes.string,
    endDate: PropTypes.string,
    isActive: PropTypes.bool,
  }),
  expenses: PropTypes.array,
  compact: PropTypes.bool,
  showProgress: PropTypes.bool,
  onPress: PropTypes.func,
  style: PropTypes.object,
  showCategoryBreakdown: PropTypes.bool,
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.surface,
    borderRadius: 12,
    padding: Spacing.md,
    elevation: 2,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  compactContainer: {
    padding: Spacing.sm,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 80,
  },
  loadingText: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
    marginLeft: Spacing.sm,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 80,
  },
  errorText: {
    fontSize: FontSize.sm,
    color: Colors.error,
    marginLeft: Spacing.sm,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 80,
  },
  emptyText: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: Spacing.md,
  },
  seasonInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  seasonEmoji: {
    fontSize: 24,
    marginRight: Spacing.sm,
  },
  seasonDetails: {
    flex: 1,
  },
  seasonName: {
    fontSize: FontSize.lg,
    fontWeight: FontWeight.semibold,
    color: Colors.text,
    marginBottom: Spacing.xs,
  },
  compactSeasonName: {
    fontSize: FontSize.md,
    marginBottom: 0,
  },
  dateRange: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
  },
  activeBadge: {
    backgroundColor: Colors.primary,
    borderRadius: 4,
    paddingHorizontal: Spacing.xs,
    paddingVertical: 2,
  },
  activeBadgeText: {
    fontSize: FontSize.xs,
    color: Colors.surface,
    fontWeight: FontWeight.medium,
  },
  statsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  compactStatsContainer: {
    marginBottom: Spacing.sm,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statValue: {
    fontSize: FontSize.lg,
    fontWeight: FontWeight.bold,
    color: Colors.primary,
    marginBottom: Spacing.xs,
  },
  compactStatValue: {
    fontSize: FontSize.md,
  },
  statLabel: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
    textAlign: 'center',
  },
  statDivider: {
    width: 1,
    height: 30,
    backgroundColor: Colors.border,
    marginHorizontal: Spacing.sm,
  },
  categorySection: {
    borderTopWidth: 1,
    borderTopColor: Colors.border,
    paddingTop: Spacing.md,
  },
  categoryTitle: {
    fontSize: FontSize.md,
    fontWeight: FontWeight.medium,
    color: Colors.text,
    marginBottom: Spacing.sm,
  },
  categoryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.xs,
  },
  categoryEmoji: {
    fontSize: 16,
    marginRight: Spacing.xs,
  },
  categoryName: {
    fontSize: FontSize.sm,
    color: Colors.text,
    flex: 1,
  },
  categoryAmount: {
    fontSize: FontSize.sm,
    fontWeight: FontWeight.medium,
    color: Colors.primary,
  },
  progressSection: {
    borderTopWidth: 1,
    borderTopColor: Colors.border,
    paddingTop: Spacing.sm,
    marginTop: Spacing.sm,
  },
  progressLabel: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
    textAlign: 'center',
  },
});

export default SeasonSummary;
