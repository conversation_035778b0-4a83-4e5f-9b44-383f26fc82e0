/**
 * Sync Status Indicator Component
 * Shows the current sync status and data storage mode
 */

import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { DataManager } from '../services/DataManager';
import { useAuth } from '../context/AuthContext';
import { Colors } from '../constants/Colors';

export default function SyncStatusIndicator({ style }) {
  const [syncStatus, setSyncStatus] = useState(null);
  const { isAuthenticated, isGuestMode } = useAuth();

  useEffect(() => {
    updateSyncStatus();
    
    // Update sync status every 30 seconds
    const interval = setInterval(updateSyncStatus, 30000);
    
    return () => clearInterval(interval);
  }, [isAuthenticated, isGuestMode]);

  const updateSyncStatus = () => {
    try {
      const status = DataManager.getSyncStatus();
      setSyncStatus(status);
    } catch (error) {
      console.error('Failed to get sync status:', error);
    }
  };

  const handlePress = async () => {
    if (isAuthenticated && syncStatus?.pendingCount > 0) {
      try {
        await DataManager.syncWithBackend();
        updateSyncStatus();
      } catch (error) {
        console.error('Manual sync failed:', error);
      }
    }
  };

  // Don't show sync status for guest users
  if (isGuestMode || !isAuthenticated) {
    return null;
  }

  if (!syncStatus) {
    return null;
  }

  const getStatusInfo = () => {

    if (syncStatus.syncInProgress) {
      return {
        icon: 'sync',
        text: 'Senkronize ediliyor...',
        color: Colors.primary,
        description: 'Veriler buluta aktarılıyor'
      };
    }

    if (syncStatus.synced) {
      return {
        icon: 'cloud-done-outline',
        text: 'Senkronize',
        color: Colors.success,
        description: 'Tüm veriler bulutta güvende'
      };
    }

    return {
      icon: 'cloud-upload-outline',
      text: `${syncStatus.pendingCount} bekliyor`,
      color: Colors.warning,
      description: 'Bazı veriler henüz buluta aktarılmadı'
    };
  };

  const statusInfo = getStatusInfo();

  return (
    <TouchableOpacity 
      style={[styles.container, style]}
      onPress={handlePress}
      disabled={isGuestMode || syncStatus.synced || syncStatus.syncInProgress}
    >
      <View style={styles.content}>
        <Ionicons 
          name={statusInfo.icon} 
          size={16} 
          color={statusInfo.color}
          style={syncStatus.syncInProgress ? styles.rotating : null}
        />
        <Text style={[styles.text, { color: statusInfo.color }]}>
          {statusInfo.text}
        </Text>
      </View>
      
      {statusInfo.description && (
        <Text style={styles.description}>
          {statusInfo.description}
        </Text>
      )}
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 8,
    padding: 8,
    marginHorizontal: 16,
    marginVertical: 4,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.1)',
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  text: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4,
  },
  description: {
    fontSize: 10,
    color: Colors.textSecondary,
    textAlign: 'center',
    marginTop: 2,
  },
  rotating: {
    // Animation would be added here if needed
  },
});
