/**
 * Tutorial Overlay Component for Çiftçi Not Defterim
 * Provides interactive step-by-step guidance with highlighting and tooltips
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Dimensions,
  Modal,
  StatusBar,
  BackHandler,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { Colors } from '../../constants/Colors';
import { Spacing, FontSize, FontWeight, BorderRadius } from '../../constants/Dimensions';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

export default function TutorialOverlay({
  visible,
  steps,
  currentStep,
  onNext,
  onPrevious,
  onSkip,
  onComplete,
  maskOpacity = 0.8,
  highlightPadding = 8,
}) {
  const [dimensions, setDimensions] = useState({ width: screenWidth, height: screenHeight });
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;

  useEffect(() => {
    if (visible) {
      // Animate in
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 100,
          friction: 8,
          useNativeDriver: true,
        }),
      ]).start();

      // Handle back button
      const backHandler = BackHandler.addEventListener('hardwareBackPress', handleBackPress);
      return () => backHandler.remove();
    } else {
      // Animate out
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 0.8,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [visible]);

  useEffect(() => {
    const updateDimensions = ({ window }) => {
      setDimensions({ width: window.width, height: window.height });
    };

    const subscription = Dimensions.addEventListener('change', updateDimensions);
    return () => subscription?.remove();
  }, []);

  const handleBackPress = () => {
    if (currentStep > 0) {
      onPrevious();
    } else {
      onSkip();
    }
    return true;
  };

  const getCurrentStepData = () => {
    return steps[currentStep] || {};
  };

  const renderMask = () => {
    const step = getCurrentStepData();
    const { target } = step;

    if (!target) {
      // Full screen mask
      return (
        <View style={[styles.mask, { backgroundColor: `rgba(0, 0, 0, ${maskOpacity})` }]} />
      );
    }

    // Create cutout mask for highlighting target element
    const { x, y, width, height } = target;
    const highlightX = x - highlightPadding;
    const highlightY = y - highlightPadding;
    const highlightWidth = width + (highlightPadding * 2);
    const highlightHeight = height + (highlightPadding * 2);

    return (
      <View style={styles.maskContainer}>
        {/* Top mask */}
        <View
          style={[
            styles.maskSection,
            {
              top: 0,
              left: 0,
              width: dimensions.width,
              height: highlightY,
              backgroundColor: `rgba(0, 0, 0, ${maskOpacity})`,
            },
          ]}
        />
        
        {/* Left mask */}
        <View
          style={[
            styles.maskSection,
            {
              top: highlightY,
              left: 0,
              width: highlightX,
              height: highlightHeight,
              backgroundColor: `rgba(0, 0, 0, ${maskOpacity})`,
            },
          ]}
        />
        
        {/* Right mask */}
        <View
          style={[
            styles.maskSection,
            {
              top: highlightY,
              left: highlightX + highlightWidth,
              width: dimensions.width - (highlightX + highlightWidth),
              height: highlightHeight,
              backgroundColor: `rgba(0, 0, 0, ${maskOpacity})`,
            },
          ]}
        />
        
        {/* Bottom mask */}
        <View
          style={[
            styles.maskSection,
            {
              top: highlightY + highlightHeight,
              left: 0,
              width: dimensions.width,
              height: dimensions.height - (highlightY + highlightHeight),
              backgroundColor: `rgba(0, 0, 0, ${maskOpacity})`,
            },
          ]}
        />
        
        {/* Highlight border */}
        <View
          style={[
            styles.highlight,
            {
              top: highlightY,
              left: highlightX,
              width: highlightWidth,
              height: highlightHeight,
            },
          ]}
        />
      </View>
    );
  };

  const renderTooltip = () => {
    const step = getCurrentStepData();
    const { title, description, target, tooltipPosition = 'bottom' } = step;

    if (!title && !description) return null;

    let tooltipStyle = styles.tooltip;
    let arrowStyle = styles.tooltipArrow;

    if (target) {
      const { x, y, width, height } = target;
      const centerX = x + (width / 2);
      const centerY = y + (height / 2);

      switch (tooltipPosition) {
        case 'top':
          tooltipStyle = [
            styles.tooltip,
            {
              bottom: dimensions.height - y + highlightPadding + 10,
              left: Math.max(Spacing.lg, Math.min(centerX - 150, dimensions.width - 300 - Spacing.lg)),
            },
          ];
          arrowStyle = [styles.tooltipArrow, styles.tooltipArrowBottom];
          break;
        
        case 'bottom':
          tooltipStyle = [
            styles.tooltip,
            {
              top: y + height + highlightPadding + 10,
              left: Math.max(Spacing.lg, Math.min(centerX - 150, dimensions.width - 300 - Spacing.lg)),
            },
          ];
          arrowStyle = [styles.tooltipArrow, styles.tooltipArrowTop];
          break;
        
        case 'left':
          tooltipStyle = [
            styles.tooltip,
            {
              top: Math.max(Spacing.lg, centerY - 100),
              right: dimensions.width - x + highlightPadding + 10,
            },
          ];
          arrowStyle = [styles.tooltipArrow, styles.tooltipArrowRight];
          break;
        
        case 'right':
          tooltipStyle = [
            styles.tooltip,
            {
              top: Math.max(Spacing.lg, centerY - 100),
              left: x + width + highlightPadding + 10,
            },
          ];
          arrowStyle = [styles.tooltipArrow, styles.tooltipArrowLeft];
          break;
        
        default:
          // Center tooltip
          tooltipStyle = [
            styles.tooltip,
            styles.tooltipCenter,
          ];
          arrowStyle = null;
      }
    } else {
      // Center tooltip when no target
      tooltipStyle = [styles.tooltip, styles.tooltipCenter];
      arrowStyle = null;
    }

    return (
      <Animated.View
        style={[
          tooltipStyle,
          {
            opacity: fadeAnim,
            transform: [{ scale: scaleAnim }],
          },
        ]}
      >
        {arrowStyle && <View style={arrowStyle} />}
        
        {title && <Text style={styles.tooltipTitle}>{title}</Text>}
        {description && <Text style={styles.tooltipDescription}>{description}</Text>}
        
        <View style={styles.tooltipActions}>
          <View style={styles.stepIndicator}>
            <Text style={styles.stepText}>
              {currentStep + 1} / {steps.length}
            </Text>
          </View>
          
          <View style={styles.actionButtons}>
            {currentStep > 0 && (
              <TouchableOpacity style={styles.previousButton} onPress={onPrevious}>
                <Ionicons name="chevron-back" size={20} color={Colors.textSecondary} />
                <Text style={styles.previousButtonText}>Geri</Text>
              </TouchableOpacity>
            )}
            
            <TouchableOpacity style={styles.skipButton} onPress={onSkip}>
              <Text style={styles.skipButtonText}>Atla</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.nextButton}
              onPress={currentStep === steps.length - 1 ? onComplete : onNext}
            >
              <Text style={styles.nextButtonText}>
                {currentStep === steps.length - 1 ? 'Bitir' : 'İleri'}
              </Text>
              <Ionicons name="chevron-forward" size={20} color={Colors.surface} />
            </TouchableOpacity>
          </View>
        </View>
      </Animated.View>
    );
  };

  if (!visible) return null;

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      statusBarTranslucent
    >
      <StatusBar backgroundColor="transparent" barStyle="light-content" />
      
      <View style={styles.container}>
        {renderMask()}
        {renderTooltip()}
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
  },
  mask: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  maskContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  maskSection: {
    position: 'absolute',
  },
  highlight: {
    position: 'absolute',
    borderWidth: 2,
    borderColor: Colors.primary,
    borderRadius: BorderRadius.md,
  },
  tooltip: {
    position: 'absolute',
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    maxWidth: 300,
    minWidth: 250,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  tooltipCenter: {
    top: '50%',
    left: '50%',
    marginTop: -100,
    marginLeft: -150,
  },
  tooltipArrow: {
    position: 'absolute',
    width: 0,
    height: 0,
    borderStyle: 'solid',
  },
  tooltipArrowTop: {
    top: -8,
    left: '50%',
    marginLeft: -8,
    borderLeftWidth: 8,
    borderRightWidth: 8,
    borderBottomWidth: 8,
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    borderBottomColor: Colors.surface,
  },
  tooltipArrowBottom: {
    bottom: -8,
    left: '50%',
    marginLeft: -8,
    borderLeftWidth: 8,
    borderRightWidth: 8,
    borderTopWidth: 8,
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    borderTopColor: Colors.surface,
  },
  tooltipArrowLeft: {
    left: -8,
    top: '50%',
    marginTop: -8,
    borderTopWidth: 8,
    borderBottomWidth: 8,
    borderRightWidth: 8,
    borderTopColor: 'transparent',
    borderBottomColor: 'transparent',
    borderRightColor: Colors.surface,
  },
  tooltipArrowRight: {
    right: -8,
    top: '50%',
    marginTop: -8,
    borderTopWidth: 8,
    borderBottomWidth: 8,
    borderLeftWidth: 8,
    borderTopColor: 'transparent',
    borderBottomColor: 'transparent',
    borderLeftColor: Colors.surface,
  },
  tooltipTitle: {
    fontSize: FontSize.lg,
    fontWeight: FontWeight.bold,
    color: Colors.text,
    marginBottom: Spacing.sm,
  },
  tooltipDescription: {
    fontSize: FontSize.md,
    color: Colors.textSecondary,
    lineHeight: 20,
    marginBottom: Spacing.lg,
  },
  tooltipActions: {
    flexDirection: 'column',
  },
  stepIndicator: {
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  stepText: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
    fontWeight: FontWeight.medium,
  },
  actionButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  previousButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.md,
  },
  previousButtonText: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
    marginLeft: Spacing.xs,
  },
  skipButton: {
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.md,
  },
  skipButtonText: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
  },
  nextButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.primary,
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.md,
    borderRadius: BorderRadius.md,
  },
  nextButtonText: {
    fontSize: FontSize.sm,
    color: Colors.surface,
    fontWeight: FontWeight.medium,
    marginRight: Spacing.xs,
  },
});

// Tutorial Step Component
export const TutorialStep = ({
  title,
  description,
  target,
  tooltipPosition = 'bottom',
  action,
  validation,
}) => {
  return {
    title,
    description,
    target,
    tooltipPosition,
    action,
    validation,
  };
};

// Tutorial Manager Hook
export const useTutorial = (steps, options = {}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const [isCompleted, setIsCompleted] = useState(false);

  const {
    autoStart = false,
    onComplete,
    onSkip,
    onStepChange,
  } = options;

  useEffect(() => {
    if (autoStart) {
      start();
    }
  }, [autoStart]);

  const start = () => {
    setCurrentStep(0);
    setIsVisible(true);
    setIsCompleted(false);
    onStepChange?.(0);
  };

  const next = () => {
    if (currentStep < steps.length - 1) {
      const nextStep = currentStep + 1;
      setCurrentStep(nextStep);
      onStepChange?.(nextStep);
    } else {
      complete();
    }
  };

  const previous = () => {
    if (currentStep > 0) {
      const prevStep = currentStep - 1;
      setCurrentStep(prevStep);
      onStepChange?.(prevStep);
    }
  };

  const skip = () => {
    setIsVisible(false);
    setIsCompleted(true);
    onSkip?.();
  };

  const complete = () => {
    setIsVisible(false);
    setIsCompleted(true);
    onComplete?.();
  };

  const goToStep = (stepIndex) => {
    if (stepIndex >= 0 && stepIndex < steps.length) {
      setCurrentStep(stepIndex);
      onStepChange?.(stepIndex);
    }
  };

  return {
    currentStep,
    isVisible,
    isCompleted,
    start,
    next,
    previous,
    skip,
    complete,
    goToStep,
  };
};
