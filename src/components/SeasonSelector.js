import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import PropTypes from 'prop-types';

import { DataManager } from '../services/DataManager';
import { Colors } from '../constants/Colors';
import { Spacing, FontSize, FontWeight } from '../constants/Dimensions';

/**
 * Sezon seçici dropdown component
 * Expense ekleme ve düzenleme ekranlarında kullanılır
 * 
 * @param {Array} seasons - Sezon listesi (opsiyonel, otomatik yüklenebilir)
 * @param {Object} selectedSeason - Se<PERSON><PERSON> sezon objesi
 * @param {Function} onSelect - Sezon seçildiğinde çalışacak callback
 * @param {boolean} disabled - Component disabled durumu
 * @param {string} placeholder - Placeholder text
 * @param {boolean} showActiveBadge - Aktif sezon badge'i gösterilsin mi
 * @param {Object} style - Ek stil tanımları
 * @param {string} error - <PERSON>a mesajı
 */
const SeasonSelector = ({
  seasons: propSeasons,
  selectedSeason,
  onSelect,
  disabled = false,
  placeholder = 'Sezon seçiniz',
  showActiveBadge = true,
  style = {},
  error = null
}) => {
  const [seasons, setSeasons] = useState(propSeasons || []);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [activeSeason, setActiveSeason] = useState(null);

  useEffect(() => {
    if (!propSeasons) {
      loadSeasons();
    } else {
      setSeasons(propSeasons);
    }
  }, [propSeasons]);

  const loadSeasons = async () => {
    try {
      setLoading(true);
      
      const [allSeasons, currentActiveSeason] = await Promise.all([
        DataManager.getSeasons(),
        DataManager.getActiveSeason()
      ]);
      
      setSeasons(allSeasons || []);
      setActiveSeason(currentActiveSeason);
      
      // If no season selected and active season exists, auto-select it
      if (!selectedSeason && currentActiveSeason && onSelect) {
        onSelect(currentActiveSeason);
      }
    } catch (error) {
      console.error('Error loading seasons:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSeasonSelect = (season) => {
    setModalVisible(false);
    if (onSelect) {
      onSelect(season);
    }
  };

  const formatSeasonDate = (dateString) => {
    if (!dateString) return null;
    return new Date(dateString).toLocaleDateString('tr-TR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const getSeasonDateRange = (season) => {
    if (!season.startDate && !season.endDate) {
      return 'Açık dönem';
    }
    
    const startText = season.startDate 
      ? formatSeasonDate(season.startDate)
      : 'Başlangıç: Açık';
    
    const endText = season.endDate 
      ? formatSeasonDate(season.endDate)
      : 'Bitiş: Açık';
    
    return `${startText} - ${endText}`;
  };

  const renderSeasonItem = ({ item: season }) => {
    const isSelected = selectedSeason && (
      season.id === selectedSeason.id || 
      season._id === selectedSeason._id
    );
    
    const isActive = season.isActive || (
      activeSeason && (
        season.id === activeSeason.id || 
        season._id === activeSeason._id
      )
    );

    return (
      <TouchableOpacity
        style={[
          styles.seasonItem,
          isSelected && styles.selectedSeasonItem
        ]}
        onPress={() => handleSeasonSelect(season)}
        accessibilityRole="button"
        accessibilityLabel={`${season.name} sezonunu seç`}
        accessibilityState={{ selected: isSelected }}
      >
        <View style={styles.seasonItemHeader}>
          <Text style={styles.seasonEmoji}>{season.emoji || '🌱'}</Text>
          <View style={styles.seasonItemInfo}>
            <View style={styles.seasonNameRow}>
              <Text style={[
                styles.seasonName,
                isSelected && styles.selectedSeasonName
              ]}>
                {season.name}
              </Text>
              {isActive && showActiveBadge && (
                <View style={styles.activeBadge}>
                  <Text style={styles.activeBadgeText}>Aktif</Text>
                </View>
              )}
            </View>
            <Text style={styles.seasonDateRange}>
              {getSeasonDateRange(season)}
            </Text>
            {season.description && (
              <Text style={styles.seasonDescription} numberOfLines={1}>
                {season.description}
              </Text>
            )}
          </View>
          {isSelected && (
            <Ionicons 
              name="checkmark-circle" 
              size={20} 
              color={Colors.primary} 
            />
          )}
        </View>
      </TouchableOpacity>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="leaf-outline" size={48} color={Colors.textSecondary} />
      <Text style={styles.emptyStateText}>Henüz sezon oluşturulmamış</Text>
      <Text style={styles.emptyStateSubtext}>
        Önce bir sezon oluşturun
      </Text>
    </View>
  );

  const getDisplayText = () => {
    if (selectedSeason) {
      return `${selectedSeason.emoji || '🌱'} ${selectedSeason.name}`;
    }
    return placeholder;
  };

  return (
    <View style={[styles.container, style]}>
      <TouchableOpacity
        style={[
          styles.selector,
          disabled && styles.selectorDisabled,
          error && styles.selectorError
        ]}
        onPress={() => !disabled && setModalVisible(true)}
        disabled={disabled}
        accessibilityRole="button"
        accessibilityLabel="Sezon seçici"
        accessibilityHint="Sezon listesini açmak için dokunun"
        accessibilityState={{ disabled }}
      >
        <Text style={[
          styles.selectorText,
          !selectedSeason && styles.placeholderText,
          disabled && styles.disabledText
        ]}>
          {getDisplayText()}
        </Text>
        
        {loading ? (
          <ActivityIndicator size="small" color={Colors.textSecondary} />
        ) : (
          <Ionicons 
            name="chevron-down" 
            size={20} 
            color={disabled ? Colors.textSecondary : Colors.text} 
          />
        )}
      </TouchableOpacity>

      {error && (
        <Text style={styles.errorText}>{error}</Text>
      )}

      <Modal
        visible={modalVisible}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Sezon Seçin</Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setModalVisible(false)}
              >
                <Ionicons name="close" size={24} color={Colors.text} />
              </TouchableOpacity>
            </View>

            <FlatList
              data={seasons}
              renderItem={renderSeasonItem}
              keyExtractor={(item) => item.id || item._id}
              style={styles.seasonList}
              ListEmptyComponent={renderEmptyState}
              showsVerticalScrollIndicator={false}
            />
          </View>
        </View>
      </Modal>
    </View>
  );
};

SeasonSelector.propTypes = {
  seasons: PropTypes.array,
  selectedSeason: PropTypes.shape({
    id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    _id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    name: PropTypes.string.isRequired,
    emoji: PropTypes.string,
    startDate: PropTypes.string,
    endDate: PropTypes.string,
    isActive: PropTypes.bool,
    description: PropTypes.string,
  }),
  onSelect: PropTypes.func.isRequired,
  disabled: PropTypes.bool,
  placeholder: PropTypes.string,
  showActiveBadge: PropTypes.bool,
  style: PropTypes.object,
  error: PropTypes.string,
};

const styles = StyleSheet.create({
  container: {
    marginBottom: Spacing.sm,
  },
  selector: {
    backgroundColor: Colors.surface,
    borderRadius: 8,
    padding: Spacing.md,
    borderWidth: 1,
    borderColor: Colors.border,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    minHeight: 48,
  },
  selectorDisabled: {
    backgroundColor: Colors.background,
    opacity: 0.6,
  },
  selectorError: {
    borderColor: Colors.error,
  },
  selectorText: {
    fontSize: FontSize.md,
    color: Colors.text,
    flex: 1,
  },
  placeholderText: {
    color: Colors.textSecondary,
  },
  disabledText: {
    color: Colors.textSecondary,
  },
  errorText: {
    fontSize: FontSize.sm,
    color: Colors.error,
    marginTop: Spacing.xs,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: Colors.surface,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
    paddingBottom: 34, // Safe area bottom
    minHeight: 300, // Minimum height to prevent jumping
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  modalTitle: {
    fontSize: FontSize.lg,
    fontWeight: FontWeight.semibold,
    color: Colors.text,
  },
  closeButton: {
    padding: Spacing.xs,
  },
  seasonList: {
    paddingHorizontal: Spacing.md,
    flexGrow: 1, // Allow list to grow and maintain scroll
  },
  seasonItem: {
    paddingVertical: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  selectedSeasonItem: {
    backgroundColor: Colors.primaryLight,
    borderRadius: 8,
    marginVertical: Spacing.xs,
    marginHorizontal: 0, // Prevent horizontal shifting
    paddingHorizontal: Spacing.sm,
    borderBottomWidth: 0,
  },
  seasonItemHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  seasonEmoji: {
    fontSize: 20,
    marginRight: Spacing.sm,
  },
  seasonItemInfo: {
    flex: 1,
  },
  seasonNameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.xs,
  },
  seasonName: {
    fontSize: FontSize.md,
    fontWeight: FontWeight.medium,
    color: Colors.text,
    flex: 1,
  },
  selectedSeasonName: {
    color: Colors.primary,
  },
  activeBadge: {
    backgroundColor: Colors.primary,
    borderRadius: 4,
    paddingHorizontal: Spacing.xs,
    paddingVertical: 2,
    marginLeft: Spacing.xs,
  },
  activeBadgeText: {
    fontSize: FontSize.xs,
    color: Colors.surface,
    fontWeight: FontWeight.medium,
  },
  seasonDateRange: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
    marginBottom: Spacing.xs,
  },
  seasonDescription: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
    fontStyle: 'italic',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: Spacing.xl,
  },
  emptyStateText: {
    fontSize: FontSize.md,
    fontWeight: FontWeight.medium,
    color: Colors.text,
    marginTop: Spacing.md,
  },
  emptyStateSubtext: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
    marginTop: Spacing.xs,
  },
});

export default SeasonSelector;
