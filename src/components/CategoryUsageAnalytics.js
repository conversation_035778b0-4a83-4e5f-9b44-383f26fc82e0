import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { Colors } from '../constants/Colors';
import { Spacing, FontSize, FontWeight, BorderRadius } from '../constants/Dimensions';
import { formatAmount } from '../utils/validation';
import Logger, { LogCategory } from '../utils/Logger';
import Analytics, { AnalyticsEvent } from '../utils/Analytics';

const { width: screenWidth } = Dimensions.get('window');

export default function CategoryUsageAnalytics({
  categories = [],
  expenses = [],
  timeRange = 'month', // 'week', 'month', 'quarter', 'year'
  onTimeRangeChange,
}) {
  const [analytics, setAnalytics] = useState({
    totalExpenses: 0,
    totalAmount: 0,
    categoryStats: [],
    trends: {},
  });

  useEffect(() => {
    calculateAnalytics();
    Analytics.trackEvent(AnalyticsEvent.CATEGORY_ANALYTICS_VIEWED, {
      timeRange,
      categoryCount: categories.length,
      expenseCount: expenses.length,
    });
  }, [categories, expenses, timeRange]);

  const calculateAnalytics = () => {
    try {
      const filteredExpenses = filterExpensesByTimeRange(expenses, timeRange);
      
      const categoryStats = categories.map(category => {
        const categoryExpenses = filteredExpenses.filter(
          expense => expense.categoryId === category.id
        );
        
        const totalAmount = categoryExpenses.reduce(
          (sum, expense) => sum + expense.amount, 0
        );
        
        const avgAmount = categoryExpenses.length > 0 
          ? totalAmount / categoryExpenses.length 
          : 0;

        const lastUsed = categoryExpenses.length > 0
          ? Math.max(...categoryExpenses.map(e => new Date(e.date).getTime()))
          : null;

        return {
          ...category,
          usageCount: categoryExpenses.length,
          totalAmount,
          avgAmount,
          percentage: 0, // Will be calculated after
          lastUsed: lastUsed ? new Date(lastUsed).toISOString() : null,
          trend: calculateTrend(category.id, filteredExpenses),
        };
      });

      const totalAmount = categoryStats.reduce((sum, stat) => sum + stat.totalAmount, 0);
      const totalExpenses = categoryStats.reduce((sum, stat) => sum + stat.usageCount, 0);

      // Calculate percentages
      categoryStats.forEach(stat => {
        stat.percentage = totalAmount > 0 ? (stat.totalAmount / totalAmount) * 100 : 0;
      });

      // Sort by usage count (descending)
      categoryStats.sort((a, b) => b.usageCount - a.usageCount);

      setAnalytics({
        totalExpenses,
        totalAmount,
        categoryStats,
        trends: calculateOverallTrends(filteredExpenses),
      });

      Logger.debug(LogCategory.ANALYTICS, 'Category analytics calculated', {
        timeRange,
        totalExpenses,
        totalAmount,
        categoryCount: categoryStats.length,
      });
    } catch (error) {
      Logger.error(LogCategory.ANALYTICS, 'Failed to calculate analytics', error);
    }
  };

  const filterExpensesByTimeRange = (expenses, range) => {
    const now = new Date();
    const startDate = new Date();

    switch (range) {
      case 'week':
        startDate.setDate(now.getDate() - 7);
        break;
      case 'month':
        startDate.setMonth(now.getMonth() - 1);
        break;
      case 'quarter':
        startDate.setMonth(now.getMonth() - 3);
        break;
      case 'year':
        startDate.setFullYear(now.getFullYear() - 1);
        break;
      default:
        startDate.setMonth(now.getMonth() - 1);
    }

    return expenses.filter(expense => new Date(expense.date) >= startDate);
  };

  const calculateTrend = (categoryId, expenses) => {
    const categoryExpenses = expenses.filter(e => e.categoryId === categoryId);
    if (categoryExpenses.length < 2) return 'stable';

    // Simple trend calculation based on recent vs older expenses
    const midPoint = Math.floor(categoryExpenses.length / 2);
    const recentExpenses = categoryExpenses.slice(0, midPoint);
    const olderExpenses = categoryExpenses.slice(midPoint);

    const recentAvg = recentExpenses.reduce((sum, e) => sum + e.amount, 0) / recentExpenses.length;
    const olderAvg = olderExpenses.reduce((sum, e) => sum + e.amount, 0) / olderExpenses.length;

    const changePercent = ((recentAvg - olderAvg) / olderAvg) * 100;

    if (changePercent > 10) return 'increasing';
    if (changePercent < -10) return 'decreasing';
    return 'stable';
  };

  const calculateOverallTrends = (expenses) => {
    // Calculate trends for the overall spending patterns
    return {
      mostUsedDay: getMostUsedDay(expenses),
      averageExpenseAmount: expenses.length > 0 
        ? expenses.reduce((sum, e) => sum + e.amount, 0) / expenses.length 
        : 0,
      expenseFrequency: expenses.length / getDaysInRange(timeRange),
    };
  };

  const getMostUsedDay = (expenses) => {
    const dayCount = {};
    expenses.forEach(expense => {
      const day = new Date(expense.date).getDay();
      dayCount[day] = (dayCount[day] || 0) + 1;
    });

    const maxDay = Object.keys(dayCount).reduce((a, b) => 
      dayCount[a] > dayCount[b] ? a : b, 0
    );

    const dayNames = ['Pazar', 'Pazartesi', 'Salı', 'Çarşamba', 'Perşembe', 'Cuma', 'Cumartesi'];
    return dayNames[maxDay] || 'Bilinmiyor';
  };

  const getDaysInRange = (range) => {
    switch (range) {
      case 'week': return 7;
      case 'month': return 30;
      case 'quarter': return 90;
      case 'year': return 365;
      default: return 30;
    }
  };

  const getTrendIcon = (trend) => {
    switch (trend) {
      case 'increasing':
        return <Ionicons name="trending-up" size={16} color={Colors.error} />;
      case 'decreasing':
        return <Ionicons name="trending-down" size={16} color={Colors.success} />;
      default:
        return <Ionicons name="remove" size={16} color={Colors.textSecondary} />;
    }
  };

  const getTrendColor = (trend) => {
    switch (trend) {
      case 'increasing': return Colors.error;
      case 'decreasing': return Colors.success;
      default: return Colors.textSecondary;
    }
  };

  const renderTimeRangeSelector = () => (
    <View style={styles.timeRangeSelector}>
      {[
        { key: 'week', label: 'Hafta' },
        { key: 'month', label: 'Ay' },
        { key: 'quarter', label: '3 Ay' },
        { key: 'year', label: 'Yıl' },
      ].map((range) => (
        <TouchableOpacity
          key={range.key}
          style={[
            styles.timeRangeButton,
            timeRange === range.key && styles.timeRangeButtonActive,
          ]}
          onPress={() => onTimeRangeChange?.(range.key)}
        >
          <Text
            style={[
              styles.timeRangeButtonText,
              timeRange === range.key && styles.timeRangeButtonTextActive,
            ]}
          >
            {range.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderOverallStats = () => (
    <View style={styles.overallStats}>
      <View style={styles.statCard}>
        <Text style={styles.statValue}>{analytics.totalExpenses}</Text>
        <Text style={styles.statLabel}>Toplam Gider</Text>
      </View>
      <View style={styles.statCard}>
        <Text style={styles.statValue}>{formatAmount(analytics.totalAmount)}</Text>
        <Text style={styles.statLabel}>Toplam Tutar</Text>
      </View>
      <View style={styles.statCard}>
        <Text style={styles.statValue}>
          {formatAmount(analytics.trends.averageExpenseAmount)}
        </Text>
        <Text style={styles.statLabel}>Ortalama Gider</Text>
      </View>
    </View>
  );

  const renderCategoryStats = () => (
    <View style={styles.categoryStats}>
      <Text style={styles.sectionTitle}>Kategori Analizi</Text>
      {analytics.categoryStats.map((stat, index) => (
        <View key={stat.id} style={styles.categoryStatItem}>
          <View style={styles.categoryStatHeader}>
            <View style={styles.categoryStatInfo}>
              <Text style={styles.categoryStatEmoji}>{stat.emoji}</Text>
              <View style={styles.categoryStatDetails}>
                <Text style={styles.categoryStatName}>{stat.name}</Text>
                <Text style={styles.categoryStatUsage}>
                  {stat.usageCount} kullanım • {stat.percentage.toFixed(1)}%
                </Text>
              </View>
            </View>
            <View style={styles.categoryStatTrend}>
              {getTrendIcon(stat.trend)}
              <Text style={[styles.trendText, { color: getTrendColor(stat.trend) }]}>
                {stat.trend === 'increasing' ? 'Artış' : 
                 stat.trend === 'decreasing' ? 'Azalış' : 'Sabit'}
              </Text>
            </View>
          </View>
          
          <View style={styles.categoryStatBar}>
            <View
              style={[
                styles.categoryStatBarFill,
                { 
                  width: `${stat.percentage}%`,
                  backgroundColor: stat.color,
                }
              ]}
            />
          </View>
          
          <View style={styles.categoryStatFooter}>
            <Text style={styles.categoryStatAmount}>
              {formatAmount(stat.totalAmount)}
            </Text>
            <Text style={styles.categoryStatAverage}>
              Ort: {formatAmount(stat.avgAmount)}
            </Text>
            {stat.lastUsed && (
              <Text style={styles.categoryStatLastUsed}>
                Son: {new Date(stat.lastUsed).toLocaleDateString('tr-TR')}
              </Text>
            )}
          </View>
        </View>
      ))}
    </View>
  );

  const renderInsights = () => (
    <View style={styles.insights}>
      <Text style={styles.sectionTitle}>İçgörüler</Text>
      
      <View style={styles.insightCard}>
        <Ionicons name="calendar-outline" size={20} color={Colors.primary} />
        <Text style={styles.insightText}>
          En çok gider girdiğiniz gün: {analytics.trends.mostUsedDay}
        </Text>
      </View>
      
      <View style={styles.insightCard}>
        <Ionicons name="stats-chart-outline" size={20} color={Colors.primary} />
        <Text style={styles.insightText}>
          Günlük ortalama gider sıklığı: {analytics.trends.expenseFrequency.toFixed(1)}
        </Text>
      </View>
      
      {analytics.categoryStats.length > 0 && (
        <View style={styles.insightCard}>
          <Ionicons name="trophy-outline" size={20} color={Colors.primary} />
          <Text style={styles.insightText}>
            En çok kullanılan kategori: {analytics.categoryStats[0].name}
          </Text>
        </View>
      )}
    </View>
  );

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {renderTimeRangeSelector()}
      {renderOverallStats()}
      {renderCategoryStats()}
      {renderInsights()}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  timeRangeSelector: {
    flexDirection: 'row',
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    backgroundColor: Colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  timeRangeButton: {
    flex: 1,
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.md,
    borderRadius: BorderRadius.md,
    marginHorizontal: Spacing.xs,
    backgroundColor: Colors.background,
    borderWidth: 1,
    borderColor: Colors.border,
    alignItems: 'center',
  },
  timeRangeButtonActive: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  timeRangeButtonText: {
    fontSize: FontSize.sm,
    color: Colors.text,
    fontWeight: FontWeight.medium,
  },
  timeRangeButtonTextActive: {
    color: Colors.surface,
    fontWeight: FontWeight.bold,
  },
  overallStats: {
    flexDirection: 'row',
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.lg,
  },
  statCard: {
    flex: 1,
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
    marginHorizontal: Spacing.xs,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.border,
  },
  statValue: {
    fontSize: FontSize.lg,
    fontWeight: FontWeight.bold,
    color: Colors.primary,
    marginBottom: Spacing.xs,
  },
  statLabel: {
    fontSize: FontSize.xs,
    color: Colors.textSecondary,
    textAlign: 'center',
  },
  categoryStats: {
    paddingHorizontal: Spacing.lg,
    paddingBottom: Spacing.lg,
  },
  sectionTitle: {
    fontSize: FontSize.lg,
    fontWeight: FontWeight.bold,
    color: Colors.text,
    marginBottom: Spacing.md,
  },
  categoryStatItem: {
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
    marginBottom: Spacing.md,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  categoryStatHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  categoryStatInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  categoryStatEmoji: {
    fontSize: 24,
    marginRight: Spacing.sm,
  },
  categoryStatDetails: {
    flex: 1,
  },
  categoryStatName: {
    fontSize: FontSize.md,
    fontWeight: FontWeight.medium,
    color: Colors.text,
  },
  categoryStatUsage: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
    marginTop: Spacing.xs,
  },
  categoryStatTrend: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  trendText: {
    fontSize: FontSize.xs,
    marginLeft: Spacing.xs,
    fontWeight: FontWeight.medium,
  },
  categoryStatBar: {
    height: 6,
    backgroundColor: Colors.border,
    borderRadius: 3,
    marginVertical: Spacing.sm,
    overflow: 'hidden',
  },
  categoryStatBarFill: {
    height: '100%',
    borderRadius: 3,
  },
  categoryStatFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  categoryStatAmount: {
    fontSize: FontSize.md,
    fontWeight: FontWeight.bold,
    color: Colors.text,
  },
  categoryStatAverage: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
  },
  categoryStatLastUsed: {
    fontSize: FontSize.xs,
    color: Colors.textSecondary,
  },
  insights: {
    paddingHorizontal: Spacing.lg,
    paddingBottom: Spacing.xl,
  },
  insightCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
    marginBottom: Spacing.sm,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  insightText: {
    fontSize: FontSize.sm,
    color: Colors.text,
    marginLeft: Spacing.md,
    flex: 1,
  },
});
