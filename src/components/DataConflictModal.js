import React from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const DataConflictModal = ({
  visible,
  onClose,
  onContinue,
  guestDataCount = 0,
  guestFieldCount = 0,
  guestCategoryCount = 0,
  userDataCount = 0
}) => {
  const getTotalGuestItems = () => {
    return guestDataCount + guestFieldCount + guestCategoryCount;
  };

  const getGuestDataSummary = () => {
    const items = [];
    if (guestDataCount > 0) items.push(`${guestDataCount} gider`);
    if (guestFieldCount > 0) items.push(`${guestFieldCount} tarla`);
    if (guestCategoryCount > 0) items.push(`${guestCategoryCount} kategori`);
    return items.join(', ');
  };

  const handleContinue = () => {
    const guestSummary = getGuestDataSummary();
    Alert.alert(
      'Emin misiniz?',
      `Google hesabınızdaki ${userDataCount} adet gider SİLİNECEK ve misafir verileriniz (${guestSummary}) ile DEĞİŞTİRİLECEK. Bu işlem geri alınamaz!`,
      [
        {
          text: 'İptal',
          style: 'cancel'
        },
        {
          text: 'Değiştir',
          style: 'destructive',
          onPress: onContinue
        }
      ]
    );
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <View style={styles.modalContainer}>
          {/* Header */}
          <View style={styles.header}>
            <Ionicons name="warning" size={32} color="#E74C3C" />
            <Text style={styles.title}>Veri Değiştirme Uyarısı</Text>
          </View>

          {/* Content */}
          <View style={styles.content}>
            <Text style={styles.description}>
              Bu Google hesabında zaten <Text style={styles.highlight}>{userDataCount} adet gider</Text> bulunuyor.
            </Text>

            <Text style={styles.description}>
              Misafir modunda <Text style={styles.highlight}>{getGuestDataSummary()}</Text> var.
            </Text>

            {/* Guest Data Details */}
            {getTotalGuestItems() > 0 && (
              <View style={styles.infoBox}>
                <Ionicons name="information-circle" size={20} color="#3498DB" />
                <View style={styles.infoContent}>
                  <Text style={styles.infoText}>Misafir Verileriniz:</Text>
                  {guestDataCount > 0 && <Text style={styles.infoDetail}>• {guestDataCount} gider kaydı</Text>}
                  {guestFieldCount > 0 && <Text style={styles.infoDetail}>• {guestFieldCount} tarla bilgisi</Text>}
                  {guestCategoryCount > 0 && <Text style={styles.infoDetail}>• {guestCategoryCount} özel kategori</Text>}
                </View>
              </View>
            )}

            <View style={styles.warningBox}>
              <Ionicons name="alert-circle" size={20} color="#E74C3C" />
              <Text style={styles.warningText}>
                Google hesabınızdaki mevcut veriler SİLİNECEK ve misafir verileriniz ile değiştirilecek!
              </Text>
            </View>

            <Text style={styles.suggestion}>
              ⚠️ <Text style={styles.suggestionBold}>Önerimiz:</Text> Verilerinizi kaybetmemek için farklı bir Google hesabı kullanın.
            </Text>
          </View>

          {/* Actions */}
          <View style={styles.actions}>
            <TouchableOpacity
              style={[styles.button, styles.secondaryButton]}
              onPress={onClose}
            >
              <Text style={styles.secondaryButtonText}>İptal</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.button, styles.dangerButton]}
              onPress={handleContinue}
            >
              <Ionicons name="trash" size={16} color="#FFFFFF" />
              <Text style={styles.dangerButtonText}>Değiştir</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 24,
    width: '100%',
    maxWidth: 400,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 8,
  },
  header: {
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2C3E50',
    marginTop: 8,
  },
  content: {
    marginBottom: 24,
  },
  description: {
    fontSize: 16,
    color: '#34495E',
    lineHeight: 24,
    marginBottom: 12,
    textAlign: 'center',
  },
  highlight: {
    fontWeight: 'bold',
    color: '#2C3E50',
  },
  warningBox: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFEBEE',
    padding: 12,
    borderRadius: 8,
    marginVertical: 16,
    borderLeftWidth: 4,
    borderLeftColor: '#E74C3C',
  },
  warningText: {
    fontSize: 14,
    color: '#C62828',
    fontWeight: '600',
    marginLeft: 8,
    flex: 1,
  },
  infoBox: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#E8F4FD',
    padding: 12,
    borderRadius: 8,
    marginVertical: 16,
    borderLeftWidth: 4,
    borderLeftColor: '#3498DB',
  },
  infoContent: {
    marginLeft: 8,
    flex: 1,
  },
  infoText: {
    fontSize: 14,
    color: '#2980B9',
    fontWeight: '600',
    marginBottom: 4,
  },
  infoDetail: {
    fontSize: 13,
    color: '#2980B9',
    marginLeft: 8,
    lineHeight: 18,
  },
  suggestion: {
    fontSize: 14,
    color: '#7F8C8D',
    lineHeight: 20,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  suggestionBold: {
    fontWeight: 'bold',
    color: '#2C3E50',
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 8,
  },
  button: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    minHeight: 44,
  },
  secondaryButton: {
    backgroundColor: '#ECF0F1',
    borderWidth: 1,
    borderColor: '#BDC3C7',
  },
  primaryButton: {
    backgroundColor: '#3498DB',
  },
  dangerButton: {
    backgroundColor: '#E74C3C',
  },
  successButton: {
    backgroundColor: '#27AE60',
  },
  secondaryButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2C3E50',
  },
  primaryButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
    marginLeft: 4,
  },
  dangerButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
    marginLeft: 4,
  },
  successButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
    marginLeft: 4,
  },
});

export default DataConflictModal;
