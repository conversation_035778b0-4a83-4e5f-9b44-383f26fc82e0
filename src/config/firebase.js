/**
 * Firebase Configuration for Çiftçi Not Defterim
 * This file contains Firebase project configuration
 */

// Firebase configuration object

export const firebaseConfig = {
  apiKey: "AIzaSyA1KuLDihgTxfqzVb1kAeh1tw3XWRirRlY", 
  authDomain: "ciftcinotdefterim.firebaseapp.com",
  projectId: "ciftcinotdefterim",
  storageBucket: "ciftcinotdefterim.appspot.com",
  messagingSenderId: "680682523446",
  appId: "1:680682523446:web:28b30fea7e232f7789c197",
  measurementId: "G-494717461"
};

export const googleSignInConfig = {
  webClientId: "680682523446-rd7a61p4qvp2r8nih9sv151dlj4mji0r.apps.googleusercontent.com",
  offlineAccess: true,
  hostedDomain: '',
  forceCodeForRefreshToken: true,
};

// Environment-specific configurations
export const getFirebaseConfig = () => {
  const environment = __DEV__ ? 'development' : 'production';
  
  switch (environment) {
    case 'development':
      return {
        ...firebaseConfig,
        // Development-specific overrides
        projectId: "ciftci-not-defterim-dev",
      };
    case 'production':
      return {
        ...firebaseConfig,
        // Production-specific overrides
        projectId: "ciftci-not-defterim-prod",
      };
    default:
      return firebaseConfig;
  }
};

// Firebase initialization status
export let firebaseInitialized = false;

// Initialize Firebase (called from App.js)
export const initializeFirebase = async () => {
  try {
    // Firebase is automatically initialized by @react-native-firebase/app
    // This function can be used for additional setup if needed
    
    firebaseInitialized = true;
    console.log('Firebase initialized successfully');
    return true;
  } catch (error) {
    console.error('Firebase initialization error:', error);
    return false;
  }
};

// Firebase service configurations
export const firebaseServices = {
  auth: {
    persistence: true,
    enableMultiFactor: false,
  },
  firestore: {
    enablePersistence: true,
    cacheSizeBytes: 40000000, // 40MB cache
  },
  storage: {
    maxUploadRetryTime: 120000, // 2 minutes
    maxOperationRetryTime: 120000,
  },
  analytics: {
    enabled: !__DEV__, // Disable analytics in development
  }
};

export default firebaseConfig;
