/**
 * Seasonal Models for Çiftçi Not Defterim
 * Agricultural season definitions and farming cycle models
 */

// Agricultural seasons based on Turkish farming cycles
export const AgriculturalSeasons = {
  SPRING: {
    id: 'spring',
    name: '<PERSON>lk<PERSON>ar Ekimi',
    shortName: '<PERSON><PERSON>bahar',
    emoji: '🌱',
    color: '#4CAF50',
    months: [3, 4, 5], // March, April, May
    description: 'Ekim ve fide dikimi dönemi',
    activities: [
      'Toprak hazırlığı',
      'Tohum ekimi',
      'Fide dikimi',
      '<PERSON><PERSON> gübreleme',
      'Sulama sistemleri kurulumu',
    ],
    commonExpenses: [
      'Tohum ve fide',
      '<PERSON><PERSON><PERSON>',
      '<PERSON>rak işleme',
      'Sulama ekipmanları',
      'İşçilik',
    ],
    weatherConditions: 'Ilıman, yağışlı',
    averageTemperature: '15-20°C',
  },
  SUMMER: {
    id: 'summer',
    name: '<PERSON><PERSON>',
    shortName: 'Yaz',
    emoji: '☀️',
    color: '#FF9800',
    months: [6, 7, 8], // June, July, August
    description: '<PERSON>k<PERSON><PERSON> ve büyüme dönemi',
    activities: [
      '<PERSON><PERSON><PERSON><PERSON> sulama',
      '<PERSON>t temizliği',
      '<PERSON>laçlama',
      'Ek gübreleme',
      'Hastalık kontrolü',
    ],
    commonExpenses: [
      'Su ve sulama',
      'İlaç ve pestisit',
      'Gübre',
      'Yakıt (sulama pompaları)',
      'İşçilik',
    ],
    weatherConditions: 'Sıcak, kurak',
    averageTemperature: '25-35°C',
  },
  AUTUMN: {
    id: 'autumn',
    name: 'Sonbahar Hasadı',
    shortName: 'Sonbahar',
    emoji: '🍂',
    color: '#FF5722',
    months: [9, 10, 11], // September, October, November
    description: 'Hasat ve depolama dönemi',
    activities: [
      'Hasat işlemleri',
      'Ürün depolama',
      'Pazarlama',
      'Kış hazırlığı',
      'Makine bakımı',
    ],
    commonExpenses: [
      'Hasat işçiliği',
      'Depolama malzemeleri',
      'Nakliye',
      'Makine bakımı',
      'Pazarlama',
    ],
    weatherConditions: 'Serin, değişken',
    averageTemperature: '10-20°C',
  },
  WINTER: {
    id: 'winter',
    name: 'Kış Hazırlığı',
    shortName: 'Kış',
    emoji: '❄️',
    color: '#2196F3',
    months: [12, 1, 2], // December, January, February
    description: 'Planlama ve hazırlık dönemi',
    activities: [
      'Toprak analizi',
      'Planlama',
      'Ekipman bakımı',
      'Eğitim ve araştırma',
      'Gelecek sezon hazırlığı',
    ],
    commonExpenses: [
      'Toprak analizi',
      'Makine bakımı',
      'Planlama danışmanlığı',
      'Eğitim',
      'Ekipman yenileme',
    ],
    weatherConditions: 'Soğuk, yağışlı',
    averageTemperature: '0-10°C',
  },
};

// Crop-specific seasonal cycles
export const CropCycles = {
  WHEAT: {
    id: 'wheat',
    name: 'Buğday',
    emoji: '🌾',
    type: 'cereal',
    seasons: {
      planting: ['autumn', 'winter'], // Kışlık buğday
      growing: ['winter', 'spring'],
      harvesting: ['summer'],
      resting: ['autumn'],
    },
    duration: 8, // months
    commonVarieties: ['Kışlık Buğday', 'Yazlık Buğday'],
  },
  CORN: {
    id: 'corn',
    name: 'Mısır',
    emoji: '🌽',
    type: 'cereal',
    seasons: {
      planting: ['spring'],
      growing: ['spring', 'summer'],
      harvesting: ['autumn'],
      resting: ['winter'],
    },
    duration: 6, // months
    commonVarieties: ['Tatlı Mısır', 'Yem Mısırı', 'Patlamış Mısır'],
  },
  TOMATO: {
    id: 'tomato',
    name: 'Domates',
    emoji: '🍅',
    type: 'vegetable',
    seasons: {
      planting: ['spring'],
      growing: ['spring', 'summer'],
      harvesting: ['summer', 'autumn'],
      resting: ['winter'],
    },
    duration: 5, // months
    commonVarieties: ['Sera Domatesi', 'Açık Alan Domatesi'],
  },
  OLIVE: {
    id: 'olive',
    name: 'Zeytin',
    emoji: '🫒',
    type: 'fruit',
    seasons: {
      planting: ['spring'], // For new trees
      growing: ['spring', 'summer'],
      harvesting: ['autumn'],
      pruning: ['winter'],
    },
    duration: 12, // perennial
    commonVarieties: ['Gemlik', 'Ayvalık', 'Memecik'],
  },
  COTTON: {
    id: 'cotton',
    name: 'Pamuk',
    emoji: '🌿',
    type: 'industrial',
    seasons: {
      planting: ['spring'],
      growing: ['spring', 'summer'],
      harvesting: ['autumn'],
      resting: ['winter'],
    },
    duration: 6, // months
    commonVarieties: ['Beyaz Altın', 'Nazilli 84'],
  },
};

// Regional farming patterns for Turkey
export const RegionalPatterns = {
  MEDITERRANEAN: {
    id: 'mediterranean',
    name: 'Akdeniz Bölgesi',
    climate: 'Mediterranean',
    characteristics: [
      'Sıcak, kurak yazlar',
      'Ilıman, yağışlı kışlar',
      'Uzun vejetasyon süresi',
    ],
    commonCrops: ['citrus', 'olive', 'cotton', 'vegetables'],
    seasonAdjustments: {
      spring: { start: 2, end: 5 }, // Earlier spring
      summer: { start: 5, end: 10 }, // Longer summer
      autumn: { start: 10, end: 12 },
      winter: { start: 12, end: 2 },
    },
  },
  CENTRAL_ANATOLIA: {
    id: 'central_anatolia',
    name: 'İç Anadolu Bölgesi',
    climate: 'Continental',
    characteristics: [
      'Sıcak, kurak yazlar',
      'Soğuk, kar yağışlı kışlar',
      'Büyük sıcaklık farkları',
    ],
    commonCrops: ['wheat', 'barley', 'sugar_beet', 'sunflower'],
    seasonAdjustments: {
      spring: { start: 3, end: 5 },
      summer: { start: 6, end: 8 },
      autumn: { start: 9, end: 11 },
      winter: { start: 12, end: 2 },
    },
  },
  BLACK_SEA: {
    id: 'black_sea',
    name: 'Karadeniz Bölgesi',
    climate: 'Oceanic',
    characteristics: [
      'Yıl boyunca yağışlı',
      'Ilıman sıcaklıklar',
      'Yüksek nem',
    ],
    commonCrops: ['tea', 'hazelnut', 'corn', 'tobacco'],
    seasonAdjustments: {
      spring: { start: 3, end: 6 }, // Longer spring
      summer: { start: 6, end: 8 },
      autumn: { start: 9, end: 11 },
      winter: { start: 12, end: 2 },
    },
  },
  AEGEAN: {
    id: 'aegean',
    name: 'Ege Bölgesi',
    climate: 'Mediterranean',
    characteristics: [
      'Sıcak, kurak yazlar',
      'Ilıman kışlar',
      'Verimli topraklar',
    ],
    commonCrops: ['olive', 'grape', 'cotton', 'tobacco'],
    seasonAdjustments: {
      spring: { start: 3, end: 5 },
      summer: { start: 6, end: 9 },
      autumn: { start: 9, end: 11 },
      winter: { start: 12, end: 2 },
    },
  },
};

// Seasonal expense categories mapping
export const SeasonalExpenseMapping = {
  spring: {
    primary: ['seed', 'fertilizer', 'labor', 'equipment'],
    secondary: ['fuel', 'water', 'transport'],
    description: 'Ekim ve hazırlık giderleri ağırlıklı',
  },
  summer: {
    primary: ['water', 'pesticide', 'fertilizer', 'fuel'],
    secondary: ['labor', 'equipment', 'maintenance'],
    description: 'Bakım ve sulama giderleri ağırlıklı',
  },
  autumn: {
    primary: ['labor', 'transport', 'storage', 'marketing'],
    secondary: ['fuel', 'equipment', 'maintenance'],
    description: 'Hasat ve pazarlama giderleri ağırlıklı',
  },
  winter: {
    primary: ['maintenance', 'planning', 'equipment', 'education'],
    secondary: ['fuel', 'storage', 'analysis'],
    description: 'Bakım ve planlama giderleri ağırlıklı',
  },
};

// Helper functions for seasonal calculations
export const SeasonalHelpers = {
  // Get current season based on date
  getCurrentSeason: (date = new Date()) => {
    const month = date.getMonth() + 1; // JavaScript months are 0-based
    
    if (month >= 3 && month <= 5) return AgriculturalSeasons.SPRING;
    if (month >= 6 && month <= 8) return AgriculturalSeasons.SUMMER;
    if (month >= 9 && month <= 11) return AgriculturalSeasons.AUTUMN;
    return AgriculturalSeasons.WINTER;
  },

  // Get season by month
  getSeasonByMonth: (month) => {
    if (month >= 3 && month <= 5) return AgriculturalSeasons.SPRING;
    if (month >= 6 && month <= 8) return AgriculturalSeasons.SUMMER;
    if (month >= 9 && month <= 11) return AgriculturalSeasons.AUTUMN;
    return AgriculturalSeasons.WINTER;
  },

  // Get season by date string
  getSeasonByDate: (dateString) => {
    const date = new Date(dateString);
    return SeasonalHelpers.getCurrentSeason(date);
  },

  // Get all seasons as array
  getAllSeasons: () => Object.values(AgriculturalSeasons),

  // Get season duration in days
  getSeasonDuration: (seasonId) => {
    const season = AgriculturalSeasons[seasonId.toUpperCase()];
    return season ? season.months.length * 30 : 90; // Approximate
  },

  // Check if date is in specific season
  isDateInSeason: (date, seasonId) => {
    const season = SeasonalHelpers.getSeasonByDate(date);
    return season.id === seasonId;
  },

  // Get next season
  getNextSeason: (currentSeasonId) => {
    const seasons = ['spring', 'summer', 'autumn', 'winter'];
    const currentIndex = seasons.indexOf(currentSeasonId);
    const nextIndex = (currentIndex + 1) % seasons.length;
    return AgriculturalSeasons[seasons[nextIndex].toUpperCase()];
  },

  // Get previous season
  getPreviousSeason: (currentSeasonId) => {
    const seasons = ['spring', 'summer', 'autumn', 'winter'];
    const currentIndex = seasons.indexOf(currentSeasonId);
    const prevIndex = currentIndex === 0 ? 3 : currentIndex - 1;
    return AgriculturalSeasons[seasons[prevIndex].toUpperCase()];
  },

  // Get seasonal expense priorities
  getSeasonalExpensePriorities: (seasonId) => {
    return SeasonalExpenseMapping[seasonId] || SeasonalExpenseMapping.spring;
  },

  // Calculate season progress (0-100%)
  getSeasonProgress: (date = new Date()) => {
    const season = SeasonalHelpers.getCurrentSeason(date);
    const month = date.getMonth() + 1;
    const day = date.getDate();
    
    const seasonMonths = season.months;
    const currentMonthIndex = seasonMonths.indexOf(month);
    
    if (currentMonthIndex === -1) return 0;
    
    const daysInMonth = new Date(date.getFullYear(), month, 0).getDate();
    const monthProgress = day / daysInMonth;
    const seasonProgress = (currentMonthIndex + monthProgress) / seasonMonths.length;
    
    return Math.round(seasonProgress * 100);
  },

  // Get recommended crops for season
  getRecommendedCrops: (seasonId) => {
    return Object.values(CropCycles).filter(crop => 
      crop.seasons.planting.includes(seasonId) || 
      crop.seasons.growing.includes(seasonId)
    );
  },
};

export default {
  AgriculturalSeasons,
  CropCycles,
  RegionalPatterns,
  SeasonalExpenseMapping,
  SeasonalHelpers,
};
