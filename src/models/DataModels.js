/**
 * Data Models and Schemas for Çiftçi Not Defterim
 * Comprehensive type definitions and validation schemas
 */

// User Model
export const UserModel = {
  id: 'string', // Primary key
  email: 'string', // Unique email address
  name: 'string', // Display name
  authProvider: 'string', // 'google', 'guest', etc.
  preferences: {
    currency: 'string', // 'TRY', 'USD', etc.
    language: 'string', // 'tr', 'en', etc.
    theme: 'string', // 'light', 'dark', 'auto'
    notifications: 'boolean',
    defaultSeason: 'string' // Default season preference
  },
  createdAt: 'datetime',
  updatedAt: 'datetime'
};

// Category Model
export const CategoryModel = {
  id: 'string', // Primary key
  userId: 'string', // Foreign key to users table (null for default categories)
  name: 'string', // Category name (required)
  emoji: 'string', // Emoji icon
  color: 'string', // Hex color code
  icon: 'string', // Icon name for fallback
  isDefault: 'boolean', // Whether this is a system default category
  usageCount: 'number', // How many times this category has been used
  description: 'string', // Optional description
  isActive: 'boolean', // Whether category is active/visible
  sortOrder: 'number', // Custom sort order
  createdAt: 'datetime',
  updatedAt: 'datetime'
};

// Season Model
export const SeasonModel = {
  id: 'string', // Primary key ('spring', 'summer', 'autumn', 'winter')
  name: 'string', // Display name
  startMonth: 'number', // Starting month (1-12)
  endMonth: 'number', // Ending month (1-12)
  description: 'string', // Season description
  typicalCrops: 'array', // JSON array of typical crops for this season
  color: 'string', // Theme color for the season
  icon: 'string', // Season icon
  budgetMultiplier: 'number', // Budget adjustment factor for this season
  createdAt: 'datetime'
};

// Expense Model
export const ExpenseModel = {
  id: 'string', // Primary key
  userId: 'string', // Foreign key to users table
  categoryId: 'string', // Foreign key to categories table
  seasonId: 'string', // Foreign key to seasons table
  amount: 'number', // Expense amount (required)
  description: 'string', // Optional description
  date: 'date', // Expense date (required)
  location: {
    latitude: 'number',
    longitude: 'number',
    address: 'string' // Human-readable address
  },
  photos: 'array', // Array of photo URLs/paths
  tags: 'array', // Array of custom tags
  isRecurring: 'boolean', // Whether this is a recurring expense
  recurringPattern: {
    frequency: 'string', // 'daily', 'weekly', 'monthly', 'yearly'
    interval: 'number', // Every N periods
    endDate: 'date' // When recurring ends
  },
  paymentMethod: 'string', // 'cash', 'card', 'bank_transfer', etc.
  vendor: 'string', // Vendor/supplier name
  invoiceNumber: 'string', // Invoice or receipt number
  isVerified: 'boolean', // Whether expense has been verified
  createdAt: 'datetime',
  updatedAt: 'datetime'
};

// Budget Model
export const BudgetModel = {
  id: 'string', // Primary key
  userId: 'string', // Foreign key to users table
  seasonId: 'string', // Foreign key to seasons table
  categoryId: 'string', // Foreign key to categories table (null for total budget)
  budgetAmount: 'number', // Planned budget amount
  spentAmount: 'number', // Amount spent so far
  year: 'number', // Budget year
  isActive: 'boolean', // Whether budget is active
  alertThreshold: 'number', // Percentage threshold for alerts (0-100)
  notes: 'string', // Budget notes
  createdAt: 'datetime',
  updatedAt: 'datetime'
};

// Report Model
export const ReportModel = {
  id: 'string', // Primary key
  userId: 'string', // Foreign key to users table
  name: 'string', // Report name
  type: 'string', // 'seasonal', 'category', 'trend', 'comparison'
  parameters: 'object', // Report parameters (JSON)
  data: 'object', // Cached report data (JSON)
  isScheduled: 'boolean', // Whether report is scheduled
  scheduleFrequency: 'string', // 'weekly', 'monthly', 'seasonal'
  lastGenerated: 'datetime',
  createdAt: 'datetime',
  updatedAt: 'datetime'
};

// Notification Model
export const NotificationModel = {
  id: 'string', // Primary key
  userId: 'string', // Foreign key to users table
  type: 'string', // 'budget_alert', 'reminder', 'seasonal_tip', etc.
  title: 'string', // Notification title
  message: 'string', // Notification message
  data: 'object', // Additional data (JSON)
  isRead: 'boolean', // Whether notification has been read
  priority: 'string', // 'low', 'medium', 'high'
  scheduledFor: 'datetime', // When to show notification
  expiresAt: 'datetime', // When notification expires
  createdAt: 'datetime'
};

// Default Categories Data
export const DefaultCategories = [
  {
    id: 'fertilizer',
    name: 'Gübre',
    emoji: '🌱',
    color: '#4CAF50',
    icon: 'leaf',
    description: 'Toprak gübresi, yaprak gübresi ve organik gübreler',
    isDefault: true,
    sortOrder: 1
  },
  {
    id: 'pesticide',
    name: 'İlaç',
    emoji: '🐛',
    color: '#FF5722',
    icon: 'bug',
    description: 'Böcek ilacı, fungisit, herbisit ve diğer tarım ilaçları',
    isDefault: true,
    sortOrder: 2
  },
  {
    id: 'water',
    name: 'Su & Sulama',
    emoji: '💧',
    color: '#2196F3',
    icon: 'water',
    description: 'Sulama suyu, damla sulama sistemi ve su masrafları',
    isDefault: true,
    sortOrder: 3
  },
  {
    id: 'labor',
    name: 'İşçilik',
    emoji: '👷',
    color: '#9C27B0',
    icon: 'people',
    description: 'Gündelik işçi, sezonluk işçi ve işçilik masrafları',
    isDefault: true,
    sortOrder: 4
  },
  {
    id: 'fuel',
    name: 'Yakıt & Enerji',
    emoji: '⛽',
    color: '#FF9800',
    icon: 'car',
    description: 'Mazot, benzin, elektrik ve enerji masrafları',
    isDefault: true,
    sortOrder: 5
  },
  {
    id: 'equipment',
    name: 'Makine & Ekipman',
    emoji: '🚜',
    color: '#795548',
    icon: 'build',
    description: 'Traktör, pulluk, ekim makinesi ve diğer tarım aletleri',
    isDefault: true,
    sortOrder: 6
  },
  {
    id: 'seed',
    name: 'Tohum & Fide',
    emoji: '🌾',
    color: '#8BC34A',
    icon: 'grain',
    description: 'Tohum, fide, fidan ve ekim materyalleri',
    isDefault: true,
    sortOrder: 7
  },
  {
    id: 'maintenance',
    name: 'Bakım & Onarım',
    emoji: '🔧',
    color: '#607D8B',
    icon: 'build',
    description: 'Makine bakımı, onarım ve yedek parça masrafları',
    isDefault: true,
    sortOrder: 8
  },
  {
    id: 'transport',
    name: 'Nakliye',
    emoji: '🚛',
    color: '#FF5722',
    icon: 'truck',
    description: 'Ürün nakliyesi, malzeme taşıma ve ulaşım masrafları',
    isDefault: true,
    sortOrder: 9
  },
  {
    id: 'other',
    name: 'Diğer',
    emoji: '📝',
    color: '#9E9E9E',
    icon: 'more-horiz',
    description: 'Diğer tarımsal giderler ve çeşitli masraflar',
    isDefault: true,
    sortOrder: 10
  }
];

// Default Seasons Data
export const DefaultSeasons = [
  {
    id: 'spring',
    name: 'İlkbahar Ekimi',
    startMonth: 3,
    endMonth: 5,
    description: 'İlkbahar ekim ve dikim dönemi',
    typicalCrops: ['buğday', 'arpa', 'mısır', 'ayçiçeği', 'şeker pancarı'],
    color: '#4CAF50',
    icon: 'flower',
    budgetMultiplier: 1.2
  },
  {
    id: 'summer',
    name: 'Yaz Bakımı',
    startMonth: 6,
    endMonth: 8,
    description: 'Yaz bakım, sulama ve ilaçlama dönemi',
    typicalCrops: ['domates', 'biber', 'patlıcan', 'kavun', 'karpuz'],
    color: '#FF9800',
    icon: 'sunny',
    budgetMultiplier: 1.5
  },
  {
    id: 'autumn',
    name: 'Sonbahar Hasadı',
    startMonth: 9,
    endMonth: 11,
    description: 'Hasat, depolama ve satış dönemi',
    typicalCrops: ['buğday', 'mısır', 'ayçiçeği', 'şeker pancarı', 'patates'],
    color: '#FF5722',
    icon: 'harvest',
    budgetMultiplier: 0.8
  },
  {
    id: 'winter',
    name: 'Kış Hazırlığı',
    startMonth: 12,
    endMonth: 2,
    description: 'Kış hazırlığı, planlama ve bakım dönemi',
    typicalCrops: ['kış buğdayı', 'arpa', 'çavdar'],
    color: '#2196F3',
    icon: 'snow',
    budgetMultiplier: 0.6
  }
];

// Validation Schemas
export const ValidationSchemas = {
  expense: {
    amount: {
      required: true,
      type: 'number',
      min: 0.01,
      max: 999999999
    },
    categoryId: {
      required: true,
      type: 'string'
    },
    date: {
      required: true,
      type: 'date'
    },
    description: {
      required: false,
      type: 'string',
      maxLength: 500
    }
  },
  category: {
    name: {
      required: true,
      type: 'string',
      minLength: 1,
      maxLength: 50
    },
    emoji: {
      required: false,
      type: 'string',
      maxLength: 10
    },
    color: {
      required: false,
      type: 'string',
      pattern: '^#[0-9A-Fa-f]{6}$'
    }
  }
};

// Database Schema SQL
export const DatabaseSchema = {
  users: `
    CREATE TABLE IF NOT EXISTS users (
      id TEXT PRIMARY KEY,
      email TEXT UNIQUE,
      name TEXT,
      auth_provider TEXT,
      preferences TEXT, -- JSON
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    );
  `,
  categories: `
    CREATE TABLE IF NOT EXISTS categories (
      id TEXT PRIMARY KEY,
      user_id TEXT,
      name TEXT NOT NULL,
      emoji TEXT,
      color TEXT,
      icon TEXT,
      is_default BOOLEAN DEFAULT 0,
      usage_count INTEGER DEFAULT 0,
      description TEXT,
      is_active BOOLEAN DEFAULT 1,
      sort_order INTEGER DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users(id)
    );
  `,
  seasons: `
    CREATE TABLE IF NOT EXISTS seasons (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      start_month INTEGER,
      end_month INTEGER,
      description TEXT,
      typical_crops TEXT, -- JSON
      color TEXT,
      icon TEXT,
      budget_multiplier REAL DEFAULT 1.0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    );
  `,
  expenses: `
    CREATE TABLE IF NOT EXISTS expenses (
      id TEXT PRIMARY KEY,
      user_id TEXT,
      category_id TEXT,
      season_id TEXT,
      amount REAL NOT NULL,
      description TEXT,
      date DATE NOT NULL,
      location_lat REAL,
      location_lng REAL,
      location_address TEXT,
      photos TEXT, -- JSON
      tags TEXT, -- JSON
      is_recurring BOOLEAN DEFAULT 0,
      recurring_pattern TEXT, -- JSON
      payment_method TEXT,
      vendor TEXT,
      invoice_number TEXT,
      is_verified BOOLEAN DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users(id),
      FOREIGN KEY (category_id) REFERENCES categories(id),
      FOREIGN KEY (season_id) REFERENCES seasons(id)
    );
  `,
  budgets: `
    CREATE TABLE IF NOT EXISTS budgets (
      id TEXT PRIMARY KEY,
      user_id TEXT,
      season_id TEXT,
      category_id TEXT,
      budget_amount REAL NOT NULL,
      spent_amount REAL DEFAULT 0,
      year INTEGER NOT NULL,
      is_active BOOLEAN DEFAULT 1,
      alert_threshold REAL DEFAULT 80,
      notes TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users(id),
      FOREIGN KEY (season_id) REFERENCES seasons(id),
      FOREIGN KEY (category_id) REFERENCES categories(id)
    );
  `,
  indexes: [
    'CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(date);',
    'CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category_id);',
    'CREATE INDEX IF NOT EXISTS idx_expenses_season ON expenses(season_id);',
    'CREATE INDEX IF NOT EXISTS idx_expenses_user ON expenses(user_id);',
    'CREATE INDEX IF NOT EXISTS idx_categories_user ON categories(user_id);',
    'CREATE INDEX IF NOT EXISTS idx_budgets_user_season ON budgets(user_id, season_id);'
  ]
};
