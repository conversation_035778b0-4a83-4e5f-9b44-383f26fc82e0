/**
 * Analytics Utilities for Çiftçi Not Defterim
 * User behavior tracking and app usage analytics
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import Logger, { LogCategory } from './Logger';

// Event types
export const AnalyticsEvent = {
  // App lifecycle
  APP_OPENED: 'app_opened',
  APP_CLOSED: 'app_closed',
  APP_BACKGROUNDED: 'app_backgrounded',
  APP_FOREGROUNDED: 'app_foregrounded',
  
  // Authentication
  LOGIN_ATTEMPT: 'login_attempt',
  LOGIN_SUCCESS: 'login_success',
  LOGIN_FAILED: 'login_failed',
  LOGOUT: 'logout',
  GUEST_MODE_ENTERED: 'guest_mode_entered',
  
  // Expense management
  EXPENSE_CREATED: 'expense_created',
  EXPENSE_EDITED: 'expense_edited',
  EXPENSE_DELETED: 'expense_deleted',
  EXPENSE_VIEWED: 'expense_viewed',
  
  // Category management
  CATEGORY_CREATED: 'category_created',
  CATEGORY_EDITED: 'category_edited',
  CATEGORY_DELETED: 'category_deleted',
  CATEGORY_SELECTED: 'category_selected',
  
  // Navigation
  SCREEN_VIEWED: 'screen_viewed',
  TAB_SWITCHED: 'tab_switched',
  
  // Features
  REPORT_GENERATED: 'report_generated',
  EXPORT_PERFORMED: 'export_performed',
  SEARCH_PERFORMED: 'search_performed',
  FILTER_APPLIED: 'filter_applied',
  
  // Tutorial
  TUTORIAL_STARTED: 'tutorial_started',
  TUTORIAL_COMPLETED: 'tutorial_completed',
  TUTORIAL_SKIPPED: 'tutorial_skipped',
  TUTORIAL_STEP_VIEWED: 'tutorial_step_viewed',
  
  // Errors
  ERROR_OCCURRED: 'error_occurred',
  CRASH_OCCURRED: 'crash_occurred',
};

class Analytics {
  constructor() {
    this.sessionId = this.generateSessionId();
    this.sessionStartTime = Date.now();
    this.isEnabled = true;
    this.eventQueue = [];
    this.maxQueueSize = 100;
    this.flushInterval = 30000; // 30 seconds
    this.userProperties = {};
    
    this.startSession();
    this.setupPeriodicFlush();
  }

  // Generate unique session ID
  generateSessionId() {
    return `analytics_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Start analytics session
  startSession() {
    this.trackEvent(AnalyticsEvent.APP_OPENED, {
      sessionId: this.sessionId,
      timestamp: this.sessionStartTime,
    });
  }

  // End analytics session
  endSession() {
    const sessionDuration = Date.now() - this.sessionStartTime;
    this.trackEvent(AnalyticsEvent.APP_CLOSED, {
      sessionId: this.sessionId,
      sessionDuration,
    });
    this.flush();
  }

  // Enable/disable analytics
  setEnabled(enabled) {
    this.isEnabled = enabled;
    Logger.info(LogCategory.ANALYTICS, `Analytics ${enabled ? 'enabled' : 'disabled'}`);
  }

  // Set user properties
  setUserProperties(properties) {
    this.userProperties = { ...this.userProperties, ...properties };
    Logger.debug(LogCategory.ANALYTICS, 'User properties updated', properties);
  }

  // Track event
  trackEvent(eventName, properties = {}) {
    if (!this.isEnabled) return;

    const event = {
      id: `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name: eventName,
      timestamp: Date.now(),
      sessionId: this.sessionId,
      properties: {
        ...this.userProperties,
        ...properties,
      },
      userAgent: navigator.userAgent,
      url: window.location?.href,
    };

    this.eventQueue.push(event);
    Logger.debug(LogCategory.ANALYTICS, `Event tracked: ${eventName}`, event);

    // Auto-flush if queue is full
    if (this.eventQueue.length >= this.maxQueueSize) {
      this.flush();
    }
  }

  // Track screen view
  trackScreenView(screenName, properties = {}) {
    this.trackEvent(AnalyticsEvent.SCREEN_VIEWED, {
      screenName,
      ...properties,
    });
  }

  // Track user action with timing
  trackTimedAction(actionName, startTime, properties = {}) {
    const duration = Date.now() - startTime;
    this.trackEvent(actionName, {
      duration,
      ...properties,
    });
  }

  // Track error
  trackError(error, context = {}) {
    this.trackEvent(AnalyticsEvent.ERROR_OCCURRED, {
      errorName: error.name,
      errorMessage: error.message,
      errorStack: error.stack,
      context,
    });
  }

  // Track expense operations
  trackExpenseCreated(expense, categoryName) {
    this.trackEvent(AnalyticsEvent.EXPENSE_CREATED, {
      amount: expense.amount,
      categoryId: expense.categoryId,
      categoryName,
      hasDescription: !!expense.description,
      hasPhotos: expense.photos && expense.photos.length > 0,
    });
  }

  trackExpenseEdited(expenseId, changes) {
    this.trackEvent(AnalyticsEvent.EXPENSE_EDITED, {
      expenseId,
      changedFields: Object.keys(changes),
      changeCount: Object.keys(changes).length,
    });
  }

  trackExpenseDeleted(expenseId, amount, categoryName) {
    this.trackEvent(AnalyticsEvent.EXPENSE_DELETED, {
      expenseId,
      amount,
      categoryName,
    });
  }

  // Track category operations
  trackCategoryCreated(category) {
    this.trackEvent(AnalyticsEvent.CATEGORY_CREATED, {
      categoryName: category.name,
      hasCustomEmoji: !!category.emoji,
      hasCustomColor: !!category.color,
    });
  }

  trackCategorySelected(categoryId, categoryName) {
    this.trackEvent(AnalyticsEvent.CATEGORY_SELECTED, {
      categoryId,
      categoryName,
    });
  }

  // Track authentication
  trackLoginAttempt(method) {
    this.trackEvent(AnalyticsEvent.LOGIN_ATTEMPT, { method });
  }

  trackLoginSuccess(method, userInfo) {
    this.trackEvent(AnalyticsEvent.LOGIN_SUCCESS, {
      method,
      isNewUser: userInfo.isNewUser,
    });
    
    this.setUserProperties({
      userId: userInfo.id,
      authMethod: method,
      isGuest: method === 'guest',
    });
  }

  trackLoginFailed(method, error) {
    this.trackEvent(AnalyticsEvent.LOGIN_FAILED, {
      method,
      errorMessage: error.message,
    });
  }

  // Track tutorial progress
  trackTutorialStarted() {
    this.trackEvent(AnalyticsEvent.TUTORIAL_STARTED);
  }

  trackTutorialStepViewed(stepNumber, stepName) {
    this.trackEvent(AnalyticsEvent.TUTORIAL_STEP_VIEWED, {
      stepNumber,
      stepName,
    });
  }

  trackTutorialCompleted(totalSteps, timeSpent) {
    this.trackEvent(AnalyticsEvent.TUTORIAL_COMPLETED, {
      totalSteps,
      timeSpent,
    });
  }

  trackTutorialSkipped(currentStep, totalSteps) {
    this.trackEvent(AnalyticsEvent.TUTORIAL_SKIPPED, {
      currentStep,
      totalSteps,
      completionRate: currentStep / totalSteps,
    });
  }

  // Track feature usage
  trackReportGenerated(reportType, dateRange, categoryCount) {
    this.trackEvent(AnalyticsEvent.REPORT_GENERATED, {
      reportType,
      dateRange,
      categoryCount,
    });
  }

  trackExportPerformed(exportType, itemCount) {
    this.trackEvent(AnalyticsEvent.EXPORT_PERFORMED, {
      exportType,
      itemCount,
    });
  }

  trackSearchPerformed(query, resultCount) {
    this.trackEvent(AnalyticsEvent.SEARCH_PERFORMED, {
      queryLength: query.length,
      resultCount,
      hasResults: resultCount > 0,
    });
  }

  // Flush events to storage/remote
  async flush() {
    if (this.eventQueue.length === 0) return;

    try {
      const events = [...this.eventQueue];
      this.eventQueue = [];

      // Store events locally
      await this.storeEventsLocally(events);

      // Send to remote analytics service (if configured)
      await this.sendEventsToRemote(events);

      Logger.debug(LogCategory.ANALYTICS, `Flushed ${events.length} analytics events`);
    } catch (error) {
      Logger.error(LogCategory.ANALYTICS, 'Failed to flush analytics events', error);
      // Re-add events to queue on failure
      this.eventQueue.unshift(...events);
    }
  }

  // Store events locally
  async storeEventsLocally(events) {
    try {
      const existingEvents = await this.getStoredEvents();
      const allEvents = [...existingEvents, ...events];
      
      // Keep only recent events (last 1000)
      const recentEvents = allEvents.slice(-1000);
      
      await AsyncStorage.setItem('analytics_events', JSON.stringify(recentEvents));
    } catch (error) {
      Logger.error(LogCategory.ANALYTICS, 'Failed to store events locally', error);
    }
  }

  // Send events to remote analytics service
  async sendEventsToRemote(events) {
    try {
      // Implement remote analytics service integration
      // Example: Google Analytics, Firebase Analytics, custom backend
      
      // For now, just log that we would send them
      Logger.debug(LogCategory.ANALYTICS, `Would send ${events.length} events to remote service`);
    } catch (error) {
      Logger.error(LogCategory.ANALYTICS, 'Failed to send events to remote', error);
    }
  }

  // Get stored events
  async getStoredEvents() {
    try {
      const events = await AsyncStorage.getItem('analytics_events');
      return events ? JSON.parse(events) : [];
    } catch (error) {
      Logger.error(LogCategory.ANALYTICS, 'Failed to get stored events', error);
      return [];
    }
  }

  // Get analytics summary
  async getAnalyticsSummary() {
    try {
      const events = await this.getStoredEvents();
      const summary = {
        totalEvents: events.length,
        sessionId: this.sessionId,
        sessionDuration: Date.now() - this.sessionStartTime,
        eventsByType: {},
        userProperties: this.userProperties,
      };

      // Count events by type
      events.forEach(event => {
        summary.eventsByType[event.name] = (summary.eventsByType[event.name] || 0) + 1;
      });

      return summary;
    } catch (error) {
      Logger.error(LogCategory.ANALYTICS, 'Failed to get analytics summary', error);
      return null;
    }
  }

  // Clear stored analytics data
  async clearAnalyticsData() {
    try {
      await AsyncStorage.removeItem('analytics_events');
      this.eventQueue = [];
      Logger.info(LogCategory.ANALYTICS, 'Analytics data cleared');
    } catch (error) {
      Logger.error(LogCategory.ANALYTICS, 'Failed to clear analytics data', error);
    }
  }

  // Setup periodic flush
  setupPeriodicFlush() {
    setInterval(() => {
      this.flush();
    }, this.flushInterval);
  }

  // Create timed action tracker
  startTimedAction(actionName) {
    const startTime = Date.now();
    return {
      end: (properties = {}) => {
        this.trackTimedAction(actionName, startTime, properties);
      }
    };
  }
}

// Create singleton instance
const analytics = new Analytics();

// Configure analytics based on environment
if (__DEV__) {
  analytics.setEnabled(false); // Disable in development
} else {
  analytics.setEnabled(true); // Enable in production
}

export default analytics;
