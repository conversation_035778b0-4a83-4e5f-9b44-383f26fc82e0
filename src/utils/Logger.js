/**
 * Logging and Debugging Utilities for Çiftçi Not Defterim
 * Comprehensive logging system for debugging, analytics, and error tracking
 */

import AsyncStorage from '@react-native-async-storage/async-storage';

// Log levels
export const LogLevel = {
  DEBUG: 0,
  INFO: 1,
  WARN: 2,
  ERROR: 3,
  FATAL: 4,
};

// Log level names
const LogLevelNames = {
  [LogLevel.DEBUG]: 'DEBUG',
  [LogLevel.INFO]: 'INFO',
  [LogLevel.WARN]: 'WARN',
  [LogLevel.ERROR]: 'ERROR',
  [LogLevel.FATAL]: 'FATAL',
};

// Log categories
export const LogCategory = {
  AUTH: 'AUTH',
  DATABASE: 'DATABASE',
  UI: 'UI',
  API: 'API',
  PERFORMANCE: 'PERFORMANCE',
  USER_ACTION: 'USER_ACTION',
  ERROR: 'ERROR',
  ANALYTICS: 'ANALYTICS',
};

class Logger {
  constructor() {
    this.logLevel = __DEV__ ? LogLevel.DEBUG : LogLevel.INFO;
    this.maxLogEntries = 1000;
    this.logBuffer = [];
    this.enableConsoleLogging = true;
    this.enableFileLogging = true;
    this.enableRemoteLogging = false;
    this.sessionId = this.generateSessionId();
  }

  // Generate unique session ID
  generateSessionId() {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Set log level
  setLogLevel(level) {
    this.logLevel = level;
  }

  // Enable/disable different logging methods
  configure(options = {}) {
    this.enableConsoleLogging = options.console !== false;
    this.enableFileLogging = options.file !== false;
    this.enableRemoteLogging = options.remote === true;
    this.maxLogEntries = options.maxEntries || 1000;
    
    if (options.level !== undefined) {
      this.setLogLevel(options.level);
    }
  }

  // Create log entry
  createLogEntry(level, category, message, data = null, error = null) {
    const timestamp = new Date().toISOString();
    const entry = {
      id: `${timestamp}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp,
      sessionId: this.sessionId,
      level,
      levelName: LogLevelNames[level],
      category,
      message,
      data,
      error: error ? {
        name: error.name,
        message: error.message,
        stack: error.stack,
      } : null,
      userAgent: navigator.userAgent,
      url: window.location?.href,
    };

    return entry;
  }

  // Main logging method
  log(level, category, message, data = null, error = null) {
    // Check if log level is enabled
    if (level < this.logLevel) {
      return;
    }

    const entry = this.createLogEntry(level, category, message, data, error);

    // Console logging
    if (this.enableConsoleLogging) {
      this.logToConsole(entry);
    }

    // File logging (AsyncStorage)
    if (this.enableFileLogging) {
      this.logToFile(entry);
    }

    // Remote logging
    if (this.enableRemoteLogging) {
      this.logToRemote(entry);
    }

    // Add to buffer
    this.addToBuffer(entry);
  }

  // Console logging with appropriate method
  logToConsole(entry) {
    const { level, category, message, data, error } = entry;
    const prefix = `[${entry.levelName}] [${category}]`;
    
    switch (level) {
      case LogLevel.DEBUG:
        console.debug(prefix, message, data, error);
        break;
      case LogLevel.INFO:
        console.info(prefix, message, data, error);
        break;
      case LogLevel.WARN:
        console.warn(prefix, message, data, error);
        break;
      case LogLevel.ERROR:
      case LogLevel.FATAL:
        console.error(prefix, message, data, error);
        break;
      default:
        console.log(prefix, message, data, error);
    }
  }

  // File logging to AsyncStorage
  async logToFile(entry) {
    try {
      const logs = await this.getStoredLogs();
      logs.push(entry);
      
      // Keep only the most recent entries
      if (logs.length > this.maxLogEntries) {
        logs.splice(0, logs.length - this.maxLogEntries);
      }
      
      await AsyncStorage.setItem('app_logs', JSON.stringify(logs));
    } catch (error) {
      console.error('Failed to log to file:', error);
    }
  }

  // Remote logging (to be implemented with your backend)
  async logToRemote(entry) {
    try {
      // Implement remote logging to your backend service
      // Example:
      // await fetch('/api/logs', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(entry)
      // });
    } catch (error) {
      console.error('Failed to log to remote:', error);
    }
  }

  // Add to in-memory buffer
  addToBuffer(entry) {
    this.logBuffer.push(entry);
    
    // Keep buffer size manageable
    if (this.logBuffer.length > this.maxLogEntries) {
      this.logBuffer.shift();
    }
  }

  // Convenience methods for different log levels
  debug(category, message, data = null) {
    this.log(LogLevel.DEBUG, category, message, data);
  }

  info(category, message, data = null) {
    this.log(LogLevel.INFO, category, message, data);
  }

  warn(category, message, data = null) {
    this.log(LogLevel.WARN, category, message, data);
  }

  error(category, message, error = null, data = null) {
    this.log(LogLevel.ERROR, category, message, data, error);
  }

  fatal(category, message, error = null, data = null) {
    this.log(LogLevel.FATAL, category, message, data, error);
  }

  // User action logging
  logUserAction(action, data = null) {
    this.info(LogCategory.USER_ACTION, `User action: ${action}`, data);
  }

  // Performance logging
  logPerformance(operation, duration, data = null) {
    this.info(LogCategory.PERFORMANCE, `${operation} took ${duration}ms`, data);
  }

  // Database operation logging
  logDatabaseOperation(operation, table, data = null) {
    this.debug(LogCategory.DATABASE, `Database ${operation} on ${table}`, data);
  }

  // API call logging
  logApiCall(method, url, status, duration, data = null) {
    const message = `${method} ${url} - ${status} (${duration}ms)`;
    if (status >= 400) {
      this.error(LogCategory.API, message, null, data);
    } else {
      this.info(LogCategory.API, message, data);
    }
  }

  // Authentication logging
  logAuth(action, success, data = null) {
    const message = `Auth ${action}: ${success ? 'success' : 'failed'}`;
    if (success) {
      this.info(LogCategory.AUTH, message, data);
    } else {
      this.warn(LogCategory.AUTH, message, data);
    }
  }

  // Get stored logs
  async getStoredLogs() {
    try {
      const logs = await AsyncStorage.getItem('app_logs');
      return logs ? JSON.parse(logs) : [];
    } catch (error) {
      console.error('Failed to get stored logs:', error);
      return [];
    }
  }

  // Get logs from buffer
  getBufferLogs() {
    return [...this.logBuffer];
  }

  // Clear stored logs
  async clearStoredLogs() {
    try {
      await AsyncStorage.removeItem('app_logs');
      this.logBuffer = [];
      this.info(LogCategory.ANALYTICS, 'Logs cleared');
    } catch (error) {
      console.error('Failed to clear logs:', error);
    }
  }

  // Export logs for debugging
  async exportLogs() {
    try {
      const storedLogs = await this.getStoredLogs();
      const bufferLogs = this.getBufferLogs();
      
      return {
        sessionId: this.sessionId,
        exportTime: new Date().toISOString(),
        storedLogs,
        bufferLogs,
        totalEntries: storedLogs.length + bufferLogs.length,
      };
    } catch (error) {
      console.error('Failed to export logs:', error);
      return null;
    }
  }

  // Get log statistics
  async getLogStatistics() {
    try {
      const logs = await this.getStoredLogs();
      const stats = {
        totalLogs: logs.length,
        byLevel: {},
        byCategory: {},
        sessionId: this.sessionId,
        oldestLog: logs.length > 0 ? logs[0].timestamp : null,
        newestLog: logs.length > 0 ? logs[logs.length - 1].timestamp : null,
      };

      // Count by level and category
      logs.forEach(log => {
        stats.byLevel[log.levelName] = (stats.byLevel[log.levelName] || 0) + 1;
        stats.byCategory[log.category] = (stats.byCategory[log.category] || 0) + 1;
      });

      return stats;
    } catch (error) {
      console.error('Failed to get log statistics:', error);
      return null;
    }
  }

  // Performance measurement utility
  startTimer(name) {
    const startTime = Date.now();
    return {
      end: (data = null) => {
        const duration = Date.now() - startTime;
        this.logPerformance(name, duration, data);
        return duration;
      }
    };
  }

  // Async operation wrapper with logging
  async withLogging(operation, category, description, data = null) {
    const timer = this.startTimer(description);
    
    try {
      this.debug(category, `Starting: ${description}`, data);
      const result = await operation();
      timer.end({ success: true, ...data });
      this.debug(category, `Completed: ${description}`, data);
      return result;
    } catch (error) {
      timer.end({ success: false, error: error.message, ...data });
      this.error(category, `Failed: ${description}`, error, data);
      throw error;
    }
  }
}

// Create singleton instance
const logger = new Logger();

// Configure logger based on environment
if (__DEV__) {
  logger.configure({
    level: LogLevel.DEBUG,
    console: true,
    file: true,
    remote: false,
    maxEntries: 500,
  });
} else {
  logger.configure({
    level: LogLevel.INFO,
    console: false,
    file: true,
    remote: true,
    maxEntries: 1000,
  });
}

export default logger;
