/**
 * Season Detection Logic for Çiftçi Not Defterim
 * Automatically detects current agricultural season based on various factors
 */

import { AgriculturalSeasons, RegionalPatterns, SeasonalHelpers } from '../models/SeasonalModels';
import Logger, { LogCategory } from './Logger';
import Analytics, { AnalyticsEvent } from './Analytics';

export class SeasonDetector {
  constructor(userLocation = null, userPreferences = {}) {
    this.userLocation = userLocation;
    this.userPreferences = userPreferences;
    this.currentDate = new Date();
  }

  /**
   * Main method to detect current season
   * @param {Date} date - Optional date to check (defaults to current date)
   * @returns {Object} Season information with confidence score
   */
  detectCurrentSeason(date = null) {
    try {
      const checkDate = date || this.currentDate;
      
      // Get base season from calendar
      const baseSeason = this.getCalendarBasedSeason(checkDate);
      
      // Apply regional adjustments
      const regionalSeason = this.applyRegionalAdjustments(baseSeason, checkDate);
      
      // Consider weather patterns
      const weatherAdjustedSeason = this.considerWeatherPatterns(regionalSeason, checkDate);
      
      // Calculate confidence score
      const confidence = this.calculateConfidence(weatherAdjustedSeason, checkDate);
      
      const result = {
        season: weatherAdjustedSeason,
        confidence,
        detectionMethod: 'automatic',
        factors: this.getDetectionFactors(checkDate),
        recommendations: this.getSeasonalRecommendations(weatherAdjustedSeason),
        nextSeason: SeasonalHelpers.getNextSeason(weatherAdjustedSeason.id),
        progress: SeasonalHelpers.getSeasonProgress(checkDate),
      };

      Analytics.trackEvent(AnalyticsEvent.SEASON_DETECTED, {
        seasonId: result.season.id,
        confidence: result.confidence,
        detectionMethod: result.detectionMethod,
        userLocation: this.userLocation?.region,
      });

      Logger.info(LogCategory.SEASON_DETECTION, 'Season detected', {
        season: result.season.id,
        confidence: result.confidence,
        date: checkDate.toISOString(),
      });

      return result;
    } catch (error) {
      Logger.error(LogCategory.SEASON_DETECTION, 'Failed to detect season', error);
      
      // Fallback to simple calendar-based detection
      const fallbackSeason = this.getCalendarBasedSeason(date || this.currentDate);
      return {
        season: fallbackSeason,
        confidence: 0.7,
        detectionMethod: 'fallback',
        factors: { calendar: true },
        recommendations: this.getSeasonalRecommendations(fallbackSeason),
        nextSeason: SeasonalHelpers.getNextSeason(fallbackSeason.id),
        progress: SeasonalHelpers.getSeasonProgress(date || this.currentDate),
      };
    }
  }

  /**
   * Get season based on calendar months
   */
  getCalendarBasedSeason(date) {
    return SeasonalHelpers.getCurrentSeason(date);
  }

  /**
   * Apply regional climate adjustments
   */
  applyRegionalAdjustments(baseSeason, date) {
    if (!this.userLocation?.region) {
      return baseSeason;
    }

    const regionalPattern = RegionalPatterns[this.userLocation.region];
    if (!regionalPattern) {
      return baseSeason;
    }

    const month = date.getMonth() + 1;
    const adjustments = regionalPattern.seasonAdjustments;

    // Check if current month falls into adjusted season boundaries
    for (const [seasonId, adjustment] of Object.entries(adjustments)) {
      if (this.isMonthInRange(month, adjustment.start, adjustment.end)) {
        const adjustedSeason = AgriculturalSeasons[seasonId.toUpperCase()];
        if (adjustedSeason) {
          Logger.debug(LogCategory.SEASON_DETECTION, 'Applied regional adjustment', {
            originalSeason: baseSeason.id,
            adjustedSeason: adjustedSeason.id,
            region: this.userLocation.region,
          });
          return adjustedSeason;
        }
      }
    }

    return baseSeason;
  }

  /**
   * Consider weather patterns for season detection
   */
  considerWeatherPatterns(season, date) {
    // This would integrate with weather API in a real implementation
    // For now, we'll use mock weather consideration
    
    const mockWeatherFactors = this.getMockWeatherFactors(date);
    
    // If weather strongly indicates a different season, adjust
    if (mockWeatherFactors.confidence > 0.8) {
      const weatherSeason = mockWeatherFactors.suggestedSeason;
      if (weatherSeason && weatherSeason.id !== season.id) {
        Logger.debug(LogCategory.SEASON_DETECTION, 'Weather-based season adjustment', {
          originalSeason: season.id,
          weatherSeason: weatherSeason.id,
          confidence: mockWeatherFactors.confidence,
        });
        return weatherSeason;
      }
    }

    return season;
  }

  /**
   * Mock weather factors (would be replaced with real weather API)
   */
  getMockWeatherFactors(date) {
    const month = date.getMonth() + 1;
    
    // Simple mock based on typical Turkish weather patterns
    if (month >= 6 && month <= 8) {
      return {
        confidence: 0.9,
        suggestedSeason: AgriculturalSeasons.SUMMER,
        factors: ['high_temperature', 'low_rainfall'],
      };
    } else if (month >= 12 || month <= 2) {
      return {
        confidence: 0.85,
        suggestedSeason: AgriculturalSeasons.WINTER,
        factors: ['low_temperature', 'high_rainfall'],
      };
    }
    
    return {
      confidence: 0.6,
      suggestedSeason: null,
      factors: ['moderate_conditions'],
    };
  }

  /**
   * Calculate confidence score for season detection
   */
  calculateConfidence(season, date) {
    let confidence = 0.7; // Base confidence
    
    // Calendar alignment
    const calendarSeason = this.getCalendarBasedSeason(date);
    if (calendarSeason.id === season.id) {
      confidence += 0.2;
    }

    // Regional data availability
    if (this.userLocation?.region && RegionalPatterns[this.userLocation.region]) {
      confidence += 0.1;
    }

    // User preferences alignment
    if (this.userPreferences.manualSeasonOverride) {
      confidence -= 0.2; // Lower confidence if user has manual overrides
    }

    // Historical data consistency (mock)
    confidence += 0.1;

    return Math.min(confidence, 1.0);
  }

  /**
   * Get factors that influenced season detection
   */
  getDetectionFactors(date) {
    return {
      calendar: true,
      regional: !!this.userLocation?.region,
      weather: true, // Mock
      historical: false, // Would be true with real historical data
      userPreferences: Object.keys(this.userPreferences).length > 0,
    };
  }

  /**
   * Get seasonal recommendations based on detected season
   */
  getSeasonalRecommendations(season) {
    const recommendations = {
      activities: season.activities || [],
      expenses: season.commonExpenses || [],
      tips: [],
    };

    // Add season-specific tips
    switch (season.id) {
      case 'spring':
        recommendations.tips = [
          'Toprak sıcaklığını kontrol edin',
          'Tohum kalitesine dikkat edin',
          'Sulama sistemlerini hazırlayın',
        ];
        break;
      case 'summer':
        recommendations.tips = [
          'Düzenli sulama yapın',
          'Hastalık ve zararlıları takip edin',
          'Sıcak saatlerde çalışmaktan kaçının',
        ];
        break;
      case 'autumn':
        recommendations.tips = [
          'Hasat zamanlamasını iyi planlayın',
          'Depolama koşullarını hazırlayın',
          'Pazarlama stratejinizi belirleyin',
        ];
        break;
      case 'winter':
        recommendations.tips = [
          'Makine bakımlarını yapın',
          'Gelecek sezon için planlama yapın',
          'Eğitim ve araştırmaya zaman ayırın',
        ];
        break;
    }

    return recommendations;
  }

  /**
   * Check if month is in range (handles year boundary)
   */
  isMonthInRange(month, start, end) {
    if (start <= end) {
      return month >= start && month <= end;
    } else {
      // Range crosses year boundary (e.g., Dec-Feb)
      return month >= start || month <= end;
    }
  }

  /**
   * Force set season (for user override)
   */
  setManualSeason(seasonId, reason = 'user_override') {
    const season = AgriculturalSeasons[seasonId.toUpperCase()];
    if (!season) {
      throw new Error(`Invalid season ID: ${seasonId}`);
    }

    Analytics.trackEvent(AnalyticsEvent.SEASON_MANUALLY_SET, {
      seasonId,
      reason,
      previousSeason: this.detectCurrentSeason().season.id,
    });

    Logger.info(LogCategory.SEASON_DETECTION, 'Season manually set', {
      seasonId,
      reason,
    });

    return {
      season,
      confidence: 1.0,
      detectionMethod: 'manual',
      factors: { manual: true },
      recommendations: this.getSeasonalRecommendations(season),
      nextSeason: SeasonalHelpers.getNextSeason(season.id),
      progress: 0, // Reset progress for manual override
    };
  }

  /**
   * Get season history for analytics
   */
  getSeasonHistory(startDate, endDate) {
    const history = [];
    const current = new Date(startDate);
    
    while (current <= endDate) {
      const seasonInfo = this.detectCurrentSeason(current);
      history.push({
        date: new Date(current),
        season: seasonInfo.season,
        confidence: seasonInfo.confidence,
      });
      
      // Move to next month
      current.setMonth(current.getMonth() + 1);
    }

    return history;
  }

  /**
   * Predict next season transition
   */
  predictNextTransition() {
    const currentSeason = this.detectCurrentSeason();
    const nextSeason = currentSeason.nextSeason;
    
    // Calculate approximate transition date
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth() + 1;
    const nextSeasonStartMonth = nextSeason.months[0];
    
    let transitionDate = new Date(currentDate);
    if (nextSeasonStartMonth > currentMonth) {
      transitionDate.setMonth(nextSeasonStartMonth - 1);
    } else {
      transitionDate.setFullYear(transitionDate.getFullYear() + 1);
      transitionDate.setMonth(nextSeasonStartMonth - 1);
    }
    transitionDate.setDate(1);

    return {
      nextSeason,
      estimatedTransitionDate: transitionDate,
      daysUntilTransition: Math.ceil((transitionDate - currentDate) / (1000 * 60 * 60 * 24)),
      currentSeasonProgress: currentSeason.progress,
    };
  }
}

// Singleton instance for global use
let globalSeasonDetector = null;

export const getSeasonDetector = (userLocation = null, userPreferences = {}) => {
  if (!globalSeasonDetector) {
    globalSeasonDetector = new SeasonDetector(userLocation, userPreferences);
  }
  return globalSeasonDetector;
};

export const updateSeasonDetectorConfig = (userLocation, userPreferences) => {
  globalSeasonDetector = new SeasonDetector(userLocation, userPreferences);
  return globalSeasonDetector;
};

// Utility functions
export const getCurrentSeason = (date = null) => {
  const detector = getSeasonDetector();
  return detector.detectCurrentSeason(date);
};

export const getSeasonProgress = (date = null) => {
  return SeasonalHelpers.getSeasonProgress(date || new Date());
};

export const isSeasonTransitionPeriod = (date = null, threshold = 10) => {
  const progress = getSeasonProgress(date);
  return progress >= (100 - threshold) || progress <= threshold;
};

export default {
  SeasonDetector,
  getSeasonDetector,
  updateSeasonDetectorConfig,
  getCurrentSeason,
  getSeasonProgress,
  isSeasonTransitionPeriod,
};
