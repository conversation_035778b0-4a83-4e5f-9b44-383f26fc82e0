/**
 * Validation utilities for Çiftçi Not Defterim
 * Input validation, data sanitization, and format checking
 */

// Amount validation with decimal separator handling
export const validateAmount = (value) => {
  if (!value && value !== 0) {
    return { isValid: false, error: '<PERSON>tar gereklidir' };
  }

  // Convert string to number, handling Turkish decimal separators
  let numericValue = value;
  if (typeof value === 'string') {
    // Replace Turkish comma with dot for decimal
    numericValue = value.replace(',', '.');
    
    // Check for multiple decimal separators
    const dotCount = (numericValue.match(/\./g) || []).length;
    if (dotCount > 1) {
      return { isValid: false, error: 'Geçersiz tutar formatı' };
    }
    
    // Check for invalid characters
    if (!/^\d+\.?\d*$/.test(numericValue)) {
      return { isValid: false, error: 'Sadece sayı girebilirsiniz' };
    }
    
    numericValue = parseFloat(numericValue);
  }

  if (isNaN(numericValue)) {
    return { isValid: false, error: 'Geçerli bir tutar giriniz' };
  }

  if (numericValue <= 0) {
    return { isValid: false, error: 'Tutar sıfırdan büyük olmalıdır' };
  }

  if (numericValue > 999999999) {
    return { isValid: false, error: 'Tutar çok büyük' };
  }

  return { isValid: true, value: numericValue };
};

// Format amount for display
export const formatAmount = (amount, currency = 'TRY') => {
  if (!amount && amount !== 0) return '₺0,00';
  
  return new Intl.NumberFormat('tr-TR', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount);
};

// Sanitize amount input (prevent consecutive separators)
export const sanitizeAmountInput = (input) => {
  if (!input) return '';
  
  let sanitized = input.toString();
  
  // Remove any non-numeric characters except comma and dot
  sanitized = sanitized.replace(/[^0-9,\.]/g, '');
  
  // Prevent consecutive separators
  sanitized = sanitized.replace(/[,\.]{2,}/g, ',');
  
  // Ensure only one decimal separator
  const parts = sanitized.split(/[,\.]/);
  if (parts.length > 2) {
    sanitized = parts[0] + ',' + parts.slice(1).join('');
  }
  
  // Limit decimal places to 2
  if (parts.length === 2 && parts[1].length > 2) {
    sanitized = parts[0] + ',' + parts[1].substring(0, 2);
  }
  
  return sanitized;
};

// Category name validation
export const validateCategoryName = (name) => {
  if (!name || name.trim().length === 0) {
    return { isValid: false, error: 'Kategori adı gereklidir' };
  }

  if (name.trim().length > 50) {
    return { isValid: false, error: 'Kategori adı çok uzun (maksimum 50 karakter)' };
  }

  return { isValid: true, value: name.trim() };
};

// Description validation
export const validateDescription = (description) => {
  if (!description) {
    return { isValid: true, value: '' };
  }

  if (description.length > 500) {
    return { isValid: false, error: 'Açıklama çok uzun (maksimum 500 karakter)' };
  }

  return { isValid: true, value: description.trim() };
};

// Date validation
export const validateDate = (date) => {
  if (!date) {
    return { isValid: false, error: 'Tarih gereklidir' };
  }

  const dateObj = new Date(date);
  if (isNaN(dateObj.getTime())) {
    return { isValid: false, error: 'Geçerli bir tarih giriniz' };
  }

  // Check if date is not too far in the future (max 1 year)
  const oneYearFromNow = new Date();
  oneYearFromNow.setFullYear(oneYearFromNow.getFullYear() + 1);
  
  if (dateObj > oneYearFromNow) {
    return { isValid: false, error: 'Tarih çok ileri bir tarih olamaz' };
  }

  // Check if date is not too far in the past (max 10 years)
  const tenYearsAgo = new Date();
  tenYearsAgo.setFullYear(tenYearsAgo.getFullYear() - 10);
  
  if (dateObj < tenYearsAgo) {
    return { isValid: false, error: 'Tarih çok eski bir tarih olamaz' };
  }

  return { isValid: true, value: dateObj };
};

// Email validation
export const validateEmail = (email) => {
  if (!email) {
    return { isValid: false, error: 'E-posta adresi gereklidir' };
  }

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return { isValid: false, error: 'Geçerli bir e-posta adresi giriniz' };
  }

  return { isValid: true, value: email.toLowerCase().trim() };
};

// Color validation (hex format)
export const validateColor = (color) => {
  if (!color) {
    return { isValid: true, value: '#607D8B' }; // Default color
  }

  const hexRegex = /^#[0-9A-Fa-f]{6}$/;
  if (!hexRegex.test(color)) {
    return { isValid: false, error: 'Geçerli bir renk kodu giriniz (#RRGGBB)' };
  }

  return { isValid: true, value: color.toUpperCase() };
};

// SEASON INTEGRATION: Season validation
export const validateSeasonId = (seasonId) => {
  if (!seasonId) {
    return { isValid: false, error: 'Sezon seçimi gereklidir' };
  }

  if (typeof seasonId !== 'string' || seasonId.trim().length === 0) {
    return { isValid: false, error: 'Geçerli bir sezon seçiniz' };
  }

  return { isValid: true, value: seasonId.trim() };
};

// Expense validation (complete expense object)
export const validateExpense = (expense) => {
  const errors = {};

  // Validate amount
  const amountValidation = validateAmount(expense.amount);
  if (!amountValidation.isValid) {
    errors.amount = amountValidation.error;
  }

  // Validate category
  if (!expense.categoryId) {
    errors.categoryId = 'Kategori seçimi gereklidir';
  }

  // SEASON INTEGRATION: Validate season
  const seasonValidation = validateSeasonId(expense.seasonId);
  if (!seasonValidation.isValid) {
    errors.seasonId = seasonValidation.error;
  }

  // Validate date
  const dateValidation = validateDate(expense.date);
  if (!dateValidation.isValid) {
    errors.date = dateValidation.error;
  }

  // Validate description (optional)
  if (expense.description) {
    const descValidation = validateDescription(expense.description);
    if (!descValidation.isValid) {
      errors.description = descValidation.error;
    }
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
    sanitizedData: {
      amount: amountValidation.isValid ? amountValidation.value : expense.amount,
      categoryId: expense.categoryId,
      seasonId: seasonValidation.isValid ? seasonValidation.value : expense.seasonId,  // SEASON INTEGRATION: Use validated season value
      date: dateValidation.isValid ? dateValidation.value : expense.date,
      description: expense.description ? expense.description.trim() : ''
    }
  };
};

// Category validation (complete category object)
export const validateCategory = (category) => {
  const errors = {};

  // Validate name
  const nameValidation = validateCategoryName(category.name);
  if (!nameValidation.isValid) {
    errors.name = nameValidation.error;
  }

  // Validate color (optional)
  if (category.color) {
    const colorValidation = validateColor(category.color);
    if (!colorValidation.isValid) {
      errors.color = colorValidation.error;
    }
  }

  // Validate emoji (optional, basic check)
  if (category.emoji && category.emoji.length > 10) {
    errors.emoji = 'Emoji çok uzun';
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
    sanitizedData: {
      name: nameValidation.isValid ? nameValidation.value : category.name,
      emoji: category.emoji || '📝',
      color: category.color || '#607D8B',
      icon: category.icon || 'more-horiz',
      description: category.description ? category.description.trim() : ''
    }
  };
};

// Field name validation
export const validateFieldName = (name) => {
  if (!name || name.trim().length === 0) {
    return { isValid: false, error: 'Tarla adı gereklidir' };
  }

  if (name.trim().length > 50) {
    return { isValid: false, error: 'Tarla adı çok uzun (maksimum 50 karakter)' };
  }

  return { isValid: true, value: name.trim() };
};

// Field size validation (optional)
export const validateFieldSize = (value, isRequired = false) => {
  // If not required and value is empty, return valid
  if (!isRequired && (!value && value !== 0)) {
    return { isValid: true, value: null };
  }

  // If required and value is empty, return error
  if (isRequired && (!value && value !== 0)) {
    return { isValid: false, error: 'Alan büyüklüğü gereklidir' };
  }

  // If value is provided, validate it
  if (value || value === 0) {
    let numericValue = value;
    if (typeof value === 'string') {
      // Replace Turkish comma with dot for decimal
      numericValue = value.replace(',', '.');

      // Check for multiple decimal separators
      const dotCount = (numericValue.match(/\./g) || []).length;
      if (dotCount > 1) {
        return { isValid: false, error: 'Geçersiz alan formatı' };
      }

      // Check for invalid characters
      if (!/^\d+\.?\d*$/.test(numericValue)) {
        return { isValid: false, error: 'Sadece sayı girebilirsiniz' };
      }

      numericValue = parseFloat(numericValue);
    }

    if (isNaN(numericValue) || numericValue <= 0) {
      return { isValid: false, error: 'Alan değeri sıfırdan büyük olmalıdır' };
    }

    if (numericValue > 100000) {
      return { isValid: false, error: 'Alan değeri çok büyük (maksimum 100,000)' };
    }

    return { isValid: true, value: numericValue };
  }

  return { isValid: true, value: null };
};

// Field notes validation
export const validateFieldNotes = (notes) => {
  if (!notes) {
    return { isValid: true, value: '' };
  }

  if (notes.length > 200) {
    return { isValid: false, error: 'Notlar çok uzun (maksimum 200 karakter)' };
  }

  return { isValid: true, value: notes.trim() };
};

// Field address validation
export const validateFieldAddress = (address) => {
  if (!address) {
    return { isValid: true, value: '' };
  }

  if (address.length > 200) {
    return { isValid: false, error: 'Adres çok uzun (maksimum 200 karakter)' };
  }

  return { isValid: true, value: address.trim() };
};

// Field validation (complete field object)
export const validateField = (field) => {
  const errors = {};

  // Validate name (required)
  const nameValidation = validateFieldName(field.name);
  if (!nameValidation.isValid) {
    errors.name = nameValidation.error;
  }

  // Validate size value (optional - only validate if provided)
  let sizeValidation = { isValid: true, value: null };
  if (field.size?.value || field.size?.value === 0) {
    sizeValidation = validateFieldSize(field.size.value, false);
    if (!sizeValidation.isValid) {
      errors['size.value'] = sizeValidation.error;
    }
  }

  // Validate notes (optional)
  let notesValidation = { isValid: true, value: '' };
  if (field.notes) {
    notesValidation = validateFieldNotes(field.notes);
    if (!notesValidation.isValid) {
      errors.notes = notesValidation.error;
    }
  }

  // Validate address (optional)
  let addressValidation = { isValid: true, value: '' };
  if (field.location?.address) {
    addressValidation = validateFieldAddress(field.location.address);
    if (!addressValidation.isValid) {
      errors['location.address'] = addressValidation.error;
    }
  }

  // Validate size unit
  const validUnits = ['dekar', 'dönüm', 'hectare', 'acre'];
  if (field.size?.unit && !validUnits.includes(field.size.unit)) {
    errors['size.unit'] = 'Geçersiz alan birimi';
  }

  // Validate soil type
  const validSoilTypes = ['clay', 'sandy', 'loamy', 'silty', 'peaty', 'chalky', 'other'];
  if (field.soilType && !validSoilTypes.includes(field.soilType)) {
    errors.soilType = 'Geçersiz toprak tipi';
  }

  // Validate irrigation type
  const validIrrigationTypes = ['drip', 'sprinkler', 'flood', 'manual', 'none'];
  if (field.irrigationType && !validIrrigationTypes.includes(field.irrigationType)) {
    errors.irrigationType = 'Geçersiz sulama tipi';
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
    sanitizedData: {
      name: nameValidation.isValid ? nameValidation.value : field.name,
      size: {
        value: sizeValidation.isValid ? sizeValidation.value : field.size?.value,
        unit: field.size?.unit || 'dekar'
      },
      location: field.location?.address ? {
        address: addressValidation.isValid ? addressValidation.value : field.location.address.trim()
      } : undefined,
      soilType: field.soilType || 'other',
      irrigationType: field.irrigationType || 'none',
      notes: notesValidation.isValid ? notesValidation.value : (field.notes ? field.notes.trim() : '')
    }
  };
};

// Format date for display
export const formatDate = (date, locale = 'tr-TR') => {
  if (!date) return '';

  const dateObj = new Date(date);
  if (isNaN(dateObj.getTime())) return '';
  
  return dateObj.toLocaleDateString(locale, {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

// Format date for input fields
export const formatDateForInput = (date) => {
  if (!date) return '';
  
  const dateObj = new Date(date);
  if (isNaN(dateObj.getTime())) return '';
  
  return dateObj.toISOString().split('T')[0];
};

// Check if string contains only numbers and allowed separators
export const isNumericInput = (input) => {
  return /^[0-9,\.]*$/.test(input);
};

// Sanitize text input (remove harmful characters)
export const sanitizeTextInput = (input, maxLength = 500) => {
  if (!input) return '';
  
  return input
    .toString()
    .trim()
    .substring(0, maxLength)
    .replace(/[<>]/g, ''); // Remove potential HTML tags
};

// Validate file upload (for photos)
export const validateFileUpload = (file) => {
  if (!file) {
    return { isValid: false, error: 'Dosya seçilmedi' };
  }

  // Check file size (max 5MB)
  const maxSize = 5 * 1024 * 1024;
  if (file.size > maxSize) {
    return { isValid: false, error: 'Dosya boyutu çok büyük (maksimum 5MB)' };
  }

  // Check file type
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
  if (!allowedTypes.includes(file.type)) {
    return { isValid: false, error: 'Desteklenmeyen dosya formatı (sadece JPG, PNG, WebP)' };
  }

  return { isValid: true };
};

// Generate validation summary
export const getValidationSummary = (validationResults) => {
  const allErrors = [];
  let isValid = true;

  Object.values(validationResults).forEach(result => {
    if (result && !result.isValid) {
      isValid = false;
      if (result.error) {
        allErrors.push(result.error);
      }
      if (result.errors) {
        allErrors.push(...Object.values(result.errors));
      }
    }
  });

  return {
    isValid,
    errors: allErrors,
    errorCount: allErrors.length
  };
};
