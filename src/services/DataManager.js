/**
 * Unified Data Manager for Çiftçi Not Defterim
 * Handles data operations based on authentication state
 * - Guest mode: Uses local storage (AsyncStorage)
 * - Authenticated mode: Uses backend API with local caching
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import AuthService from './AuthService';
import { APIClient } from './APIClient';
import { DefaultCategories } from '../constants/Categories';
import { DefaultCrops } from '../constants/DefaultCrops';
import BackupService from './BackupService';
import FieldValidationService from './FieldValidationService';
import ExpenseValidationService from './ExpenseValidationService';
import ValidationService from './ValidationService';

/**
 * Migration Backup Service
 * Handles backup and restore operations for guest data migration
 */
class MigrationBackupService {
  constructor() {
    this.backupService = BackupService;
  }

  /**
   * Create backup before migration
   */
  async createBackup() {
    return await this.backupService.createMigrationBackup();
  }

  /**
   * <PERSON>ore from backup
   */
  async rollback(backupId) {
    return await this.backupService.restoreFromBackup(backupId);
  }

  /**
   * Check backup integrity
   */
  async checkIntegrity() {
    return await this.backupService.checkBackupIntegrity();
  }

  /**
   * Clean up backup after successful migration
   */
  async cleanup(backupId) {
    return await this.backupService.cleanupBackup(backupId);
  }

  /**
   * Get backup metadata
   */
  async getMetadata() {
    return await this.backupService.getBackupMetadata();
  }

  /**
   * Manual rollback with user confirmation
   */
  async manualRollback(backupId, options = {}) {
    const { skipConfirmation = false } = options;

    try {
      console.log('🔄 Manual rollback requested for backup:', backupId);

      // Check if backup exists and is valid
      const integrity = await this.checkIntegrity();
      if (!integrity.hasBackup || !integrity.isValid) {
        throw new Error('No valid backup found for rollback');
      }

      if (integrity.backupId !== backupId) {
        throw new Error(`Backup ID mismatch. Available: ${integrity.backupId}, Requested: ${backupId}`);
      }

      // Perform rollback
      const rollbackResult = await this.rollback(backupId);

      console.log('✅ Manual rollback completed successfully:', rollbackResult);
      return {
        success: true,
        backupId,
        rollbackResult,
        message: 'Veriler başarıyla geri yüklendi'
      };

    } catch (error) {
      console.error('❌ Manual rollback failed:', error);
      throw new Error(`Geri yükleme başarısız: ${error.message}`);
    }
  }

  /**
   * Clean up failed migration data
   */
  async cleanupFailedMigration(migrationProgress = null) {
    try {
      console.log('🧹 Cleaning up failed migration data...');

      if (!migrationProgress) {
        console.log('No migration progress info, skipping cleanup');
        return { success: true, message: 'No cleanup needed' };
      }

      const cleanupTasks = [];

      // If season was created but migration failed, optionally clean it up
      if (migrationProgress.seasonCreated && migrationProgress.step !== 'completed') {
        console.log('⚠️ Season was created but migration failed - leaving season for manual cleanup');
        // Note: We don't auto-delete the season as it might be useful for retry
      }

      // Clean up any partial backend data if migration was partially successful
      if (migrationProgress.expensesMigrated > 0 || migrationProgress.fieldsMigrated > 0) {
        console.log('⚠️ Partial migration detected - backend may have partial data');
        // Note: Backend cleanup is handled by the clear-data endpoint if needed
      }

      // Clean up any temporary migration flags
      cleanupTasks.push(
        AsyncStorage.removeItem('migration_in_progress'),
        AsyncStorage.removeItem('migration_temp_data')
      );

      await Promise.all(cleanupTasks);

      console.log('✅ Failed migration cleanup completed');
      return {
        success: true,
        message: 'Başarısız migration temizlendi',
        cleanedItems: cleanupTasks.length
      };

    } catch (error) {
      console.error('❌ Failed migration cleanup error:', error);
      // Don't throw error for cleanup failures
      return {
        success: false,
        error: error.message,
        message: 'Temizleme sırasında hata oluştu'
      };
    }
  }

  /**
   * Manual rollback migration for users
   * @param {string} backupId - Backup ID to rollback to
   * @param {Object} options - Rollback options
   */
  async manualRollback(backupId, options = {}) {
    try {
      console.log('🔄 MigrationBackupService: Manual rollback requested');

      // Use the backup service for rollback
      const rollbackResult = await this.rollback(backupId);

      // Clean up any failed migration artifacts
      await this.cleanupFailedMigration();

      console.log('✅ MigrationBackupService: Manual rollback completed successfully');
      return {
        success: true,
        backupId,
        rollbackResult,
        message: 'Veriler başarıyla geri yüklendi'
      };

    } catch (error) {
      console.error('❌ MigrationBackupService: Manual rollback failed:', error);
      throw new Error(`Geri yükleme başarısız: ${error.message}`);
    }
  }
}

class DataManagerService {
  constructor() {
    this.initialized = false;
    this.apiClient = APIClient;
    this.trackingModePreference = null; // Explicit tracking mode preference
    this.migrationBackup = new MigrationBackupService(); // Add backup service
    this.localData = {
      categories: [],
      expenses: [],
      fields: [],
      crops: [],
      seasons: [],
      settings: {}
    };

    // Sync queue for offline changes
    this.syncQueue = {
      seasons: []
    };
    this.syncInProgress = false;
    this.dataChangeListeners = [];
  }

  // Initialize the data manager
  async initialize() {
    try {
      // Check if already initialized for current user
      const currentUserId = this.getCurrentUserId();
      if (this.initialized && this.lastInitializedUserId === currentUserId) {
        console.log('DataManager already initialized for user:', currentUserId);
        return;
      }

      console.log('DataManager initializing...');

      // Always reload local data to ensure we have the correct user's data
      await this.loadLocalData();

      // Check authentication state
      const currentUser = AuthService.getCurrentUser();
      const isAuthenticated = currentUser && !AuthService.isInGuestMode();
      const userId = this.getCurrentUserId();

      console.log('DataManager initialization - User context:', {
        userId,
        isAuthenticated,
        isGuestMode: AuthService.isInGuestMode(),
        userEmail: currentUser?.email
      });

      if (isAuthenticated) {
        // Initialize API client with user token
        await this.apiClient.initialize();

        // Sync with backend if online
        await this.syncWithBackend();
      }

      this.initialized = true;
      this.lastInitializedUserId = userId;
      console.log('DataManager initialized successfully for user:', userId);
    } catch (error) {
      console.error('DataManager initialization error:', error);
      // Fall back to local mode
      this.initialized = true;
    }
  }

  // Load data from local storage
  async loadLocalData() {
    try {
      const [storedCategories, storedExpenses, storedFields, storedCrops, storedSeasons, storedSettings, storedTrackingMode] = await Promise.all([
        AsyncStorage.getItem(this.getUserStorageKey('categories')),
        AsyncStorage.getItem(this.getUserStorageKey('expenses')),
        AsyncStorage.getItem(this.getUserStorageKey('fields')),
        AsyncStorage.getItem(this.getUserStorageKey('crops')),
        AsyncStorage.getItem(this.getUserStorageKey('seasons')),
        AsyncStorage.getItem(this.getUserStorageKey('settings')),
        AsyncStorage.getItem(this.getUserStorageKey('trackingMode'))
      ]);

      // Parse categories with safety check
      let parsedCategories = DefaultCategories;
      if (storedCategories) {
        try {
          const parsed = JSON.parse(storedCategories);
          parsedCategories = Array.isArray(parsed) ? parsed : DefaultCategories;
        } catch (e) {
          console.warn('Failed to parse categories, using defaults:', e);
        }
      }

      // Parse expenses with safety check
      let parsedExpenses = [];
      if (storedExpenses) {
        try {
          const parsed = JSON.parse(storedExpenses);
          parsedExpenses = Array.isArray(parsed) ? parsed : [];
        } catch (e) {
          console.warn('Failed to parse expenses, using empty array:', e);
        }
      }

      // Parse fields with safety check
      let parsedFields = [];
      if (storedFields) {
        try {
          const parsed = JSON.parse(storedFields);
          parsedFields = Array.isArray(parsed) ? parsed : [];
        } catch (e) {
          console.warn('Failed to parse fields, using empty array:', e);
        }
      }

      // Parse crops with safety check
      let parsedCrops = [];
      if (storedCrops) {
        try {
          const parsed = JSON.parse(storedCrops);
          parsedCrops = Array.isArray(parsed) ? parsed : [];
        } catch (e) {
          console.warn('Failed to parse crops, using empty array:', e);
        }
      }

      // Parse settings with safety check
      let parsedSettings = {};
      if (storedSettings) {
        try {
          const parsed = JSON.parse(storedSettings);
          parsedSettings = (parsed && typeof parsed === 'object' && !Array.isArray(parsed)) ? parsed : {};
        } catch (e) {
          console.warn('Failed to parse settings, using empty object:', e);
        }
      }

      // Parse tracking mode preference with safety check
      this.trackingModePreference = null;
      if (storedTrackingMode) {
        try {
          this.trackingModePreference = JSON.parse(storedTrackingMode);
        } catch (e) {
          console.warn('Failed to parse tracking mode preference, using null:', e);
        }
      }

      // Parse seasons with safety check
      let parsedSeasons = [];
      if (storedSeasons) {
        try {
          parsedSeasons = JSON.parse(storedSeasons);
          if (!Array.isArray(parsedSeasons)) {
            parsedSeasons = [];
          }
        } catch (e) {
          console.warn('Failed to parse seasons, using empty array:', e);
          parsedSeasons = [];
        }
      }

      this.localData.categories = parsedCategories;
      this.localData.expenses = parsedExpenses;
      this.localData.fields = parsedFields;
      this.localData.crops = parsedCrops;
      this.localData.seasons = parsedSeasons;
      this.localData.settings = parsedSettings;

      // If no categories exist, save defaults
      if (!storedCategories) {
        await AsyncStorage.setItem(this.getUserStorageKey('categories'), JSON.stringify(DefaultCategories));
      }

      // If no seasons exist, create default season
      if (!storedSeasons || parsedSeasons.length === 0) {
        const defaultSeason = await this.createDefaultSeason();
        this.localData.seasons = [defaultSeason];
      }

      console.log('Local data loaded successfully');
    } catch (error) {
      console.error('Error loading local data:', error);
      // Initialize with defaults
      this.localData.categories = DefaultCategories;
      this.localData.expenses = [];
      this.localData.fields = [];
      this.localData.crops = [];
      this.localData.settings = {};
    }
  }

  // Save data to local storage
  async saveLocalData(dataType, data) {
    try {
      await AsyncStorage.setItem(this.getUserStorageKey(dataType), JSON.stringify(data));
      this.localData[dataType] = data;
    } catch (error) {
      console.error(`Error saving ${dataType} to local storage:`, error);
      throw error;
    }
  }

  // Check if user is authenticated
  isAuthenticated() {
    const currentUser = AuthService.getCurrentUser();
    return currentUser && !AuthService.isInGuestMode();
  }

  // Check if backend should be used (authenticated AND not guest mode)
  shouldUseBackend() {
    return this.isAuthenticated() && !AuthService.isInGuestMode();
  }

  // Check if user is in guest mode
  isGuestMode() {
    return AuthService.isInGuestMode();
  }

  // Get current user ID for storage keys
  getCurrentUserId() {
    const currentUser = AuthService.getCurrentUser();
    const isGuestMode = AuthService.isInGuestMode();

    if (isGuestMode || !currentUser) {
      return 'guest';
    }

    return currentUser.id || 'guest';
  }

  // Get user-specific storage key
  getUserStorageKey(dataType) {
    const userId = this.getCurrentUserId();
    return `${dataType}_${userId}`;
  }

  // Get categories
  async getCategories() {
    try {
      // GUEST MODE: Only use local storage, no backend requests
      if (this.isGuestMode()) {
        console.log('Guest mode: Using local categories only');
        if (this.localData.categories && this.localData.categories.length > 0) {
          return this.localData.categories;
        } else {
          console.log('Guest mode: No categories found, returning default categories');
          return DefaultCategories;
        }
      }

      // AUTHENTICATED MODE: Try backend first, fallback to local
      if (this.shouldUseBackend()) {
        try {
          const response = await this.apiClient.makeRequest('/categories', { method: 'GET' });
          const backendCategories = response?.data;
          if (backendCategories && backendCategories.length > 0) {
            // Update local cache
            await this.saveLocalData('categories', backendCategories);
            return backendCategories;
          }
        } catch (error) {
          console.warn('Failed to fetch categories from backend, using local data:', error);
        }
      }

      // Fallback to local data or default categories
      if (this.localData.categories && this.localData.categories.length > 0) {
        return this.localData.categories;
      } else {
        console.log('No categories found, returning default categories');
        return DefaultCategories;
      }
    } catch (error) {
      console.error('Error getting categories:', error);
      // Return default categories on error
      return DefaultCategories;
    }
  }

  // Add expense
  async addExpense(expense) {
    try {
      const currentUser = AuthService.getCurrentUser();
      const isGuestMode = this.isGuestMode();
      const shouldUseBackend = this.shouldUseBackend();

      console.log('DataManager.addExpense - Mode check:', {
        isGuestMode,
        shouldUseBackend,
        currentUser: currentUser?.email || currentUser?.name,
        userId: this.getCurrentUserId()
      });

      // SEASON INTEGRATION: Ensure expense has a valid season
      let seasonId = expense.seasonId;
      if (!seasonId) {
        console.log('DataManager.addExpense - No season provided, getting active season...');
        const activeSeason = await this.getActiveSeason();
        if (activeSeason) {
          seasonId = activeSeason.id || activeSeason._id;
          console.log('DataManager.addExpense - Using active season:', activeSeason.name);
        } else {
          console.log('DataManager.addExpense - No active season found, creating default season...');
          const defaultSeason = await this.createDefaultSeason();
          await this.setActiveSeason(defaultSeason.id);
          seasonId = defaultSeason.id;
          console.log('DataManager.addExpense - Created and activated default season:', defaultSeason.name);
        }
      }

      const newExpense = {
        id: Date.now().toString(),
        ...expense,
        seasonId: seasonId,  // SEASON INTEGRATION: Ensure seasonId is always present
        date: expense.date || new Date().toISOString(), // Ensure date field exists
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        syncStatus: isGuestMode ? 'local' : (shouldUseBackend ? 'pending' : 'local')
      };

      // GUEST MODE: Only save locally, no backend requests
      if (isGuestMode) {
        console.log('DataManager.addExpense - Guest mode: Saving only locally, no backend requests');
      }
      // AUTHENTICATED MODE: Try backend first
      else if (shouldUseBackend) {
        console.log('DataManager.addExpense - Authenticated mode: Attempting to save to backend...');
        try {
          const backendExpense = await this.apiClient.createExpense(newExpense);
          if (backendExpense) {
            newExpense.id = backendExpense.id;
            newExpense.syncStatus = 'synced';
            console.log('DataManager.addExpense - Backend save successful');
          }
        } catch (error) {
          console.warn('Failed to save expense to backend, saving locally:', error);
          newExpense.syncStatus = 'pending';
        }
      }

      // Always save to local storage
      const currentExpenses = Array.isArray(this.localData.expenses) ? this.localData.expenses : [];
      const updatedExpenses = [...currentExpenses, newExpense];
      await this.saveLocalData('expenses', updatedExpenses);

      // Notify listeners about data change
      this.notifyDataChange();

      console.log('Expense added successfully:', newExpense.id);
      return newExpense;
    } catch (error) {
      console.error('Error adding expense:', error);
      throw error;
    }
  }

  // Get expenses
  async getExpenses(limit = null) {
    try {
      // GUEST MODE: Only use local storage, no backend requests
      if (this.isGuestMode()) {
        console.log('Guest mode: Using local expenses only');
        return this.formatExpenses(this.localData.expenses, limit);
      }

      // AUTHENTICATED MODE: Try backend first, fallback to local
      if (this.shouldUseBackend()) {
        try {
          const response = await this.apiClient.makeRequest('/expenses', { method: 'GET' });
          const backendExpenses = response?.data;
          if (backendExpenses) {
            // Update local cache
            await this.saveLocalData('expenses', backendExpenses);
            return this.formatExpenses(backendExpenses, limit);
          }
        } catch (error) {
          console.warn('Failed to fetch expenses from backend, using local data:', error);
        }
      }

      // Fallback to local data
      return this.formatExpenses(this.localData.expenses, limit);
    } catch (error) {
      console.error('Error getting expenses:', error);
      return this.formatExpenses(this.localData.expenses, limit);
    }
  }

  // Format expenses with category information
  formatExpenses(expenses, limit = null) {
    // Ensure expenses is an array before processing
    const expensesArray = Array.isArray(expenses) ? expenses : [];
    let sortedExpenses = [...expensesArray].sort((a, b) => {
      // First sort by date (newest first)
      const dateComparison = new Date(b.date) - new Date(a.date);
      if (dateComparison !== 0) {
        return dateComparison;
      }
      // If dates are the same, sort by creation time (newest first)
      return new Date(b.createdAt || b.updatedAt || 0) - new Date(a.createdAt || a.updatedAt || 0);
    });

    if (limit) {
      sortedExpenses = sortedExpenses.slice(0, limit);
    }

    return sortedExpenses.map(expense => {
      // Check if this is a backend expense with populated categoryId
      if (expense.categoryId && typeof expense.categoryId === 'object' && expense.categoryId.name) {
        // Backend expense with populated category
        return {
          ...expense,
          categoryName: expense.categoryId.name,
          categoryEmoji: expense.categoryId.emoji || '📝',
          categoryColor: expense.categoryId.color || '#666'
        };
      } else {
        // Local expense or backend expense without populated category
        const currentCategories = Array.isArray(this.localData.categories) ? this.localData.categories : [];
        const categoryId = typeof expense.categoryId === 'object' ? expense.categoryId._id || expense.categoryId.id : expense.categoryId;
        const category = currentCategories.find(cat => cat.id === categoryId || cat._id === categoryId);
        return {
          ...expense,
          categoryName: category?.name || 'Bilinmeyen',
          categoryEmoji: category?.emoji || '📝',
          categoryColor: category?.color || '#666'
        };
      }
    });
  }

  // Update expense
  async updateExpense(id, expense) {
    try {
      const isGuestMode = this.isGuestMode();
      const shouldUseBackend = this.shouldUseBackend();

      // SEASON INTEGRATION: Ensure expense has a valid season
      let seasonId = expense.seasonId;
      if (!seasonId) {
        console.log('DataManager.updateExpense - No season provided, getting active season...');
        const activeSeason = await this.getActiveSeason();
        if (activeSeason) {
          seasonId = activeSeason.id || activeSeason._id;
          console.log('DataManager.updateExpense - Using active season:', activeSeason.name);
        } else {
          console.log('DataManager.updateExpense - No active season found, creating default season...');
          const defaultSeason = await this.createDefaultSeason();
          await this.setActiveSeason(defaultSeason.id);
          seasonId = defaultSeason.id;
          console.log('DataManager.updateExpense - Created and activated default season:', defaultSeason.name);
        }
      }

      const updatedExpense = {
        ...expense,
        seasonId: seasonId,  // SEASON INTEGRATION: Ensure seasonId is always present
        updatedAt: new Date().toISOString(),
        syncStatus: isGuestMode ? 'local' : (shouldUseBackend ? 'pending' : 'local')
      };

      // GUEST MODE: Only update locally, no backend requests
      if (isGuestMode) {
        console.log('Guest mode: Updating expense locally only, no backend requests');
      }
      // AUTHENTICATED MODE: Try backend first
      else if (shouldUseBackend) {
        try {
          const backendExpense = await this.apiClient.updateExpense(id, updatedExpense);
          if (backendExpense) {
            updatedExpense.syncStatus = 'synced';
          }
        } catch (error) {
          console.warn('Failed to update expense in backend, updating locally:', error);
          updatedExpense.syncStatus = 'pending';
        }
      }

      // Always update local storage
      const currentExpenses = Array.isArray(this.localData.expenses) ? this.localData.expenses : [];
      const expenseIndex = currentExpenses.findIndex(exp => (exp._id || exp.id) === id);
      if (expenseIndex !== -1) {
        currentExpenses[expenseIndex] = { ...currentExpenses[expenseIndex], ...updatedExpense };
        this.localData.expenses = currentExpenses;
        await this.saveLocalData('expenses', currentExpenses);

        // Notify listeners about data change
        this.notifyDataChange();
      }

      return updatedExpense;
    } catch (error) {
      console.error('Error updating expense:', error);
      throw error;
    }
  }

  // Delete expense
  async deleteExpense(id) {
    try {
      const isGuestMode = this.isGuestMode();
      const shouldUseBackend = this.shouldUseBackend();

      // GUEST MODE: Only delete locally, no backend requests
      if (isGuestMode) {
        console.log('Guest mode: Deleting expense locally only, no backend requests');
      }
      // AUTHENTICATED MODE: Try backend first
      else if (shouldUseBackend) {
        try {
          await this.apiClient.deleteExpense(id);
        } catch (error) {
          console.warn('Failed to delete expense from backend, deleting locally:', error);
        }
      }

      // Always delete from local storage
      const currentExpenses = Array.isArray(this.localData.expenses) ? this.localData.expenses : [];
      console.log('DataManager - Before delete:', currentExpenses.length, 'expenses, looking for ID:', id);
      const updatedExpenses = currentExpenses.filter(expense => (expense._id || expense.id) !== id);
      console.log('DataManager - After delete:', updatedExpenses.length, 'expenses');
      this.localData.expenses = updatedExpenses;
      await this.saveLocalData('expenses', updatedExpenses);

      // Notify listeners about data change
      this.notifyDataChange();

      return true;
    } catch (error) {
      console.error('Error deleting expense:', error);
      throw error;
    }
  }

  // Sync with backend (for authenticated users only)
  async syncWithBackend() {
    // GUEST MODE: Skip sync completely
    if (this.isGuestMode()) {
      console.log('Guest mode: Skipping backend sync');
      return;
    }

    if (!this.shouldUseBackend() || this.syncInProgress) {
      return;
    }

    try {
      this.syncInProgress = true;
      console.log('Starting sync with backend...');

      // Sync pending expenses
      // Ensure expenses is an array before processing
      const currentExpenses = Array.isArray(this.localData.expenses) ? this.localData.expenses : [];
      const pendingExpenses = currentExpenses.filter(exp => exp.syncStatus === 'pending');

      for (const expense of pendingExpenses) {
        try {
          if (expense.id.startsWith('temp_')) {
            // Create new expense in backend
            const backendExpense = await this.apiClient.createExpense(expense);
            if (backendExpense) {
              // Update local expense with backend ID
              const expenseIndex = currentExpenses.findIndex(exp => exp.id === expense.id);
              if (expenseIndex !== -1) {
                currentExpenses[expenseIndex] = {
                  ...currentExpenses[expenseIndex],
                  id: backendExpense.id,
                  syncStatus: 'synced'
                };
              }
            }
          } else {
            // Update existing expense in backend
            await this.apiClient.updateExpense(expense.id, expense);
            const expenseIndex = currentExpenses.findIndex(exp => exp.id === expense.id);
            if (expenseIndex !== -1) {
              currentExpenses[expenseIndex].syncStatus = 'synced';
            }
          }
        } catch (error) {
          console.warn(`Failed to sync expense ${expense.id}:`, error);
        }
      }

      // Update local data and save
      this.localData.expenses = currentExpenses;
      await this.saveLocalData('expenses', currentExpenses);

      console.log('Sync completed successfully');
    } catch (error) {
      console.error('Sync failed:', error);
    } finally {
      this.syncInProgress = false;
    }
  }

  // Migrate guest data to authenticated user (REPLACES existing user data)
  async migrateGuestDataToUser() {
    console.log('🔄 MIGRATION START: migrateGuestDataToUser called');
    console.log('🔄 MIGRATION: Authentication check...');
    if (!this.isAuthenticated()) {
      console.error('❌ MIGRATION ERROR: User not authenticated');
      throw new Error('User must be authenticated to migrate data');
    }
    console.log('✅ MIGRATION: User is authenticated, proceeding...');

    let backupId = null;
    let migrationProgress = {
      step: 'start',
      completed: false,
      seasonCreated: false,
      dataDeleted: false,
      expensesMigrated: 0,
      fieldsMigrated: 0
    };

    try {
      console.log('🔄 MIGRATION STEP 0: Creating backup of guest data...');
      const backupResult = await this.migrationBackup.createBackup();
      backupId = backupResult.backupId;
      console.log('✅ MIGRATION STEP 0: Backup created successfully:', {
        backupId,
        metadata: backupResult.metadata
      });

      console.log('🔄 MIGRATION STEP 1: Starting guest data migration (REPLACE mode)...');

      // Get guest data from local storage
      console.log('🔄 MIGRATION STEP 1A: Loading guest data from AsyncStorage...');
      const [guestExpenses, guestCategories] = await Promise.all([
        AsyncStorage.getItem('expenses_guest'),
        AsyncStorage.getItem('categories_guest')
      ]);
      const guestExpenseData = guestExpenses ? JSON.parse(guestExpenses) : [];
      const guestCategoryData = guestCategories ? JSON.parse(guestCategories) : [];

      // Create a mapping from guest category ID to guest category name
      const guestCategoryMap = guestCategoryData.reduce((acc, category) => {
        acc[category.id] = category.name.toLowerCase();
        return acc;
      }, {});

      console.log('🔄 MIGRATION STEP 1A RESULT:', {
        rawExpenses: guestExpenses ? 'Found' : 'Not found',
        rawCategories: guestCategories ? 'Found' : 'Not found',
        parsedExpenseCount: guestExpenseData.length,
        parsedCategoryCount: guestCategoryData.length,
        guestCategoryMap,
        parsedCount: guestExpenseData.length,
        sampleData: guestExpenseData.slice(0, 2)
      });

      if (guestExpenseData.length === 0) {
        console.log('⚠️ MIGRATION: No guest data to migrate');
        // Clean up backup since no migration needed
        if (backupId) {
          await this.migrationBackup.cleanup(backupId);
        }
        return { success: true, migratedCount: 0 };
      }

      console.log(`✅ MIGRATION STEP 1: Found ${guestExpenseData.length} guest expenses to migrate`);

      // STEP 1.5: Basic guest data validation (simplified)
      console.log('🔄 MIGRATION STEP 1.5: Validating guest data before migration...');
      
      // Simple validation - check if expense data is valid
      const invalidExpenses = guestExpenseData.filter(expense => {
        return !expense.amount || isNaN(parseFloat(expense.amount)) || parseFloat(expense.amount) <= 0;
      });

      if (invalidExpenses.length > 0) {
        console.error('❌ MIGRATION STEP 1.5 ERROR: Invalid expense data found:', invalidExpenses.length);
        throw new Error(`${invalidExpenses.length} geçersiz gider kaydı bulundu. Lütfen verilerinizi kontrol edin.`);
      }

      console.log('✅ MIGRATION STEP 1.5: Guest data validation passed', {
        totalExpenses: guestExpenseData.length,
        validExpenses: guestExpenseData.length - invalidExpenses.length
      });

      // STEP 2: Create season FIRST (before any data deletion)
      console.log('🔄 MIGRATION STEP 2: Creating season for migration (SAFE ORDER)...');
      let seasonId = null;
      migrationProgress.step = 'season_creation';

      try {
        // Ensure APIClient is initialized before migration
        if (!this.apiClient || !this.apiClient.initialized) {
          console.log('🔄 MIGRATION STEP 2A: APIClient not initialized, initializing for migration...');
          await this.initializeAPIClient();
          console.log('✅ MIGRATION STEP 2A: APIClient initialized');
        }

        // DEBUG: Check auth status before migration
        console.log('🔍 MIGRATION DEBUG: Auth status check:', {
          apiClientInitialized: this.apiClient.initialized,
          hasAuthToken: !!this.apiClient.authToken,
          authTokenLength: this.apiClient.authToken?.length,
          isGuestMode: this.isGuestMode(),
          shouldUseBackend: this.shouldUseBackend(),
          currentUser: AuthService.getCurrentUser()?.email
        });

        // DEBUG: Get fresh auth token for migration
        try {
          const freshToken = await this.apiClient.getAuthToken();
          console.log('🔍 MIGRATION DEBUG: Fresh auth token obtained:', {
            hasToken: !!freshToken,
            tokenLength: freshToken?.length,
            tokenPreview: freshToken ? `${freshToken.substring(0, 20)}...` : 'null'
          });
        } catch (tokenError) {
          console.error('🔍 MIGRATION DEBUG: Failed to get fresh auth token:', tokenError);
          throw new Error(`Auth token alınamadı: ${tokenError.message}`);
        }

        // Create season for migration with unique name
        const currentYear = new Date().getFullYear();
        const migrationDate = new Date().toLocaleDateString('tr-TR', {
          day: '2-digit',
          month: '2-digit'
        });
        const seasonData = {
          name: `${currentYear} Sezonu (Aktarım ${migrationDate})`,
          description: 'Guest verilerinden aktarılan sezon',
          startDate: new Date().toISOString(),
          isActive: true,
          isDefault: true,
          color: '#4CAF50',
          emoji: '🌱'
        };

        console.log('🔄 MIGRATION STEP 2B: Creating season with data:', {
          ...seasonData,
          dataTypes: {
            name: typeof seasonData.name,
            description: typeof seasonData.description,
            startDate: typeof seasonData.startDate,
            isActive: typeof seasonData.isActive,
            isDefault: typeof seasonData.isDefault,
            color: typeof seasonData.color,
            emoji: typeof seasonData.emoji
          },
          dataValues: {
            isActive: seasonData.isActive,
            isDefault: seasonData.isDefault,
            startDateFormat: seasonData.startDate,
            emojiLength: seasonData.emoji?.length,
            colorFormat: seasonData.color
          }
        });

        console.log('🔄 MIGRATION STEP 2B-API: Making API request to /seasons with method POST');
        console.log('🔄 MIGRATION STEP 2B-BODY: Request body:', JSON.stringify(seasonData, null, 2));

        const seasonResponse = await this.apiClient.makeRequest('/seasons', {
          method: 'POST',
          body: JSON.stringify(seasonData)
        });

        console.log('🔄 MIGRATION STEP 2B-RESPONSE: API response received:', {
          status: seasonResponse?.status,
          success: seasonResponse?.success,
          hasData: !!seasonResponse?.data,
          dataKeys: seasonResponse?.data ? Object.keys(seasonResponse.data) : [],
          errorCode: seasonResponse?.error?.code,
          errorMessage: seasonResponse?.error?.message
        });

        seasonId = seasonResponse?.data?._id || seasonResponse?.data?.id;
        if (!seasonId) {
          throw new Error('Season creation failed - no ID returned');
        }

        migrationProgress.seasonCreated = true;
        migrationProgress.step = 'season_created';
        console.log('✅ MIGRATION STEP 2: Season created successfully:', { seasonId, seasonResponse });

      } catch (error) {
        console.error('❌ MIGRATION STEP 2 ERROR: Failed to create season:', {
          errorMessage: error.message,
          errorStack: error.stack,
          errorName: error.name,
          apiClientInitialized: !!this.apiClient?.initialized,
          seasonDataSent: 'seasonData variable out of scope',
          errorDetails: error.response?.data || error.details || 'No additional details'
        });

        // Log specific validation errors if available
        if (error.response?.data?.error?.details?.validationErrors) {
          console.error('❌ MIGRATION STEP 2 VALIDATION ERRORS:', error.response.data.error.details.validationErrors);
        }

        throw new Error(`Sezon oluşturulamadı: ${error.message}`);
      }

      // STEP 3: Get categories and create mapping for migration
      console.log('🔄 MIGRATION STEP 3: Getting categories for migration...');
      let categoryMapping = {};
      let defaultCategoryId = null;

      // Ensure APIClient is initialized before migration
      if (!this.apiClient || !this.apiClient.initialized) {
        console.log('🔄 MIGRATION STEP 3A: APIClient not initialized, initializing for migration...');
        await this.initializeAPIClient();
        console.log('✅ MIGRATION STEP 3A: APIClient initialized');
      }

      // Debug APIClient status
      console.log('APIClient status before migration:', {
        exists: !!this.apiClient,
        initialized: this.apiClient?.initialized,
        hasAuthToken: !!this.apiClient?.authToken,
        hasGetCategoriesMethod: typeof this.apiClient?.getCategories === 'function',
        methods: this.apiClient ? Object.getOwnPropertyNames(Object.getPrototypeOf(this.apiClient)) : 'no apiClient'
      });

      try {
        console.log('🔄 MIGRATION STEP 3B: Fetching categories from backend...');
        const response = await this.apiClient.makeRequest('/categories', { method: 'GET' });
        const categories = response?.data;
        console.log('✅ MIGRATION STEP 3B: Categories received from API:', {
          categories,
          type: typeof categories,
          isArray: Array.isArray(categories),
          length: categories?.length,
          firstCategory: categories?.[0]
        });

        // Extract categories array from response
        const categoriesArray = Array.isArray(categories) ? categories : categories?.data;
        console.log('Extracted categories array:', {
          categoriesArray,
          isArray: Array.isArray(categoriesArray),
          length: categoriesArray?.length,
          firstCategory: categoriesArray?.[0]
        });

        if (categoriesArray && categoriesArray.length > 0) {
          // Create mapping from category names to IDs
          categoriesArray.forEach(category => {
            const categoryId = category._id || category.id; // Use _id first, fallback to id
            const name = category.name?.toLowerCase();
            if (name && categoryId) {
              categoryMapping[name] = categoryId;
              // Also map English names to Turkish categories
              if (name.includes('gübre')) categoryMapping['fertilizer'] = categoryId;
              if (name.includes('işçilik')) categoryMapping['labor'] = categoryId;
              if (name.includes('ilaç')) categoryMapping['pesticide'] = categoryId;
              if (name.includes('su')) categoryMapping['water'] = categoryId;
              if (name.includes('yakıt')) categoryMapping['fuel'] = categoryId;
              if (name.includes('tohum')) categoryMapping['seed'] = categoryId;
              if (name.includes('makine')) categoryMapping['machine'] = categoryId;
              if (name.includes('makine')) categoryMapping['machinery'] = categoryId; // Both variants
              if (name.includes('depolama')) categoryMapping['storage'] = categoryId;

              // Map by emoji as additional fallback
              if (category.emoji) {
                if (category.emoji === '🌱') categoryMapping['fertilizer'] = categoryId;
                if (category.emoji === '👷') categoryMapping['labor'] = categoryId;
                if (category.emoji === '🐛') categoryMapping['pesticide'] = categoryId;
                if (category.emoji === '💧') categoryMapping['water'] = categoryId;
                if (category.emoji === '⛽') categoryMapping['fuel'] = categoryId;
                if (category.emoji === '🌾') categoryMapping['seed'] = categoryId;
                if (category.emoji === '🚜') categoryMapping['machinery'] = categoryId;
                if (category.emoji === '🚜') categoryMapping['machine'] = categoryId;
                if (category.emoji === '🏪') categoryMapping['storage'] = categoryId;
              }
            }
          });

          // Use first available category as default
          const firstCategory = categoriesArray.find(c => c._id || c.id);
          defaultCategoryId = firstCategory ? (firstCategory._id || firstCategory.id) : null;

          console.log('Category mapping created:', {
            mappingCount: Object.keys(categoryMapping).length,
            defaultCategoryId,
            availableCategories: categoriesArray?.map(c => ({
              id: c._id || c.id,
              name: c.name,
              emoji: c.emoji
            }))
          });
        }
      } catch (error) {
        console.error('Failed to get categories for migration:', error);
        console.error('Category fetch error details:', {
          message: error.message,
          status: error.status,
          apiInitialized: this.apiClient.initialized,
          hasAuthToken: !!this.apiClient.authToken
        });

        // If categories can't be fetched, try to create default categories first
        console.log('Attempting to create default categories for new user...');
        try {
          await this.createDefaultCategoriesForUser();
          // Retry getting categories
          const retryResponse = await this.apiClient.makeRequest('/categories', { method: 'GET' });
          const retryCategories = retryResponse?.data;
          if (retryCategories && retryCategories.length > 0) {
            const firstCategory = retryCategories.find(c => c._id || c.id);
            defaultCategoryId = firstCategory ? (firstCategory._id || firstCategory.id) : null;
            console.log('Default categories created, using first category as default:', defaultCategoryId);

            // Update category mapping with new categories
            retryCategories.forEach(category => {
              const categoryId = category._id || category.id;
              const name = category.name?.toLowerCase();
              if (name && categoryId) {
                categoryMapping[name] = categoryId;
                // Map English names to Turkish categories
                if (name.includes('gübre')) categoryMapping['fertilizer'] = categoryId;
                if (name.includes('işçilik')) categoryMapping['labor'] = categoryId;
                if (name.includes('ilaç')) categoryMapping['pesticide'] = categoryId;
                if (name.includes('su')) categoryMapping['water'] = categoryId;
                if (name.includes('yakıt')) categoryMapping['fuel'] = categoryId;
                if (name.includes('tohum')) categoryMapping['seed'] = categoryId;
                if (name.includes('makine')) categoryMapping['machine'] = categoryId;
                if (name.includes('makine')) categoryMapping['machinery'] = categoryId;
                if (name.includes('depolama')) categoryMapping['storage'] = categoryId;
              }
            });
          }
        } catch (createError) {
          console.error('Failed to create default categories:', createError);
          throw new Error('Kategori bilgisi alınamadı');
        }
      }

      if (!defaultCategoryId) {
        throw new Error('Varsayılan kategori bulunamadı');
      }

      // STEP 3: Get or create backend season for migration
      let activeSeasonId = null;
      try {
        console.log('Getting backend seasons for migration...');

        // Try to get seasons from backend first
        try {
          console.log('Making API request to get seasons...');
          const response = await this.apiClient.makeRequest('/seasons', { method: 'GET' });
          console.log('Seasons API response:', {
            response,
            hasData: !!response?.data,
            dataLength: response?.data?.length,
            dataType: typeof response?.data
          });

          if (response && response.data && response.data.length > 0) {
            // Find active season from backend
            const activeSeason = response.data.find(s => s.isActive);
            if (activeSeason) {
              activeSeasonId = activeSeason._id || activeSeason.id;
              console.log('Using existing backend active season for migration:', activeSeason.name, activeSeasonId);
            } else {
              // Use first season if no active season found
              const firstSeason = response.data[0];
              activeSeasonId = firstSeason._id || firstSeason.id;
              console.log('Using first backend season for migration:', firstSeason.name, activeSeasonId);
            }
          } else {
            console.log('No seasons found in backend response');
          }
        } catch (backendError) {
          console.warn('Failed to get seasons from backend:', backendError);
        }

        // If no backend season found, create one
        if (!activeSeasonId) {
          console.log('No backend season found, creating default season...');
          const currentYear = new Date().getFullYear();
          const seasonData = {
            name: `${currentYear} Sezonu`,
            description: 'Migration için oluşturulan varsayılan sezon',
            startDate: new Date().toISOString(),
            isActive: true,
            isDefault: true,
            color: '#4CAF50',
            emoji: '🌱'
          };

          console.log('🔄 MIGRATION STEP 3C: Creating season with data:', {
            ...seasonData,
            dataTypes: {
              name: typeof seasonData.name,
              description: typeof seasonData.description,
              startDate: typeof seasonData.startDate,
              isActive: typeof seasonData.isActive,
              isDefault: typeof seasonData.isDefault,
              color: typeof seasonData.color,
              emoji: typeof seasonData.emoji
            },
            dataValues: {
              isActive: seasonData.isActive,
              isDefault: seasonData.isDefault,
              startDateFormat: seasonData.startDate,
              emojiLength: seasonData.emoji?.length,
              colorFormat: seasonData.color
            }
          });

          console.log('🔄 MIGRATION STEP 3C-API: Making API request to /seasons with method POST');
          console.log('🔄 MIGRATION STEP 3C-BODY: Request body:', JSON.stringify(seasonData, null, 2));

          const response = await this.apiClient.makeRequest('/seasons', {
            method: 'POST',
            body: JSON.stringify(seasonData)
          });

          console.log('🔄 MIGRATION STEP 3C-RESPONSE: API response received:', {
            status: response?.status,
            success: response?.success,
            hasData: !!response?.data,
            dataKeys: response?.data ? Object.keys(response.data) : [],
            errorCode: response?.error?.code,
            errorMessage: response?.error?.message
          });
          console.log('🔄 MIGRATION STEP 3C RESULT: Season creation response:', response);

          if (response && response.data) {
            activeSeasonId = response.data._id || response.data.id;
            console.log('Created backend season for migration:', response.data.name, activeSeasonId);
          }
        }
      } catch (seasonError) {
        console.warn('Error getting/creating backend season for migration:', seasonError);
        // Continue without season ID - backend will handle this
      }

      // STEP 4: Upload guest data to backend
      console.log('Uploading guest data to backend...');
      let migratedCount = 0;

      for (const expense of guestExpenseData) {
        try {
          // Comprehensive expense validation using ExpenseValidationService
          console.log('🔄 Validating expense for migration:', expense.description || 'unnamed');
          const migrationValidation = ExpenseValidationService.isMigrationReady(expense);

          if (!migrationValidation.isReady) {
            console.warn('❌ Skipping expense with validation errors:', {
              expense: expense.description || 'unnamed',
              errors: migrationValidation.errors
            });
            migrationProgress.expensesMigrated++; // Count as processed but failed
            continue;
          }

          if (migrationValidation.warnings.length > 0) {
            console.warn('⚠️ Expense has warnings but will be migrated:', {
              expense: expense.description,
              warnings: migrationValidation.warnings
            });
          }

          // Use sanitized expense data from validation
          const sanitizedExpense = migrationValidation.expense;

          // Map categoryId from guest format to backend format
          let mappedCategoryId = defaultCategoryId;
          if (sanitizedExpense.categoryId && guestCategoryMap[sanitizedExpense.categoryId]) {
            const guestCategoryName = guestCategoryMap[sanitizedExpense.categoryId];
            mappedCategoryId = categoryMapping[guestCategoryName] || defaultCategoryId;
            
            console.log('Category mapping:', {
              originalId: sanitizedExpense.categoryId,
              guestCategoryName,
              mapped: mappedCategoryId,
              defaultUsed: mappedCategoryId === defaultCategoryId
            });
          }

          // Convert guest expense to backend format
          const migratedExpense = {
            categoryId: mappedCategoryId || defaultCategoryId,
            amount: sanitizedExpense.amount,
            description: sanitizedExpense.description || '',
            date: sanitizedExpense.date || new Date().toISOString().split('T')[0],
            seasonId: seasonId || activeSeasonId, // Use created season ID
            location: sanitizedExpense.location || {},
            photos: sanitizedExpense.photos || [],
            tags: sanitizedExpense.tags || [],
            paymentMethod: sanitizedExpense.paymentMethod || 'cash',
            currency: sanitizedExpense.currency || 'TRY'
          };

          // Ensure we have valid IDs
          if (!migratedExpense.categoryId) {
            console.warn('No category ID available, using default');
            migratedExpense.categoryId = defaultCategoryId;
          }
          
          if (!migratedExpense.seasonId) {
            console.warn('No season ID available, using active season');
            migratedExpense.seasonId = activeSeasonId;
          }

          console.log('Migrating expense:', {
            original: { id: expense.id, categoryId: expense.categoryId, amount: expense.amount },
            migrated: { categoryId: migratedExpense.categoryId, amount: migratedExpense.amount },
            categoryMapping: { [expense.categoryId]: mappedCategoryId }
          });

          console.log('Full migrated expense data:', migratedExpense);

          const backendExpense = await this.apiClient.createExpense(migratedExpense);
          if (backendExpense) {
            migratedCount++;
            console.log(`Successfully migrated expense ${expense.id || 'unknown'}`);
          }
        } catch (error) {
          console.error(`Failed to migrate expense ${expense.id || 'unknown'}:`, {
            error: error.message,
            expense: {
              id: expense.id,
              categoryId: expense.categoryId,
              amount: expense.amount
            },
            errorDetails: error
          });
          // Continue with next expense instead of failing completely
        }
      }

      // STEP 5: Migrate guest fields if any
      console.log('Migrating guest fields...');
      const guestFields = await AsyncStorage.getItem('fields_guest');
      const guestFieldData = guestFields ? JSON.parse(guestFields) : [];
      let migratedFieldCount = 0;

      if (guestFieldData.length > 0) {
        console.log(`Found ${guestFieldData.length} guest fields to migrate`);

        for (const field of guestFieldData) {
          try {
            // Comprehensive field validation using FieldValidationService
            console.log('🔄 Validating field for migration:', field.name || 'unnamed');
            const migrationValidation = FieldValidationService.isMigrationReady(field);

            if (!migrationValidation.isReady) {
              console.warn('❌ Skipping field with validation errors:', {
                field: field.name || 'unnamed',
                errors: migrationValidation.errors
              });
              migrationProgress.fieldsMigrated++; // Count as processed but failed
              continue;
            }

            if (migrationValidation.warnings.length > 0) {
              console.warn('⚠️ Field has warnings but will be migrated:', {
                field: field.name,
                warnings: migrationValidation.warnings
              });
            }

            // Use sanitized field data from validation
            const sanitizedField = migrationValidation.field;

            // Convert guest field to backend format with validated data
            const migratedField = {
              name: sanitizedField.name,
              size: {
                value: sanitizedField.size?.value || 1,
                unit: sanitizedField.size?.unit || 'dekar'
              },
              location: sanitizedField.location || {},
              soilType: sanitizedField.soilType || 'other',
              irrigationType: sanitizedField.irrigationType || 'none',
              notes: sanitizedField.notes || '',
              isDefault: sanitizedField.isDefault || false,
              isActive: sanitizedField.isActive !== false // Default to true
            };

            console.log('Migrating field:', {
              original: { id: field._id, name: field.name, size: field.size },
              migrated: { name: migratedField.name, size: migratedField.size }
            });

            const backendField = await this.apiClient.createField(migratedField);
            if (backendField) {
              migratedFieldCount++;
              console.log(`Successfully migrated field ${field.name || field._id}`);
            }
          } catch (error) {
            console.error(`Failed to migrate field ${field.name || field._id}:`, {
              error: error.message,
              field: {
                id: field._id,
                name: field.name,
                size: field.size
              },
              errorDetails: error
            });
            // Continue with next field instead of failing completely
          }
        }
      }

      // STEP 6: Migration completed successfully - no need to delete backend data
      console.log('✅ MIGRATION STEP 6: Migration completed successfully, keeping migrated data in backend');
      migrationProgress.dataDeleted = false; // We're not deleting backend data anymore
      migrationProgress.step = 'migration_completed';

      // STEP 7: Clear guest data and reload from backend
      console.log('🔄 MIGRATION STEP 7: Clearing guest data and syncing...');
      await Promise.all([
        AsyncStorage.removeItem('expenses_guest'),
        AsyncStorage.removeItem('fields_guest')
      ]);
      console.log('Guest data cleared from local storage');

      // Force reload data from backend to refresh UI
      console.log('DataManager: Force syncing after migration...');
      await this.syncWithBackend();

      // Notify listeners about data change
      this.notifyDataChange();

      migrationProgress.completed = true;
      migrationProgress.step = 'completed';
      console.log(`Migration completed: ${migratedCount} expenses and ${migratedFieldCount} fields migrated (SAFE ORDER)`, migrationProgress);

      // Clean up backup after successful migration
      if (backupId) {
        await this.migrationBackup.cleanup(backupId);
        console.log('✅ MIGRATION: Backup cleaned up after successful migration');
      }

      return {
        success: true,
        migratedCount,
        migratedFieldCount,
        backupId,
        migrationProgress
      };
    } catch (error) {
      console.error('❌ MIGRATION FAILED:', error);
      console.error('Migration error details:', {
        message: error.message,
        stack: error.stack,
        name: error.name,
        code: error.code,
        migrationProgress: migrationProgress || 'not available'
      });

      // Attempt rollback if backup exists
      if (backupId) {
        try {
          console.log('🔄 MIGRATION: Attempting automatic rollback...');
          const rollbackResult = await this.migrationBackup.rollback(backupId);
          console.log('✅ MIGRATION: Automatic rollback successful:', rollbackResult);

          // Reload local data after rollback
          await this.loadLocalData();
          this.notifyDataChange();

          // Add rollback info to error
          error.rollbackSuccessful = true;
          error.rollbackResult = rollbackResult;

        } catch (rollbackError) {
          console.error('❌ MIGRATION: Automatic rollback failed:', rollbackError);
          error.rollbackSuccessful = false;
          error.rollbackError = rollbackError.message;
          // Don't throw rollback error, keep original migration error
        }
      }

      // Enhanced error message with migration progress
      const userFriendlyMessage = this.getMigrationErrorMessage(error, migrationProgress);
      const enhancedError = new Error(userFriendlyMessage);
      enhancedError.migrationProgress = migrationProgress;
      enhancedError.rollbackSuccessful = error.rollbackSuccessful;
      throw enhancedError;
    }
  }

  // Create default categories for new user during migration
  async createDefaultCategoriesForUser() {
    try {
      console.log('Creating default categories for user...');

      const defaultCategories = [
        { name: 'Gübre', emoji: '🌱', color: '#4CAF50', icon: 'leaf' },
        { name: 'İşçilik', emoji: '👷', color: '#9C27B0', icon: 'people' },
        { name: 'İlaç', emoji: '🐛', color: '#FF5722', icon: 'bug' },
        { name: 'Su', emoji: '💧', color: '#2196F3', icon: 'water' },
        { name: 'Yakıt', emoji: '⛽', color: '#FF9800', icon: 'car' },
        { name: 'Tohum', emoji: '🌾', color: '#8BC34A', icon: 'grain' },
        { name: 'Makine', emoji: '🚜', color: '#795548', icon: 'construct' },
        { name: 'Depolama', emoji: '🏪', color: '#607D8B', icon: 'archive' }
      ];

      const createdCategories = [];
      for (const categoryData of defaultCategories) {
        try {
          const createdCategory = await this.apiClient.createCategory(categoryData);
          if (createdCategory) {
            createdCategories.push(createdCategory);
            console.log(`Created category: ${categoryData.name}`);
          }
        } catch (error) {
          console.warn(`Failed to create category ${categoryData.name}:`, error);
          // Continue with other categories
        }
      }

      console.log(`Successfully created ${createdCategories.length} default categories`);
      return createdCategories;
    } catch (error) {
      console.error('Error creating default categories:', error);
      throw error;
    }
  }

  // Get user-friendly migration error message with progress info
  getMigrationErrorMessage(error, migrationProgress = null) {
    const errorMessage = error.message || '';
    let baseMessage = '';

    // Determine error type based on migration progress
    if (migrationProgress) {
      switch (migrationProgress.step) {
        case 'season_creation':
          baseMessage = 'Sezon oluşturulurken hata oluştu. ';
          break;
        case 'season_created':
          baseMessage = 'Veri aktarımı sırasında hata oluştu. ';
          break;
        case 'data_deleted':
          baseMessage = 'Veri temizleme sırasında hata oluştu. ';
          break;
        default:
          baseMessage = 'Veri aktarımı sırasında hata oluştu. ';
      }
    }

    // Specific error messages
    if (errorMessage.includes('Sezon oluşturulamadı')) {
      baseMessage = 'Sezon oluşturulamadı. İnternet bağlantınızı kontrol edin ve tekrar deneyin.';
    } else if (errorMessage.includes('Mevcut veriler silinemedi')) {
      baseMessage = 'Google hesabınızdaki mevcut veriler silinemedi. İnternet bağlantınızı kontrol edin ve tekrar deneyin.';
    }

    if (errorMessage.includes('Kategori bilgisi alınamadı')) {
      return 'Kategori bilgileri alınamadı. İnternet bağlantınızı kontrol edin ve tekrar deneyin.';
    }

    if (errorMessage.includes('Varsayılan kategori bulunamadı')) {
      return 'Sistem kategorileri yüklenemedi. Lütfen daha sonra tekrar deneyin.';
    }

    if (errorMessage.includes('Network Error') || errorMessage.includes('fetch')) {
      return 'İnternet bağlantısı sorunu. Bağlantınızı kontrol edin ve tekrar deneyin.';
    }

    if (errorMessage.includes('401') || errorMessage.includes('Unauthorized')) {
      return 'Yetkilendirme hatası. Lütfen çıkış yapıp tekrar giriş yapmayı deneyin.';
    }

    if (errorMessage.includes('500') || errorMessage.includes('Internal Server Error')) {
      return 'Sunucu hatası. Lütfen daha sonra tekrar deneyin.';
    }

    // Default error message
    return 'Veri yedekleme sırasında bir hata oluştu. Lütfen tekrar deneyin.';
  }

  // Helper function to generate local UUID
  generateLocalId() {
    // Simple UUID v4 implementation for local IDs
    return 'local_' + 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  // Helper function to get current season (legacy)
  getCurrentSeason() {
    const month = new Date().getMonth() + 1; // 1-12
    if (month >= 3 && month <= 5) return 'spring';
    if (month >= 6 && month <= 8) return 'summer';
    if (month >= 9 && month <= 11) return 'autumn';
    return 'winter';
  }

  // Clear local data (used after migration or logout)
  async clearLocalData() {
    try {
      await Promise.all([
        AsyncStorage.removeItem(this.getUserStorageKey('expenses')),
        AsyncStorage.removeItem(this.getUserStorageKey('categories')),
        AsyncStorage.removeItem(this.getUserStorageKey('fields')),
        AsyncStorage.removeItem(this.getUserStorageKey('seasons')),
        AsyncStorage.removeItem(this.getUserStorageKey('settings'))
      ]);

      this.localData = {
        categories: DefaultCategories,
        expenses: [],
        fields: [],
        seasons: [],
        settings: {}
      };

      // Save default categories
      await this.saveLocalData('categories', DefaultCategories);

      console.log('Local data cleared successfully');
    } catch (error) {
      console.error('Error clearing local data:', error);
      throw error;
    }
  }

  // Clear data for a specific user (used when switching between user modes)
  async clearUserData(userId) {
    try {
      await Promise.all([
        AsyncStorage.removeItem(`expenses_${userId}`),
        AsyncStorage.removeItem(`categories_${userId}`),
        AsyncStorage.removeItem(`fields_${userId}`),
        AsyncStorage.removeItem(`settings_${userId}`)
      ]);

      console.log(`User data cleared successfully for user: ${userId}`);
    } catch (error) {
      console.error(`Error clearing user data for ${userId}:`, error);
      throw error;
    }
  }

  // Reset DataManager for user context change (e.g., sign out, sign in)
  async resetForUserChange() {
    try {
      console.log('DataManager: Resetting for user context change...');

      // Reset initialization flag to force reload
      this.initialized = false;

      // Clear in-memory data
      this.localData = {
        categories: [],
        expenses: [],
        settings: {}
      };

      // Reinitialize with new user context
      await this.initialize();

      console.log('DataManager: Reset completed for new user context');
    } catch (error) {
      console.error('DataManager: Error during user context reset:', error);
      throw error;
    }
  }

  // Initialize API client if not already initialized
  async initializeAPIClient() {
    try {
      if (!this.apiClient.initialized) {
        await this.apiClient.initialize();
      }
    } catch (error) {
      console.error('DataManager: Failed to initialize API client:', error);
      throw error;
    }
  }

  // Check if guest data exists for migration
  async checkGuestDataExists() {
    try {
      const [guestExpenses, guestFields] = await Promise.all([
        AsyncStorage.getItem('expenses_guest'),
        AsyncStorage.getItem('fields_guest')
      ]);

      const expenseData = guestExpenses ? JSON.parse(guestExpenses) : [];
      const fieldData = guestFields ? JSON.parse(guestFields) : [];

      console.log('Guest data check:', {
        expenseCount: expenseData.length,
        fieldCount: fieldData.length,
        hasData: expenseData.length > 0 || fieldData.length > 0
      });

      return {
        hasData: expenseData.length > 0 || fieldData.length > 0,
        expenseCount: expenseData.length,
        fieldCount: fieldData.length,
        expenses: expenseData,
        fields: fieldData
      };
    } catch (error) {
      console.error('Error checking guest data:', error);
      return {
        hasData: false,
        expenseCount: 0,
        fieldCount: 0,
        expenses: [],
        fields: []
      };
    }
  }

  // Check if current authenticated user has existing data in backend
  async checkUserHasExistingData() {
    try {
      console.log('DataManager: Checking if user has existing data...');

      if (!this.isAuthenticated()) {
        console.log('DataManager: User not authenticated, cannot check backend data');
        return { hasData: false, expenseCount: 0, categoryCount: 0 };
      }

      // Check if APIClient is initialized
      if (!this.apiClient || !this.apiClient.initialized) {
        console.log('DataManager: APIClient not initialized, initializing...');
        await this.initializeAPIClient();
      }

      // Make API request to check user data
      const response = await this.apiClient.makeRequest('/users/has-data');

      console.log('DataManager: User data check result:', response);

      return {
        hasData: response.hasData || false,
        expenseCount: response.expenseCount || 0,
        categoryCount: response.categoryCount || 0
      };
    } catch (error) {
      console.error('DataManager: Error checking user data:', error);
      // Return false on error to be safe
      return { hasData: false, expenseCount: 0, categoryCount: 0 };
    }
  }

  // Check if guest user has local data
  async hasGuestData() {
    try {
      console.log('DataManager: Checking if guest has local data...');

      // Load guest data from local storage
      const guestExpenses = await AsyncStorage.getItem('expenses_guest');
      const guestCategories = await AsyncStorage.getItem('categories_guest');
      const guestFields = await AsyncStorage.getItem('fields_guest');

      const expenseCount = guestExpenses ? JSON.parse(guestExpenses).length : 0;
      const categoryCount = guestCategories ? JSON.parse(guestCategories).length : 0;
      const fieldCount = guestFields ? JSON.parse(guestFields).length : 0;

      const hasData = expenseCount > 0 || categoryCount > 0 || fieldCount > 0;

      console.log('DataManager: Guest data check result:', {
        hasData,
        expenseCount,
        categoryCount,
        fieldCount
      });

      return {
        hasData,
        expenseCount,
        categoryCount,
        fieldCount
      };
    } catch (error) {
      console.error('DataManager: Error checking guest data:', error);
      return { hasData: false, expenseCount: 0, categoryCount: 0, fieldCount: 0 };
    }
  }

  // Get detailed guest data summary for migration preview
  async getGuestDataSummary() {
    try {
      const guestExpenses = await AsyncStorage.getItem('expenses_guest');
      const guestFields = await AsyncStorage.getItem('fields_guest');

      const expenses = guestExpenses ? JSON.parse(guestExpenses) : [];
      const fields = guestFields ? JSON.parse(guestFields) : [];

      // Calculate total expense amount
      const totalAmount = expenses.reduce((sum, expense) => {
        return sum + (parseFloat(expense.amount) || 0);
      }, 0);

      // Get field names
      const fieldNames = fields.map(field => field.name || 'İsimsiz Tarla');

      return {
        expenses: {
          count: expenses.length,
          totalAmount,
          recentExpenses: expenses.slice(-3).map(exp => ({
            description: exp.description,
            amount: exp.amount,
            date: exp.date
          }))
        },
        fields: {
          count: fields.length,
          fieldNames,
          hasDefaultField: fields.some(field => field.isDefault)
        }
      };
    } catch (error) {
      console.error('Error getting guest data summary:', error);
      return {
        expenses: { count: 0, totalAmount: 0, recentExpenses: [] },
        fields: { count: 0, fieldNames: [], hasDefaultField: false }
      };
    }
  }

  // Get sync status
  getSyncStatus() {
    // GUEST MODE: Return null to hide sync indicator
    if (this.isGuestMode()) {
      return null;
    }

    if (!this.shouldUseBackend()) {
      return null; // Return null for non-backend users to hide sync indicator
    }

    // Ensure expenses is an array before processing
    const currentExpenses = Array.isArray(this.localData.expenses) ? this.localData.expenses : [];
    const pendingCount = currentExpenses.filter(exp => exp.syncStatus === 'pending').length;

    return {
      mode: 'cloud',
      synced: pendingCount === 0,
      pendingCount,
      syncInProgress: this.syncInProgress
    };
  }

  // Data change notification system
  addDataChangeListener(listener) {
    this.dataChangeListeners.push(listener);
    return () => {
      this.dataChangeListeners = this.dataChangeListeners.filter(l => l !== listener);
    };
  }

  notifyDataChange() {
    console.log('DataManager: Notifying data change listeners:', this.dataChangeListeners.length);
    this.dataChangeListeners.forEach(listener => {
      try {
        listener();
      } catch (error) {
        console.error('DataManager: Error in data change listener:', error);
      }
    });
  }

  // ===== FIELD OPERATIONS =====

  // Get all fields
  async getFields() {
    try {
      // GUEST MODE: Only use local storage, no backend requests
      if (this.isGuestMode()) {
        console.log('Guest mode: Using local fields only');
        return this.localData.fields || [];
      }

      // AUTHENTICATED MODE: Try backend first, fallback to local
      if (this.shouldUseBackend()) {
        try {
          const response = await this.apiClient.makeRequest('/fields', { method: 'GET' });
          const backendFields = response?.data;
          if (backendFields) {
            // Update local cache
            await this.saveLocalData('fields', backendFields);
            return backendFields;
          }
        } catch (error) {
          console.warn('Failed to fetch fields from backend, using local data:', error);
        }
      }

      // Fallback to local data
      return this.localData.fields || [];
    } catch (error) {
      console.error('Error getting fields:', error);
      return this.localData.fields || [];
    }
  }

  // Get all crops
  async getCrops() {
    try {
      // GUEST MODE: Only use local storage, no backend requests
      if (this.isGuestMode()) {
        console.log('Guest mode: Using local crops only');
        const localCrops = this.localData.crops || [];
        // If no local crops, return default crops
        return localCrops.length > 0 ? localCrops : DefaultCrops;
      }

      // AUTHENTICATED MODE: Try backend first, fallback to local
      if (this.shouldUseBackend()) {
        try {
          const backendCrops = await this.apiClient.makeRequest('/crops');
          const crops = backendCrops.data || backendCrops || [];
          if (crops && crops.length > 0) {
            // Update local cache
            await this.saveLocalData('crops', crops);
            return crops;
          }
        } catch (error) {
          console.warn('Failed to fetch crops from backend, using local data:', error);
        }
      }

      // Fallback to local data or default crops
      const localCrops = this.localData.crops || [];
      return localCrops.length > 0 ? localCrops : DefaultCrops;
    } catch (error) {
      console.error('Error getting crops:', error);
      return DefaultCrops;
    }
  }

  // Add field
  async addField(field) {
    try {
      const currentUser = AuthService.getCurrentUser();
      const isGuestMode = this.isGuestMode();
      const shouldUseBackend = this.shouldUseBackend();

      console.log('DataManager.addField - Mode check:', {
        isGuestMode,
        shouldUseBackend,
        currentUser: currentUser?.email || currentUser?.name,
        userId: this.getCurrentUserId()
      });

      const newField = {
        _id: Date.now().toString(),
        ...field,
        isActive: true,
        isDefault: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        syncStatus: isGuestMode ? 'local' : (shouldUseBackend ? 'pending' : 'local')
      };

      // Check if this should be the default field (first field for user)
      const currentFields = this.localData.fields || [];
      if (currentFields.length === 0) {
        newField.isDefault = true;
      }

      // GUEST MODE: Only save locally, no backend requests
      if (isGuestMode) {
        console.log('DataManager.addField - Guest mode: Saving only locally, no backend requests');
      }
      // AUTHENTICATED MODE: Try backend first
      else if (shouldUseBackend) {
        console.log('DataManager.addField - Authenticated mode: Attempting to save to backend...');
        try {
          const backendField = await this.apiClient.createField(newField);
          if (backendField) {
            newField._id = backendField._id;
            newField.syncStatus = 'synced';
            console.log('DataManager.addField - Backend save successful');
          }
        } catch (error) {
          console.warn('Failed to save field to backend, saving locally:', error);
          newField.syncStatus = 'pending';
        }
      }

      // Always update local data
      const updatedFields = [...currentFields, newField];
      this.localData.fields = updatedFields;
      await this.saveLocalData('fields', updatedFields);

      // Notify listeners
      this.notifyDataChange();

      return newField;
    } catch (error) {
      console.error('Error adding field:', error);
      throw error;
    }
  }

  // Update field
  async updateField(fieldId, updates) {
    try {
      const isGuestMode = this.isGuestMode();
      const shouldUseBackend = this.shouldUseBackend();
      const currentFields = this.localData.fields || [];
      const fieldIndex = currentFields.findIndex(f => f._id === fieldId);

      if (fieldIndex === -1) {
        throw new Error('Field not found');
      }

      const updatedField = {
        ...currentFields[fieldIndex],
        ...updates,
        updatedAt: new Date().toISOString(),
        syncStatus: isGuestMode ? 'local' : (shouldUseBackend ? 'pending' : 'local')
      };

      // GUEST MODE: Only update locally, no backend requests
      if (isGuestMode) {
        console.log('Guest mode: Updating field locally only, no backend requests');
      }
      // AUTHENTICATED MODE: Try backend first
      else if (shouldUseBackend) {
        try {
          const backendField = await this.apiClient.updateField(fieldId, updates);
          if (backendField) {
            updatedField.syncStatus = 'synced';
          }
        } catch (error) {
          console.warn('Failed to update field in backend, updating locally:', error);
          updatedField.syncStatus = 'pending';
        }
      }

      // Update local data
      const updatedFields = [...currentFields];
      updatedFields[fieldIndex] = updatedField;
      this.localData.fields = updatedFields;
      await this.saveLocalData('fields', updatedFields);

      // Notify listeners
      this.notifyDataChange();

      return updatedField;
    } catch (error) {
      console.error('Error updating field:', error);
      throw error;
    }
  }

  // Delete field
  async deleteField(fieldId) {
    try {
      const isGuestMode = this.isGuestMode();
      const shouldUseBackend = this.shouldUseBackend();
      const currentFields = this.localData.fields || [];
      const fieldToDelete = currentFields.find(f => f._id === fieldId);

      if (!fieldToDelete) {
        throw new Error('Field not found');
      }

      if (fieldToDelete.isDefault) {
        throw new Error('Cannot delete default field');
      }

      // GUEST MODE: Only delete locally, no backend requests
      if (isGuestMode) {
        console.log('Guest mode: Deleting field locally only, no backend requests');
      }
      // AUTHENTICATED MODE: Try backend first
      else if (shouldUseBackend) {
        try {
          await this.apiClient.deleteField(fieldId);
        } catch (error) {
          console.warn('Failed to delete field from backend:', error);
          // Continue with local deletion
        }
      }

      // Remove from local data
      const updatedFields = currentFields.filter(f => f._id !== fieldId);
      this.localData.fields = updatedFields;
      await this.saveLocalData('fields', updatedFields);

      // Notify listeners
      this.notifyDataChange();

      return true;
    } catch (error) {
      console.error('Error deleting field:', error);
      throw error;
    }
  }

  // Set default field
  async setDefaultField(fieldId) {
    try {
      const isAuth = this.isAuthenticated();
      const currentFields = this.localData.fields || [];

      // Update all fields - remove default from others, set for target
      const updatedFields = currentFields.map(field => ({
        ...field,
        isDefault: field._id === fieldId,
        updatedAt: new Date().toISOString(),
        syncStatus: isAuth ? 'pending' : 'local'
      }));

      if (isAuth) {
        // Try to update in backend
        try {
          await this.apiClient.setDefaultField(fieldId);
          // Mark all as synced if backend update successful
          updatedFields.forEach(field => {
            field.syncStatus = 'synced';
          });
        } catch (error) {
          console.warn('Failed to set default field in backend:', error);
        }
      }

      // Update local data
      this.localData.fields = updatedFields;
      await this.saveLocalData('fields', updatedFields);

      // Notify listeners
      this.notifyDataChange();

      return true;
    } catch (error) {
      console.error('Error setting default field:', error);
      throw error;
    }
  }

  // Get default field
  getDefaultField() {
    const fields = this.localData.fields || [];
    return fields.find(field => field.isDefault && field.isActive) || null;
  }

  // Ensure user has a default field (create if none exists)
  async ensureDefaultField() {
    try {
      const currentFields = this.localData.fields || [];
      const defaultField = currentFields.find(field => field.isDefault && field.isActive);

      if (!defaultField && currentFields.length === 0) {
        // Create default field for new users
        console.log('Creating default field for user...');
        const newDefaultField = await this.addField({
          name: 'Ana Tarla',
          size: { value: 1, unit: 'dekar' },
          soilType: 'other',
          irrigationType: 'none',
          notes: 'Otomatik oluşturulan varsayılan tarla'
        });
        return newDefaultField;
      } else if (!defaultField && currentFields.length > 0) {
        // Set first field as default if no default exists
        console.log('Setting first field as default...');
        const firstField = currentFields[0];
        await this.setDefaultField(firstField._id);
        return firstField;
      }

      return defaultField;
    } catch (error) {
      console.error('Error ensuring default field:', error);
      return null;
    }
  }

  // Get tracking mode for current user
  getTrackingMode() {
    // Check explicit preference first
    if (this.trackingModePreference !== null) {
      return this.trackingModePreference;
    }

    // Backward compatibility: if fields exist but no preference was ever set, assume detailed
    // This only applies when trackingModePreference is null (never been set)
    // If user explicitly set it to 'simple', respect that choice
    const fields = this.localData.fields || [];
    if (fields.length > 0 && this.trackingModePreference === null) {
      return 'detailed';
    }

    // Default to simple mode
    return 'simple';
  }

  // Set tracking mode preference
  async setTrackingMode(mode) {
    try {
      const key = this.getUserStorageKey('trackingMode');
      await AsyncStorage.setItem(key, JSON.stringify(mode));
      this.trackingModePreference = mode;
      console.log(`Tracking mode set to: ${mode}`);
    } catch (error) {
      console.error('Error setting tracking mode:', error);
      throw error;
    }
  }

  // ==================== SEASON MANAGEMENT METHODS ====================

  // Create default season
  async createDefaultSeason() {
    try {
      const currentYear = new Date().getFullYear();
      const defaultSeason = {
        id: this.generateLocalId(),
        name: `${currentYear} Sezonu`,
        description: 'Otomatik oluşturulan varsayılan sezon',
        startDate: new Date().toISOString(),
        endDate: null, // Open-ended
        isActive: true,
        isDefault: true,
        color: '#4CAF50',
        emoji: '🌱',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        syncStatus: 'local'
      };

      // SEASON INTEGRATION: Add to seasons list and save
      const currentSeasons = Array.isArray(this.localData.seasons) ? this.localData.seasons : [];
      const updatedSeasons = [...currentSeasons, defaultSeason];
      await this.saveLocalData('seasons', updatedSeasons);

      console.log('Default season created and saved:', defaultSeason.name);
      return defaultSeason;
    } catch (error) {
      console.error('Error creating default season:', error);
      throw error;
    }
  }

  // Get all seasons
  async getSeasons() {
    try {
      if (this.shouldUseBackend()) {
        // Authenticated mode: fetch from backend
        try {
          const response = await this.apiClient.makeRequest('/seasons', { method: 'GET' });
          if (response.success && response.data) {
            // Cache locally
            await this.saveLocalData('seasons', response.data);
            return response.data;
          }
        } catch (error) {
          console.warn('Backend fetch failed, using local data:', error);
          // Fall back to local data
        }
      }

      // Guest mode or fallback: use local data
      return this.localData.seasons || [];
    } catch (error) {
      console.error('Error getting seasons:', error);
      throw error;
    }
  }

  // Get active season
  async getActiveSeason() {
    try {
      const seasons = await this.getSeasons();
      const activeSeason = seasons.find(season => season.isActive);

      if (!activeSeason && seasons.length > 0) {
        // If no active season, make the first one active
        await this.setActiveSeason(seasons[0].id);
        return { ...seasons[0], isActive: true };
      }

      return activeSeason || null;
    } catch (error) {
      console.error('Error getting active season:', error);
      throw error;
    }
  }

  // Create new season
  async createSeason(seasonData) {
    try {
      if (this.shouldUseBackend()) {
        // Authenticated mode: create on backend
        try {
          const response = await this.apiClient.post('/seasons', seasonData);
          if (response.success && response.data) {
            // Update local cache
            const seasons = await this.getSeasons();
            const updatedSeasons = [...seasons, response.data];
            await this.saveLocalData('seasons', updatedSeasons);
            this.notifyDataChange('seasons');
            return response.data;
          }
        } catch (error) {
          console.warn('Backend create failed, creating locally:', error);
          // Fall through to local creation
        }
      }

      // Guest mode or fallback: create locally
      const newSeason = {
        id: this.generateLocalId(),
        ...seasonData,
        isActive: false, // Don't auto-activate
        isDefault: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        syncStatus: 'local'
      };

      const seasons = this.localData.seasons || [];
      const updatedSeasons = [...seasons, newSeason];

      await this.saveLocalData('seasons', updatedSeasons);

      // Add to sync queue if authenticated mode
      if (this.shouldUseBackend()) {
        this.addToSyncQueue('seasons', 'create', seasonData, newSeason.id);
      }

      this.notifyDataChange('seasons');

      console.log('Season created locally:', newSeason.name);
      return newSeason;
    } catch (error) {
      console.error('Error creating season:', error);
      throw error;
    }
  }

  // Update season
  async updateSeason(seasonId, updateData) {
    try {
      if (this.shouldUseBackend()) {
        // Authenticated mode: update on backend
        try {
          const response = await this.apiClient.put(`/seasons/${seasonId}`, updateData);
          if (response.success && response.data) {
            // Update local cache
            const seasons = await this.getSeasons();
            const updatedSeasons = seasons.map(season =>
              season.id === seasonId || season._id === seasonId
                ? { ...season, ...response.data, updatedAt: new Date().toISOString() }
                : season
            );
            await this.saveLocalData('seasons', updatedSeasons);
            this.notifyDataChange('seasons');
            return response.data;
          }
        } catch (error) {
          console.warn('Backend update failed, updating locally:', error);
          // Fall through to local update
        }
      }

      // Guest mode or fallback: update locally
      const seasons = this.localData.seasons || [];
      const updatedSeasons = seasons.map(season =>
        season.id === seasonId
          ? { ...season, ...updateData, updatedAt: new Date().toISOString(), syncStatus: 'local' }
          : season
      );

      await this.saveLocalData('seasons', updatedSeasons);

      // Add to sync queue if authenticated mode
      if (this.shouldUseBackend()) {
        this.addToSyncQueue('seasons', 'update', updateData, seasonId);
      }

      this.notifyDataChange('seasons');

      const updatedSeason = updatedSeasons.find(s => s.id === seasonId);
      console.log('Season updated locally:', updatedSeason?.name);
      return updatedSeason;
    } catch (error) {
      console.error('Error updating season:', error);
      throw error;
    }
  }

  // Set active season
  async setActiveSeason(seasonId) {
    try {
      if (this.shouldUseBackend()) {
        // Authenticated mode: activate on backend
        try {
          const response = await this.apiClient.patch(`/seasons/${seasonId}/activate`);
          if (response.success && response.data) {
            // Update local cache
            const seasons = await this.getSeasons();
            const updatedSeasons = seasons.map(season => ({
              ...season,
              isActive: (season.id === seasonId || season._id === seasonId),
              updatedAt: new Date().toISOString()
            }));
            await this.saveLocalData('seasons', updatedSeasons);
            this.notifyDataChange('seasons');
            return response.data.activeSeason;
          }
        } catch (error) {
          console.warn('Backend activation failed, activating locally:', error);
          // Fall through to local activation
        }
      }

      // Guest mode or fallback: activate locally
      const seasons = this.localData.seasons || [];
      const updatedSeasons = seasons.map(season => ({
        ...season,
        isActive: season.id === seasonId,
        updatedAt: new Date().toISOString(),
        syncStatus: 'local'
      }));

      await this.saveLocalData('seasons', updatedSeasons);
      this.notifyDataChange('seasons');

      const activatedSeason = updatedSeasons.find(s => s.id === seasonId);
      console.log('Season activated locally:', activatedSeason?.name);
      return activatedSeason;
    } catch (error) {
      console.error('Error setting active season:', error);
      throw error;
    }
  }

  // Delete season
  async deleteSeason(seasonId) {
    try {
      // Check if this is the only season
      const seasons = await this.getSeasons();
      if (seasons.length <= 1) {
        throw new Error('En az bir sezonunuz olmalıdır. Bu sezon silinemez.');
      }

      // Check if season has expenses
      const seasonExpenses = this.localData.expenses.filter(expense =>
        expense.seasonId === seasonId
      );

      if (seasonExpenses.length > 0) {
        throw new Error(`Bu sezona ait ${seasonExpenses.length} gider kaydı bulunmaktadır. Önce giderleri silin veya başka bir sezona taşıyın.`);
      }

      if (this.shouldUseBackend()) {
        // Authenticated mode: delete on backend
        try {
          const response = await this.apiClient.delete(`/seasons/${seasonId}`);
          if (response.success) {
            // Update local cache
            const updatedSeasons = seasons.filter(season =>
              season.id !== seasonId && season._id !== seasonId
            );
            await this.saveLocalData('seasons', updatedSeasons);
            this.notifyDataChange('seasons');
            return true;
          }
        } catch (error) {
          console.warn('Backend delete failed, deleting locally:', error);
          // Fall through to local deletion
        }
      }

      // Guest mode or fallback: delete locally
      const seasonToDelete = seasons.find(s => s.id === seasonId);
      const updatedSeasons = seasons.filter(season => season.id !== seasonId);

      // If deleted season was active, activate another one
      if (seasonToDelete?.isActive && updatedSeasons.length > 0) {
        updatedSeasons[0].isActive = true;
        updatedSeasons[0].updatedAt = new Date().toISOString();
      }

      await this.saveLocalData('seasons', updatedSeasons);
      this.notifyDataChange('seasons');

      console.log('Season deleted locally:', seasonToDelete?.name);
      return true;
    } catch (error) {
      console.error('Error deleting season:', error);
      throw error;
    }
  }

  // Get season by ID
  async getSeasonById(seasonId) {
    try {
      const seasons = await this.getSeasons();
      return seasons.find(season => season.id === seasonId || season._id === seasonId) || null;
    } catch (error) {
      console.error('Error getting season by ID:', error);
      throw error;
    }
  }

  // Migrate existing user to season system
  async migrateExistingUser() {
    try {
      console.log('Starting existing user migration to season system...');

      // Check if migration already completed
      const migrationStatus = await AsyncStorage.getItem(this.getUserStorageKey('season_migration_completed'));
      if (migrationStatus === 'true') {
        console.log('Season migration already completed for this user');
        return { success: true, message: 'Migration already completed' };
      }

      // Check if user already has seasons
      const existingSeasons = await this.getSeasons();
      if (existingSeasons && existingSeasons.length > 0) {
        console.log('User already has seasons, marking migration as completed');
        await AsyncStorage.setItem(this.getUserStorageKey('season_migration_completed'), 'true');
        return { success: true, message: 'User already has seasons' };
      }

      // Create default season for existing user
      const defaultSeason = await this.createDefaultSeason();
      console.log('Default season created for existing user:', defaultSeason.name);

      // Get all existing expenses
      const allExpenses = await this.getExpenses();
      console.log(`Found ${allExpenses.length} existing expenses to migrate`);

      if (allExpenses.length > 0) {
        // Update all expenses to use the new default season
        const updatedExpenses = allExpenses.map(expense => ({
          ...expense,
          seasonId: defaultSeason.id,
          legacySeasonId: expense.seasonId || null, // Store old season ID if exists
          updatedAt: new Date().toISOString(),
          syncStatus: 'local'
        }));

        // Save updated expenses
        await this.saveLocalData('expenses', updatedExpenses);
        console.log(`Migrated ${updatedExpenses.length} expenses to new season system`);
      }

      // Update seasons list with the new default season
      await this.saveLocalData('seasons', [defaultSeason]);

      // Mark migration as completed
      await AsyncStorage.setItem(this.getUserStorageKey('season_migration_completed'), 'true');

      this.notifyDataChange('seasons');
      this.notifyDataChange('expenses');

      console.log('Existing user migration completed successfully');
      return {
        success: true,
        message: 'Kullanıcı sezon sistemine başarıyla geçirildi',
        results: {
          seasonsCreated: 1,
          expensesMigrated: allExpenses.length,
          defaultSeason: defaultSeason
        }
      };
    } catch (error) {
      console.error('Existing user migration failed:', error);
      return {
        success: false,
        error: error.message,
        message: 'Kullanıcı geçişi başarısız oldu'
      };
    }
  }

  // Check if user needs season migration
  async needsSeasonMigration() {
    try {
      // Check migration status
      const migrationStatus = await AsyncStorage.getItem(this.getUserStorageKey('season_migration_completed'));
      if (migrationStatus === 'true') {
        return false;
      }

      // Check if user has seasons
      const seasons = await this.getSeasons();
      if (seasons && seasons.length > 0) {
        // User has seasons but migration not marked as completed
        await AsyncStorage.setItem(this.getUserStorageKey('season_migration_completed'), 'true');
        return false;
      }

      // Check if user has expenses (indicating they're an existing user)
      const expenses = await this.getExpenses();
      return expenses && expenses.length > 0;
    } catch (error) {
      console.error('Error checking migration status:', error);
      return false;
    }
  }

  // Sync seasons with backend
  async syncSeasons() {
    try {
      if (!this.shouldUseBackend()) {
        console.log('Guest mode: Skipping season sync');
        return { success: true, message: 'Guest mode - no sync needed' };
      }

      console.log('Starting season sync...');

      // 1. Fetch latest data from backend
      let backendSeasons = [];
      try {
        const response = await this.apiClient.makeRequest('/seasons', { method: 'GET' });
        if (response.success && response.data) {
          backendSeasons = response.data;
        }
      } catch (error) {
        console.warn('Failed to fetch backend seasons:', error);
        return { success: false, error: 'Network error during sync' };
      }

      // 2. Get local seasons
      const localSeasons = this.localData.seasons || [];

      // 3. Process sync queue (pending local changes)
      const syncResults = {
        created: 0,
        updated: 0,
        conflicts: 0,
        errors: []
      };

      for (const queueItem of this.syncQueue.seasons) {
        try {
          if (queueItem.operation === 'create') {
            const response = await this.apiClient.post('/seasons', queueItem.data);
            if (response.success) {
              // Replace local ID with backend ID
              const localIndex = localSeasons.findIndex(s => s.id === queueItem.localId);
              if (localIndex >= 0) {
                localSeasons[localIndex] = {
                  ...response.data,
                  syncStatus: 'synced'
                };
              }
              syncResults.created++;
            }
          } else if (queueItem.operation === 'update') {
            const response = await this.apiClient.put(`/seasons/${queueItem.id}`, queueItem.data);
            if (response.success) {
              const localIndex = localSeasons.findIndex(s => s.id === queueItem.id || s._id === queueItem.id);
              if (localIndex >= 0) {
                localSeasons[localIndex] = {
                  ...response.data,
                  syncStatus: 'synced'
                };
              }
              syncResults.updated++;
            }
          } else if (queueItem.operation === 'delete') {
            const response = await this.apiClient.delete(`/seasons/${queueItem.id}`);
            if (response.success) {
              const localIndex = localSeasons.findIndex(s => s.id === queueItem.id || s._id === queueItem.id);
              if (localIndex >= 0) {
                localSeasons.splice(localIndex, 1);
              }
            }
          }
        } catch (error) {
          console.error('Sync queue item failed:', error);
          syncResults.errors.push({
            operation: queueItem.operation,
            error: error.message
          });
        }
      }

      // 4. Clear successful sync queue items
      this.syncQueue.seasons = this.syncQueue.seasons.filter(item =>
        syncResults.errors.some(error => error.operation === item.operation)
      );

      // 5. Merge backend data with local data
      const mergedSeasons = this.mergeSeasonData(localSeasons, backendSeasons);

      // 6. Save merged data locally
      await this.saveLocalData('seasons', mergedSeasons);
      this.notifyDataChange('seasons');

      console.log('Season sync completed:', syncResults);
      return {
        success: true,
        results: syncResults,
        message: 'Sezonlar başarıyla senkronize edildi'
      };
    } catch (error) {
      console.error('Season sync failed:', error);
      return {
        success: false,
        error: error.message,
        message: 'Senkronizasyon başarısız oldu'
      };
    }
  }

  // Merge local and backend season data
  mergeSeasonData(localSeasons, backendSeasons) {
    try {
      const merged = [...backendSeasons];

      // Add local-only seasons (not yet synced)
      localSeasons.forEach(localSeason => {
        if (localSeason.syncStatus === 'local' || localSeason.id?.startsWith('local_')) {
          // Check if this local season conflicts with backend data
          const conflict = backendSeasons.find(backendSeason =>
            backendSeason.name === localSeason.name
          );

          if (conflict) {
            // Mark as conflict for user resolution
            localSeason.syncStatus = 'conflict';
            localSeason.conflictWith = conflict._id;
          }

          merged.push(localSeason);
        }
      });

      // Mark all backend seasons as synced
      merged.forEach(season => {
        if (!season.syncStatus || season.syncStatus === 'pending') {
          season.syncStatus = 'synced';
        }
      });

      return merged;
    } catch (error) {
      console.error('Error merging season data:', error);
      return backendSeasons; // Fallback to backend data
    }
  }

  // Add item to sync queue
  addToSyncQueue(type, operation, data, localId = null) {
    try {
      if (!this.syncQueue[type]) {
        this.syncQueue[type] = [];
      }

      const queueItem = {
        id: Date.now().toString(),
        operation, // 'create', 'update', 'delete'
        data,
        localId,
        timestamp: new Date().toISOString(),
        retryCount: 0
      };

      this.syncQueue[type].push(queueItem);
      console.log(`Added to sync queue [${type}]:`, operation);
    } catch (error) {
      console.error('Error adding to sync queue:', error);
    }
  }

  // Retry failed sync operations
  async retrySyncQueue() {
    try {
      if (!this.shouldUseBackend()) {
        return { success: true, message: 'Guest mode - no retry needed' };
      }

      const maxRetries = 3;
      let retryResults = {
        seasons: { success: 0, failed: 0 }
      };

      // Retry season sync queue
      for (const queueItem of this.syncQueue.seasons) {
        if (queueItem.retryCount >= maxRetries) {
          console.warn('Max retries reached for sync item:', queueItem.id);
          continue;
        }

        try {
          queueItem.retryCount++;

          if (queueItem.operation === 'create') {
            const response = await this.apiClient.post('/seasons', queueItem.data);
            if (response.success) {
              retryResults.seasons.success++;
              // Remove from queue
              this.syncQueue.seasons = this.syncQueue.seasons.filter(item => item.id !== queueItem.id);
            }
          }
          // Add other operations as needed
        } catch (error) {
          console.error('Retry failed for sync item:', queueItem.id, error);
          retryResults.seasons.failed++;
        }
      }

      console.log('Sync retry completed:', retryResults);
      return { success: true, results: retryResults };
    } catch (error) {
      console.error('Sync retry failed:', error);
      return { success: false, error: error.message };
    }
  }

  // Activate detailed mode for guest users
  async activateDetailedModeForGuest() {
    try {
      if (!AuthService.isInGuestMode()) {
        throw new Error('This method is only for guest users');
      }

      // Ensure default field exists
      const defaultField = await this.ensureDefaultField();

      if (!defaultField) {
        throw new Error('Failed to create default field');
      }

      console.log('Detailed mode activated for guest user');
      return {
        success: true,
        defaultField,
        message: 'Detaylı mod başarıyla aktifleştirildi'
      };
    } catch (error) {
      console.error('Error activating detailed mode for guest:', error);
      throw error;
    }
  }

  /**
   * Manual rollback migration for users
   * @param {string} backupId - Backup ID to rollback to
   * @param {Object} options - Rollback options
   */
  async rollbackMigration(backupId, options = {}) {
    try {
      console.log('🔄 DataManager: Manual migration rollback requested');

      // Use the backup service for rollback
      const rollbackResult = await this.migrationBackup.manualRollback(backupId, options);

      // Reload local data after rollback
      await this.loadLocalData();

      // Notify listeners about data change
      this.notifyDataChange();

      console.log('✅ DataManager: Migration rollback completed successfully');
      return rollbackResult;

    } catch (error) {
      console.error('❌ DataManager: Migration rollback failed:', error);
      throw error;
    }
  }

  /**
   * Check if rollback is available
   */
  async isRollbackAvailable() {
    try {
      const metadata = await this.migrationBackup.getMetadata();
      const integrity = await this.migrationBackup.checkIntegrity();

      return {
        available: !!(metadata && integrity.hasBackup && integrity.isValid),
        backupId: metadata?.backupId,
        timestamp: metadata?.timestamp,
        integrity: integrity
      };
    } catch (error) {
      console.error('Error checking rollback availability:', error);
      return { available: false, error: error.message };
    }
  }

  /**
   * Get rollback status and options for user
   */
  async getRollbackStatus() {
    try {
      const availability = await this.isRollbackAvailable();

      if (!availability.available) {
        return {
          canRollback: false,
          message: 'Geri yükleme için backup bulunamadı',
          reason: availability.error || 'No backup available'
        };
      }

      const metadata = await this.migrationBackup.getMetadata();
      const backupDate = new Date(metadata.timestamp);
      const timeDiff = Date.now() - backupDate.getTime();
      const hoursDiff = Math.floor(timeDiff / (1000 * 60 * 60));

      return {
        canRollback: true,
        backupId: availability.backupId,
        backupDate: backupDate.toLocaleString('tr-TR'),
        hoursAgo: hoursDiff,
        message: `${hoursDiff} saat önce oluşturulan backup'tan geri yükleme yapılabilir`,
        itemCounts: metadata.itemCounts
      };

    } catch (error) {
      console.error('Error getting rollback status:', error);
      return {
        canRollback: false,
        message: 'Geri yükleme durumu kontrol edilemedi',
        error: error.message
      };
    }
  }

  /**
   * Handle invalid field data during migration
   * @param {Object} field - Invalid field data
   * @param {Array} errors - Validation errors
   * @returns {Object} Handling result
   */
  handleInvalidField(field, errors) {
    const strategy = {
      action: 'skip', // 'skip', 'fix', 'prompt'
      reason: '',
      fixedField: null
    };

    // Critical errors - always skip
    const criticalErrors = errors.filter(error =>
      error.includes('zorunludur') ||
      error.includes('boş olamaz')
    );

    if (criticalErrors.length > 0) {
      strategy.action = 'skip';
      strategy.reason = `Kritik hatalar: ${criticalErrors.join(', ')}`;
      return strategy;
    }

    // Non-critical errors - try to fix
    const fixableErrors = errors.filter(error =>
      error.includes('negatif olamaz') ||
      error.includes('çok uzun') ||
      error.includes('geçersiz')
    );

    if (fixableErrors.length > 0) {
      strategy.action = 'fix';
      strategy.reason = `Düzeltilebilir hatalar: ${fixableErrors.join(', ')}`;
      strategy.fixedField = this.attemptFieldFix(field, fixableErrors);
      return strategy;
    }

    // No errors (shouldn't happen in this context)
    strategy.action = 'skip';
    strategy.reason = 'Bilinmeyen hata';
    return strategy;
  }

  /**
   * Attempt to fix field data
   * @param {Object} field - Field data to fix
   * @param {Array} errors - Errors to fix
   * @returns {Object} Fixed field data
   */
  attemptFieldFix(field, errors) {
    const fixed = { ...field };

    errors.forEach(error => {
      if (error.includes('negatif olamaz')) {
        if (fixed.size && fixed.size.value < 0) {
          fixed.size.value = Math.abs(fixed.size.value);
        }
      }

      if (error.includes('çok uzun')) {
        if (error.includes('Tarla adı') && fixed.name) {
          fixed.name = fixed.name.substring(0, 50);
        }
        if (error.includes('Notlar') && fixed.notes) {
          fixed.notes = fixed.notes.substring(0, 200);
        }
        if (error.includes('Adres') && fixed.location?.address) {
          fixed.location.address = fixed.location.address.substring(0, 200);
        }
      }

      if (error.includes('geçersiz')) {
        if (error.includes('alan birimi')) {
          fixed.size = fixed.size || {};
          fixed.size.unit = 'dekar';
        }
        if (error.includes('toprak türü')) {
          fixed.soilType = 'other';
        }
        if (error.includes('sulama türü')) {
          fixed.irrigationType = 'none';
        }
      }
    });

    return fixed;
  }

  /**
   * Get comprehensive validation status for UI
   * @returns {Object} Validation status for user interface
   */
  async getValidationStatus() {
    try {
      const validationResult = await ValidationService.validateAllGuestData();
      const report = ValidationService.generateValidationReport(validationResult);

      return {
        isReady: validationResult.isValid,
        report,
        summary: validationResult.summary,
        canProceed: validationResult.isValid,
        requiresAction: !validationResult.isValid,
        actionMessage: validationResult.isValid
          ? 'Verileriniz migration için hazır'
          : 'Migration öncesi hataları düzeltmeniz gerekiyor'
      };

    } catch (error) {
      console.error('Error getting validation status:', error);
      return {
        isReady: false,
        report: { title: 'Validation Error', summary: 'Doğrulama sırasında hata oluştu' },
        summary: {},
        canProceed: false,
        requiresAction: true,
        actionMessage: 'Doğrulama sistemi hatası'
      };
    }
  }
}

// Create singleton instance
export const DataManager = new DataManagerService();
