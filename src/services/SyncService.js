/**
 * Offline Data Synchronization Service for Çiftçi Not Defterim
 * Handles sync between local SQLite and cloud storage
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-community/netinfo';
import auth from '@react-native-firebase/auth';
import { Platform } from 'react-native';
import Logger, { LogCategory } from '../utils/Logger';
import Analytics, { AnalyticsEvent } from '../utils/Analytics';
import AuthService from './AuthService';

// Sync status
export const SyncStatus = {
  IDLE: 'idle',
  SYNCING: 'syncing',
  SUCCESS: 'success',
  ERROR: 'error',
  CONFLICT: 'conflict',
};

// Sync operation types
export const SyncOperation = {
  CREATE: 'create',
  UPDATE: 'update',
  DELETE: 'delete',
};

class SyncService {
  constructor() {
    this.isOnline = false;
    this.syncInProgress = false;
    this.syncQueue = [];
    this.conflictQueue = [];
    this.lastSyncTime = null;
    this.syncInterval = 5 * 60 * 1000; // 5 minutes
    this.maxRetries = 3;
    this.syncListeners = [];

    this.setupNetworkListener();
    this.setupPeriodicSync();
  }

  // Check if user is in guest mode
  isGuestMode() {
    return AuthService.isInGuestMode();
  }

  // Check if sync should be enabled (not guest mode)
  shouldSync() {
    return !this.isGuestMode();
  }

  // Initialize sync service
  async initialize() {
    try {
      // GUEST MODE: Skip sync initialization
      if (this.isGuestMode()) {
        Logger.info(LogCategory.DATABASE, 'Guest mode: SyncService initialization skipped');
        return;
      }

      // Check network status
      const netInfo = await NetInfo.fetch();
      this.isOnline = netInfo.isConnected;

      // Load sync queue from storage
      await this.loadSyncQueue();

      // Load last sync time
      this.lastSyncTime = await AsyncStorage.getItem('last_sync_time');

      Logger.info(LogCategory.DATABASE, 'SyncService initialized', {
        isOnline: this.isOnline,
        queueSize: this.syncQueue.length,
        lastSyncTime: this.lastSyncTime,
      });

      // Start initial sync if online and should sync
      if (this.isOnline && this.syncQueue.length > 0 && this.shouldSync()) {
        this.startSync();
      }
    } catch (error) {
      Logger.error(LogCategory.DATABASE, 'SyncService initialization failed', error);
    }
  }

  // Setup network status listener
  setupNetworkListener() {
    NetInfo.addEventListener(state => {
      const wasOnline = this.isOnline;
      this.isOnline = state.isConnected;
      
      Logger.info(LogCategory.DATABASE, 'Network status changed', {
        isConnected: this.isOnline,
        type: state.type,
      });

      // Start sync when coming back online
      if (!wasOnline && this.isOnline && this.syncQueue.length > 0) {
        this.startSync();
      }

      this.notifyListeners({
        type: 'network_status_changed',
        isOnline: this.isOnline,
      });
    });
  }

  // Setup periodic sync
  setupPeriodicSync() {
    setInterval(() => {
      // GUEST MODE: Skip periodic sync
      if (this.isGuestMode()) {
        return;
      }

      if (this.isOnline && !this.syncInProgress && this.shouldSync()) {
        this.startSync();
      }
    }, this.syncInterval);
  }

  // Add sync operation to queue
  async addToSyncQueue(operation) {
    try {
      // GUEST MODE: Skip sync queue completely
      if (this.isGuestMode()) {
        Logger.debug(LogCategory.DATABASE, 'Guest mode: Skipping sync queue addition');
        return null;
      }

      const syncItem = {
        id: `sync_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: Date.now(),
        operation: operation.type,
        table: operation.table,
        data: operation.data,
        localId: operation.localId,
        retries: 0,
        status: 'pending',
      };

      this.syncQueue.push(syncItem);
      await this.saveSyncQueue();

      Logger.debug(LogCategory.DATABASE, 'Added to sync queue', syncItem);

      // Start sync if online and should sync
      if (this.isOnline && !this.syncInProgress && this.shouldSync()) {
        this.startSync();
      }

      return syncItem.id;
    } catch (error) {
      Logger.error(LogCategory.DATABASE, 'Failed to add to sync queue', error);
      throw error;
    }
  }

  // Start synchronization process
  async startSync() {
    // GUEST MODE: Skip sync completely
    if (this.isGuestMode()) {
      Logger.debug(LogCategory.DATABASE, 'Guest mode: Skipping sync process');
      return;
    }

    if (this.syncInProgress || !this.isOnline || this.syncQueue.length === 0 || !this.shouldSync()) {
      return;
    }

    try {
      this.syncInProgress = true;
      this.syncStartTime = Date.now();
      this.notifyListeners({ type: 'sync_started' });

      Logger.info(LogCategory.DATABASE, 'Starting sync', {
        queueSize: this.syncQueue.length,
      });

      Analytics.trackEvent(AnalyticsEvent.SYNC_STARTED, {
        queueSize: this.syncQueue.length,
      });

      const results = {
        success: 0,
        failed: 0,
        conflicts: 0,
      };

      // Process sync queue
      for (let i = 0; i < this.syncQueue.length; i++) {
        const item = this.syncQueue[i];
        
        if (item.status === 'completed') {
          continue;
        }

        try {
          const result = await this.processSyncItem(item);
          
          if (result.success) {
            item.status = 'completed';
            results.success++;
          } else if (result.conflict) {
            item.status = 'conflict';
            this.conflictQueue.push(item);
            results.conflicts++;
          } else {
            item.retries++;
            if (item.retries >= this.maxRetries) {
              item.status = 'failed';
              results.failed++;
            }
          }
        } catch (error) {
          Logger.error(LogCategory.DATABASE, 'Sync item processing failed', error, item);
          item.retries++;
          if (item.retries >= this.maxRetries) {
            item.status = 'failed';
            results.failed++;
          }
        }
      }

      // Remove completed items from queue
      this.syncQueue = this.syncQueue.filter(item => item.status !== 'completed');
      await this.saveSyncQueue();

      // Update last sync time
      this.lastSyncTime = new Date().toISOString();
      await AsyncStorage.setItem('last_sync_time', this.lastSyncTime);

      Logger.info(LogCategory.DATABASE, 'Sync completed', results);

      Analytics.trackEvent(AnalyticsEvent.SYNC_COMPLETED, {
        ...results,
        duration: Date.now() - this.syncStartTime,
      });

      this.notifyListeners({
        type: 'sync_completed',
        results,
        hasConflicts: results.conflicts > 0,
      });

    } catch (error) {
      Logger.error(LogCategory.DATABASE, 'Sync failed', error);
      
      Analytics.trackEvent(AnalyticsEvent.SYNC_FAILED, {
        error: error.message,
      });

      this.notifyListeners({
        type: 'sync_failed',
        error: error.message,
      });
    } finally {
      this.syncInProgress = false;
    }
  }

  // Process individual sync item
  async processSyncItem(item) {
    try {
      switch (item.operation) {
        case SyncOperation.CREATE:
          return await this.syncCreate(item);
        case SyncOperation.UPDATE:
          return await this.syncUpdate(item);
        case SyncOperation.DELETE:
          return await this.syncDelete(item);
        default:
          throw new Error(`Unknown sync operation: ${item.operation}`);
      }
    } catch (error) {
      Logger.error(LogCategory.DATABASE, 'Sync item processing error', error, item);
      return { success: false, error: error.message };
    }
  }

  // Sync create operation
  async syncCreate(item) {
    try {
      // Send to cloud storage
      const response = await this.sendToCloud('POST', `/${item.table}`, item.data);
      
      if (response.success) {
        // Update local record with cloud ID
        await this.updateLocalRecord(item.table, item.localId, {
          cloudId: response.data.id,
          lastSynced: new Date().toISOString(),
        });
        
        return { success: true };
      } else if (response.conflict) {
        return { success: false, conflict: true, data: response.data };
      } else {
        return { success: false, error: response.error };
      }
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  // Sync update operation
  async syncUpdate(item) {
    try {
      const cloudId = item.data.cloudId || item.localId;
      const response = await this.sendToCloud('PUT', `/${item.table}/${cloudId}`, item.data);
      
      if (response.success) {
        await this.updateLocalRecord(item.table, item.localId, {
          lastSynced: new Date().toISOString(),
        });
        
        return { success: true };
      } else if (response.conflict) {
        return { success: false, conflict: true, data: response.data };
      } else {
        return { success: false, error: response.error };
      }
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  // Sync delete operation
  async syncDelete(item) {
    try {
      const cloudId = item.data.cloudId || item.localId;
      const response = await this.sendToCloud('DELETE', `/${item.table}/${cloudId}`);
      
      if (response.success) {
        return { success: true };
      } else if (response.notFound) {
        // Already deleted on cloud, consider success
        return { success: true };
      } else {
        return { success: false, error: response.error };
      }
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  // Send data to cloud storage
  async sendToCloud(method, endpoint, data = null) {
    try {
      // Android emülatörde localhost yerine ******** kullanılmalı
      const baseURL = Platform.OS === 'android' && __DEV__
        ? 'http://********:3000/api/v1'
        : 'http://localhost:3000/api/v1'; // Backend API URL

      Logger.debug(LogCategory.DATABASE, 'Sending to cloud', {
        method,
        endpoint,
        data,
      });

      // Get Firebase token for authentication
      const firebaseToken = await this.getFirebaseToken();

      const response = await fetch(`${baseURL}${endpoint}`, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${firebaseToken}`,
        },
        body: data ? JSON.stringify(data) : null,
      });

      const result = await response.json();

      if (response.ok) {
        return {
          success: true,
          data: result.data || result,
        };
      } else {
        throw new Error(result.error?.message || 'API request failed');
      }
    } catch (error) {
      Logger.error(LogCategory.DATABASE, 'Cloud API error', error);
      return { success: false, error: error.message };
    }
  }

  // Get Firebase authentication token
  async getFirebaseToken() {
    try {
      const currentUser = auth().currentUser;
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      const token = await currentUser.getIdToken();
      return token;
    } catch (error) {
      Logger.error(LogCategory.DATABASE, 'Failed to get Firebase token', error);
      throw error;
    }
  }

  // Update local record
  async updateLocalRecord(table, localId, updates) {
    try {
      // This would update the local SQLite record
      Logger.debug(LogCategory.DATABASE, 'Updating local record', {
        table,
        localId,
        updates,
      });
    } catch (error) {
      Logger.error(LogCategory.DATABASE, 'Local record update failed', error);
      throw error;
    }
  }

  // Pull changes from cloud
  async pullFromCloud() {
    try {
      if (!this.isOnline) {
        throw new Error('No internet connection');
      }

      Logger.info(LogCategory.DATABASE, 'Pulling changes from cloud');

      // Get last sync timestamp
      const lastSync = this.lastSyncTime || '1970-01-01T00:00:00.000Z';
      
      // Fetch changes from cloud
      const response = await this.sendToCloud('GET', `/sync/changes?since=${lastSync}`);
      
      if (response.success) {
        const changes = response.data.changes || [];
        
        for (const change of changes) {
          await this.applyCloudChange(change);
        }

        Logger.info(LogCategory.DATABASE, 'Cloud changes applied', {
          changeCount: changes.length,
        });

        return { success: true, changeCount: changes.length };
      } else {
        throw new Error(response.error);
      }
    } catch (error) {
      Logger.error(LogCategory.DATABASE, 'Pull from cloud failed', error);
      throw error;
    }
  }

  // Apply cloud change to local database
  async applyCloudChange(change) {
    try {
      // This would apply the change to local SQLite database
      Logger.debug(LogCategory.DATABASE, 'Applying cloud change', change);
    } catch (error) {
      Logger.error(LogCategory.DATABASE, 'Failed to apply cloud change', error, change);
      throw error;
    }
  }

  // Save sync queue to storage
  async saveSyncQueue() {
    try {
      await AsyncStorage.setItem('sync_queue', JSON.stringify(this.syncQueue));
    } catch (error) {
      Logger.error(LogCategory.DATABASE, 'Failed to save sync queue', error);
    }
  }

  // Load sync queue from storage
  async loadSyncQueue() {
    try {
      const queueData = await AsyncStorage.getItem('sync_queue');
      this.syncQueue = queueData ? JSON.parse(queueData) : [];
    } catch (error) {
      Logger.error(LogCategory.DATABASE, 'Failed to load sync queue', error);
      this.syncQueue = [];
    }
  }

  // Add sync listener
  addSyncListener(listener) {
    this.syncListeners.push(listener);
    
    return () => {
      const index = this.syncListeners.indexOf(listener);
      if (index > -1) {
        this.syncListeners.splice(index, 1);
      }
    };
  }

  // Notify sync listeners
  notifyListeners(event) {
    this.syncListeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        Logger.error(LogCategory.DATABASE, 'Sync listener error', error);
      }
    });
  }

  // Get sync status
  getSyncStatus() {
    return {
      isOnline: this.isOnline,
      syncInProgress: this.syncInProgress,
      queueSize: this.syncQueue.length,
      conflictCount: this.conflictQueue.length,
      lastSyncTime: this.lastSyncTime,
    };
  }

  // Force sync
  async forceSync() {
    // GUEST MODE: Skip force sync
    if (this.isGuestMode()) {
      Logger.debug(LogCategory.DATABASE, 'Guest mode: Skipping force sync');
      return;
    }

    if (this.syncInProgress) {
      throw new Error('Sync already in progress');
    }

    if (!this.isOnline) {
      throw new Error('No internet connection');
    }

    if (!this.shouldSync()) {
      throw new Error('Sync not available in current mode');
    }

    await this.startSync();
  }

  // Clear sync queue
  async clearSyncQueue() {
    this.syncQueue = [];
    this.conflictQueue = [];
    await this.saveSyncQueue();
    await AsyncStorage.removeItem('sync_conflicts');
    
    Logger.info(LogCategory.DATABASE, 'Sync queue cleared');
  }

  // Get conflicts
  getConflicts() {
    return [...this.conflictQueue];
  }

  // Resolve conflict
  async resolveConflict(conflictId, resolution) {
    try {
      const conflictIndex = this.conflictQueue.findIndex(c => c.id === conflictId);
      
      if (conflictIndex === -1) {
        throw new Error('Conflict not found');
      }

      const conflict = this.conflictQueue[conflictIndex];
      
      // Apply resolution
      if (resolution === 'use_local') {
        // Re-add to sync queue with force flag
        conflict.force = true;
        this.syncQueue.push(conflict);
      } else if (resolution === 'use_cloud') {
        // Apply cloud data locally
        await this.applyCloudChange(conflict.cloudData);
      }

      // Remove from conflict queue
      this.conflictQueue.splice(conflictIndex, 1);
      
      await this.saveSyncQueue();
      
      Logger.info(LogCategory.DATABASE, 'Conflict resolved', {
        conflictId,
        resolution,
      });

      return { success: true };
    } catch (error) {
      Logger.error(LogCategory.DATABASE, 'Failed to resolve conflict', error);
      throw error;
    }
  }
}

export default new SyncService();
