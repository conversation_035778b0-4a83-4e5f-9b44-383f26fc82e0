/**
 * Expense Validation Service for Çiftçi Not Defterim
 * Provides comprehensive expense data validation matching backend rules
 */

class ExpenseValidationService {
  constructor() {
    // Backend validation rules (from backend/src/models/Expense.js)
    this.validationRules = {
      amount: {
        required: true,
        min: 0.01, // Must be positive
        max: 1000000, // 1M TL max
        type: 'number'
      },
      date: {
        required: true,
        type: 'date',
        // Date range: 1 year ago to 1 year from now
        minYearsAgo: 1,
        maxYearsFromNow: 1
      },
      categoryId: {
        required: true,
        type: 'objectId'
      },
      seasonId: {
        required: true,
        type: 'objectId'
      },
      description: {
        required: false,
        maxLength: 500,
        type: 'string'
      },
      notes: {
        required: false,
        maxLength: 1000,
        type: 'string'
      },
      tags: {
        required: false,
        type: 'array',
        maxItems: 10,
        itemMaxLength: 30
      },
      currency: {
        required: false,
        enum: ['TRY', 'USD', 'EUR'],
        default: 'TRY'
      }
    };

    // Turkish error messages
    this.errorMessages = {
      amount: {
        required: '<PERSON>tar zorunludur',
        min: 'Tutar 0\'dan büyük olmalıdır',
        max: 'Tutar en fazla 1.000.000 TL olabilir',
        type: 'Tutar sayı olmalıdır'
      },
      date: {
        required: 'Tarih zorunludur',
        type: 'Geçerli bir tarih giriniz',
        range: 'Tarih son 1 yıl ile gelecek 1 yıl arasında olmalıdır'
      },
      categoryId: {
        required: 'Kategori seçimi zorunludur',
        type: 'Geçersiz kategori ID\'si'
      },
      seasonId: {
        required: 'Sezon seçimi zorunludur',
        type: 'Geçersiz sezon ID\'si'
      },
      description: {
        maxLength: 'Açıklama en fazla 500 karakter olabilir'
      },
      notes: {
        maxLength: 'Notlar en fazla 1000 karakter olabilir'
      },
      tags: {
        maxItems: 'En fazla 10 etiket ekleyebilirsiniz',
        itemMaxLength: 'Etiket en fazla 30 karakter olabilir'
      },
      currency: {
        enum: 'Desteklenen para birimi: TRY, USD, EUR'
      }
    };
  }

  /**
   * Validate a single expense
   * @param {Object} expense - Expense data to validate
   * @returns {Object} Validation result
   */
  validateExpense(expense) {
    const errors = [];
    const warnings = [];

    try {
      // Amount validation (required)
      if (expense.amount === undefined || expense.amount === null || expense.amount === '') {
        errors.push(this.errorMessages.amount.required);
      } else {
        const amount = parseFloat(expense.amount);
        if (isNaN(amount)) {
          errors.push(this.errorMessages.amount.type);
        } else if (amount <= 0) {
          errors.push(this.errorMessages.amount.min);
        } else if (amount > this.validationRules.amount.max) {
          errors.push(this.errorMessages.amount.max);
        }
      }

      // Date validation (required)
      if (!expense.date) {
        errors.push(this.errorMessages.date.required);
      } else {
        const dateValidation = this.validateDate(expense.date);
        if (!dateValidation.isValid) {
          errors.push(dateValidation.error);
        }
      }

      // Category ID validation (required)
      if (!expense.categoryId) {
        errors.push(this.errorMessages.categoryId.required);
      } else if (!this.isValidObjectId(expense.categoryId)) {
        errors.push(this.errorMessages.categoryId.type);
      }

      // Season ID validation (required)
      if (!expense.seasonId) {
        errors.push(this.errorMessages.seasonId.required);
      } else if (!this.isValidObjectId(expense.seasonId)) {
        errors.push(this.errorMessages.seasonId.type);
      }

      // Description validation (optional)
      if (expense.description && expense.description.length > this.validationRules.description.maxLength) {
        errors.push(this.errorMessages.description.maxLength);
      }

      // Notes validation (optional)
      if (expense.notes && expense.notes.length > this.validationRules.notes.maxLength) {
        errors.push(this.errorMessages.notes.maxLength);
      }

      // Tags validation (optional)
      if (expense.tags && Array.isArray(expense.tags)) {
        if (expense.tags.length > this.validationRules.tags.maxItems) {
          errors.push(this.errorMessages.tags.maxItems);
        }
        
        expense.tags.forEach((tag, index) => {
          if (typeof tag === 'string' && tag.length > this.validationRules.tags.itemMaxLength) {
            errors.push(`Etiket ${index + 1}: ${this.errorMessages.tags.itemMaxLength}`);
          }
        });
      }

      // Currency validation (optional)
      if (expense.currency && !this.validationRules.currency.enum.includes(expense.currency)) {
        errors.push(this.errorMessages.currency.enum);
      }

      return {
        isValid: errors.length === 0,
        errors,
        warnings,
        expense: this.sanitizeExpense(expense)
      };

    } catch (error) {
      return {
        isValid: false,
        errors: [`Validation error: ${error.message}`],
        warnings: [],
        expense: null
      };
    }
  }

  /**
   * Validate multiple expenses (batch validation)
   * @param {Array} expenses - Array of expense data to validate
   * @returns {Object} Validation result for all expenses
   */
  validateExpenses(expenses) {
    if (!Array.isArray(expenses)) {
      return {
        isValid: false,
        errors: ['Expenses must be an array'],
        validExpenses: [],
        invalidExpenses: [],
        totalCount: 0,
        validCount: 0,
        invalidCount: 0
      };
    }

    const validExpenses = [];
    const invalidExpenses = [];
    const allErrors = [];

    expenses.forEach((expense, index) => {
      const validation = this.validateExpense(expense);
      
      if (validation.isValid) {
        validExpenses.push({
          index,
          expense: validation.expense,
          warnings: validation.warnings
        });
      } else {
        invalidExpenses.push({
          index,
          expense,
          errors: validation.errors,
          warnings: validation.warnings
        });
        allErrors.push(...validation.errors.map(error => `Expense ${index + 1}: ${error}`));
      }
    });

    return {
      isValid: invalidExpenses.length === 0,
      errors: allErrors,
      validExpenses,
      invalidExpenses,
      totalCount: expenses.length,
      validCount: validExpenses.length,
      invalidCount: invalidExpenses.length
    };
  }

  /**
   * Validate date according to backend rules
   * @param {*} date - Date to validate
   * @returns {Object} Validation result
   */
  validateDate(date) {
    try {
      const dateObj = new Date(date);
      
      if (isNaN(dateObj.getTime())) {
        return { isValid: false, error: this.errorMessages.date.type };
      }

      // Backend rule: 1 year ago to 1 year from now
      const now = new Date();
      const oneYearAgo = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());
      const oneYearFromNow = new Date(now.getFullYear() + 1, now.getMonth(), now.getDate());

      if (dateObj < oneYearAgo || dateObj > oneYearFromNow) {
        return { isValid: false, error: this.errorMessages.date.range };
      }

      return { isValid: true, value: dateObj };

    } catch (error) {
      return { isValid: false, error: this.errorMessages.date.type };
    }
  }

  /**
   * Check if string is valid ObjectId format
   * @param {string} id - ID to validate
   * @returns {boolean} True if valid ObjectId format
   */
  isValidObjectId(id) {
    if (!id || typeof id !== 'string') return false;
    
    // MongoDB ObjectId is 24 character hex string
    const objectIdRegex = /^[0-9a-fA-F]{24}$/;
    return objectIdRegex.test(id);
  }

  /**
   * Sanitize expense data (trim strings, set defaults)
   * @param {Object} expense - Expense data to sanitize
   * @returns {Object} Sanitized expense data
   */
  sanitizeExpense(expense) {
    const sanitized = { ...expense };

    // Trim string fields
    if (sanitized.description) {
      sanitized.description = sanitized.description.trim();
    }

    if (sanitized.notes) {
      sanitized.notes = sanitized.notes.trim();
    }

    // Sanitize tags
    if (sanitized.tags && Array.isArray(sanitized.tags)) {
      sanitized.tags = sanitized.tags
        .map(tag => typeof tag === 'string' ? tag.trim() : tag)
        .filter(tag => tag && tag.length > 0);
    }

    // Set defaults
    if (!sanitized.currency) {
      sanitized.currency = this.validationRules.currency.default;
    }

    // Parse amount to number
    if (sanitized.amount !== undefined && sanitized.amount !== null) {
      sanitized.amount = parseFloat(sanitized.amount);
    }

    return sanitized;
  }

  /**
   * Check if expense data is migration-ready
   * @param {Object} expense - Expense data to check
   * @returns {Object} Migration readiness result
   */
  isMigrationReady(expense) {
    // For migration, use special validation that accepts guest format IDs
    const errors = [];
    const warnings = [];

    try {
      // Amount validation (required)
      if (expense.amount === undefined || expense.amount === null || expense.amount === '') {
        errors.push(this.errorMessages.amount.required);
      } else {
        const amount = parseFloat(expense.amount);
        if (isNaN(amount)) {
          errors.push(this.errorMessages.amount.type);
        } else if (amount <= 0) {
          errors.push(this.errorMessages.amount.min);
        } else if (amount > this.validationRules.amount.max) {
          errors.push(this.errorMessages.amount.max);
        }
      }

      // Date validation (required)
      if (!expense.date) {
        errors.push(this.errorMessages.date.required);
      } else {
        const dateValidation = this.validateDate(expense.date);
        if (!dateValidation.isValid) {
          errors.push(dateValidation.error);
        }
      }

      // Category ID validation for migration (more lenient)
      if (!expense.categoryId) {
        errors.push(this.errorMessages.categoryId.required);
      } else if (typeof expense.categoryId !== 'string' || expense.categoryId.trim() === '') {
        errors.push('Kategori ID\'si geçerli bir string olmalıdır');
      } else {
        // For migration, accept both guest format (fertilizer, labor, etc.) and ObjectId format
        const isGuestFormat = /^[a-zA-Z_]+$/.test(expense.categoryId);
        const isObjectIdFormat = this.isValidObjectId(expense.categoryId);

        if (!isGuestFormat && !isObjectIdFormat) {
          warnings.push('Geçersiz kategori ID\'si');
        }
      }

      // Season ID validation for migration (more lenient)
      if (!expense.seasonId) {
        errors.push(this.errorMessages.seasonId.required);
      } else if (typeof expense.seasonId !== 'string' || expense.seasonId.trim() === '') {
        errors.push('Sezon ID\'si geçerli bir string olmalıdır');
      } else {
        // For migration, accept both guest format and ObjectId format
        const isGuestFormat = /^[a-zA-Z0-9_-]+$/.test(expense.seasonId);
        const isObjectIdFormat = this.isValidObjectId(expense.seasonId);

        if (!isGuestFormat && !isObjectIdFormat) {
          warnings.push('Geçersiz sezon ID\'si');
        }
      }

      // Description validation (optional)
      if (expense.description && expense.description.length > this.validationRules.description.maxLength) {
        warnings.push(this.errorMessages.description.maxLength);
      }

      // Sanitize expense data
      const sanitizedExpense = this.sanitizeExpense(expense);

      return {
        isReady: errors.length === 0,
        errors: errors,
        warnings: warnings,
        canMigrate: errors.length === 0,
        expense: sanitizedExpense
      };

    } catch (error) {
      console.error('Migration validation error:', error);
      return {
        isReady: false,
        errors: ['Validation sırasında hata oluştu'],
        warnings: [],
        canMigrate: false,
        expense: expense
      };
    }
  }

  /**
   * Get validation rules for frontend forms
   * @returns {Object} Validation rules
   */
  getValidationRules() {
    return this.validationRules;
  }

  /**
   * Get error messages for frontend forms
   * @returns {Object} Error messages
   */
  getErrorMessages() {
    return this.errorMessages;
  }
}

export default new ExpenseValidationService();
