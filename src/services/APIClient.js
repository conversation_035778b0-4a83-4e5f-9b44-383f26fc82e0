/**
 * API Client for Çiftçi Not Defterim Backend
 * Handles all HTTP requests to the backend API with authentication and error handling
 */

import auth from '@react-native-firebase/auth';
import { Platform } from 'react-native';
import AuthService from './AuthService';

// Android emülatörde localhost yerine ******** kullanılmalı
const getApiBaseUrl = () => {
  if (__DEV__) {
    // Development ortamında platform kontrolü
    if (Platform.OS === 'android') {
      return 'http://********:3000/api/v1'; // Android emülatör için
    } else {
      return 'http://localhost:3000/api/v1'; // iOS simulator ve web için
    }
  } else {
    return 'https://api.ciftcinotdefterim.com/api/v1'; // Production
  }
};

const API_BASE_URL = getApiBaseUrl();

class APIClientService {
  constructor() {
    this.baseURL = API_BASE_URL;
    this.authToken = null;
    this.initialized = false;
  }

  // Check if user is in guest mode
  isGuestMode() {
    return AuthService.isInGuestMode();
  }

  // Check if API requests should be made (not guest mode)
  shouldMakeRequest() {
    return !this.isGuestMode();
  }

  // Initialize API client with authentication
  async initialize() {
    try {
      // GUEST MODE: Skip initialization
      if (this.isGuestMode()) {
        console.log('Guest mode: Skipping APIClient initialization');
        this.initialized = false;
        return;
      }

      const currentUser = auth().currentUser;
      if (currentUser) {
        this.authToken = await currentUser.getIdToken();
        this.initialized = true;
        console.log('APIClient initialized with authentication');
      } else {
        throw new Error('No authenticated user found');
      }
    } catch (error) {
      console.error('APIClient initialization failed:', error);
      throw error;
    }
  }

  // Get fresh auth token
  async getAuthToken() {
    try {
      const currentUser = auth().currentUser;
      if (currentUser) {
        console.log('Getting fresh auth token...');
        this.authToken = await currentUser.getIdToken(true); // Force refresh
        console.log('Auth token refreshed successfully, length:', this.authToken?.length);
        return this.authToken;
      } else {
        throw new Error('No authenticated user found');
      }
    } catch (error) {
      console.error('Failed to get auth token:', error);
      throw error;
    }
  }

  // Make HTTP request with error handling and retry logic
  async makeRequest(endpoint, options = {}) {
    // GUEST MODE: Block all API requests
    if (this.isGuestMode()) {
      console.log('Guest mode: Blocking API request to', endpoint);
      throw new Error('API requests not available in guest mode');
    }

    if (!this.shouldMakeRequest()) {
      throw new Error('API requests not available in current mode');
    }

    const maxRetries = 3;
    let lastError;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        // Ensure we have a valid auth token
        if (!this.authToken) {
          await this.getAuthToken();
        }

        const url = `${this.baseURL}${endpoint}`;
        const requestOptions = {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.authToken}`,
            ...options.headers
          },
          ...options
        };

        console.log(`API Request (attempt ${attempt}): ${requestOptions.method} ${url}`);

        const response = await fetch(url, requestOptions);

        // Handle authentication errors
        if (response.status === 401) {
          console.log('Token expired, refreshing...');
          await this.getAuthToken();
          
          // Retry with new token
          requestOptions.headers.Authorization = `Bearer ${this.authToken}`;
          const retryResponse = await fetch(url, requestOptions);
          
          if (retryResponse.status === 401) {
            throw new Error('Authentication failed after token refresh');
          }
          
          return await this.handleResponse(retryResponse);
        }

        return await this.handleResponse(response);

      } catch (error) {
        lastError = error;
        console.warn(`API request attempt ${attempt} failed:`, error.message);

        // Don't retry on certain errors
        if (error.message.includes('Authentication failed') ||
            error.message.includes('Network request failed') ||
            error.message.includes('çok sık yapılamaz') ||
            error.message.includes('onay gerektiriyor') ||
            error.status === 429 ||
            error.requiresConfirmation ||
            attempt === maxRetries) {
          break;
        }

        // Wait before retry (exponential backoff)
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
      }
    }

    console.error('API request failed after all retries:', lastError);
    throw lastError;
  }

  // Handle API response
  async handleResponse(response) {
    try {
      const data = await response.json();

      if (!response.ok) {
        // Special handling for confirmation required responses
        if (response.status === 400 && data.requiresConfirmation) {
          const error = new Error(data.message || `HTTP ${response.status}`);
          error.status = response.status;
          error.requiresConfirmation = true;
          error.validationData = data;
          throw error;
        }

        const error = new Error(data.error?.message || data.message || `HTTP ${response.status}`);
        error.status = response.status;
        error.code = data.error?.code;
        throw error;
      }

      // If response has success field, return the whole response object
      // This preserves backend response structure for proper frontend handling
      if (data.hasOwnProperty('success')) {
        // Still normalize the data field if it exists
        if (data.data) {
          if (Array.isArray(data.data)) {
            data.data = data.data.map(item => this.normalizeItem(item));
          } else if (data.data && typeof data.data === 'object') {
            data.data = this.normalizeItem(data.data);
          }
        }
        return data;
      }

      // For responses without success field, use the old logic
      const result = data.data || data;

      // Normalize MongoDB _id to id for frontend compatibility
      if (Array.isArray(result)) {
        return result.map(item => this.normalizeItem(item));
      } else if (result && typeof result === 'object') {
        return this.normalizeItem(result);
      }

      return result;
    } catch (error) {
      if (error.name === 'SyntaxError') {
        throw new Error('Invalid response format from server');
      }
      throw error;
    }
  }

  // Normalize MongoDB objects for frontend compatibility
  normalizeItem(item) {
    if (!item || typeof item !== 'object') {
      return item;
    }

    const normalized = { ...item };

    // Convert _id to id
    if (item._id && !item.id) {
      normalized.id = item._id;
    }

    // Handle populated categoryId in expenses
    if (item.categoryId && typeof item.categoryId === 'object') {
      // If categoryId is populated, normalize it too
      normalized.categoryId = this.normalizeItem(item.categoryId);
    }

    return normalized;
  }



  // Categories API
  async getCategories() {
    try {
      return await this.makeRequest('/categories');
    } catch (error) {
      console.error('Failed to get categories:', error);
      throw error;
    }
  }

  async createCategory(category) {
    try {
      return await this.makeRequest('/categories', {
        method: 'POST',
        body: JSON.stringify(category)
      });
    } catch (error) {
      console.error('Failed to create category:', error);
      throw error;
    }
  }

  // Expenses API
  async getExpenses(limit = null, filters = {}) {
    try {
      const queryParams = new URLSearchParams();
      
      if (limit) queryParams.append('limit', limit.toString());
      if (filters.startDate) queryParams.append('startDate', filters.startDate);
      if (filters.endDate) queryParams.append('endDate', filters.endDate);
      if (filters.categoryId) queryParams.append('categoryId', filters.categoryId);
      if (filters.search) queryParams.append('search', filters.search);

      const endpoint = `/expenses${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      return await this.makeRequest(endpoint);
    } catch (error) {
      console.error('Failed to get expenses:', error);
      throw error;
    }
  }

  async createExpense(expense) {
    try {
      // Helper function to check if ID is valid MongoDB ObjectId format
      const isValidObjectId = (id) => {
        return id && typeof id === 'string' && /^[0-9a-fA-F]{24}$/.test(id);
      };

      // Remove local-only fields and validate IDs
      const cleanExpense = {
        categoryId: expense.categoryId,
        amount: expense.amount,
        description: expense.description,
        date: expense.date,
        location: expense.location,
        photos: expense.photos || [],
        cropType: expense.cropType,
        farmArea: expense.farmArea,
        // TWO-MODE SYSTEM: New fields
        fieldId: expense.fieldId,
        cropId: expense.cropId,
        trackingMode: expense.trackingMode
      };

      // Only include seasonId if it's a valid MongoDB ObjectId
      // Local seasonIds (timestamp-based) will be ignored, backend will use active season
      if (isValidObjectId(expense.seasonId)) {
        cleanExpense.seasonId = expense.seasonId;
      }

      // Only include fieldId if it's a valid MongoDB ObjectId
      if (isValidObjectId(expense.fieldId)) {
        cleanExpense.fieldId = expense.fieldId;
      }

      // Only include cropId if it's a valid MongoDB ObjectId
      if (isValidObjectId(expense.cropId)) {
        cleanExpense.cropId = expense.cropId;
      }

      return await this.makeRequest('/expenses', {
        method: 'POST',
        body: JSON.stringify(cleanExpense)
      });
    } catch (error) {
      console.error('Failed to create expense:', error);
      throw error;
    }
  }

  async updateExpense(id, expense) {
    try {
      // Remove local-only fields
      const cleanExpense = {
        categoryId: expense.categoryId,
        amount: expense.amount,
        description: expense.description,
        date: expense.date,
        location: expense.location,
        photos: expense.photos || [],
        cropType: expense.cropType,
        farmArea: expense.farmArea
      };

      return await this.makeRequest(`/expenses/${id}`, {
        method: 'PUT',
        body: JSON.stringify(cleanExpense)
      });
    } catch (error) {
      console.error('Failed to update expense:', error);
      throw error;
    }
  }

  async deleteExpense(id) {
    try {
      return await this.makeRequest(`/expenses/${id}`, {
        method: 'DELETE'
      });
    } catch (error) {
      console.error('Failed to delete expense:', error);
      throw error;
    }
  }

  async getExpenseById(id) {
    try {
      return await this.makeRequest(`/expenses/${id}`);
    } catch (error) {
      console.error('Failed to get expense by ID:', error);
      throw error;
    }
  }

  // User API
  async getUserProfile() {
    try {
      return await this.makeRequest('/users/profile');
    } catch (error) {
      console.error('Failed to get user profile:', error);
      throw error;
    }
  }

  async updateUserProfile(profile) {
    try {
      return await this.makeRequest('/users/profile', {
        method: 'PUT',
        body: JSON.stringify(profile)
      });
    } catch (error) {
      console.error('Failed to update user profile:', error);
      throw error;
    }
  }

  // Tracking Mode API
  async getTrackingMode() {
    try {
      return await this.makeRequest('/users/mode');
    } catch (error) {
      console.error('Failed to get tracking mode:', error);
      throw error;
    }
  }

  async switchTrackingMode(mode, confirmed = false) {
    try {
      const requestBody = { mode };
      if (confirmed) {
        requestBody.confirmed = true;
      }

      return await this.makeRequest('/users/mode', {
        method: 'POST',
        body: JSON.stringify(requestBody)
      });
    } catch (error) {
      console.error('Failed to switch tracking mode:', error);
      throw error;
    }
  }

  // Sync API
  async pushChanges(changes) {
    try {
      return await this.makeRequest('/sync/push', {
        method: 'POST',
        body: JSON.stringify({ changes })
      });
    } catch (error) {
      console.error('Failed to push changes:', error);
      throw error;
    }
  }

  async pullChanges(lastSyncTime) {
    try {
      const endpoint = `/sync/pull${lastSyncTime ? `?since=${lastSyncTime}` : ''}`;
      return await this.makeRequest(endpoint);
    } catch (error) {
      console.error('Failed to pull changes:', error);
      throw error;
    }
  }

  // Health check
  async healthCheck() {
    try {
      const response = await fetch(`${this.baseURL.replace('/api/v1', '')}/health`);
      return response.ok;
    } catch (error) {
      console.error('Health check failed:', error);
      return false;
    }
  }

  // Test connection without authentication
  async testConnection() {
    try {
      const response = await fetch(this.baseURL.replace('/api/v1', '/'));
      const data = await response.json();
      return { success: response.ok, data };
    } catch (error) {
      console.error('Connection test failed:', error);
      return { success: false, error: error.message };
    }
  }

  // Get API status
  getStatus() {
    return {
      initialized: this.initialized,
      hasAuthToken: !!this.authToken,
      baseURL: this.baseURL
    };
  }

  // Reset client (for logout)
  reset() {
    this.authToken = null;
    this.initialized = false;
    console.log('APIClient reset');
  }

  // ===== FIELD OPERATIONS =====

  // Get all fields
  async getFields() {
    try {
      return await this.makeRequest('/fields');
    } catch (error) {
      console.error('APIClient: Error getting fields:', error);
      throw error;
    }
  }

  // Create field
  async createField(fieldData) {
    try {
      return await this.makeRequest('/fields', {
        method: 'POST',
        body: JSON.stringify(fieldData)
      });
    } catch (error) {
      console.error('APIClient: Error creating field:', error);
      throw error;
    }
  }

  // Update field
  async updateField(fieldId, updates) {
    try {
      return await this.makeRequest(`/fields/${fieldId}`, {
        method: 'PUT',
        body: JSON.stringify(updates)
      });
    } catch (error) {
      console.error('APIClient: Error updating field:', error);
      throw error;
    }
  }

  // Delete field
  async deleteField(fieldId) {
    try {
      return await this.makeRequest(`/fields/${fieldId}`, {
        method: 'DELETE'
      });
    } catch (error) {
      console.error('APIClient: Error deleting field:', error);
      throw error;
    }
  }

  // Set default field
  async setDefaultField(fieldId) {
    try {
      return await this.makeRequest(`/fields/${fieldId}/set-default`, {
        method: 'POST'
      });
    } catch (error) {
      console.error('APIClient: Error setting default field:', error);
      throw error;
    }
  }

  // AI Import/Export System Methods

  /**
   * Process text with AI
   */
  async processAIText(text, context = {}) {
    if (!this.shouldMakeRequest()) {
      throw new Error('AI özelliği için Google ile giriş yapmanız gerekli');
    }

    try {
      const response = await this.makeRequest('/import/ai-chat', {
        method: 'POST',
        body: JSON.stringify({ text, context }),
      });
      return response;
    } catch (error) {
      console.error('AI text processing error:', error);
      throw error;
    }
  }

  /**
   * Ask follow-up question to AI
   */
  async askAIQuestion(originalText, question, previousData) {
    if (!this.shouldMakeRequest()) {
      throw new Error('AI özelliği için Google ile giriş yapmanız gerekli');
    }

    try {
      const response = await this.makeRequest('/import/ai-question', {
        method: 'POST',
        body: JSON.stringify({ originalText, question, previousData }),
      });
      return response;
    } catch (error) {
      console.error('AI question error:', error);
      throw error;
    }
  }

  /**
   * Validate AI-processed data
   */
  async validateAIData(data) {
    if (!this.shouldMakeRequest()) {
      throw new Error('AI özelliği için Google ile giriş yapmanız gerekli');
    }

    try {
      const response = await this.makeRequest('/import/ai-validate', {
        method: 'POST',
        body: JSON.stringify({ data }),
      });
      return response;
    } catch (error) {
      console.error('AI validation error:', error);
      throw error;
    }
  }

  /**
   * Get AI service status
   */
  async getAIStatus() {
    if (!this.shouldMakeRequest()) {
      throw new Error('AI özelliği için Google ile giriş yapmanız gerekli');
    }

    try {
      const response = await this.makeRequest('/import/ai-status', {
        method: 'GET',
      });
      return response;
    } catch (error) {
      console.error('AI status error:', error);
      throw error;
    }
  }

  /**
   * Download Excel template
   */
  async downloadExcelTemplate() {
    if (!this.shouldMakeRequest()) {
      throw new Error('Template indirme için Google ile giriş yapmanız gerekli');
    }

    try {
      const response = await this.makeRequest('/import/template', {
        method: 'GET',
      });
      return response;
    } catch (error) {
      console.error('Template download error:', error);
      throw error;
    }
  }

  /**
   * Upload Excel file
   */
  async uploadExcelFile(fileUri, fileName) {
    if (!this.shouldMakeRequest()) {
      throw new Error('Excel yükleme için Google ile giriş yapmanız gerekli');
    }

    try {
      const formData = new FormData();
      formData.append('file', {
        uri: fileUri,
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        name: fileName || 'import.xlsx',
      });

      const response = await this.makeRequest('/import/excel', {
        method: 'POST',
        body: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response;
    } catch (error) {
      console.error('Excel upload error:', error);
      throw error;
    }
  }

  /**
   * Download Excel template
   */
  async downloadTemplate() {
    if (!this.shouldMakeRequest()) {
      throw new Error('Template indirme için Google ile giriş yapmanız gerekli');
    }

    try {
      const response = await this.makeRequest('/import/template', {
        method: 'GET',
      });
      return response;
    } catch (error) {
      console.error('Template download error:', error);
      throw error;
    }
  }

  /**
   * Get import history
   */
  async getImportHistory() {
    if (!this.shouldMakeRequest()) {
      throw new Error('Import geçmişi için Google ile giriş yapmanız gerekli');
    }

    try {
      const response = await this.makeRequest('/import/history', {
        method: 'GET',
      });
      return response;
    } catch (error) {
      console.error('Import history error:', error);
      throw error;
    }
  }
}

// Create singleton instance
export const APIClient = new APIClientService();
