/**
 * Field Validation Service for Çiftçi Not Defterim
 * Provides comprehensive field data validation matching backend rules
 */

class FieldValidationService {
  constructor() {
    // Backend validation rules (from backend/src/models/Field.js)
    this.validationRules = {
      name: {
        required: true,
        maxLength: 50,
        minLength: 1
      },
      size: {
        value: {
          min: 0,
          type: 'number'
        },
        unit: {
          enum: ['dekar', 'dönüm', 'hectare', 'acre'],
          default: 'dekar'
        }
      },
      location: {
        address: {
          maxLength: 200
        },
        coordinates: {
          latitude: {
            min: -90,
            max: 90
          },
          longitude: {
            min: -180,
            max: 180
          }
        }
      },
      soilType: {
        enum: ['clay', 'sandy', 'loamy', 'silty', 'peaty', 'chalky', 'other'],
        default: 'other'
      },
      irrigationType: {
        enum: ['drip', 'sprinkler', 'flood', 'manual', 'none'],
        default: 'none'
      },
      notes: {
        maxLength: 200
      }
    };

    // Turkish error messages
    this.errorMessages = {
      name: {
        required: 'Tarla adı zorunludur',
        maxLength: 'Tarla adı en fazla 50 karakter olabilir',
        minLength: 'Tarla adı boş olamaz'
      },
      size: {
        value: {
          min: 'Alan değeri negatif olamaz',
          type: 'Alan değeri sayı olmalıdır'
        },
        unit: {
          enum: 'Geçersiz alan birimi'
        }
      },
      location: {
        address: {
          maxLength: 'Adres en fazla 200 karakter olabilir'
        },
        coordinates: {
          latitude: 'Enlem -90 ile 90 arasında olmalıdır',
          longitude: 'Boylam -180 ile 180 arasında olmalıdır'
        }
      },
      soilType: {
        enum: 'Geçersiz toprak türü'
      },
      irrigationType: {
        enum: 'Geçersiz sulama türü'
      },
      notes: {
        maxLength: 'Notlar en fazla 200 karakter olabilir'
      }
    };
  }

  /**
   * Validate a single field
   * @param {Object} field - Field data to validate
   * @returns {Object} Validation result
   */
  validateField(field) {
    const errors = [];
    const warnings = [];

    try {
      // Name validation (required)
      if (!field.name) {
        errors.push(this.errorMessages.name.required);
      } else {
        const trimmedName = field.name.trim();
        if (trimmedName.length === 0) {
          errors.push(this.errorMessages.name.minLength);
        } else if (trimmedName.length > this.validationRules.name.maxLength) {
          errors.push(this.errorMessages.name.maxLength);
        }
      }

      // Size validation
      if (field.size) {
        // Size value validation
        if (field.size.value !== undefined && field.size.value !== null) {
          if (typeof field.size.value !== 'number') {
            errors.push(this.errorMessages.size.value.type);
          } else if (field.size.value < this.validationRules.size.value.min) {
            errors.push(this.errorMessages.size.value.min);
          }
        }

        // Size unit validation
        if (field.size.unit && !this.validationRules.size.unit.enum.includes(field.size.unit)) {
          errors.push(this.errorMessages.size.unit.enum);
        }
      }

      // Location validation
      if (field.location) {
        // Address validation
        if (field.location.address && field.location.address.length > this.validationRules.location.address.maxLength) {
          errors.push(this.errorMessages.location.address.maxLength);
        }

        // Coordinates validation
        if (field.location.coordinates) {
          const { latitude, longitude } = field.location.coordinates;
          
          if (latitude !== undefined && latitude !== null) {
            if (latitude < this.validationRules.location.coordinates.latitude.min || 
                latitude > this.validationRules.location.coordinates.latitude.max) {
              errors.push(this.errorMessages.location.coordinates.latitude);
            }
          }

          if (longitude !== undefined && longitude !== null) {
            if (longitude < this.validationRules.location.coordinates.longitude.min || 
                longitude > this.validationRules.location.coordinates.longitude.max) {
              errors.push(this.errorMessages.location.coordinates.longitude);
            }
          }
        }
      }

      // Soil type validation
      if (field.soilType && !this.validationRules.soilType.enum.includes(field.soilType)) {
        errors.push(this.errorMessages.soilType.enum);
      }

      // Irrigation type validation
      if (field.irrigationType && !this.validationRules.irrigationType.enum.includes(field.irrigationType)) {
        errors.push(this.errorMessages.irrigationType.enum);
      }

      // Notes validation
      if (field.notes && field.notes.length > this.validationRules.notes.maxLength) {
        errors.push(this.errorMessages.notes.maxLength);
      }

      return {
        isValid: errors.length === 0,
        errors,
        warnings,
        field: this.sanitizeField(field)
      };

    } catch (error) {
      return {
        isValid: false,
        errors: [`Validation error: ${error.message}`],
        warnings: [],
        field: null
      };
    }
  }

  /**
   * Validate multiple fields
   * @param {Array} fields - Array of field data to validate
   * @returns {Object} Validation result for all fields
   */
  validateFields(fields) {
    if (!Array.isArray(fields)) {
      return {
        isValid: false,
        errors: ['Fields must be an array'],
        validFields: [],
        invalidFields: [],
        totalCount: 0,
        validCount: 0,
        invalidCount: 0
      };
    }

    const validFields = [];
    const invalidFields = [];
    const allErrors = [];

    fields.forEach((field, index) => {
      const validation = this.validateField(field);
      
      if (validation.isValid) {
        validFields.push({
          index,
          field: validation.field,
          warnings: validation.warnings
        });
      } else {
        invalidFields.push({
          index,
          field,
          errors: validation.errors,
          warnings: validation.warnings
        });
        allErrors.push(...validation.errors.map(error => `Field ${index + 1}: ${error}`));
      }
    });

    return {
      isValid: invalidFields.length === 0,
      errors: allErrors,
      validFields,
      invalidFields,
      totalCount: fields.length,
      validCount: validFields.length,
      invalidCount: invalidFields.length
    };
  }

  /**
   * Sanitize field data (trim strings, set defaults)
   * @param {Object} field - Field data to sanitize
   * @returns {Object} Sanitized field data
   */
  sanitizeField(field) {
    const sanitized = { ...field };

    // Trim string fields
    if (sanitized.name) {
      sanitized.name = sanitized.name.trim();
    }

    if (sanitized.location?.address) {
      sanitized.location.address = sanitized.location.address.trim();
    }

    if (sanitized.notes) {
      sanitized.notes = sanitized.notes.trim();
    }

    // Set defaults
    if (sanitized.size && !sanitized.size.unit) {
      sanitized.size.unit = this.validationRules.size.unit.default;
    }

    if (!sanitized.soilType) {
      sanitized.soilType = this.validationRules.soilType.default;
    }

    if (!sanitized.irrigationType) {
      sanitized.irrigationType = this.validationRules.irrigationType.default;
    }

    return sanitized;
  }

  /**
   * Get validation rules for frontend forms
   * @returns {Object} Validation rules
   */
  getValidationRules() {
    return this.validationRules;
  }

  /**
   * Get error messages for frontend forms
   * @returns {Object} Error messages
   */
  getErrorMessages() {
    return this.errorMessages;
  }

  /**
   * Check if field data is migration-ready
   * @param {Object} field - Field data to check
   * @returns {Object} Migration readiness result
   */
  isMigrationReady(field) {
    const validation = this.validateField(field);
    
    // For migration, we're more lenient - only check critical fields
    const criticalErrors = validation.errors.filter(error => 
      error.includes('zorunludur') || 
      error.includes('boş olamaz') ||
      error.includes('negatif olamaz')
    );

    return {
      isReady: criticalErrors.length === 0,
      errors: criticalErrors,
      warnings: validation.errors.filter(error => !criticalErrors.includes(error)),
      canMigrate: criticalErrors.length === 0,
      field: validation.field
    };
  }
}

export default new FieldValidationService();
