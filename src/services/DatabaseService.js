import AsyncStorage from '@react-native-async-storage/async-storage';
import { DefaultCategories } from '../constants/Categories';

class DatabaseService {
  constructor() {
    this.categories = DefaultCategories;
    this.expenses = [];
  }

  // Veritabanını başlat
  async initDatabase() {
    try {
      // AsyncStorage'dan verileri yükle
      const storedCategories = await AsyncStorage.getItem('categories');
      const storedExpenses = await AsyncStorage.getItem('expenses');
      
      if (storedCategories) {
        this.categories = JSON.parse(storedCategories);
      } else {
        // İlk kez çalışıyorsa varsayılan kategorileri kaydet
        await AsyncStorage.setItem('categories', JSON.stringify(DefaultCategories));
        this.categories = DefaultCategories;
      }
      
      if (storedExpenses) {
        this.expenses = JSON.parse(storedExpenses);
      }
      
      console.log('Veritabanı başar<PERSON>yla başlatıldı (AsyncStorage)');
    } catch (error) {
      console.error('Veritabanı başlatma hatası:', error);
      throw error;
    }
  }

  // Kategorileri getir
  async getCategories() {
    return this.categories;
  }

  // Gider ekle
  async addExpense(expense) {
    const newExpense = {
      id: Date.now().toString(),
      ...expense,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    this.expenses.push(newExpense);
    await AsyncStorage.setItem('expenses', JSON.stringify(this.expenses));
    return newExpense;
  }

  // Giderleri getir
  async getExpenses(limit = null) {
    let sortedExpenses = [...this.expenses].sort((a, b) => new Date(b.date) - new Date(a.date));
    if (limit) {
      sortedExpenses = sortedExpenses.slice(0, limit);
    }
    
    // Kategori bilgilerini ekle
    return sortedExpenses.map(expense => {
      const category = this.categories.find(cat => cat.id === expense.categoryId);
      return {
        ...expense,
        categoryName: category?.name || 'Bilinmeyen',
        categoryEmoji: category?.emoji || '📝',
        categoryColor: category?.color || '#666'
      };
    });
  }

  // Tarih aralığına göre giderleri getir
  async getExpensesByDateRange(startDate, endDate) {
    const filtered = this.expenses.filter(expense => {
      const expenseDate = new Date(expense.date);
      return expenseDate >= new Date(startDate) && expenseDate <= new Date(endDate);
    });
    
    return filtered.map(expense => {
      const category = this.categories.find(cat => cat.id === expense.categoryId);
      return {
        ...expense,
        categoryName: category?.name || 'Bilinmeyen',
        categoryEmoji: category?.emoji || '📝',
        categoryColor: category?.color || '#666'
      };
    });
  }

  // Kategoriye göre toplam giderleri getir
  async getTotalExpensesByCategory(startDate, endDate) {
    const expenses = await this.getExpensesByDateRange(startDate, endDate);
    const categoryTotals = {};
    
    expenses.forEach(expense => {
      if (!categoryTotals[expense.categoryId]) {
        const category = this.categories.find(cat => cat.id === expense.categoryId);
        categoryTotals[expense.categoryId] = {
          id: expense.categoryId,
          name: category?.name || 'Bilinmeyen',
          emoji: category?.emoji || '📝',
          color: category?.color || '#666',
          total: 0
        };
      }
      categoryTotals[expense.categoryId].total += expense.amount;
    });
    
    return Object.values(categoryTotals).sort((a, b) => b.total - a.total);
  }

  // Gider güncelle
  async updateExpense(id, expense) {
    const index = this.expenses.findIndex(exp => exp.id === id);
    if (index !== -1) {
      this.expenses[index] = {
        ...this.expenses[index],
        ...expense,
        updatedAt: new Date().toISOString()
      };
      await AsyncStorage.setItem('expenses', JSON.stringify(this.expenses));
    }
  }

  // Gider sil
  async deleteExpense(id) {
    this.expenses = this.expenses.filter(expense => expense.id !== id);
    await AsyncStorage.setItem('expenses', JSON.stringify(this.expenses));
  }

  // Ayarlar
  async getSetting(key) {
    try {
      const value = await AsyncStorage.getItem(`setting_${key}`);
      return value;
    } catch (error) {
      console.error('Ayar okuma hatası:', error);
      return null;
    }
  }

  async setSetting(key, value) {
    try {
      await AsyncStorage.setItem(`setting_${key}`, value);
    } catch (error) {
      console.error('Ayar kaydetme hatası:', error);
    }
  }
}

export default new DatabaseService();
