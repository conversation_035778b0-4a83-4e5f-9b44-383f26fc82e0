/**
 * Authentication Service for Çiftçi Not Defterim
 * Handles Google Sign-In, guest mode, and user session management
 */
import auth from '@react-native-firebase/auth';
import { GoogleSignin } from '@react-native-google-signin/google-signin';
import AsyncStorage from '@react-native-async-storage/async-storage';
class AuthService {
  constructor() {
    this.currentUser = null;
    this.isGuestMode = false;
    this.authStateListeners = [];
    this.initialized = false;
  }
  // Initialize authentication service
  async initialize() {
    if (this.initialized) return;
    try {
      console.log('AuthService initialization started...');
      // Configure Google Sign-In
      GoogleSignin.configure({
        webClientId: '680682523446-rd7a61p4qvp2r8nih9sv151dlj4mji0r.apps.googleusercontent.com',
        offlineAccess: true,
        hostedDomain: '',
        forceCodeForRefreshToken: true,
      });
      console.log('Google Sign-In configured');
      // Set up auth state listener first
      auth().onAuthStateChanged(this.handleAuthStateChange.bind(this));
      console.log('Firebase auth state listener set up');
      // Then check for existing authentication state
      console.log('Checking existing auth state...');
      await this.checkAuthState();
      console.log('Auth state check completed');
      this.initialized = true;
      console.log('AuthService initialized successfully');
    } catch (error) {
      console.error('AuthService initialization error:', error);
      throw error;
    }
  }
  // Check current authentication state
  async checkAuthState() {
    try {
      // Check for guest mode
      const guestMode = await AsyncStorage.getItem('guest_mode');
      if (guestMode === 'true') {
        this.isGuestMode = true;
        this.currentUser = {
          id: 'guest',
          email: null,
          name: 'Misafir Kullanıcı',
          authProvider: 'guest',
          isGuest: true
        };
        this.notifyAuthStateListeners();
        return;
      }
      // Check Firebase auth state
      const firebaseUser = auth().currentUser;
      if (firebaseUser) {
        this.currentUser = await this.mapFirebaseUser(firebaseUser);
        this.isGuestMode = false;
        // Set user authenticated flag for AppNavigator
        await AsyncStorage.setItem('user_authenticated', 'true');
        this.notifyAuthStateListeners();
      }
    } catch (error) {
      console.error('Error checking auth state:', error);
    }
  }
  // Handle Firebase auth state changes
  async handleAuthStateChange(firebaseUser) {
    console.log('Firebase auth state changed:', {
      hasUser: !!firebaseUser,
      userEmail: firebaseUser?.email,
      isGuestMode: this.isGuestMode,
      currentUser: this.currentUser?.email
    });
    if (firebaseUser && !this.isGuestMode) {
      this.currentUser = await this.mapFirebaseUser(firebaseUser);
      console.log('Updated current user from Firebase auth state change:', this.currentUser.email);
      this.notifyAuthStateListeners();
    } else if (!firebaseUser && !this.isGuestMode) {
      this.currentUser = null;
      console.log('Cleared current user from Firebase auth state change');
      this.notifyAuthStateListeners();
    }
  }
  // Map Firebase user to our user format
  async mapFirebaseUser(firebaseUser) {
    try {
      // Get fresh ID token
      const accessToken = await firebaseUser.getIdToken(true); // force refresh

      return {
        id: firebaseUser.uid,
        email: firebaseUser.email,
        name: firebaseUser.displayName || 'Kullanıcı',
        photoURL: firebaseUser.photoURL,
        authProvider: 'google',
        isGuest: false,
        emailVerified: firebaseUser.emailVerified,
        createdAt: firebaseUser.metadata.creationTime,
        lastSignIn: firebaseUser.metadata.lastSignInTime,
        accessToken: accessToken
      };
    } catch (error) {
      console.error('Error getting ID token:', error);
      // Return user without token if token fetch fails
      return {
        id: firebaseUser.uid,
        email: firebaseUser.email,
        name: firebaseUser.displayName || 'Kullanıcı',
        photoURL: firebaseUser.photoURL,
        authProvider: 'google',
        isGuest: false,
        emailVerified: firebaseUser.emailVerified,
        createdAt: firebaseUser.metadata.creationTime,
        lastSignIn: firebaseUser.metadata.lastSignInTime,
        accessToken: null
      };
    }
  }
  // Sign in with Google
  async signInWithGoogle() {
    try {
      console.log('=== GOOGLE SIGN-IN DEBUG START ===');
      console.log('Starting Google Sign-In process...');
      console.log('GoogleSignin available:', !!GoogleSignin);
      console.log('GoogleSignin methods:', GoogleSignin ? Object.getOwnPropertyNames(GoogleSignin) : 'N/A');

      // Check if device supports Google Play Services
      console.log('Checking Google Play Services...');
      try {
        const playServicesResult = await GoogleSignin.hasPlayServices({
          showPlayServicesUpdateDialog: true
        });
        console.log('Google Play Services check result:', playServicesResult);
        console.log('Google Play Services check passed');
      } catch (playServicesError) {
        console.error('Google Play Services check failed:', {
          error: playServicesError,
          message: playServicesError?.message,
          code: playServicesError?.code,
          stack: playServicesError?.stack
        });
        throw new Error(`Google Play Services hatası: ${playServicesError?.message || 'Bilinmeyen hata'}`);
      }

      // Get user info from Google
      console.log('Calling GoogleSignin.signIn()...');
      console.log('Current GoogleSignin configuration:', await GoogleSignin.getTokens().catch(() => 'No tokens'));

      const googleSignInResult = await GoogleSignin.signIn();
      console.log('GoogleSignin.signIn() result:', {
        hasIdToken: !!googleSignInResult?.idToken,
        hasUser: !!googleSignInResult?.user,
        resultKeys: googleSignInResult ? Object.keys(googleSignInResult) : 'null/undefined',
        type: googleSignInResult?.type,
        data: googleSignInResult?.data,
        fullResult: googleSignInResult
      });

      // Check if user cancelled the sign-in
      if (googleSignInResult && googleSignInResult.type === 'cancel') {
        console.log('User cancelled Google Sign-In');
        return { success: false, error: 'Giriş işlemi iptal edildi', cancelled: true };
      }

      // Check if sign-in was successful
      if (!googleSignInResult || googleSignInResult.type !== 'success') {
        console.log('Google Sign-In was not successful:', googleSignInResult);
        throw new Error('Google Sign-In was not successful');
      }

      // Safely extract idToken from the correct location
      const idToken = googleSignInResult.data?.idToken;
      if (!idToken) {
        console.log('Google Sign-In result does not contain idToken in data:', googleSignInResult);
        throw new Error('Google Sign-In did not return a valid idToken');
      }

      console.log('Successfully extracted idToken from googleSignInResult.data.idToken');
      console.log('Successfully extracted idToken');

      // Create Firebase credential
      const googleCredential = auth.GoogleAuthProvider.credential(idToken);
      console.log('Created Firebase credential');

      // Sign in with Firebase
      const userCredential = await auth().signInWithCredential(googleCredential);
      console.log('Firebase sign-in successful');

      // Clear guest mode if it was set
      await AsyncStorage.removeItem('guest_mode');
      this.isGuestMode = false;

      // Set user authenticated flag for AppNavigator
      await AsyncStorage.setItem('user_authenticated', 'true');

      const user = await this.mapFirebaseUser(userCredential.user);
      this.currentUser = user;

      // Notify auth state listeners
      this.notifyAuthStateListeners();

      // Check for data conflicts and handle migration AFTER authentication is complete
      console.log('🔄 AUTH: Checking for data conflicts...');
      const conflictResult = await this.checkDataConflict();
      console.log('🔄 AUTH: Data conflict check result:', conflictResult);

      console.log('Google sign-in successful:', user.email);
      return {
        success: true,
        user,
        conflictResult
      };
    } catch (error) {
      console.error('=== GOOGLE SIGN-IN ERROR DETAILS ===');
      console.error('Google sign-in error details:', {
        error,
        errorType: typeof error,
        errorConstructor: error?.constructor?.name,
        errorMessage: error?.message,
        errorCode: error?.code,
        errorStack: error?.stack,
        errorName: error?.name,
        errorToString: error?.toString(),
        errorJSON: JSON.stringify(error, Object.getOwnPropertyNames(error))
      });

      // Check specific error types
      if (error?.code) {
        console.error('Error code analysis:', {
          code: error.code,
          isNetworkError: error.code.includes('network'),
          isConfigError: error.code.includes('config'),
          isAuthError: error.code.includes('auth'),
          isCancelledError: error.code === '12501' || error.code === 'SIGN_IN_CANCELLED'
        });
      }

      let errorMessage = 'Giriş yapılırken bir hata oluştu';

      // Güvenli error handling
      try {
        if (error && typeof error === 'object') {
          // Önce error.code'u kontrol et, sonra error.message'ı
          const errorCode = error.code || '';
          const errorMessageFromError = error.message || '';
          const errorString = String(errorCode || errorMessageFromError).toLowerCase();

          console.log('Processing error string:', errorString);

          if (errorString.includes('auth/account-exists-with-different-credential')) {
            errorMessage = 'Bu e-posta adresi farklı bir yöntemle kayıtlı';
          } else if (errorString.includes('auth/invalid-credential')) {
            errorMessage = 'Geçersiz kimlik bilgileri';
          } else if (errorString.includes('auth/operation-not-allowed')) {
            errorMessage = 'Google ile giriş şu anda devre dışı';
          } else if (errorString.includes('auth/user-disabled')) {
            errorMessage = 'Bu hesap devre dışı bırakılmış';
          } else if (errorString.includes('auth/user-not-found')) {
            errorMessage = 'Kullanıcı bulunamadı';
          } else if (errorString.includes('auth/wrong-password')) {
            errorMessage = 'Yanlış şifre';
          } else if (errorString.includes('developer_error')) {
            errorMessage = 'Google Sign-In konfigürasyon hatası. SHA-1 fingerprint eksik olabilir.';
          } else if (errorString.includes('sign_in_cancelled') || errorString.includes('cancelled')) {
            errorMessage = 'Giriş işlemi iptal edildi';
          } else if (errorString.includes('in_progress')) {
            errorMessage = 'Giriş işlemi devam ediyor';
          } else if (errorString.includes('play_services_not_available')) {
            errorMessage = 'Google Play Services mevcut değil';
          } else if (errorString.includes('network_error') || errorString.includes('network-request-failed')) {
            errorMessage = 'İnternet bağlantınızı kontrol edin';
          } else if (errorString.includes('google sign-in did not return a valid idtoken')) {
            errorMessage = 'Google giriş işlemi tamamlanamadı. Lütfen tekrar deneyin.';
          }
        } else if (error === null || error === undefined) {
          errorMessage = 'Bilinmeyen bir hata oluştu. Lütfen tekrar deneyin.';
          console.error('Google sign-in error is null/undefined');
        }
      } catch (errorHandlingError) {
        console.error('Error while handling Google sign-in error:', errorHandlingError);
        errorMessage = 'Giriş yapılırken beklenmeyen bir hata oluştu';
      }

      return { success: false, error: errorMessage };
    }
  }

  // Check for data conflicts between guest and authenticated user
  async checkDataConflict() {
    try {
      console.log('🔄 DATA CONFLICT: Starting data conflict check...');

      // Check if there's guest data
      const guestExpenses = await AsyncStorage.getItem('expenses_guest');
      const guestExpenseData = guestExpenses ? JSON.parse(guestExpenses) : [];

      console.log('🔄 DATA CONFLICT: Guest data check:', {
        hasGuestData: guestExpenseData.length > 0,
        guestExpenseCount: guestExpenseData.length
      });

      if (guestExpenseData.length === 0) {
        console.log('✅ DATA CONFLICT: No guest data found, no conflict');
        return { hasConflict: false, guestDataCount: 0 };
      }

      // There is guest data, we need to handle migration
      console.log('⚠️ DATA CONFLICT: Guest data found, migration needed');

      // Import DataManager dynamically to avoid circular dependency
      const { DataManager } = await import('./DataManager');
      console.log('🔄 DATA CONFLICT: DataManager imported:', {
        DataManager,
        type: typeof DataManager,
        hasMethod: typeof DataManager?.migrateGuestDataToUser
      });

      console.log('🔄 DATA CONFLICT: Starting migration...');
      const migrationResult = await DataManager.migrateGuestDataToUser();
      console.log('✅ DATA CONFLICT: Migration completed:', migrationResult);

      return {
        hasConflict: true,
        guestDataCount: guestExpenseData.length,
        migrationResult
      };
    } catch (error) {
      console.error('❌ DATA CONFLICT ERROR:', error);
      return {
        hasConflict: false,
        error: error.message,
        guestDataCount: 0
      };
    }
  }

  // Sign in as guest
  async signInAsGuest() {
    try {
      // Set guest mode flag
      await AsyncStorage.setItem('guest_mode', 'true');
      // Sign out from Firebase if signed in
      if (auth().currentUser) {
        await auth().signOut();
      }
      // Sign out from Google if signed in (with safety checks)
      try {
        if (GoogleSignin && typeof GoogleSignin.isSignedIn === 'function') {
          const isSignedIn = await GoogleSignin.isSignedIn();
          if (isSignedIn) {
            await GoogleSignin.signOut();
            console.log('Google Sign-Out successful before guest mode');
          }
        } else {
          console.log('GoogleSignin.isSignedIn not available - skipping Google sign out');
        }
      } catch (googleError) {
        console.warn('Google Sign-Out failed before guest mode:', googleError);
        // Continue with guest mode setup
      }
      this.isGuestMode = true;
      this.currentUser = {
        id: 'guest',
        email: null,
        name: 'Misafir Kullanıcı',
        authProvider: 'guest',
        isGuest: true
      };
      this.notifyAuthStateListeners();
      console.log('Guest mode activated');
      return { success: true, user: this.currentUser };
    } catch (error) {
      console.error('Guest sign-in error:', error);
      return { success: false, error: 'Misafir moduna geçilirken hata oluştu' };
    }
  }
  // Sign out
  async signOut() {
    try {
      if (this.isGuestMode) {
        // Clear guest mode
        await AsyncStorage.removeItem('guest_mode');
        this.isGuestMode = false;
      } else {
        // Sign out from Firebase
        await auth().signOut();
        // Sign out from Google (with safety checks)
        try {
          if (GoogleSignin && typeof GoogleSignin.isSignedIn === 'function') {
            const isSignedIn = await GoogleSignin.isSignedIn();
            if (isSignedIn) {
              await GoogleSignin.signOut();
              console.log('Google Sign-Out successful');
            }
          } else {
            console.log('GoogleSignin.isSignedIn not available - skipping Google sign out');
          }
        } catch (googleError) {
          console.warn('Google Sign-Out failed:', googleError);
          // Continue with the rest of sign out process
        }
        // Clear user authenticated flag
        await AsyncStorage.removeItem('user_authenticated');
      }
      this.currentUser = null;
      this.notifyAuthStateListeners();
      console.log('Sign out successful');
      return { success: true };
    } catch (error) {
      console.error('Sign out error:', error);
      return { success: false, error: 'Çıkış yapılırken hata oluştu' };
    }
  }
  // Get current user
  getCurrentUser() {
    return this.currentUser;
  }
  // Check if user is authenticated
  isAuthenticated() {
    return this.currentUser !== null;
  }
  // Check if in guest mode
  isInGuestMode() {
    return this.isGuestMode;
  }
  // Add auth state listener
  addAuthStateListener(listener) {
    this.authStateListeners.push(listener);
    // Return unsubscribe function
    return () => {
      const index = this.authStateListeners.indexOf(listener);
      if (index > -1) {
        this.authStateListeners.splice(index, 1);
      }
    };
  }
  // Notify all auth state listeners
  notifyAuthStateListeners() {
    console.log('Notifying auth state listeners:', {
      currentUser: this.currentUser?.email || this.currentUser?.name,
      isGuestMode: this.isGuestMode,
      listenerCount: this.authStateListeners.length
    });
    this.authStateListeners.forEach(listener => {
      try {
        listener(this.currentUser, this.isGuestMode);
      } catch (error) {
        console.error('Error in auth state listener:', error);
      }
    });
  }
  // Update user profile
  async updateProfile(updates) {
    try {
      if (this.isGuestMode) {
        // Update guest user info locally
        this.currentUser = { ...this.currentUser, ...updates };
        this.notifyAuthStateListeners();
        return { success: true };
      }
      if (!auth().currentUser) {
        return { success: false, error: 'Kullanıcı oturumu bulunamadı' };
      }
      // Update Firebase profile
      await auth().currentUser.updateProfile(updates);
      // Update local user info
      this.currentUser = { ...this.currentUser, ...updates };
      this.notifyAuthStateListeners();
      return { success: true };
    } catch (error) {
      console.error('Profile update error:', error);
      return { success: false, error: 'Profil güncellenirken hata oluştu' };
    }
  }
  // Delete account
  async deleteAccount() {
    try {
      if (this.isGuestMode) {
        // Just clear guest mode
        await this.signOut();
        return { success: true };
      }
      if (!auth().currentUser) {
        return { success: false, error: 'Kullanıcı oturumu bulunamadı' };
      }
      // Delete Firebase account
      await auth().currentUser.delete();
      // Sign out from Google (with safety checks)
      try {
        if (GoogleSignin && typeof GoogleSignin.isSignedIn === 'function') {
          const isSignedIn = await GoogleSignin.isSignedIn();
          if (isSignedIn) {
            await GoogleSignin.signOut();
            console.log('Google Sign-Out successful after account deletion');
          }
        } else {
          console.log('GoogleSignin.isSignedIn not available - skipping Google sign out');
        }
      } catch (googleError) {
        console.warn('Google Sign-Out failed after account deletion:', googleError);
        // Continue with cleanup
      }
      this.currentUser = null;
      this.notifyAuthStateListeners();
      return { success: true };
    } catch (error) {
      console.error('Account deletion error:', error);
      return { success: false, error: 'Hesap silinirken hata oluştu' };
    }
  }
  // Re-authenticate user (required for sensitive operations)
  async reauthenticate() {
    try {
      if (this.isGuestMode) {
        return { success: true }; // Guest mode doesn't need re-authentication
      }
      // Get fresh Google credentials (with safety checks)
      if (!GoogleSignin || typeof GoogleSignin.signIn !== 'function') {
        throw new Error('GoogleSignin not available for re-authentication');
      }
      const { idToken } = await GoogleSignin.signIn();
      const googleCredential = auth.GoogleAuthProvider.credential(idToken);
      // Re-authenticate with Firebase
      await auth().currentUser.reauthenticateWithCredential(googleCredential);
      return { success: true };
    } catch (error) {
      console.error('Re-authentication error:', error);
      return { success: false, error: 'Yeniden kimlik doğrulama başarısız' };
    }
  }
  // Get user preferences
  async getUserPreferences() {
    try {
      const userId = this.currentUser?.id || 'guest';
      const preferences = await AsyncStorage.getItem(`user_preferences_${userId}`);
      return preferences ? JSON.parse(preferences) : {
        currency: 'TRY',
        language: 'tr',
        theme: 'light',
        notifications: true,
        defaultSeason: 'spring'
      };
    } catch (error) {
      console.error('Error getting user preferences:', error);
      return {};
    }
  }
  // Save user preferences
  async saveUserPreferences(preferences) {
    try {
      const userId = this.currentUser?.id || 'guest';
      await AsyncStorage.setItem(`user_preferences_${userId}`, JSON.stringify(preferences));
      return { success: true };
    } catch (error) {
      console.error('Error saving user preferences:', error);
      return { success: false, error: 'Tercihler kaydedilirken hata oluştu' };
    }
  }

  // Enhanced helper methods for guest mode detection

  // Check if current user is authenticated (not guest)
  // Note: This method is already defined above, this duplicate definition has been removed

  // Check if backend operations should be allowed
  shouldUseBackend() {
    return this.isAuthenticated() && !this.isGuestMode;
  }

  // Get user mode description
  getUserMode() {
    if (this.isGuestMode) {
      return 'guest';
    } else if (this.isAuthenticated()) {
      return 'authenticated';
    } else {
      return 'unauthenticated';
    }
  }

  // Check if user can access cloud features
  canAccessCloudFeatures() {
    return this.shouldUseBackend();
  }
}
export default new AuthService();
