/**
 * Data Migration Service for Çiftçi Not Defterim
 * Handles migration from AsyncStorage to SQLite and version upgrades
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import Logger, { LogCategory } from '../utils/Logger';
import { DefaultCategories, DefaultSeasons } from '../models/DataModels';

class MigrationService {
  constructor() {
    this.currentVersion = '1.0.0';
    this.migrations = new Map();
    this.setupMigrations();
  }

  // Setup available migrations
  setupMigrations() {
    // Migration from AsyncStorage to SQLite (v0.9.0 -> v1.0.0)
    this.migrations.set('0.9.0_to_1.0.0', {
      version: '1.0.0',
      description: 'Migrate from AsyncStorage to SQLite',
      migrate: this.migrateAsyncStorageToSQLite.bind(this),
    });

    // Future migrations can be added here
    // this.migrations.set('1.0.0_to_1.1.0', {
    //   version: '1.1.0',
    //   description: 'Add new features',
    //   migrate: this.migrateToV1_1_0.bind(this),
    // });
  }

  // Check if migration is needed
  async checkMigrationNeeded() {
    try {
      const currentAppVersion = await this.getCurrentAppVersion();
      const lastMigrationVersion = await this.getLastMigrationVersion();

      Logger.info(LogCategory.DATABASE, 'Migration check', {
        currentAppVersion,
        lastMigrationVersion,
        targetVersion: this.currentVersion,
      });

      // If no previous migration version, check for AsyncStorage data
      if (!lastMigrationVersion) {
        const hasAsyncStorageData = await this.hasAsyncStorageData();
        if (hasAsyncStorageData) {
          return {
            needed: true,
            fromVersion: '0.9.0',
            toVersion: this.currentVersion,
            type: 'initial_migration',
          };
        }
      }

      // Check for version upgrades
      if (lastMigrationVersion && lastMigrationVersion !== this.currentVersion) {
        return {
          needed: true,
          fromVersion: lastMigrationVersion,
          toVersion: this.currentVersion,
          type: 'version_upgrade',
        };
      }

      return { needed: false };
    } catch (error) {
      Logger.error(LogCategory.DATABASE, 'Migration check failed', error);
      return { needed: false, error: error.message };
    }
  }

  // Perform migration
  async performMigration(db) {
    try {
      const migrationInfo = await this.checkMigrationNeeded();
      
      if (!migrationInfo.needed) {
        Logger.info(LogCategory.DATABASE, 'No migration needed');
        return { success: true, message: 'No migration needed' };
      }

      Logger.info(LogCategory.DATABASE, 'Starting migration', migrationInfo);

      const migrationKey = `${migrationInfo.fromVersion}_to_${migrationInfo.toVersion}`;
      const migration = this.migrations.get(migrationKey);

      if (!migration) {
        throw new Error(`No migration found for ${migrationKey}`);
      }

      // Create backup before migration
      await this.createBackup();

      // Perform migration
      const result = await migration.migrate(db);

      if (result.success) {
        // Update migration version
        await this.setLastMigrationVersion(this.currentVersion);
        Logger.info(LogCategory.DATABASE, 'Migration completed successfully', result);
      }

      return result;
    } catch (error) {
      Logger.error(LogCategory.DATABASE, 'Migration failed', error);
      return { success: false, error: error.message };
    }
  }

  // Migrate from AsyncStorage to SQLite
  async migrateAsyncStorageToSQLite(db) {
    try {
      Logger.info(LogCategory.DATABASE, 'Starting AsyncStorage to SQLite migration');

      let migratedItems = {
        categories: 0,
        expenses: 0,
        settings: 0,
      };

      // Migrate categories
      const categoriesResult = await this.migrateCategoriesData(db);
      migratedItems.categories = categoriesResult.count;

      // Migrate expenses
      const expensesResult = await this.migrateExpensesData(db);
      migratedItems.expenses = expensesResult.count;

      // Migrate settings
      const settingsResult = await this.migrateSettingsData();
      migratedItems.settings = settingsResult.count;

      // Mark migration as completed
      await AsyncStorage.setItem('migration_completed', 'true');
      await AsyncStorage.setItem('migration_timestamp', new Date().toISOString());

      Logger.info(LogCategory.DATABASE, 'AsyncStorage migration completed', migratedItems);

      return {
        success: true,
        message: 'AsyncStorage migration completed',
        migratedItems,
      };
    } catch (error) {
      Logger.error(LogCategory.DATABASE, 'AsyncStorage migration failed', error);
      throw error;
    }
  }

  // Migrate categories data
  async migrateCategoriesData(db) {
    try {
      const storedCategories = await AsyncStorage.getItem('categories');
      let count = 0;

      if (storedCategories) {
        const categories = JSON.parse(storedCategories);
        
        for (const category of categories) {
          try {
            await db.executeSql(
              `INSERT OR REPLACE INTO categories (id, name, emoji, color, icon, is_default, created_at, updated_at) 
               VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
              [
                category.id,
                category.name,
                category.emoji || '📝',
                category.color || '#607D8B',
                category.icon || 'more-horiz',
                DefaultCategories.some(dc => dc.id === category.id) ? 1 : 0,
                new Date().toISOString(),
                new Date().toISOString()
              ]
            );
            count++;
          } catch (error) {
            Logger.warn(LogCategory.DATABASE, `Failed to migrate category ${category.id}`, error);
          }
        }
      }

      // Ensure default categories exist
      for (const defaultCategory of DefaultCategories) {
        try {
          await db.executeSql(
            `INSERT OR IGNORE INTO categories (id, name, emoji, color, icon, is_default, created_at, updated_at) 
             VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
            [
              defaultCategory.id,
              defaultCategory.name,
              defaultCategory.emoji,
              defaultCategory.color,
              defaultCategory.icon,
              1,
              new Date().toISOString(),
              new Date().toISOString()
            ]
          );
        } catch (error) {
          Logger.warn(LogCategory.DATABASE, `Failed to add default category ${defaultCategory.id}`, error);
        }
      }

      return { success: true, count };
    } catch (error) {
      Logger.error(LogCategory.DATABASE, 'Categories migration failed', error);
      throw error;
    }
  }

  // Migrate expenses data
  async migrateExpensesData(db) {
    try {
      const storedExpenses = await AsyncStorage.getItem('expenses');
      let count = 0;

      if (storedExpenses) {
        const expenses = JSON.parse(storedExpenses);
        
        for (const expense of expenses) {
          try {
            const seasonId = this.getSeasonFromDate(new Date(expense.date));
            
            await db.executeSql(
              `INSERT INTO expenses (id, category_id, season_id, amount, description, date, created_at, updated_at) 
               VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
              [
                expense.id || Date.now().toString() + Math.random().toString(36).substr(2, 9),
                expense.categoryId,
                seasonId,
                expense.amount,
                expense.description || '',
                expense.date,
                expense.createdAt || new Date().toISOString(),
                expense.updatedAt || new Date().toISOString()
              ]
            );
            count++;
          } catch (error) {
            Logger.warn(LogCategory.DATABASE, `Failed to migrate expense ${expense.id}`, error);
          }
        }
      }

      return { success: true, count };
    } catch (error) {
      Logger.error(LogCategory.DATABASE, 'Expenses migration failed', error);
      throw error;
    }
  }

  // Migrate settings data
  async migrateSettingsData() {
    try {
      let count = 0;
      const settingsKeys = [
        'user_preferences',
        'tutorial_completed',
        'app_version',
        'last_backup',
      ];

      for (const key of settingsKeys) {
        try {
          const value = await AsyncStorage.getItem(key);
          if (value !== null) {
            // Settings are kept in AsyncStorage for now
            count++;
          }
        } catch (error) {
          Logger.warn(LogCategory.DATABASE, `Failed to check setting ${key}`, error);
        }
      }

      return { success: true, count };
    } catch (error) {
      Logger.error(LogCategory.DATABASE, 'Settings migration failed', error);
      throw error;
    }
  }

  // Get season from date
  getSeasonFromDate(date) {
    const month = date.getMonth() + 1; // JavaScript months are 0-based
    
    if (month >= 3 && month <= 5) return 'spring';
    if (month >= 6 && month <= 8) return 'summer';
    if (month >= 9 && month <= 11) return 'autumn';
    return 'winter';
  }

  // Check if AsyncStorage has data
  async hasAsyncStorageData() {
    try {
      const categories = await AsyncStorage.getItem('categories');
      const expenses = await AsyncStorage.getItem('expenses');
      
      return !!(categories || expenses);
    } catch (error) {
      Logger.error(LogCategory.DATABASE, 'Failed to check AsyncStorage data', error);
      return false;
    }
  }

  // Get current app version
  async getCurrentAppVersion() {
    try {
      return await AsyncStorage.getItem('app_version') || '0.9.0';
    } catch (error) {
      return '0.9.0';
    }
  }

  // Get last migration version
  async getLastMigrationVersion() {
    try {
      return await AsyncStorage.getItem('last_migration_version');
    } catch (error) {
      return null;
    }
  }

  // Set last migration version
  async setLastMigrationVersion(version) {
    try {
      await AsyncStorage.setItem('last_migration_version', version);
      await AsyncStorage.setItem('app_version', version);
    } catch (error) {
      Logger.error(LogCategory.DATABASE, 'Failed to set migration version', error);
    }
  }

  // Create backup before migration
  async createBackup() {
    try {
      const backup = {
        timestamp: new Date().toISOString(),
        version: await this.getCurrentAppVersion(),
        data: {},
      };

      // Backup AsyncStorage data
      const keys = await AsyncStorage.getAllKeys();
      for (const key of keys) {
        try {
          const value = await AsyncStorage.getItem(key);
          if (value !== null) {
            backup.data[key] = value;
          }
        } catch (error) {
          Logger.warn(LogCategory.DATABASE, `Failed to backup key ${key}`, error);
        }
      }

      // Store backup
      await AsyncStorage.setItem('migration_backup', JSON.stringify(backup));
      
      Logger.info(LogCategory.DATABASE, 'Migration backup created', {
        keyCount: Object.keys(backup.data).length,
      });

      return { success: true, backup };
    } catch (error) {
      Logger.error(LogCategory.DATABASE, 'Failed to create backup', error);
      throw error;
    }
  }

  // Restore from backup
  async restoreFromBackup() {
    try {
      const backupData = await AsyncStorage.getItem('migration_backup');
      
      if (!backupData) {
        throw new Error('No backup found');
      }

      const backup = JSON.parse(backupData);
      
      // Restore data
      for (const [key, value] of Object.entries(backup.data)) {
        await AsyncStorage.setItem(key, value);
      }

      Logger.info(LogCategory.DATABASE, 'Backup restored successfully', {
        version: backup.version,
        keyCount: Object.keys(backup.data).length,
      });

      return { success: true, backup };
    } catch (error) {
      Logger.error(LogCategory.DATABASE, 'Failed to restore backup', error);
      throw error;
    }
  }

  // Clean up old data after successful migration
  async cleanupOldData() {
    try {
      const keysToRemove = [
        'categories',
        'expenses',
        'migration_backup',
      ];

      for (const key of keysToRemove) {
        await AsyncStorage.removeItem(key);
      }

      Logger.info(LogCategory.DATABASE, 'Old data cleaned up', { keysRemoved: keysToRemove });
      
      return { success: true };
    } catch (error) {
      Logger.error(LogCategory.DATABASE, 'Failed to cleanup old data', error);
      return { success: false, error: error.message };
    }
  }

  // Get migration status
  async getMigrationStatus() {
    try {
      const migrationCompleted = await AsyncStorage.getItem('migration_completed');
      const migrationTimestamp = await AsyncStorage.getItem('migration_timestamp');
      const lastMigrationVersion = await this.getLastMigrationVersion();
      const currentAppVersion = await this.getCurrentAppVersion();

      return {
        completed: migrationCompleted === 'true',
        timestamp: migrationTimestamp,
        lastVersion: lastMigrationVersion,
        currentVersion: currentAppVersion,
        targetVersion: this.currentVersion,
      };
    } catch (error) {
      Logger.error(LogCategory.DATABASE, 'Failed to get migration status', error);
      return null;
    }
  }
}

export default new MigrationService();
