/**
 * Validation Service for Çiftçi Not Defterim
 * Provides comprehensive validation for all data types before migration
 */

import FieldValidationService from './FieldValidationService';
import ExpenseValidationService from './ExpenseValidationService';
import AsyncStorage from '@react-native-async-storage/async-storage';

class ValidationService {
  constructor() {
    this.fieldValidator = FieldValidationService;
    this.expenseValidator = ExpenseValidationService;
  }

  /**
   * Validate all guest data before migration
   * @returns {Object} Comprehensive validation result
   */
  async validateAllGuestData() {
    try {
      console.log('🔍 ValidationService: Starting comprehensive guest data validation...');

      const validationResults = {
        isValid: true,
        errors: [],
        warnings: [],
        summary: {
          expenses: { total: 0, valid: 0, invalid: 0 },
          fields: { total: 0, valid: 0, invalid: 0 },
          categories: { total: 0, valid: 0, invalid: 0 }
        },
        details: {
          expenses: [],
          fields: [],
          categories: []
        }
      };

      // Validate expenses
      const expenseValidation = await this.validateGuestExpenses();
      validationResults.summary.expenses = expenseValidation.summary;
      validationResults.details.expenses = expenseValidation.details;
      if (!expenseValidation.isValid) {
        validationResults.isValid = false;
        validationResults.errors.push(...expenseValidation.errors);
      }
      validationResults.warnings.push(...expenseValidation.warnings);

      // Validate fields
      const fieldValidation = await this.validateGuestFields();
      validationResults.summary.fields = fieldValidation.summary;
      validationResults.details.fields = fieldValidation.details;
      if (!fieldValidation.isValid) {
        validationResults.isValid = false;
        validationResults.errors.push(...fieldValidation.errors);
      }
      validationResults.warnings.push(...fieldValidation.warnings);

      // Validate categories
      const categoryValidation = await this.validateGuestCategories();
      validationResults.summary.categories = categoryValidation.summary;
      validationResults.details.categories = categoryValidation.details;
      if (!categoryValidation.isValid) {
        validationResults.isValid = false;
        validationResults.errors.push(...categoryValidation.errors);
      }
      validationResults.warnings.push(...categoryValidation.warnings);

      console.log('✅ ValidationService: Validation completed', {
        isValid: validationResults.isValid,
        totalErrors: validationResults.errors.length,
        totalWarnings: validationResults.warnings.length
      });

      return validationResults;

    } catch (error) {
      console.error('❌ ValidationService: Validation failed:', error);
      return {
        isValid: false,
        errors: [`Validation system error: ${error.message}`],
        warnings: [],
        summary: { expenses: {}, fields: {}, categories: {} },
        details: { expenses: [], fields: [], categories: [] }
      };
    }
  }

  /**
   * Validate guest expenses using ExpenseValidationService
   */
  async validateGuestExpenses() {
    try {
      const guestExpenses = await AsyncStorage.getItem('expenses_guest');
      const expenseData = guestExpenses ? JSON.parse(guestExpenses) : [];

      const validation = this.expenseValidator.validateExpenses(expenseData);

      return {
        isValid: validation.isValid,
        errors: validation.errors,
        warnings: validation.validExpenses.flatMap(e => e.warnings),
        summary: {
          total: validation.totalCount,
          valid: validation.validCount,
          invalid: validation.invalidCount
        },
        details: [
          ...validation.validExpenses.map(e => ({ ...e, isValid: true })),
          ...validation.invalidExpenses.map(e => ({ ...e, isValid: false }))
        ]
      };

    } catch (error) {
      return {
        isValid: false,
        errors: [`Expense validation error: ${error.message}`],
        warnings: [],
        summary: { total: 0, valid: 0, invalid: 0 },
        details: []
      };
    }
  }

  /**
   * Validate guest fields
   */
  async validateGuestFields() {
    try {
      const guestFields = await AsyncStorage.getItem('fields_guest');
      const fieldData = guestFields ? JSON.parse(guestFields) : [];

      const validation = this.fieldValidator.validateFields(fieldData);

      return {
        isValid: validation.isValid,
        errors: validation.errors,
        warnings: validation.validFields.flatMap(f => f.warnings),
        summary: {
          total: validation.totalCount,
          valid: validation.validCount,
          invalid: validation.invalidCount
        },
        details: [
          ...validation.validFields.map(f => ({ ...f, isValid: true })),
          ...validation.invalidFields.map(f => ({ ...f, isValid: false }))
        ]
      };

    } catch (error) {
      return {
        isValid: false,
        errors: [`Field validation error: ${error.message}`],
        warnings: [],
        summary: { total: 0, valid: 0, invalid: 0 },
        details: []
      };
    }
  }

  /**
   * Validate guest categories
   */
  async validateGuestCategories() {
    try {
      const guestCategories = await AsyncStorage.getItem('categories_guest');
      const categoryData = guestCategories ? JSON.parse(guestCategories) : [];

      const validation = {
        isValid: true,
        errors: [],
        warnings: [],
        summary: { total: categoryData.length, valid: 0, invalid: 0 },
        details: []
      };

      categoryData.forEach((category, index) => {
        const categoryValidation = this.validateCategory(category, index);
        
        if (categoryValidation.isValid) {
          validation.summary.valid++;
        } else {
          validation.summary.invalid++;
          validation.isValid = false;
          validation.errors.push(...categoryValidation.errors);
        }

        validation.details.push(categoryValidation);
      });

      return validation;

    } catch (error) {
      return {
        isValid: false,
        errors: [`Category validation error: ${error.message}`],
        warnings: [],
        summary: { total: 0, valid: 0, invalid: 0 },
        details: []
      };
    }
  }



  /**
   * Validate single category
   */
  validateCategory(category, index) {
    const errors = [];
    const warnings = [];

    // Name validation
    if (!category.name || category.name.trim().length === 0) {
      errors.push(`Kategori ${index + 1}: İsim eksik`);
    } else if (category.name.length > 50) {
      errors.push(`Kategori ${index + 1}: İsim çok uzun (max 50 karakter)`);
    }

    // Color validation
    if (category.color && !/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(category.color)) {
      warnings.push(`Kategori ${index + 1}: Geçersiz renk formatı`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      index,
      category
    };
  }

  /**
   * Generate validation report for user
   */
  generateValidationReport(validationResults) {
    const report = {
      title: 'Veri Doğrulama Raporu',
      summary: '',
      canMigrate: validationResults.isValid,
      sections: []
    };

    // Summary
    const totalItems = Object.values(validationResults.summary).reduce((sum, section) => sum + section.total, 0);
    const totalErrors = validationResults.errors.length;
    const totalWarnings = validationResults.warnings.length;

    if (validationResults.isValid) {
      report.summary = `✅ Tüm veriler migration için hazır (${totalItems} öğe kontrol edildi)`;
    } else {
      report.summary = `❌ ${totalErrors} hata tespit edildi. Migration öncesi düzeltme gerekli.`;
    }

    // Add sections
    if (totalErrors > 0) {
      report.sections.push({
        title: '🚨 Düzeltilmesi Gereken Hatalar',
        items: validationResults.errors,
        type: 'error'
      });
    }

    if (totalWarnings > 0) {
      report.sections.push({
        title: '⚠️ Uyarılar',
        items: validationResults.warnings,
        type: 'warning'
      });
    }

    // Add summary section
    report.sections.push({
      title: '📊 Özet',
      items: [
        `Giderler: ${validationResults.summary.expenses.valid}/${validationResults.summary.expenses.total} geçerli`,
        `Tarlalar: ${validationResults.summary.fields.valid}/${validationResults.summary.fields.total} geçerli`,
        `Kategoriler: ${validationResults.summary.categories.valid}/${validationResults.summary.categories.total} geçerli`
      ],
      type: 'info'
    });

    return report;
  }

  /**
   * Generate fix suggestions for validation errors
   * @param {Object} validationResults - Validation results
   * @returns {Object} Fix suggestions
   */
  generateFixSuggestions(validationResults) {
    const suggestions = {
      canAutoFix: false,
      autoFixCount: 0,
      manualFixCount: 0,
      suggestions: []
    };

    // Analyze field errors for auto-fix opportunities
    validationResults.details.fields.forEach((field, index) => {
      if (!field.isValid) {
        field.errors.forEach(error => {
          if (error.includes('negatif olamaz')) {
            suggestions.suggestions.push({
              type: 'auto-fix',
              target: `field-${index}`,
              error: error,
              suggestion: 'Alan değeri otomatik olarak pozitif yapılacak',
              action: 'fix-negative-area'
            });
            suggestions.autoFixCount++;
          } else if (error.includes('çok uzun')) {
            suggestions.suggestions.push({
              type: 'auto-fix',
              target: `field-${index}`,
              error: error,
              suggestion: 'Metin otomatik olarak kısaltılacak',
              action: 'truncate-text'
            });
            suggestions.autoFixCount++;
          } else if (error.includes('zorunludur')) {
            suggestions.suggestions.push({
              type: 'manual-fix',
              target: `field-${index}`,
              error: error,
              suggestion: 'Bu alan doldurulmalı veya tarla silinmelidir',
              action: 'fill-required-field'
            });
            suggestions.manualFixCount++;
          }
        });
      }
    });

    // Analyze expense errors for auto-fix opportunities
    validationResults.details.expenses.forEach((expense, index) => {
      if (!expense.isValid) {
        expense.errors.forEach(error => {
          if (error.includes('Tutar 0\'dan büyük olmalıdır')) {
            suggestions.suggestions.push({
              type: 'manual-fix',
              target: `expense-${index}`,
              error: error,
              suggestion: 'Geçerli bir tutar giriniz veya gideri siliniz',
              action: 'fix-amount'
            });
            suggestions.manualFixCount++;
          } else if (error.includes('çok uzun')) {
            suggestions.suggestions.push({
              type: 'auto-fix',
              target: `expense-${index}`,
              error: error,
              suggestion: 'Açıklama otomatik olarak kısaltılacak',
              action: 'truncate-description'
            });
            suggestions.autoFixCount++;
          } else if (error.includes('zorunludur')) {
            suggestions.suggestions.push({
              type: 'manual-fix',
              target: `expense-${index}`,
              error: error,
              suggestion: 'Bu alan doldurulmalı veya gider silinmelidir',
              action: 'fill-required-field'
            });
            suggestions.manualFixCount++;
          }
        });
      }
    });

    suggestions.canAutoFix = suggestions.autoFixCount > 0;

    return suggestions;
  }

  /**
   * Apply automatic fixes to validation errors
   * @param {Object} validationResults - Validation results
   * @returns {Object} Fix results
   */
  async applyAutoFixes(validationResults) {
    try {
      console.log('🔧 ValidationService: Applying automatic fixes...');

      const fixResults = {
        success: false,
        fixedCount: 0,
        errors: [],
        fixedItems: []
      };

      // Auto-fix field issues
      const guestFields = await AsyncStorage.getItem('fields_guest');
      const fieldData = guestFields ? JSON.parse(guestFields) : [];
      let fieldsModified = false;

      validationResults.details.fields.forEach((field, index) => {
        if (!field.isValid && fieldData[index]) {
          field.errors.forEach(error => {
            if (error.includes('negatif olamaz') && fieldData[index].size?.value < 0) {
              fieldData[index].size.value = Math.abs(fieldData[index].size.value);
              fixResults.fixedCount++;
              fixResults.fixedItems.push(`Tarla ${index + 1}: Alan değeri pozitif yapıldı`);
              fieldsModified = true;
            }

            if (error.includes('çok uzun')) {
              if (fieldData[index].name && fieldData[index].name.length > 50) {
                fieldData[index].name = fieldData[index].name.substring(0, 50);
                fixResults.fixedCount++;
                fixResults.fixedItems.push(`Tarla ${index + 1}: İsim kısaltıldı`);
                fieldsModified = true;
              }

              if (fieldData[index].notes && fieldData[index].notes.length > 200) {
                fieldData[index].notes = fieldData[index].notes.substring(0, 200);
                fixResults.fixedCount++;
                fixResults.fixedItems.push(`Tarla ${index + 1}: Notlar kısaltıldı`);
                fieldsModified = true;
              }
            }
          });
        }
      });

      // Auto-fix expense issues
      const guestExpenses = await AsyncStorage.getItem('expenses_guest');
      const expenseData = guestExpenses ? JSON.parse(guestExpenses) : [];
      let expensesModified = false;

      validationResults.details.expenses.forEach((expense, index) => {
        if (!expense.isValid && expenseData[index]) {
          expense.errors.forEach(error => {
            if (error.includes('çok uzun')) {
              if (expenseData[index].description && expenseData[index].description.length > 500) {
                expenseData[index].description = expenseData[index].description.substring(0, 500);
                fixResults.fixedCount++;
                fixResults.fixedItems.push(`Gider ${index + 1}: Açıklama kısaltıldı`);
                expensesModified = true;
              }
            }
          });
        }
      });

      // Save fixed data
      if (fieldsModified) {
        await AsyncStorage.setItem('fields_guest', JSON.stringify(fieldData));
      }

      if (expensesModified) {
        await AsyncStorage.setItem('expenses_guest', JSON.stringify(expenseData));
      }

      fixResults.success = fixResults.fixedCount > 0;

      console.log('✅ ValidationService: Auto-fixes applied:', {
        fixedCount: fixResults.fixedCount,
        fieldsModified,
        expensesModified
      });

      return fixResults;

    } catch (error) {
      console.error('❌ ValidationService: Auto-fix failed:', error);
      return {
        success: false,
        fixedCount: 0,
        errors: [error.message],
        fixedItems: []
      };
    }
  }

  /**
   * Validation bypass for advanced users
   * @param {Object} options - Bypass options
   * @returns {Object} Bypass result
   */
  async bypassValidation(options = {}) {
    const {
      skipFieldValidation = false,
      skipExpenseValidation = false,
      skipCategoryValidation = false,
      reason = 'Advanced user bypass'
    } = options;

    console.log('⚠️ ValidationService: Validation bypass requested:', {
      skipFieldValidation,
      skipExpenseValidation,
      skipCategoryValidation,
      reason
    });

    // Create a mock validation result that passes
    const bypassResult = {
      isValid: true,
      errors: [],
      warnings: [`Validation bypassed: ${reason}`],
      summary: {
        expenses: { total: 0, valid: 0, invalid: 0 },
        fields: { total: 0, valid: 0, invalid: 0 },
        categories: { total: 0, valid: 0, invalid: 0 }
      },
      details: {
        expenses: [],
        fields: [],
        categories: []
      },
      bypassed: true,
      bypassOptions: options
    };

    // Still validate non-bypassed types
    if (!skipExpenseValidation) {
      const expenseValidation = await this.validateGuestExpenses();
      bypassResult.summary.expenses = expenseValidation.summary;
      bypassResult.details.expenses = expenseValidation.details;
      if (!expenseValidation.isValid) {
        bypassResult.warnings.push('Expense validation errors ignored due to bypass');
      }
    }

    if (!skipFieldValidation) {
      const fieldValidation = await this.validateGuestFields();
      bypassResult.summary.fields = fieldValidation.summary;
      bypassResult.details.fields = fieldValidation.details;
      if (!fieldValidation.isValid) {
        bypassResult.warnings.push('Field validation errors ignored due to bypass');
      }
    }

    if (!skipCategoryValidation) {
      const categoryValidation = await this.validateGuestCategories();
      bypassResult.summary.categories = categoryValidation.summary;
      bypassResult.details.categories = categoryValidation.details;
      if (!categoryValidation.isValid) {
        bypassResult.warnings.push('Category validation errors ignored due to bypass');
      }
    }

    return bypassResult;
  }

  /**
   * Performance optimized validation for large datasets
   * @param {Object} options - Optimization options
   * @returns {Object} Validation result
   */
  async validateAllGuestDataOptimized(options = {}) {
    const {
      batchSize = 100,
      skipDetailedValidation = false,
      prioritizeErrors = true
    } = options;

    try {
      console.log('🚀 ValidationService: Starting optimized validation...');
      const startTime = Date.now();

      if (skipDetailedValidation) {
        // Quick validation - only check critical fields
        return await this.quickValidation();
      }

      // Batch validation for large datasets
      const validationResults = {
        isValid: true,
        errors: [],
        warnings: [],
        summary: {
          expenses: { total: 0, valid: 0, invalid: 0 },
          fields: { total: 0, valid: 0, invalid: 0 },
          categories: { total: 0, valid: 0, invalid: 0 }
        },
        details: {
          expenses: [],
          fields: [],
          categories: []
        },
        performance: {
          startTime,
          endTime: null,
          duration: null,
          optimized: true
        }
      };

      // Process in batches to avoid blocking UI
      const expenseValidation = await this.validateGuestExpensesBatched(batchSize);
      validationResults.summary.expenses = expenseValidation.summary;
      validationResults.details.expenses = expenseValidation.details;

      if (!expenseValidation.isValid) {
        validationResults.isValid = false;
        if (prioritizeErrors) {
          validationResults.errors.push(...expenseValidation.errors.slice(0, 10)); // Limit errors
        } else {
          validationResults.errors.push(...expenseValidation.errors);
        }
      }

      const fieldValidation = await this.validateGuestFieldsBatched(batchSize);
      validationResults.summary.fields = fieldValidation.summary;
      validationResults.details.fields = fieldValidation.details;

      if (!fieldValidation.isValid) {
        validationResults.isValid = false;
        if (prioritizeErrors) {
          validationResults.errors.push(...fieldValidation.errors.slice(0, 10));
        } else {
          validationResults.errors.push(...fieldValidation.errors);
        }
      }

      const endTime = Date.now();
      validationResults.performance.endTime = endTime;
      validationResults.performance.duration = endTime - startTime;

      console.log('✅ ValidationService: Optimized validation completed:', {
        duration: validationResults.performance.duration,
        totalErrors: validationResults.errors.length
      });

      return validationResults;

    } catch (error) {
      console.error('❌ ValidationService: Optimized validation failed:', error);
      return {
        isValid: false,
        errors: [`Optimized validation error: ${error.message}`],
        warnings: [],
        summary: {},
        details: {},
        performance: { error: true }
      };
    }
  }

  /**
   * Quick validation - only critical checks
   */
  async quickValidation() {
    const guestExpenses = await AsyncStorage.getItem('expenses_guest');
    const guestFields = await AsyncStorage.getItem('fields_guest');

    const expenseData = guestExpenses ? JSON.parse(guestExpenses) : [];
    const fieldData = guestFields ? JSON.parse(guestFields) : [];

    const errors = [];

    // Quick expense checks
    expenseData.forEach((expense, index) => {
      if (!expense.amount || expense.amount <= 0) {
        errors.push(`Gider ${index + 1}: Geçersiz tutar`);
      }
      if (!expense.categoryId) {
        errors.push(`Gider ${index + 1}: Kategori eksik`);
      }
    });

    // Quick field checks
    fieldData.forEach((field, index) => {
      if (!field.name || field.name.trim().length === 0) {
        errors.push(`Tarla ${index + 1}: İsim eksik`);
      }
    });

    return {
      isValid: errors.length === 0,
      errors: errors.slice(0, 20), // Limit errors for performance
      warnings: ['Quick validation performed - some checks skipped'],
      summary: {
        expenses: { total: expenseData.length, valid: 0, invalid: 0 },
        fields: { total: fieldData.length, valid: 0, invalid: 0 },
        categories: { total: 0, valid: 0, invalid: 0 }
      },
      details: { expenses: [], fields: [], categories: [] },
      quickValidation: true
    };
  }

  /**
   * Batched expense validation for performance
   */
  async validateGuestExpensesBatched(batchSize = 100) {
    const guestExpenses = await AsyncStorage.getItem('expenses_guest');
    const expenseData = guestExpenses ? JSON.parse(guestExpenses) : [];

    if (expenseData.length <= batchSize) {
      return this.validateGuestExpenses(); // Use regular validation for small datasets
    }

    // Process in batches
    const validation = {
      isValid: true,
      errors: [],
      warnings: [],
      summary: { total: expenseData.length, valid: 0, invalid: 0 },
      details: []
    };

    for (let i = 0; i < expenseData.length; i += batchSize) {
      const batch = expenseData.slice(i, i + batchSize);
      const batchValidation = this.expenseValidator.validateExpenses(batch);

      validation.summary.valid += batchValidation.validCount;
      validation.summary.invalid += batchValidation.invalidCount;

      if (!batchValidation.isValid) {
        validation.isValid = false;
        validation.errors.push(...batchValidation.errors);
      }

      // Add small delay to prevent blocking
      if (i + batchSize < expenseData.length) {
        await new Promise(resolve => setTimeout(resolve, 1));
      }
    }

    return validation;
  }

  /**
   * Batched field validation for performance
   */
  async validateGuestFieldsBatched(batchSize = 100) {
    const guestFields = await AsyncStorage.getItem('fields_guest');
    const fieldData = guestFields ? JSON.parse(guestFields) : [];

    if (fieldData.length <= batchSize) {
      return this.validateGuestFields(); // Use regular validation for small datasets
    }

    // Process in batches
    const validation = {
      isValid: true,
      errors: [],
      warnings: [],
      summary: { total: fieldData.length, valid: 0, invalid: 0 },
      details: []
    };

    for (let i = 0; i < fieldData.length; i += batchSize) {
      const batch = fieldData.slice(i, i + batchSize);
      const batchValidation = this.fieldValidator.validateFields(batch);

      validation.summary.valid += batchValidation.validCount;
      validation.summary.invalid += batchValidation.invalidCount;

      if (!batchValidation.isValid) {
        validation.isValid = false;
        validation.errors.push(...batchValidation.errors);
      }

      // Add small delay to prevent blocking
      if (i + batchSize < fieldData.length) {
        await new Promise(resolve => setTimeout(resolve, 1));
      }
    }

    return validation;
  }
}

export default new ValidationService();
