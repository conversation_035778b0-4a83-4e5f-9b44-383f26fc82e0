/**
 * Tutorial Service for Çiftçi Not Defterim
 * Manages tutorial steps, progress tracking, and tutorial data
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import Logger, { LogCategory } from '../utils/Logger';
import Analytics, { AnalyticsEvent } from '../utils/Analytics';

// Tutorial types
export const TutorialType = {
  ONBOARDING: 'onboarding',
  EXPENSE_CREATION: 'expense_creation',
  CATEGORY_MANAGEMENT: 'category_management',
  REPORTING: 'reporting',
  SEASONAL_TRACKING: 'seasonal_tracking',
};

// Tutorial step types
export const StepType = {
  HIGHLIGHT: 'highlight',
  MODAL: 'modal',
  TOOLTIP: 'tooltip',
  ACTION: 'action',
  VALIDATION: 'validation',
};

class TutorialService {
  constructor() {
    this.tutorials = new Map();
    this.completedTutorials = new Set();
    this.currentTutorial = null;
    this.currentStep = 0;
    this.listeners = [];
    
    this.setupTutorials();
  }

  // Initialize tutorial service
  async initialize() {
    try {
      await this.loadCompletedTutorials();
      Logger.info(LogCategory.USER_ACTION, 'TutorialService initialized', {
        completedCount: this.completedTutorials.size,
      });
    } catch (error) {
      Logger.error(LogCategory.USER_ACTION, 'TutorialService initialization failed', error);
    }
  }

  // Setup available tutorials
  setupTutorials() {
    // Onboarding tutorial
    this.tutorials.set(TutorialType.ONBOARDING, {
      id: TutorialType.ONBOARDING,
      title: 'Uygulamaya Hoş Geldiniz',
      description: 'Çiftçi Not Defterim\'in temel özelliklerini öğrenin',
      steps: [
        {
          id: 'welcome',
          type: StepType.MODAL,
          title: 'Hoş Geldiniz!',
          description: 'Çiftçi Not Defterim ile tarımsal giderlerinizi kolayca takip edebilirsiniz.',
          target: null,
        },
        {
          id: 'navigation',
          type: StepType.HIGHLIGHT,
          title: 'Ana Menü',
          description: 'Alt menüden farklı bölümlere erişebilirsiniz.',
          target: { x: 0, y: 700, width: 400, height: 80 }, // Bottom tab bar
          tooltipPosition: 'top',
        },
        {
          id: 'add_expense',
          type: StepType.HIGHLIGHT,
          title: 'Gider Ekle',
          description: 'Yeni gider eklemek için bu butona dokunun.',
          target: { x: 320, y: 650, width: 60, height: 60 }, // Add button
          tooltipPosition: 'top',
        },
      ],
    });

    // Expense creation tutorial
    this.tutorials.set(TutorialType.EXPENSE_CREATION, {
      id: TutorialType.EXPENSE_CREATION,
      title: 'Gider Ekleme',
      description: 'Yeni gider nasıl eklenir öğrenin',
      steps: [
        {
          id: 'category_selection',
          type: StepType.HIGHLIGHT,
          title: 'Kategori Seçin',
          description: 'Önce giderinizin kategorisini seçin.',
          target: { x: 20, y: 200, width: 360, height: 120 },
          tooltipPosition: 'bottom',
        },
        {
          id: 'amount_input',
          type: StepType.HIGHLIGHT,
          title: 'Tutar Girin',
          description: 'Gider tutarını girin. Virgül veya nokta kullanabilirsiniz.',
          target: { x: 20, y: 340, width: 360, height: 60 },
          tooltipPosition: 'bottom',
        },
        {
          id: 'date_selection',
          type: StepType.HIGHLIGHT,
          title: 'Tarih Seçin',
          description: 'Gider tarihini seçin veya değiştirin.',
          target: { x: 20, y: 420, width: 360, height: 60 },
          tooltipPosition: 'bottom',
        },
        {
          id: 'description',
          type: StepType.HIGHLIGHT,
          title: 'Açıklama (İsteğe Bağlı)',
          description: 'Gideriniz hakkında detay ekleyebilirsiniz.',
          target: { x: 20, y: 500, width: 360, height: 80 },
          tooltipPosition: 'top',
        },
        {
          id: 'save_expense',
          type: StepType.HIGHLIGHT,
          title: 'Kaydet',
          description: 'Giderinizi kaydetmek için bu butona dokunun.',
          target: { x: 20, y: 600, width: 360, height: 50 },
          tooltipPosition: 'top',
        },
      ],
    });

    // Category management tutorial
    this.tutorials.set(TutorialType.CATEGORY_MANAGEMENT, {
      id: TutorialType.CATEGORY_MANAGEMENT,
      title: 'Kategori Yönetimi',
      description: 'Kategorileri nasıl yönetirsiniz öğrenin',
      steps: [
        {
          id: 'default_categories',
          type: StepType.MODAL,
          title: 'Varsayılan Kategoriler',
          description: 'Uygulama tarımsal giderler için hazır kategorilerle gelir.',
        },
        {
          id: 'add_category',
          type: StepType.HIGHLIGHT,
          title: 'Yeni Kategori',
          description: 'Kendi kategorinizi eklemek için bu butona dokunun.',
          target: { x: 20, y: 150, width: 360, height: 60 },
          tooltipPosition: 'bottom',
        },
        {
          id: 'category_customization',
          type: StepType.MODAL,
          title: 'Kategori Özelleştirme',
          description: 'Kategorilerinize emoji, renk ve açıklama ekleyebilirsiniz.',
        },
      ],
    });

    // Seasonal tracking tutorial
    this.tutorials.set(TutorialType.SEASONAL_TRACKING, {
      id: TutorialType.SEASONAL_TRACKING,
      title: 'Mevsimsel Takip',
      description: 'Giderlerinizi mevsimlere göre nasıl takip edersiniz',
      steps: [
        {
          id: 'seasonal_concept',
          type: StepType.MODAL,
          title: 'Mevsimsel Takip',
          description: 'Giderleriniz otomatik olarak tarım dönemlerine göre gruplandırılır.',
        },
        {
          id: 'season_selector',
          type: StepType.HIGHLIGHT,
          title: 'Mevsim Seçici',
          description: 'Farklı mevsimlerin giderlerini görüntüleyebilirsiniz.',
          target: { x: 20, y: 100, width: 360, height: 40 },
          tooltipPosition: 'bottom',
        },
        {
          id: 'seasonal_reports',
          type: StepType.HIGHLIGHT,
          title: 'Mevsimsel Raporlar',
          description: 'Raporlar sekmesinden mevsimsel analizlere erişebilirsiniz.',
          target: { x: 200, y: 700, width: 100, height: 80 },
          tooltipPosition: 'top',
        },
      ],
    });
  }

  // Get tutorial by type
  getTutorial(type) {
    return this.tutorials.get(type);
  }

  // Get all tutorials
  getAllTutorials() {
    return Array.from(this.tutorials.values());
  }

  // Check if tutorial is completed
  isTutorialCompleted(type) {
    return this.completedTutorials.has(type);
  }

  // Mark tutorial as completed
  async markTutorialCompleted(type) {
    try {
      this.completedTutorials.add(type);
      await this.saveCompletedTutorials();
      
      Analytics.trackEvent(AnalyticsEvent.TUTORIAL_COMPLETED, {
        tutorialType: type,
      });
      
      Logger.info(LogCategory.USER_ACTION, 'Tutorial completed', { type });
      
      this.notifyListeners({
        type: 'tutorial_completed',
        tutorialType: type,
      });
    } catch (error) {
      Logger.error(LogCategory.USER_ACTION, 'Failed to mark tutorial completed', error);
    }
  }

  // Mark tutorial as skipped
  async markTutorialSkipped(type, currentStep = 0) {
    try {
      Analytics.trackEvent(AnalyticsEvent.TUTORIAL_SKIPPED, {
        tutorialType: type,
        currentStep,
      });
      
      Logger.info(LogCategory.USER_ACTION, 'Tutorial skipped', { type, currentStep });
      
      this.notifyListeners({
        type: 'tutorial_skipped',
        tutorialType: type,
        currentStep,
      });
    } catch (error) {
      Logger.error(LogCategory.USER_ACTION, 'Failed to mark tutorial skipped', error);
    }
  }

  // Start tutorial
  async startTutorial(type) {
    try {
      const tutorial = this.getTutorial(type);
      if (!tutorial) {
        throw new Error(`Tutorial not found: ${type}`);
      }

      this.currentTutorial = type;
      this.currentStep = 0;

      Analytics.trackEvent(AnalyticsEvent.TUTORIAL_STARTED, {
        tutorialType: type,
      });

      Logger.info(LogCategory.USER_ACTION, 'Tutorial started', { type });

      this.notifyListeners({
        type: 'tutorial_started',
        tutorialType: type,
        tutorial,
      });

      return tutorial;
    } catch (error) {
      Logger.error(LogCategory.USER_ACTION, 'Failed to start tutorial', error);
      throw error;
    }
  }

  // Get tutorial progress
  getTutorialProgress(type) {
    const tutorial = this.getTutorial(type);
    if (!tutorial) return null;

    return {
      tutorialType: type,
      totalSteps: tutorial.steps.length,
      currentStep: this.currentTutorial === type ? this.currentStep : 0,
      completed: this.isTutorialCompleted(type),
      progress: this.isTutorialCompleted(type) ? 100 : 
                (this.currentTutorial === type ? (this.currentStep / tutorial.steps.length) * 100 : 0),
    };
  }

  // Get all tutorial progress
  getAllTutorialProgress() {
    return Array.from(this.tutorials.keys()).map(type => this.getTutorialProgress(type));
  }

  // Reset tutorial progress
  async resetTutorial(type) {
    try {
      this.completedTutorials.delete(type);
      await this.saveCompletedTutorials();
      
      if (this.currentTutorial === type) {
        this.currentTutorial = null;
        this.currentStep = 0;
      }

      Logger.info(LogCategory.USER_ACTION, 'Tutorial reset', { type });
      
      this.notifyListeners({
        type: 'tutorial_reset',
        tutorialType: type,
      });
    } catch (error) {
      Logger.error(LogCategory.USER_ACTION, 'Failed to reset tutorial', error);
    }
  }

  // Reset all tutorials
  async resetAllTutorials() {
    try {
      this.completedTutorials.clear();
      await this.saveCompletedTutorials();
      
      this.currentTutorial = null;
      this.currentStep = 0;

      Logger.info(LogCategory.USER_ACTION, 'All tutorials reset');
      
      this.notifyListeners({
        type: 'all_tutorials_reset',
      });
    } catch (error) {
      Logger.error(LogCategory.USER_ACTION, 'Failed to reset all tutorials', error);
    }
  }

  // Load completed tutorials from storage
  async loadCompletedTutorials() {
    try {
      const completed = await AsyncStorage.getItem('completed_tutorials');
      if (completed) {
        const completedArray = JSON.parse(completed);
        this.completedTutorials = new Set(completedArray);
      }
    } catch (error) {
      Logger.error(LogCategory.USER_ACTION, 'Failed to load completed tutorials', error);
    }
  }

  // Save completed tutorials to storage
  async saveCompletedTutorials() {
    try {
      const completedArray = Array.from(this.completedTutorials);
      await AsyncStorage.setItem('completed_tutorials', JSON.stringify(completedArray));
    } catch (error) {
      Logger.error(LogCategory.USER_ACTION, 'Failed to save completed tutorials', error);
    }
  }

  // Add listener
  addListener(listener) {
    this.listeners.push(listener);
    
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  // Notify listeners
  notifyListeners(event) {
    this.listeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        Logger.error(LogCategory.USER_ACTION, 'Tutorial listener error', error);
      }
    });
  }

  // Check if any tutorial should be shown
  shouldShowTutorial() {
    // Show onboarding tutorial if not completed
    if (!this.isTutorialCompleted(TutorialType.ONBOARDING)) {
      return TutorialType.ONBOARDING;
    }

    // Add logic for other tutorials based on user actions
    return null;
  }

  // Get tutorial statistics
  getTutorialStatistics() {
    const totalTutorials = this.tutorials.size;
    const completedTutorials = this.completedTutorials.size;
    
    return {
      totalTutorials,
      completedTutorials,
      completionRate: totalTutorials > 0 ? (completedTutorials / totalTutorials) * 100 : 0,
      remainingTutorials: totalTutorials - completedTutorials,
    };
  }
}

export default new TutorialService();
