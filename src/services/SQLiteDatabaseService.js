import AsyncStorage from '@react-native-async-storage/async-storage';
import SQLite from 'react-native-sqlite-storage';
import { DefaultCategories } from '../constants/Categories';

// Enable debugging
SQLite.DEBUG(true);
SQLite.enablePromise(true);

class SQLiteDatabaseService {
  constructor() {
    this.db = null;
    this.initialized = false;
    this.migrationInProgress = false;
  }

  // Initialize SQLite database
  async initDatabase() {
    if (this.initialized) return;
    
    try {
      console.log('Initializing SQLite database...');
      
      // Open database
      this.db = await SQLite.openDatabase({
        name: 'CiftciNotDefterim.db',
        location: 'default',
      });

      console.log('Database opened successfully');

      // Create tables
      await this.createTables();
      
      // Check if migration is needed
      await this.migrateFromAsyncStorage();
      
      // Initialize default categories if needed
      await this.initDefaultCategories();
      
      // Initialize seasons
      await this.initSeasons();
      
      this.initialized = true;
      console.log('Database initialized successfully');
    } catch (error) {
      console.error('Database initialization error:', error);
      throw error;
    }
  }

  // Create database tables
  async createTables() {
    try {
      // Users table
      await this.db.executeSql(`
        CREATE TABLE IF NOT EXISTS users (
          id TEXT PRIMARY KEY,
          email TEXT UNIQUE,
          name TEXT,
          auth_provider TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );
      `);

      // Categories table
      await this.db.executeSql(`
        CREATE TABLE IF NOT EXISTS categories (
          id TEXT PRIMARY KEY,
          user_id TEXT,
          name TEXT NOT NULL,
          emoji TEXT,
          color TEXT,
          icon TEXT,
          is_default BOOLEAN DEFAULT 0,
          usage_count INTEGER DEFAULT 0,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (user_id) REFERENCES users(id)
        );
      `);

      // Seasons table
      await this.db.executeSql(`
        CREATE TABLE IF NOT EXISTS seasons (
          id TEXT PRIMARY KEY,
          name TEXT NOT NULL,
          start_month INTEGER,
          end_month INTEGER,
          description TEXT,
          typical_crops TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );
      `);

      // Expenses table
      await this.db.executeSql(`
        CREATE TABLE IF NOT EXISTS expenses (
          id TEXT PRIMARY KEY,
          user_id TEXT,
          category_id TEXT,
          season_id TEXT,
          amount REAL NOT NULL,
          description TEXT,
          date DATE NOT NULL,
          location_lat REAL,
          location_lng REAL,
          photos TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (user_id) REFERENCES users(id),
          FOREIGN KEY (category_id) REFERENCES categories(id),
          FOREIGN KEY (season_id) REFERENCES seasons(id)
        );
      `);

      // Create indexes for better performance
      await this.db.executeSql(`
        CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(date);
      `);
      
      await this.db.executeSql(`
        CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category_id);
      `);

      console.log('Database tables created successfully');
    } catch (error) {
      console.error('Error creating tables:', error);
      throw error;
    }
  }

  // Migrate data from AsyncStorage to SQLite
  async migrateFromAsyncStorage() {
    if (this.migrationInProgress) return;
    
    try {
      this.migrationInProgress = true;
      console.log('Starting migration from AsyncStorage...');

      // Check if migration already completed
      const migrationCompleted = await AsyncStorage.getItem('migration_completed');
      if (migrationCompleted === 'true') {
        console.log('Migration already completed');
        return;
      }

      // Migrate categories
      const storedCategories = await AsyncStorage.getItem('categories');
      if (storedCategories) {
        const categories = JSON.parse(storedCategories);
        for (const category of categories) {
          await this.addCategory({
            ...category,
            isDefault: DefaultCategories.some(dc => dc.id === category.id)
          });
        }
        console.log(`Migrated ${categories.length} categories`);
      }

      // Migrate expenses
      const storedExpenses = await AsyncStorage.getItem('expenses');
      if (storedExpenses) {
        const expenses = JSON.parse(storedExpenses);
        for (const expense of expenses) {
          await this.addExpense({
            categoryId: expense.categoryId,
            amount: expense.amount,
            description: expense.description,
            date: expense.date,
            createdAt: expense.createdAt,
            updatedAt: expense.updatedAt
          });
        }
        console.log(`Migrated ${expenses.length} expenses`);
      }

      // Mark migration as completed
      await AsyncStorage.setItem('migration_completed', 'true');
      console.log('Migration completed successfully');
    } catch (error) {
      console.error('Migration error:', error);
      throw error;
    } finally {
      this.migrationInProgress = false;
    }
  }

  // Initialize default categories
  async initDefaultCategories() {
    try {
      // Check if default categories already exist
      const [results] = await this.db.executeSql(
        'SELECT COUNT(*) as count FROM categories WHERE is_default = 1'
      );
      
      if (results.rows.item(0).count === 0) {
        console.log('Adding default categories...');
        for (const category of DefaultCategories) {
          await this.addCategory({
            ...category,
            isDefault: true
          });
        }
        console.log('Default categories added successfully');
      }
    } catch (error) {
      console.error('Error initializing default categories:', error);
      throw error;
    }
  }

  // Initialize seasons
  async initSeasons() {
    try {
      // Check if seasons already exist
      const [results] = await this.db.executeSql(
        'SELECT COUNT(*) as count FROM seasons'
      );
      
      if (results.rows.item(0).count === 0) {
        console.log('Adding default seasons...');
        const seasons = [
          {
            id: 'spring',
            name: 'İlkbahar Ekimi',
            startMonth: 3,
            endMonth: 5,
            description: 'İlkbahar ekim dönemi',
            typicalCrops: JSON.stringify(['buğday', 'arpa', 'mısır', 'ayçiçeği'])
          },
          {
            id: 'summer',
            name: 'Yaz Bakımı',
            startMonth: 6,
            endMonth: 8,
            description: 'Yaz bakım ve sulama dönemi',
            typicalCrops: JSON.stringify(['domates', 'biber', 'patlıcan', 'kavun'])
          },
          {
            id: 'autumn',
            name: 'Sonbahar Hasadı',
            startMonth: 9,
            endMonth: 11,
            description: 'Hasat ve depolama dönemi',
            typicalCrops: JSON.stringify(['buğday', 'mısır', 'ayçiçeği', 'şeker pancarı'])
          },
          {
            id: 'winter',
            name: 'Kış Hazırlığı',
            startMonth: 12,
            endMonth: 2,
            description: 'Kış hazırlığı ve planlama dönemi',
            typicalCrops: JSON.stringify(['kış buğdayı', 'arpa'])
          }
        ];

        for (const season of seasons) {
          await this.db.executeSql(
            'INSERT INTO seasons (id, name, start_month, end_month, description, typical_crops) VALUES (?, ?, ?, ?, ?, ?)',
            [season.id, season.name, season.startMonth, season.endMonth, season.description, season.typicalCrops]
          );
        }
        console.log('Default seasons added successfully');
      }
    } catch (error) {
      console.error('Error initializing seasons:', error);
      throw error;
    }
  }

  // Get current season based on date
  getCurrentSeason(date = new Date()) {
    const month = date.getMonth() + 1; // JavaScript months are 0-based
    
    if (month >= 3 && month <= 5) return 'spring';
    if (month >= 6 && month <= 8) return 'summer';
    if (month >= 9 && month <= 11) return 'autumn';
    return 'winter'; // December, January, February
  }

  // Add expense
  async addExpense(expense) {
    try {
      const id = Date.now().toString();
      const seasonId = this.getCurrentSeason(new Date(expense.date));
      const now = new Date().toISOString();

      await this.db.executeSql(
        `INSERT INTO expenses (id, category_id, season_id, amount, description, date, created_at, updated_at) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          id,
          expense.categoryId,
          seasonId,
          expense.amount,
          expense.description || '',
          expense.date,
          expense.createdAt || now,
          expense.updatedAt || now
        ]
      );

      // Update category usage count
      await this.db.executeSql(
        'UPDATE categories SET usage_count = usage_count + 1 WHERE id = ?',
        [expense.categoryId]
      );

      return { id, ...expense, seasonId };
    } catch (error) {
      console.error('Error adding expense:', error);
      throw error;
    }
  }

  // Get expenses
  async getExpenses(limit = null) {
    try {
      let query = `
        SELECT e.*, c.name as categoryName, c.emoji as categoryEmoji, c.color as categoryColor,
               s.name as seasonName
        FROM expenses e
        LEFT JOIN categories c ON e.category_id = c.id
        LEFT JOIN seasons s ON e.season_id = s.id
        ORDER BY e.date DESC
      `;

      const params = [];
      if (limit) {
        query += ' LIMIT ?';
        params.push(limit);
      }

      const [results] = await this.db.executeSql(query, params);
      const expenses = [];

      for (let i = 0; i < results.rows.length; i++) {
        expenses.push(results.rows.item(i));
      }

      return expenses;
    } catch (error) {
      console.error('Error getting expenses:', error);
      throw error;
    }
  }

  // Get expenses by date range
  async getExpensesByDateRange(startDate, endDate) {
    try {
      const [results] = await this.db.executeSql(
        `SELECT e.*, c.name as categoryName, c.emoji as categoryEmoji, c.color as categoryColor,
                s.name as seasonName
         FROM expenses e
         LEFT JOIN categories c ON e.category_id = c.id
         LEFT JOIN seasons s ON e.season_id = s.id
         WHERE e.date >= ? AND e.date <= ?
         ORDER BY e.date DESC`,
        [startDate, endDate]
      );

      const expenses = [];
      for (let i = 0; i < results.rows.length; i++) {
        expenses.push(results.rows.item(i));
      }

      return expenses;
    } catch (error) {
      console.error('Error getting expenses by date range:', error);
      throw error;
    }
  }

  // Get expenses by season
  async getExpensesBySeason(seasonId) {
    try {
      const [results] = await this.db.executeSql(
        `SELECT e.*, c.name as categoryName, c.emoji as categoryEmoji, c.color as categoryColor,
                s.name as seasonName
         FROM expenses e
         LEFT JOIN categories c ON e.category_id = c.id
         LEFT JOIN seasons s ON e.season_id = s.id
         WHERE e.season_id = ?
         ORDER BY e.date DESC`,
        [seasonId]
      );

      const expenses = [];
      for (let i = 0; i < results.rows.length; i++) {
        expenses.push(results.rows.item(i));
      }

      return expenses;
    } catch (error) {
      console.error('Error getting expenses by season:', error);
      throw error;
    }
  }

  // Get total expenses by category
  async getTotalExpensesByCategory(startDate, endDate) {
    try {
      const [results] = await this.db.executeSql(
        `SELECT c.id, c.name, c.emoji, c.color, SUM(e.amount) as total
         FROM expenses e
         JOIN categories c ON e.category_id = c.id
         WHERE e.date >= ? AND e.date <= ?
         GROUP BY c.id, c.name, c.emoji, c.color
         ORDER BY total DESC`,
        [startDate, endDate]
      );

      const categoryTotals = [];
      for (let i = 0; i < results.rows.length; i++) {
        categoryTotals.push(results.rows.item(i));
      }

      return categoryTotals;
    } catch (error) {
      console.error('Error getting total expenses by category:', error);
      throw error;
    }
  }

  // Update expense
  async updateExpense(id, expense) {
    try {
      const seasonId = this.getCurrentSeason(new Date(expense.date));
      const updatedAt = new Date().toISOString();

      await this.db.executeSql(
        `UPDATE expenses
         SET category_id = ?, amount = ?, description = ?, date = ?, season_id = ?, updated_at = ?
         WHERE id = ?`,
        [
          expense.categoryId,
          expense.amount,
          expense.description || '',
          expense.date,
          seasonId,
          updatedAt,
          id
        ]
      );

      return { id, ...expense, seasonId, updatedAt };
    } catch (error) {
      console.error('Error updating expense:', error);
      throw error;
    }
  }

  // Delete expense
  async deleteExpense(id) {
    try {
      await this.db.executeSql('DELETE FROM expenses WHERE id = ?', [id]);
      return true;
    } catch (error) {
      console.error('Error deleting expense:', error);
      throw error;
    }
  }

  // Add category
  async addCategory(category) {
    try {
      const id = category.id || Date.now().toString();
      const now = new Date().toISOString();

      await this.db.executeSql(
        `INSERT INTO categories (id, name, emoji, color, icon, is_default, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          id,
          category.name,
          category.emoji || '📝',
          category.color || '#607D8B',
          category.icon || 'more-horiz',
          category.isDefault ? 1 : 0,
          now,
          now
        ]
      );

      return { id, ...category };
    } catch (error) {
      console.error('Error adding category:', error);
      throw error;
    }
  }

  // Get categories
  async getCategories() {
    try {
      const [results] = await this.db.executeSql(
        'SELECT * FROM categories ORDER BY usage_count DESC, name ASC'
      );

      const categories = [];
      for (let i = 0; i < results.rows.length; i++) {
        const category = results.rows.item(i);
        categories.push({
          ...category,
          isDefault: category.is_default === 1
        });
      }

      return categories;
    } catch (error) {
      console.error('Error getting categories:', error);
      throw error;
    }
  }

  // Update category
  async updateCategory(id, category) {
    try {
      const updatedAt = new Date().toISOString();

      await this.db.executeSql(
        `UPDATE categories
         SET name = ?, emoji = ?, color = ?, icon = ?, updated_at = ?
         WHERE id = ?`,
        [
          category.name,
          category.emoji,
          category.color,
          category.icon,
          updatedAt,
          id
        ]
      );

      return { id, ...category, updatedAt };
    } catch (error) {
      console.error('Error updating category:', error);
      throw error;
    }
  }

  // Delete category
  async deleteCategory(id) {
    try {
      // Don't allow deletion of default categories
      const [results] = await this.db.executeSql(
        'SELECT is_default FROM categories WHERE id = ?',
        [id]
      );

      if (results.rows.length > 0 && results.rows.item(0).is_default === 1) {
        throw new Error('Cannot delete default category');
      }

      await this.db.executeSql('DELETE FROM categories WHERE id = ?', [id]);
      return true;
    } catch (error) {
      console.error('Error deleting category:', error);
      throw error;
    }
  }

  // Get seasons
  async getSeasons() {
    try {
      const [results] = await this.db.executeSql(
        'SELECT * FROM seasons ORDER BY start_month ASC'
      );

      const seasons = [];
      for (let i = 0; i < results.rows.length; i++) {
        const season = results.rows.item(i);
        seasons.push({
          ...season,
          typicalCrops: JSON.parse(season.typical_crops || '[]')
        });
      }

      return seasons;
    } catch (error) {
      console.error('Error getting seasons:', error);
      throw error;
    }
  }

  // Settings methods (keeping AsyncStorage for settings)
  async getSetting(key) {
    try {
      const value = await AsyncStorage.getItem(`setting_${key}`);
      return value;
    } catch (error) {
      console.error('Error getting setting:', error);
      return null;
    }
  }

  async setSetting(key, value) {
    try {
      await AsyncStorage.setItem(`setting_${key}`, value);
    } catch (error) {
      console.error('Error setting setting:', error);
    }
  }

  // Close database connection
  async closeDatabase() {
    if (this.db) {
      await this.db.close();
      this.db = null;
      this.initialized = false;
    }
  }
}

export default new SQLiteDatabaseService();
