/**
 * Backup Service for Çiftçi Not Defterim
 * Handles backup and restore operations for guest data migration
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
// Use crypto.randomUUID() or fallback to timestamp-based ID
const generateUUID = () => {
  if (typeof crypto !== 'undefined' && crypto.randomUUID) {
    return crypto.randomUUID();
  }
  // Fallback: timestamp + random
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
};

class BackupService {
  constructor() {
    this.BACKUP_KEY = 'migration_backup';
    this.BACKUP_METADATA_KEY = 'migration_backup_metadata';
  }

  /**
   * Generate unique backup ID
   */
  generateBackupId() {
    return `backup_${Date.now()}_${generateUUID()}`;
  }

  /**
   * Create backup of guest data before migration
   * @param {Object} options - Backup options
   * @returns {Object} Backup result with backupId
   */
  async createMigrationBackup(options = {}) {
    try {
      console.log('🔄 BackupService: Creating migration backup...');
      
      const backupId = this.generateBackupId();
      const timestamp = new Date().toISOString();

      // Get all guest data
      const [
        guestExpenses,
        guestFields,
        guestCategories,
        guestSeasons,
        guestSettings
      ] = await Promise.all([
        AsyncStorage.getItem('expenses_guest'),
        AsyncStorage.getItem('fields_guest'),
        AsyncStorage.getItem('categories_guest'),
        AsyncStorage.getItem('seasons_guest'),
        AsyncStorage.getItem('settings_guest')
      ]);

      // Create backup object
      const backup = {
        backupId,
        timestamp,
        version: '1.0.0',
        type: 'migration_backup',
        data: {
          expenses_guest: guestExpenses,
          fields_guest: guestFields,
          categories_guest: guestCategories,
          seasons_guest: guestSeasons,
          settings_guest: guestSettings
        },
        metadata: {
          expenseCount: guestExpenses ? JSON.parse(guestExpenses).length : 0,
          fieldCount: guestFields ? JSON.parse(guestFields).length : 0,
          categoryCount: guestCategories ? JSON.parse(guestCategories).length : 0,
          seasonCount: guestSeasons ? JSON.parse(guestSeasons).length : 0,
          hasSettings: !!guestSettings
        }
      };

      // Validate backup integrity
      const validation = await this.validateBackup(backup);
      if (!validation.isValid) {
        throw new Error(`Backup validation failed: ${validation.errors.join(', ')}`);
      }

      // Store backup
      await AsyncStorage.setItem(this.BACKUP_KEY, JSON.stringify(backup));
      
      // Store backup metadata separately for quick access
      const metadata = {
        backupId,
        timestamp,
        dataSize: JSON.stringify(backup).length,
        itemCounts: backup.metadata
      };
      await AsyncStorage.setItem(this.BACKUP_METADATA_KEY, JSON.stringify(metadata));

      console.log('✅ BackupService: Migration backup created successfully', {
        backupId,
        metadata: backup.metadata
      });

      return {
        success: true,
        backupId,
        metadata: backup.metadata,
        timestamp
      };

    } catch (error) {
      console.error('❌ BackupService: Failed to create migration backup:', error);
      throw new Error(`Backup creation failed: ${error.message}`);
    }
  }

  /**
   * Validate backup integrity
   * @param {Object} backup - Backup object to validate
   * @returns {Object} Validation result
   */
  async validateBackup(backup) {
    const errors = [];

    try {
      // Check required fields
      if (!backup.backupId) errors.push('Missing backupId');
      if (!backup.timestamp) errors.push('Missing timestamp');
      if (!backup.data) errors.push('Missing data object');

      // Validate data structure
      if (backup.data) {
        // Check if data can be parsed
        for (const [key, value] of Object.entries(backup.data)) {
          if (value !== null) {
            try {
              JSON.parse(value);
            } catch (parseError) {
              errors.push(`Invalid JSON in ${key}: ${parseError.message}`);
            }
          }
        }
      }

      // Check backup size (should not be empty if guest data exists)
      const totalSize = JSON.stringify(backup).length;
      if (totalSize < 100) {
        console.warn('BackupService: Backup seems unusually small:', totalSize);
      }

      return {
        isValid: errors.length === 0,
        errors,
        size: totalSize
      };

    } catch (error) {
      return {
        isValid: false,
        errors: [`Validation error: ${error.message}`],
        size: 0
      };
    }
  }

  /**
   * Restore guest data from backup
   * @param {string} backupId - Backup ID to restore
   * @returns {Object} Restore result
   */
  async restoreFromBackup(backupId) {
    try {
      console.log('🔄 BackupService: Restoring from backup:', backupId);

      // Get backup data
      const backupData = await AsyncStorage.getItem(this.BACKUP_KEY);
      if (!backupData) {
        throw new Error('No backup found');
      }

      const backup = JSON.parse(backupData);
      
      // Verify backup ID matches
      if (backup.backupId !== backupId) {
        throw new Error(`Backup ID mismatch. Expected: ${backupId}, Found: ${backup.backupId}`);
      }

      // Validate backup before restore
      const validation = await this.validateBackup(backup);
      if (!validation.isValid) {
        throw new Error(`Backup validation failed: ${validation.errors.join(', ')}`);
      }

      // Restore guest data
      const restorePromises = [];
      for (const [key, value] of Object.entries(backup.data)) {
        if (value !== null) {
          restorePromises.push(AsyncStorage.setItem(key, value));
        }
      }

      await Promise.all(restorePromises);

      console.log('✅ BackupService: Data restored successfully from backup:', backupId);

      return {
        success: true,
        backupId,
        restoredItems: Object.keys(backup.data).length,
        metadata: backup.metadata
      };

    } catch (error) {
      console.error('❌ BackupService: Failed to restore from backup:', error);
      throw new Error(`Restore failed: ${error.message}`);
    }
  }

  /**
   * Get backup metadata
   * @returns {Object|null} Backup metadata or null if no backup exists
   */
  async getBackupMetadata() {
    try {
      const metadataData = await AsyncStorage.getItem(this.BACKUP_METADATA_KEY);
      return metadataData ? JSON.parse(metadataData) : null;
    } catch (error) {
      console.error('BackupService: Failed to get backup metadata:', error);
      return null;
    }
  }

  /**
   * Check if backup exists
   * @returns {boolean} True if backup exists
   */
  async hasBackup() {
    try {
      const backup = await AsyncStorage.getItem(this.BACKUP_KEY);
      return !!backup;
    } catch (error) {
      console.error('BackupService: Failed to check backup existence:', error);
      return false;
    }
  }

  /**
   * Clean up backup after successful migration
   * @param {string} backupId - Backup ID to clean up
   */
  async cleanupBackup(backupId) {
    try {
      console.log('🧹 BackupService: Cleaning up backup:', backupId);

      // Verify backup ID before cleanup
      const metadata = await this.getBackupMetadata();
      if (metadata && metadata.backupId === backupId) {
        await Promise.all([
          AsyncStorage.removeItem(this.BACKUP_KEY),
          AsyncStorage.removeItem(this.BACKUP_METADATA_KEY)
        ]);
        console.log('✅ BackupService: Backup cleaned up successfully');
      } else {
        console.warn('BackupService: Backup ID mismatch during cleanup, skipping');
      }

    } catch (error) {
      console.error('❌ BackupService: Failed to cleanup backup:', error);
      // Don't throw error for cleanup failures
    }
  }

  /**
   * Get backup integrity check
   * @returns {Object} Integrity check result
   */
  async checkBackupIntegrity() {
    try {
      const backupData = await AsyncStorage.getItem(this.BACKUP_KEY);
      if (!backupData) {
        return { hasBackup: false, isValid: false };
      }

      const backup = JSON.parse(backupData);
      const validation = await this.validateBackup(backup);

      return {
        hasBackup: true,
        isValid: validation.isValid,
        errors: validation.errors,
        backupId: backup.backupId,
        timestamp: backup.timestamp,
        size: validation.size
      };

    } catch (error) {
      return {
        hasBackup: false,
        isValid: false,
        errors: [`Integrity check failed: ${error.message}`]
      };
    }
  }
}

export default new BackupService();
