import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { Colors } from '../constants/Colors';
import { Spacing, FontSize, FontWeight, BorderRadius } from '../constants/Dimensions';
import { useAuth } from '../context/AuthContext';

export default function CategoryManagement({ navigation }) {
  const { user, isAuthenticated } = useAuth();
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    if (isAuthenticated) {
      loadCategories();
    }
  }, [isAuthenticated]);

  const loadCategories = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/v1/categories', {
        headers: {
          'Authorization': `Bearer ${user?.accessToken}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setCategories(data.data);
      } else {
        throw new Error('Kategoriler yüklenemedi');
      }
    } catch (error) {
      console.error('Error loading categories:', error);
      Alert.alert('Hata', 'Kategoriler yüklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadCategories();
    setRefreshing(false);
  };

  const handleAddCategory = () => {
    navigation.navigate('AddCategory');
  };

  const handleEditCategory = (category) => {
    if (category.isDefault) {
      Alert.alert(
        'Bilgi',
        'Sistem varsayılan kategorileri düzenlenemez. Kendi özel kategorinizi oluşturabilirsiniz.',
        [{ text: 'Tamam' }]
      );
      return;
    }
    navigation.navigate('EditCategory', { categoryId: category._id });
  };

  const handleDeleteCategory = (category) => {
    if (category.isDefault) {
      Alert.alert(
        'Bilgi',
        'Sistem varsayılan kategorileri silinemez.',
        [{ text: 'Tamam' }]
      );
      return;
    }

    Alert.alert(
      'Kategori Sil',
      `"${category.name}" kategorisini silmek istediğinizden emin misiniz?`,
      [
        {
          text: 'İptal',
          style: 'cancel',
        },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: () => deleteCategory(category._id),
        },
      ]
    );
  };

  const deleteCategory = async (categoryId) => {
    try {
      const response = await fetch(`/api/v1/categories/${categoryId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${user?.accessToken}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        setCategories(categories.filter(category => category._id !== categoryId));
        Alert.alert('Başarılı', 'Kategori başarıyla silindi.');
      } else {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Kategori silinemedi');
      }
    } catch (error) {
      console.error('Error deleting category:', error);
      Alert.alert('Hata', error.message || 'Kategori silinirken bir hata oluştu.');
    }
  };

  const renderCategoryItem = ({ item }) => (
    <View style={styles.categoryCard}>
      <View style={styles.categoryHeader}>
        <View style={styles.categoryInfo}>
          <View style={styles.categoryNameContainer}>
            <Text style={styles.categoryEmoji}>{item.emoji}</Text>
            <View style={styles.categoryDetails}>
              <Text style={styles.categoryName}>{item.name}</Text>
              {item.description && (
                <Text style={styles.categoryDescription}>{item.description}</Text>
              )}
            </View>
          </View>
          
          <View style={styles.categoryMeta}>
            {item.isDefault && (
              <View style={styles.defaultBadge}>
                <Text style={styles.defaultBadgeText}>Sistem</Text>
              </View>
            )}
            
            {item.color && (
              <View style={[styles.colorIndicator, { backgroundColor: item.color }]} />
            )}
          </View>
        </View>
        
        <View style={styles.categoryActions}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => handleEditCategory(item)}
          >
            <Ionicons 
              name={item.isDefault ? "eye-outline" : "pencil"} 
              size={20} 
              color={Colors.primary} 
            />
          </TouchableOpacity>
          
          {!item.isDefault && (
            <TouchableOpacity
              style={[styles.actionButton, styles.deleteButton]}
              onPress={() => handleDeleteCategory(item)}
            >
              <Ionicons name="trash-outline" size={20} color={Colors.error} />
            </TouchableOpacity>
          )}
        </View>
      </View>
      
      {item.stats && (
        <View style={styles.categoryStats}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{item.stats.expenseCount || 0}</Text>
            <Text style={styles.statLabel}>Gider</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>
              {item.stats.totalAmount ? `₺${item.stats.totalAmount.toLocaleString('tr-TR')}` : '₺0'}
            </Text>
            <Text style={styles.statLabel}>Toplam</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>
              {item.stats.avgAmount ? `₺${item.stats.avgAmount.toLocaleString('tr-TR')}` : '₺0'}
            </Text>
            <Text style={styles.statLabel}>Ortalama</Text>
          </View>
        </View>
      )}
    </View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="pricetag-outline" size={64} color={Colors.textSecondary} />
      <Text style={styles.emptyStateTitle}>Henüz özel kategori yok</Text>
      <Text style={styles.emptyStateText}>
        Kendi özel kategorinizi ekleyerek giderlerinizi daha iyi organize edin
      </Text>
      <TouchableOpacity style={styles.addFirstButton} onPress={handleAddCategory}>
        <Text style={styles.addFirstButtonText}>İlk Kategori Ekle</Text>
      </TouchableOpacity>
    </View>
  );

  const systemCategories = categories.filter(cat => cat.isDefault);
  const userCategories = categories.filter(cat => !cat.isDefault);

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.primary} />
        <Text style={styles.loadingText}>Kategoriler yükleniyor...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="chevron-back" size={24} color={Colors.surface} />
        </TouchableOpacity>
        
        <Text style={styles.headerTitle}>Kategori Yönetimi</Text>
        
        <TouchableOpacity style={styles.addButton} onPress={handleAddCategory}>
          <Ionicons name="add" size={24} color={Colors.surface} />
        </TouchableOpacity>
      </View>

      <View style={styles.content}>
        <FlatList
          data={[
            { type: 'section', title: 'Sistem Kategorileri', data: systemCategories },
            { type: 'section', title: 'Özel Kategorilerim', data: userCategories },
          ]}
          renderItem={({ item }) => {
            if (item.type === 'section') {
              return (
                <View>
                  <Text style={styles.sectionTitle}>{item.title}</Text>
                  {item.data.length === 0 && item.title === 'Özel Kategorilerim' ? (
                    renderEmptyState()
                  ) : (
                    <FlatList
                      data={item.data}
                      renderItem={renderCategoryItem}
                      keyExtractor={(category) => category._id}
                      scrollEnabled={false}
                    />
                  )}
                </View>
              );
            }
            return null;
          }}
          keyExtractor={(item, index) => `section-${index}`}
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[Colors.primary]}
              tintColor={Colors.primary}
            />
          }
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    backgroundColor: Colors.primary,
    paddingTop: 50,
    paddingBottom: Spacing.md,
    paddingHorizontal: Spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: FontSize.xl,
    fontWeight: FontWeight.bold,
    color: Colors.surface,
    flex: 1,
    textAlign: 'center',
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    padding: Spacing.md,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background,
  },
  loadingText: {
    marginTop: Spacing.md,
    fontSize: FontSize.md,
    color: Colors.textSecondary,
  },
  listContainer: {
    paddingBottom: Spacing.lg,
  },
  sectionTitle: {
    fontSize: FontSize.lg,
    fontWeight: FontWeight.semibold,
    color: Colors.text,
    marginBottom: Spacing.md,
    marginTop: Spacing.lg,
  },
  categoryCard: {
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.md,
    padding: Spacing.md,
    marginBottom: Spacing.md,
    elevation: 2,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  categoryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  categoryInfo: {
    flex: 1,
    marginRight: Spacing.md,
  },
  categoryNameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  categoryEmoji: {
    fontSize: 32,
    marginRight: Spacing.md,
  },
  categoryDetails: {
    flex: 1,
  },
  categoryName: {
    fontSize: FontSize.lg,
    fontWeight: FontWeight.semibold,
    color: Colors.text,
  },
  categoryDescription: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
    marginTop: 2,
  },
  categoryMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: Spacing.xs,
  },
  defaultBadge: {
    backgroundColor: Colors.info,
    paddingHorizontal: Spacing.sm,
    paddingVertical: 2,
    borderRadius: BorderRadius.sm,
    marginRight: Spacing.sm,
  },
  defaultBadgeText: {
    fontSize: FontSize.xs,
    fontWeight: FontWeight.medium,
    color: Colors.surface,
  },
  colorIndicator: {
    width: 16,
    height: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  categoryActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: Colors.background,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: Spacing.xs,
  },
  deleteButton: {
    backgroundColor: Colors.errorLight,
  },
  categoryStats: {
    flexDirection: 'row',
    marginTop: Spacing.md,
    paddingTop: Spacing.md,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statValue: {
    fontSize: FontSize.md,
    fontWeight: FontWeight.semibold,
    color: Colors.text,
  },
  statLabel: {
    fontSize: FontSize.xs,
    color: Colors.textSecondary,
    marginTop: 2,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: Spacing.xl,
    paddingVertical: Spacing.xl,
  },
  emptyStateTitle: {
    fontSize: FontSize.xl,
    fontWeight: FontWeight.semibold,
    color: Colors.text,
    marginTop: Spacing.md,
    marginBottom: Spacing.sm,
    textAlign: 'center',
  },
  emptyStateText: {
    fontSize: FontSize.md,
    color: Colors.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: Spacing.xl,
  },
  addFirstButton: {
    backgroundColor: Colors.primary,
    paddingHorizontal: Spacing.xl,
    paddingVertical: Spacing.md,
    borderRadius: BorderRadius.md,
  },
  addFirstButtonText: {
    fontSize: FontSize.md,
    fontWeight: FontWeight.semibold,
    color: Colors.surface,
  },
});
