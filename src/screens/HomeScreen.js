import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Image,
} from "react-native";
import { StatusBar } from "expo-status-bar";
import { Ionicons } from "@expo/vector-icons";
import { useFocusEffect } from "@react-navigation/native";
import AsyncStorage from "@react-native-async-storage/async-storage";

import { DataManager } from "../services/DataManager";
import ExpenseCard from "../components/ExpenseCard";
import { Colors } from "../constants/Colors";
import { Spacing, FontSize, FontWeight } from "../constants/Dimensions";
import SyncStatusIndicator from "../components/SyncStatusIndicator";

export default function HomeScreen({ navigation }) {
  const [recentExpenses, setRecentExpenses] = useState([]);
  const [totalThisMonth, setTotalThisMonth] = useState(0);
  const [totalThisSeason, setTotalThisSeason] = useState(0);
  const [activeSeason, setActiveSeason] = useState(null);
  const [seasons, setSeasons] = useState([]); // SEASON INTEGRATION: For ExpenseCard season lookup
  const [viewMode, setViewMode] = useState("monthly"); // 'monthly' | 'seasonal'
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadData();
    loadViewModePreference();

    // Listen for data changes (e.g., after migration)
    const removeListener = DataManager.addDataChangeListener(() => {
      console.log("HomeScreen: Data change detected, reloading...");
      loadData();
    });

    return removeListener;
  }, []);

  // Ekran odaklandığında verileri yenile
  useFocusEffect(
    React.useCallback(() => {
      loadData();
    }, [])
  );

  // SEASON INTEGRATION: Reload data when viewMode changes
  useEffect(() => {
    if (activeSeason) {
      // Only reload if we have season data
      loadData();
    }
  }, [viewMode]);

  const loadData = async () => {
    try {
      setLoading(true);

      // DataManager'ı başlat
      await DataManager.initialize();

      // Aktif sezonu getir
      const currentActiveSeason = await DataManager.getActiveSeason();
      setActiveSeason(currentActiveSeason);
      console.log(
        "HomeScreen - Active season loaded:",
        currentActiveSeason?.name
      );

      // SEASON INTEGRATION: Load all seasons for ExpenseCard
      const allSeasons = await DataManager.getSeasons();
      setSeasons(allSeasons);
      console.log("HomeScreen - All seasons loaded:", allSeasons.length);

      // SEASON INTEGRATION: Filter expenses based on viewMode
      const allExpenses = await DataManager.getExpenses();
      let filteredExpenses = [];

      if (viewMode === "seasonal" && currentActiveSeason) {
        // Filter by active season
        const activeSeasonId =
          currentActiveSeason.id || currentActiveSeason._id;

        filteredExpenses = allExpenses.filter((expense) => {
          // Handle both string and object seasonId formats
          let expenseSeasonId = expense.seasonId;
          if (typeof expenseSeasonId === "object" && expenseSeasonId !== null) {
            expenseSeasonId = expenseSeasonId._id || expenseSeasonId.id;
          }

          return expenseSeasonId === activeSeasonId;
        });
        console.log(
          "HomeScreen - Seasonal filtering applied:",
          filteredExpenses.length,
          "expenses for season:",
          currentActiveSeason.name
        );
      } else {
        // Monthly filtering (default)
        const now = new Date();
        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);

        filteredExpenses = allExpenses.filter((expense) => {
          const expenseDate = new Date(expense.date);
          return expenseDate >= startOfMonth && expenseDate <= endOfMonth;
        });
        console.log(
          "HomeScreen - Monthly filtering applied:",
          filteredExpenses.length,
          "expenses for current month"
        );
      }

      // Sort by date (newest first) and take last 5
      filteredExpenses.sort((a, b) => new Date(b.date) - new Date(a.date));
      const recentFilteredExpenses = filteredExpenses.slice(0, 5);
      setRecentExpenses(recentFilteredExpenses);

      // Bu ayın toplam giderini hesapla
      const now = new Date();
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);

      const monthlyExpenses = allExpenses.filter((expense) => {
        const expenseDate = new Date(expense.date);
        return expenseDate >= startOfMonth && expenseDate <= endOfMonth;
      });

      const monthlyTotal = monthlyExpenses.reduce(
        (sum, expense) => sum + expense.amount,
        0
      );
      setTotalThisMonth(monthlyTotal);

      // Sezonluk toplam hesapla
      const seasonTotal = calculateSeasonTotal(
        allExpenses,
        currentActiveSeason
      );
      setTotalThisSeason(seasonTotal);
    } catch (error) {
      console.error("Veri yükleme hatası:", error);
      Alert.alert("Hata", "Veriler yüklenirken bir hata oluştu.");
    } finally {
      setLoading(false);
    }
  };

  // Sezonluk toplam hesaplama logic'i
  const calculateSeasonTotal = (expenses, season) => {
    if (!season || !expenses || expenses.length === 0) {
      return 0;
    }

    // Önce seasonId'ye göre filtreleme yap
    const seasonExpenses = expenses.filter((expense) => {
      // SeasonId kontrolü - hem id hem _id'yi kontrol et
      let expenseSeasonId = expense.seasonId;

      // Handle both string and object seasonId formats
      if (typeof expenseSeasonId === "object" && expenseSeasonId !== null) {
        expenseSeasonId = expenseSeasonId._id || expenseSeasonId.id;
      }

      const seasonId = season.id || season._id;

      if (expenseSeasonId === seasonId) {
        return true;
      }

      // Eğer seasonId eşleşmiyorsa, tarih aralığına göre kontrol et (backward compatibility)
      const expenseDate = new Date(expense.date);

      // Eğer sezonun tarih aralığı belirtilmemişse, seasonId eşleşmesi gerekir
      if (!season.startDate && !season.endDate) {
        return false; // seasonId eşleşmediği için false
      }

      // Sadece başlangıç tarihi varsa
      if (season.startDate && !season.endDate) {
        return expenseDate >= new Date(season.startDate);
      }

      // Sadece bitiş tarihi varsa
      if (!season.startDate && season.endDate) {
        return expenseDate <= new Date(season.endDate);
      }

      // Her iki tarih de varsa
      return (
        expenseDate >= new Date(season.startDate) &&
        expenseDate <= new Date(season.endDate)
      );
    });

    return seasonExpenses.reduce((sum, expense) => sum + expense.amount, 0);
  };

  // User preference yükleme
  const loadViewModePreference = async () => {
    try {
      const savedViewMode = await AsyncStorage.getItem("homescreen_view_mode");
      if (
        savedViewMode &&
        (savedViewMode === "monthly" || savedViewMode === "seasonal")
      ) {
        setViewMode(savedViewMode);
      }
    } catch (error) {
      console.warn("Failed to load view mode preference:", error);
    }
  };

  // User preference kaydetme
  const saveViewModePreference = async (mode) => {
    try {
      await AsyncStorage.setItem("homescreen_view_mode", mode);
    } catch (error) {
      console.warn("Failed to save view mode preference:", error);
    }
  };

  // Görünüm modu değiştirme
  const toggleViewMode = () => {
    const newMode = viewMode === "monthly" ? "seasonal" : "monthly";
    setViewMode(newMode);
    saveViewModePreference(newMode);
  };

  // Aktif toplam ve başlık
  const getCurrentTotal = () => {
    return viewMode === "monthly" ? totalThisMonth : totalThisSeason;
  };

  const getCurrentPeriodLabel = () => {
    if (viewMode === "monthly") {
      const now = new Date();
      return `${now.toLocaleDateString("tr-TR", {
        month: "long",
        year: "numeric",
      })}`;
    } else {
      return activeSeason?.name || "Aktif Sezon";
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("tr-TR", {
      style: "currency",
      currency: "TRY",
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("tr-TR");
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <Text>Yükleniyor...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar style="light" backgroundColor={Colors.primary} />

      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <Image
            source={require("../../assets/icon.png")}
            style={styles.headerLogo}
            resizeMode="contain"
          />
          <View style={styles.headerTextContainer}>
            <Text style={styles.headerTitle}>Çiftçi Not Defterim</Text>
            <Text style={styles.headerSubtitle}>Tarımsal Gider Takibi</Text>
          </View>
        </View>
      </View>

      {/* Sync Status Indicator */}
      <SyncStatusIndicator />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Aktif Sezon Bilgisi */}
        {activeSeason && (
          <TouchableOpacity
            style={styles.seasonCard}
            onPress={() =>
              navigation.navigate("SeasonDetail", {
                seasonId: activeSeason.id || activeSeason._id,
              })
            }
          >
            <View style={styles.seasonHeader}>
              <Text style={styles.seasonEmoji}>
                {activeSeason.emoji || "🌱"}
              </Text>
              <View style={styles.seasonInfo}>
                <Text style={styles.seasonName}>{activeSeason.name}</Text>
                <Text style={styles.seasonStatus}>Aktif Sezon</Text>
              </View>
              <Ionicons
                name="chevron-forward"
                size={20}
                color={Colors.textSecondary}
              />
            </View>
            <Text style={styles.seasonDates}>
              {activeSeason.startDate
                ? new Date(activeSeason.startDate).toLocaleDateString("tr-TR")
                : "Başlangıç: Açık"}
              {" - "}
              {activeSeason.endDate
                ? new Date(activeSeason.endDate).toLocaleDateString("tr-TR")
                : "Bitiş: Açık"}
            </Text>
          </TouchableOpacity>
        )}

        {/* Toplam Gider Kartı */}
        <View style={styles.summaryCard}>
          <View style={styles.summaryHeader}>
            <View style={styles.summaryTitleContainer}>
              <Ionicons
                name={viewMode === "monthly" ? "calendar" : "leaf"}
                size={24}
                color={Colors.primary}
              />
              <Text style={styles.summaryTitle}>{getCurrentPeriodLabel()}</Text>
            </View>
            <TouchableOpacity
              style={styles.toggleButton}
              onPress={toggleViewMode}
            >
              <Ionicons
                name="swap-horizontal"
                size={20}
                color={Colors.primary}
              />
              <Text style={styles.toggleText}>
                {viewMode === "monthly" ? "Sezonluk" : "Aylık"}
              </Text>
            </TouchableOpacity>
          </View>
          <Text style={styles.summaryAmount}>
            {formatCurrency(getCurrentTotal())}
          </Text>
          <Text style={styles.summarySubtext}>
            {viewMode === "monthly"
              ? "Bu ay toplam gider"
              : "Aktif sezon toplam gider"}
          </Text>
        </View>

        {/* Hızlı İşlemler */}
        <View style={styles.quickActions}>
          <Text style={styles.sectionTitle}>Hızlı İşlemler</Text>
          <View style={styles.actionButtons}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => navigation.navigate("AddExpense")}
            >
              <Ionicons name="add-circle" size={32} color={Colors.primary} />
              <Text style={styles.actionButtonText}>Gider Ekle</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => navigation.navigate("Expenses")}
            >
              <Ionicons name="list" size={32} color={Colors.secondary} />
              <Text style={styles.actionButtonText}>Giderleri Gör</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => navigation.navigate("Reports")}
            >
              <Ionicons name="bar-chart" size={32} color={Colors.info} />
              <Text style={styles.actionButtonText}>Raporlar</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Son Giderler - SEASON INTEGRATION: Dynamic title based on viewMode */}
        <View style={styles.recentExpenses}>
          <Text style={styles.sectionTitle}>
            {viewMode === "seasonal" && activeSeason
              ? `${activeSeason.name} - Son Giderler`
              : "Bu Ay - Son Giderler"}
          </Text>
          {recentExpenses.length === 0 ? (
            <View style={styles.emptyState}>
              <Ionicons
                name="receipt-outline"
                size={48}
                color={Colors.textSecondary}
              />
              <Text style={styles.emptyStateText}>Henüz gider kaydı yok</Text>
              <TouchableOpacity
                style={styles.addFirstExpenseButton}
                onPress={() => navigation.navigate("AddExpense")}
              >
                <Text style={styles.addFirstExpenseButtonText}>
                  İlk Gideri Ekle
                </Text>
              </TouchableOpacity>
            </View>
          ) : (
            recentExpenses.map((expense) => {
              // SEASON INTEGRATION: Find season for this expense
              const expenseSeason = seasons.find(
                (season) => (season.id || season._id) === expense.seasonId
              );

              return (
                <ExpenseCard
                  key={expense._id || expense.id}
                  expense={expense}
                  season={expenseSeason}
                  showSeason={viewMode === "seasonal"} // Show season info in seasonal mode
                  compact={false}
                  onPress={() =>
                    navigation.navigate("EditExpense", {
                      expenseId: expense._id || expense.id,
                    })
                  }
                  onEdit={() =>
                    navigation.navigate("EditExpense", {
                      expenseId: expense._id || expense.id,
                    })
                  }
                />
              );
            })
          )}
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  centered: {
    justifyContent: "center",
    alignItems: "center",
  },
  header: {
    backgroundColor: Colors.primary,
    paddingTop: 50,
    paddingBottom: Spacing.lg,
    paddingHorizontal: Spacing.md,
  },
  headerContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
  },
  headerLogo: {
    width: 40,
    height: 40,
    marginRight: Spacing.md,
  },
  headerTextContainer: {
    flex: 1,
    alignItems: "center",
  },
  headerTitle: {
    fontSize: FontSize.xxl,
    fontWeight: FontWeight.bold,
    color: Colors.surface,
    textAlign: "center",
  },
  headerSubtitle: {
    fontSize: FontSize.md,
    color: Colors.surface,
    textAlign: "center",
    marginTop: Spacing.xs,
    opacity: 0.9,
  },
  content: {
    flex: 1,
    padding: Spacing.md,
  },
  summaryCard: {
    backgroundColor: Colors.surface,
    borderRadius: 12,
    padding: Spacing.lg,
    marginBottom: Spacing.lg,
    elevation: 2,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  seasonCard: {
    backgroundColor: Colors.surface,
    borderRadius: 12,
    padding: Spacing.md,
    marginBottom: Spacing.md,
    elevation: 2,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  seasonHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: Spacing.xs,
  },
  seasonEmoji: {
    fontSize: 24,
    marginRight: Spacing.sm,
  },
  seasonInfo: {
    flex: 1,
  },
  seasonName: {
    fontSize: FontSize.lg,
    fontWeight: FontWeight.semibold,
    color: Colors.text,
  },
  seasonStatus: {
    fontSize: FontSize.sm,
    color: Colors.primary,
    fontWeight: FontWeight.medium,
  },
  seasonDates: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
    marginLeft: 36, // emoji + margin
  },
  summaryHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: Spacing.md,
  },
  summaryTitleContainer: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  summaryTitle: {
    fontSize: FontSize.lg,
    fontWeight: FontWeight.medium,
    color: Colors.text,
    marginLeft: Spacing.sm,
  },
  toggleButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: Colors.background,
    borderRadius: 8,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
  },
  toggleText: {
    fontSize: FontSize.sm,
    color: Colors.primary,
    fontWeight: FontWeight.medium,
    marginLeft: Spacing.xs,
  },
  summaryAmount: {
    fontSize: FontSize.xxxl,
    fontWeight: FontWeight.bold,
    color: Colors.primary,
    textAlign: "center",
  },
  summarySubtext: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
    textAlign: "center",
    marginTop: Spacing.xs,
  },
  quickActions: {
    marginBottom: Spacing.lg,
  },
  sectionTitle: {
    fontSize: FontSize.lg,
    fontWeight: FontWeight.semibold,
    color: Colors.text,
    marginBottom: Spacing.md,
  },
  actionButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  actionButton: {
    backgroundColor: Colors.surface,
    borderRadius: 12,
    padding: Spacing.md,
    alignItems: "center",
    flex: 1,
    marginHorizontal: Spacing.xs,
    elevation: 2,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  actionButtonText: {
    fontSize: FontSize.sm,
    fontWeight: FontWeight.medium,
    color: Colors.text,
    marginTop: Spacing.xs,
    textAlign: "center",
  },
  recentExpenses: {
    marginBottom: Spacing.xl,
  },
  emptyState: {
    backgroundColor: Colors.surface,
    borderRadius: 12,
    padding: Spacing.xl,
    alignItems: "center",
    elevation: 2,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  emptyStateText: {
    fontSize: FontSize.md,
    color: Colors.textSecondary,
    marginTop: Spacing.md,
    marginBottom: Spacing.lg,
  },
  addFirstExpenseButton: {
    backgroundColor: Colors.primary,
    borderRadius: 8,
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.lg,
  },
  addFirstExpenseButtonText: {
    color: Colors.surface,
    fontSize: FontSize.md,
    fontWeight: FontWeight.medium,
  },
  expenseItem: {
    backgroundColor: Colors.surface,
    borderRadius: 12,
    padding: Spacing.md,
    marginBottom: Spacing.sm,
    flexDirection: "row",
    alignItems: "center",
    elevation: 2,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  expenseIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.background,
    justifyContent: "center",
    alignItems: "center",
    marginRight: Spacing.md,
  },
  expenseEmoji: {
    fontSize: 20,
  },
  expenseDetails: {
    flex: 1,
  },
  expenseCategory: {
    fontSize: FontSize.md,
    fontWeight: FontWeight.medium,
    color: Colors.text,
  },
  expenseDescription: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
    marginTop: 2,
  },
  expenseDate: {
    fontSize: FontSize.xs,
    color: Colors.textSecondary,
    marginTop: 2,
  },
  expenseRight: {
    alignItems: "flex-end",
  },
  expenseAmount: {
    fontSize: FontSize.md,
    fontWeight: FontWeight.semibold,
    color: Colors.primary,
    marginBottom: Spacing.xs,
  },
  editButton: {
    padding: Spacing.xs,
    borderRadius: 10,
    backgroundColor: Colors.background,
  },
});
