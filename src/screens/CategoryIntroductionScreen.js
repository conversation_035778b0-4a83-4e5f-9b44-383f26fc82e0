import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Dimensions,
  SafeAreaView,
  StatusBar,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { Colors } from '../constants/Colors';
import { Spacing, FontSize, FontWeight, BorderRadius } from '../constants/Dimensions';
import { DefaultCategories } from '../models/DataModels';
import Logger, { LogCategory } from '../utils/Logger';
import Analytics, { AnalyticsEvent } from '../utils/Analytics';

const { width: screenWidth } = Dimensions.get('window');

export default function CategoryIntroductionScreen({ navigation, onComplete }) {
  const [currentCategoryIndex, setCurrentCategoryIndex] = useState(0);
  const [viewedCategories, setViewedCategories] = useState(new Set([0]));
  const scrollViewRef = useRef(null);
  const fadeAnim = useRef(new Animated.Value(1)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;

  const currentCategory = DefaultCategories[currentCategoryIndex];
  const isLastCategory = currentCategoryIndex === DefaultCategories.length - 1;

  const handleNext = () => {
    if (!isLastCategory) {
      const nextIndex = currentCategoryIndex + 1;
      animateToCategory(nextIndex);
    } else {
      handleComplete();
    }
  };

  const handlePrevious = () => {
    if (currentCategoryIndex > 0) {
      const prevIndex = currentCategoryIndex - 1;
      animateToCategory(prevIndex);
    }
  };

  const animateToCategory = (index) => {
    // Animate out
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 150,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 0.95,
        duration: 150,
        useNativeDriver: true,
      }),
    ]).start(() => {
      // Update category
      setCurrentCategoryIndex(index);
      setViewedCategories(prev => new Set([...prev, index]));
      
      // Scroll to category
      scrollViewRef.current?.scrollTo({
        x: index * screenWidth,
        animated: false,
      });

      // Animate in
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 150,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 150,
          useNativeDriver: true,
        }),
      ]).start();
    });

    // Track category view
    Analytics.trackEvent(AnalyticsEvent.CATEGORY_INTRODUCTION_VIEWED, {
      categoryId: DefaultCategories[index].id,
      categoryName: DefaultCategories[index].name,
      categoryIndex: index,
    });
  };

  const handleSkip = () => {
    Analytics.trackEvent(AnalyticsEvent.CATEGORY_INTRODUCTION_SKIPPED, {
      currentCategoryIndex,
      viewedCategoriesCount: viewedCategories.size,
    });
    
    handleComplete(true);
  };

  const handleComplete = (skipped = false) => {
    Analytics.trackEvent(AnalyticsEvent.CATEGORY_INTRODUCTION_COMPLETED, {
      skipped,
      viewedCategoriesCount: viewedCategories.size,
      totalCategories: DefaultCategories.length,
    });

    Logger.info(LogCategory.USER_ACTION, 'Category introduction completed', {
      skipped,
      viewedCategories: viewedCategories.size,
    });

    if (onComplete) {
      onComplete();
    } else {
      navigation.goBack();
    }
  };

  const goToCategory = (index) => {
    animateToCategory(index);
  };

  const renderCategoryCard = (category, index) => (
    <View key={category.id} style={styles.categoryCard}>
      <Animated.View
        style={[
          styles.categoryContent,
          {
            opacity: fadeAnim,
            transform: [{ scale: scaleAnim }],
          },
        ]}
      >
        {/* Category Icon */}
        <View style={[styles.categoryIconContainer, { backgroundColor: category.color }]}>
          <Text style={styles.categoryIcon}>{category.emoji}</Text>
        </View>

        {/* Category Info */}
        <Text style={styles.categoryName}>{category.name}</Text>
        <Text style={styles.categoryDescription}>{category.description}</Text>

        {/* Usage Examples */}
        <View style={styles.examplesContainer}>
          <Text style={styles.examplesTitle}>Örnek Giderler:</Text>
          {getCategoryExamples(category.id).map((example, idx) => (
            <View key={idx} style={styles.exampleItem}>
              <Ionicons name="checkmark-circle-outline" size={16} color={Colors.success} />
              <Text style={styles.exampleText}>{example}</Text>
            </View>
          ))}
        </View>

        {/* Tips */}
        <View style={styles.tipsContainer}>
          <View style={styles.tipItem}>
            <Ionicons name="bulb-outline" size={20} color={Colors.warning} />
            <Text style={styles.tipText}>{getCategoryTip(category.id)}</Text>
          </View>
        </View>
      </Animated.View>
    </View>
  );

  const renderPagination = () => (
    <View style={styles.pagination}>
      {DefaultCategories.map((_, index) => (
        <TouchableOpacity
          key={index}
          style={[
            styles.paginationDot,
            index === currentCategoryIndex && styles.paginationDotActive,
            viewedCategories.has(index) && styles.paginationDotViewed,
          ]}
          onPress={() => goToCategory(index)}
        />
      ))}
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.background} />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => navigation.goBack()}>
          <Ionicons name="chevron-back" size={24} color={Colors.text} />
        </TouchableOpacity>
        
        <Text style={styles.headerTitle}>Kategori Rehberi</Text>
        
        <TouchableOpacity style={styles.skipButton} onPress={handleSkip}>
          <Text style={styles.skipButtonText}>Atla</Text>
        </TouchableOpacity>
      </View>

      {/* Content */}
      <ScrollView
        ref={scrollViewRef}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        scrollEnabled={false}
        style={styles.categoriesContainer}
      >
        {DefaultCategories.map((category, index) => renderCategoryCard(category, index))}
      </ScrollView>

      {/* Footer */}
      <View style={styles.footer}>
        {renderPagination()}
        
        <View style={styles.navigationButtons}>
          {currentCategoryIndex > 0 && (
            <TouchableOpacity style={styles.previousButton} onPress={handlePrevious}>
              <Ionicons name="chevron-back" size={20} color={Colors.textSecondary} />
              <Text style={styles.previousButtonText}>Önceki</Text>
            </TouchableOpacity>
          )}
          
          <View style={styles.spacer} />
          
          <TouchableOpacity style={styles.nextButton} onPress={handleNext}>
            <Text style={styles.nextButtonText}>
              {isLastCategory ? 'Tamamla' : 'Sonraki'}
            </Text>
            <Ionicons 
              name={isLastCategory ? "checkmark" : "chevron-forward"} 
              size={20} 
              color={Colors.surface} 
            />
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
}

// Helper functions for category examples and tips
const getCategoryExamples = (categoryId) => {
  const examples = {
    fertilizer: ['NPK gübresi', 'Organik gübre', 'Yaprak gübresi', 'Kompost'],
    pesticide: ['Böcek ilacı', 'Fungisit', 'Herbisit', 'Akarisit'],
    water: ['Sulama suyu', 'Damla sulama', 'Yağmurlama sistemi', 'Su faturası'],
    labor: ['Gündelik işçi', 'Sezonluk işçi', 'Hasat işçiliği', 'Makine operatörü'],
    fuel: ['Mazot', 'Benzin', 'Elektrik', 'Doğalgaz'],
    equipment: ['Traktör', 'Pulluk', 'Ekim makinesi', 'Hasat makinesi'],
    seed: ['Buğday tohumu', 'Mısır tohumu', 'Sebze fidesi', 'Meyve fidanı'],
    maintenance: ['Traktör bakımı', 'Yedek parça', 'Tamir masrafı', 'Servis'],
    transport: ['Ürün nakliyesi', 'Malzeme taşıma', 'Yakıt masrafı', 'Araç kirası'],
    other: ['Sigorta', 'Vergi', 'Kira', 'Çeşitli masraflar'],
  };
  
  return examples[categoryId] || [];
};

const getCategoryTip = (categoryId) => {
  const tips = {
    fertilizer: 'Gübre alımlarınızı mevsim başında toplu yaparak maliyet avantajı sağlayabilirsiniz.',
    pesticide: 'İlaç kullanımında dozaj ve uygulama zamanına dikkat ederek etkinliği artırın.',
    water: 'Damla sulama sistemi ile su tasarrufu yaparak maliyetleri düşürebilirsiniz.',
    labor: 'İşçilik maliyetlerini azaltmak için makine kullanımını artırabilirsiniz.',
    fuel: 'Yakıt tüketimini optimize etmek için traktör bakımlarını düzenli yapın.',
    equipment: 'Makine alımlarında ikinci el seçenekleri de değerlendirin.',
    seed: 'Kaliteli tohum kullanarak verim artışı sağlayabilirsiniz.',
    maintenance: 'Düzenli bakım ile büyük arıza masraflarından kaçının.',
    transport: 'Nakliye maliyetlerini azaltmak için komşu çiftçilerle işbirliği yapın.',
    other: 'Diğer masraflarınızı da kaydetmeyi unutmayın, küçük giderler büyük toplamlar yapar.',
  };
  
  return tips[categoryId] || 'Bu kategoriyi düzenli olarak takip ederek gider kontrolü sağlayın.';
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  backButton: {
    padding: Spacing.sm,
  },
  headerTitle: {
    fontSize: FontSize.lg,
    fontWeight: FontWeight.bold,
    color: Colors.text,
  },
  skipButton: {
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.md,
  },
  skipButtonText: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
  },
  categoriesContainer: {
    flex: 1,
  },
  categoryCard: {
    width: screenWidth,
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: Spacing.xl,
  },
  categoryContent: {
    alignItems: 'center',
    maxWidth: 320,
    alignSelf: 'center',
  },
  categoryIconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Spacing.xl,
  },
  categoryIcon: {
    fontSize: 40,
  },
  categoryName: {
    fontSize: FontSize.xxl,
    fontWeight: FontWeight.bold,
    color: Colors.text,
    textAlign: 'center',
    marginBottom: Spacing.md,
  },
  categoryDescription: {
    fontSize: FontSize.md,
    color: Colors.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: Spacing.xl,
  },
  examplesContainer: {
    width: '100%',
    marginBottom: Spacing.xl,
  },
  examplesTitle: {
    fontSize: FontSize.md,
    fontWeight: FontWeight.medium,
    color: Colors.text,
    marginBottom: Spacing.md,
  },
  exampleItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  exampleText: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
    marginLeft: Spacing.sm,
  },
  tipsContainer: {
    width: '100%',
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.md,
    padding: Spacing.md,
    borderLeftWidth: 4,
    borderLeftColor: Colors.warning,
  },
  tipItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  tipText: {
    fontSize: FontSize.sm,
    color: Colors.text,
    marginLeft: Spacing.sm,
    flex: 1,
    lineHeight: 20,
  },
  footer: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.lg,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  pagination: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Spacing.lg,
  },
  paginationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: Colors.border,
    marginHorizontal: 4,
  },
  paginationDotActive: {
    backgroundColor: Colors.primary,
    width: 24,
  },
  paginationDotViewed: {
    backgroundColor: Colors.success,
  },
  navigationButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  previousButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.lg,
  },
  previousButtonText: {
    fontSize: FontSize.md,
    color: Colors.textSecondary,
    marginLeft: Spacing.xs,
  },
  spacer: {
    flex: 1,
  },
  nextButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.primary,
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.xl,
    borderRadius: BorderRadius.lg,
  },
  nextButtonText: {
    fontSize: FontSize.md,
    color: Colors.surface,
    fontWeight: FontWeight.medium,
    marginRight: Spacing.xs,
  },
});
