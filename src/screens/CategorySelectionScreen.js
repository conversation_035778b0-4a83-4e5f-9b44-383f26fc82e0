import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  StatusBar,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { Colors } from '../constants/Colors';
import { Spacing, FontSize, FontWeight, BorderRadius } from '../constants/Dimensions';
import { DefaultCategories } from '../models/DataModels';
import Logger, { LogCategory } from '../utils/Logger';
import Analytics, { AnalyticsEvent } from '../utils/Analytics';

export default function CategorySelectionScreen({ 
  navigation, 
  route,
  onCategorySelect,
  selectedCategoryId = null,
  showAddButton = true,
  title = 'Kategori Seç',
}) {
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState(selectedCategoryId);

  useEffect(() => {
    loadCategories();
    Analytics.trackEvent(AnalyticsEvent.CATEGORY_SELECTION_OPENED);
  }, []);

  const loadCategories = async () => {
    try {
      setLoading(true);
      
      // Load categories from database
      // This would be replaced with actual database call
      const mockCategories = [
        ...DefaultCategories,
        // Add some custom categories
        {
          id: 'custom_1',
          name: 'Organik Gübre',
          emoji: '🌿',
          color: '#8BC34A',
          icon: 'leaf-outline',
          isDefault: false,
          description: 'Organik gübre alımları',
        },
        {
          id: 'custom_2',
          name: 'Sera Malzemeleri',
          emoji: '🏠',
          color: '#FF9800',
          icon: 'home-outline',
          isDefault: false,
          description: 'Sera yapım ve bakım malzemeleri',
        },
      ];

      setCategories(mockCategories);
      Logger.info(LogCategory.DATABASE, 'Categories loaded', { count: mockCategories.length });
    } catch (error) {
      Logger.error(LogCategory.DATABASE, 'Failed to load categories', error);
      Alert.alert('Hata', 'Kategoriler yüklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  const handleCategorySelect = (category) => {
    setSelectedCategory(category.id);
    
    Analytics.trackEvent(AnalyticsEvent.CATEGORY_SELECTED, {
      categoryId: category.id,
      categoryName: category.name,
      isDefault: category.isDefault,
    });

    Logger.info(LogCategory.USER_ACTION, 'Category selected', {
      categoryId: category.id,
      categoryName: category.name,
    });

    if (onCategorySelect) {
      onCategorySelect(category);
    } else {
      // Navigate back with selected category
      navigation.goBack();
    }
  };

  const handleAddCategory = () => {
    Analytics.trackEvent(AnalyticsEvent.ADD_CATEGORY_BUTTON_PRESSED, {
      source: 'category_selection',
    });

    navigation.navigate('CategoryCreation', {
      onCategoryCreated: (newCategory) => {
        // Add new category to list
        setCategories(prev => [...prev, newCategory]);
        // Auto-select the new category
        handleCategorySelect(newCategory);
      },
    });
  };

  const renderCategoryItem = (category) => (
    <TouchableOpacity
      key={category.id}
      style={[
        styles.categoryItem,
        { borderColor: category.color },
        selectedCategory === category.id && [
          styles.categoryItemSelected,
          { backgroundColor: category.color }
        ],
      ]}
      onPress={() => handleCategorySelect(category)}
    >
      <View style={styles.categoryContent}>
        <View style={styles.categoryHeader}>
          <Text style={styles.categoryEmoji}>{category.emoji}</Text>
          <View style={styles.categoryInfo}>
            <Text
              style={[
                styles.categoryName,
                selectedCategory === category.id && styles.categoryNameSelected,
              ]}
            >
              {category.name}
            </Text>
            {category.description && (
              <Text
                style={[
                  styles.categoryDescription,
                  selectedCategory === category.id && styles.categoryDescriptionSelected,
                ]}
              >
                {category.description}
              </Text>
            )}
          </View>
          {!category.isDefault && (
            <View style={styles.customBadge}>
              <Text style={styles.customBadgeText}>Özel</Text>
            </View>
          )}
        </View>
        
        {selectedCategory === category.id && (
          <View style={styles.selectedIndicator}>
            <Ionicons name="checkmark-circle" size={24} color={Colors.surface} />
          </View>
        )}
      </View>
    </TouchableOpacity>
  );

  const renderAddCategoryButton = () => (
    <TouchableOpacity
      style={styles.addCategoryButton}
      onPress={handleAddCategory}
    >
      <View style={styles.addCategoryContent}>
        <View style={styles.addIconContainer}>
          <Ionicons name="add" size={32} color={Colors.primary} />
        </View>
        <View style={styles.addCategoryInfo}>
          <Text style={styles.addCategoryTitle}>Yeni Kategori Ekle</Text>
          <Text style={styles.addCategoryDescription}>
            Kendi özel kategorinizi oluşturun
          </Text>
        </View>
        <Ionicons name="chevron-forward" size={20} color={Colors.textSecondary} />
      </View>
    </TouchableOpacity>
  );

  const renderCategoryGroups = () => {
    const defaultCategories = categories.filter(cat => cat.isDefault !== false);
    const customCategories = categories.filter(cat => cat.isDefault === false);

    return (
      <ScrollView style={styles.categoriesContainer} showsVerticalScrollIndicator={false}>
        {/* Add Category Button */}
        {showAddButton && renderAddCategoryButton()}

        {/* Default Categories */}
        {defaultCategories.length > 0 && (
          <View style={styles.categoryGroup}>
            <Text style={styles.groupTitle}>Varsayılan Kategoriler</Text>
            <Text style={styles.groupDescription}>
              Tarımsal giderler için hazır kategoriler
            </Text>
            {defaultCategories.map(renderCategoryItem)}
          </View>
        )}

        {/* Custom Categories */}
        {customCategories.length > 0 && (
          <View style={styles.categoryGroup}>
            <Text style={styles.groupTitle}>Özel Kategoriler</Text>
            <Text style={styles.groupDescription}>
              Sizin oluşturduğunuz kategoriler
            </Text>
            {customCategories.map(renderCategoryItem)}
          </View>
        )}

        {/* Empty State for Custom Categories */}
        {customCategories.length === 0 && showAddButton && (
          <View style={styles.emptyCustomCategories}>
            <Ionicons name="folder-outline" size={48} color={Colors.textSecondary} />
            <Text style={styles.emptyTitle}>Henüz özel kategori yok</Text>
            <Text style={styles.emptyDescription}>
              İhtiyaçlarınıza özel kategoriler oluşturabilirsiniz
            </Text>
          </View>
        )}
      </ScrollView>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor={Colors.background} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.primary} />
          <Text style={styles.loadingText}>Kategoriler yükleniyor...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.background} />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.headerButton} onPress={() => navigation.goBack()}>
          <Ionicons name="chevron-back" size={24} color={Colors.text} />
        </TouchableOpacity>
        
        <Text style={styles.headerTitle}>{title}</Text>
        
        <View style={styles.headerButton} />
      </View>

      {/* Content */}
      {renderCategoryGroups()}

      {/* Footer Info */}
      <View style={styles.footer}>
        <Text style={styles.footerText}>
          💡 İpucu: Kategoriler giderlerinizi düzenli tutmanıza yardımcı olur
        </Text>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: FontSize.md,
    color: Colors.textSecondary,
    marginTop: Spacing.md,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  headerButton: {
    padding: Spacing.sm,
    minWidth: 40,
  },
  headerTitle: {
    fontSize: FontSize.lg,
    fontWeight: FontWeight.bold,
    color: Colors.text,
  },
  categoriesContainer: {
    flex: 1,
    paddingHorizontal: Spacing.lg,
  },
  addCategoryButton: {
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.lg,
    borderWidth: 2,
    borderColor: Colors.primary,
    borderStyle: 'dashed',
    marginVertical: Spacing.lg,
  },
  addCategoryContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: Spacing.lg,
  },
  addIconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: Colors.primaryLight,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.md,
  },
  addCategoryInfo: {
    flex: 1,
  },
  addCategoryTitle: {
    fontSize: FontSize.md,
    fontWeight: FontWeight.bold,
    color: Colors.primary,
    marginBottom: Spacing.xs,
  },
  addCategoryDescription: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
  },
  categoryGroup: {
    marginVertical: Spacing.lg,
  },
  groupTitle: {
    fontSize: FontSize.lg,
    fontWeight: FontWeight.bold,
    color: Colors.text,
    marginBottom: Spacing.xs,
  },
  groupDescription: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
    marginBottom: Spacing.md,
  },
  categoryItem: {
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.lg,
    borderWidth: 2,
    marginBottom: Spacing.md,
    overflow: 'hidden',
  },
  categoryItemSelected: {
    borderWidth: 2,
  },
  categoryContent: {
    padding: Spacing.lg,
  },
  categoryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryEmoji: {
    fontSize: 32,
    marginRight: Spacing.md,
  },
  categoryInfo: {
    flex: 1,
  },
  categoryName: {
    fontSize: FontSize.md,
    fontWeight: FontWeight.bold,
    color: Colors.text,
    marginBottom: Spacing.xs,
  },
  categoryNameSelected: {
    color: Colors.surface,
  },
  categoryDescription: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
  },
  categoryDescriptionSelected: {
    color: Colors.surface,
    opacity: 0.9,
  },
  customBadge: {
    backgroundColor: Colors.warning,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.sm,
  },
  customBadgeText: {
    fontSize: FontSize.xs,
    color: Colors.surface,
    fontWeight: FontWeight.bold,
  },
  selectedIndicator: {
    position: 'absolute',
    top: Spacing.md,
    right: Spacing.md,
  },
  emptyCustomCategories: {
    alignItems: 'center',
    paddingVertical: Spacing.xl,
    marginVertical: Spacing.lg,
  },
  emptyTitle: {
    fontSize: FontSize.md,
    fontWeight: FontWeight.medium,
    color: Colors.text,
    marginTop: Spacing.md,
    marginBottom: Spacing.sm,
  },
  emptyDescription: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
  },
  footer: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  footerText: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
    textAlign: 'center',
    lineHeight: 18,
  },
});
