import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  StatusBar,
  Alert,
  Switch,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';

import { Colors } from '../constants/Colors';
import { Spacing, FontSize, FontWeight, BorderRadius } from '../constants/Dimensions';
import { DefaultCategories } from '../models/DataModels';
import Logger, { LogCategory } from '../utils/Logger';
import Analytics, { AnalyticsEvent } from '../utils/Analytics';

const SetupStep = {
  WELCOME: 'welcome',
  CATEGORIES: 'categories',
  PREFERENCES: 'preferences',
  COMPLETE: 'complete',
};

export default function FirstTimeSetupScreen({ navigation, onComplete }) {
  const [currentStep, setCurrentStep] = useState(SetupStep.WELCOME);
  const [selectedCategories, setSelectedCategories] = useState(new Set());
  const [preferences, setPreferences] = useState({
    currency: 'TRY',
    notifications: true,
    autoBackup: true,
    defaultSeason: 'spring',
    theme: 'light',
  });
  const [setupStartTime] = useState(Date.now());

  useEffect(() => {
    // Pre-select all default categories
    const defaultCategoryIds = DefaultCategories.map(cat => cat.id);
    setSelectedCategories(new Set(defaultCategoryIds));

    Analytics.trackEvent(AnalyticsEvent.FIRST_TIME_SETUP_STARTED);
    Logger.info(LogCategory.USER_ACTION, 'First-time setup started');
  }, []);

  const handleNext = () => {
    switch (currentStep) {
      case SetupStep.WELCOME:
        setCurrentStep(SetupStep.CATEGORIES);
        break;
      case SetupStep.CATEGORIES:
        setCurrentStep(SetupStep.PREFERENCES);
        break;
      case SetupStep.PREFERENCES:
        handleComplete();
        break;
      default:
        break;
    }
  };

  const handleBack = () => {
    switch (currentStep) {
      case SetupStep.CATEGORIES:
        setCurrentStep(SetupStep.WELCOME);
        break;
      case SetupStep.PREFERENCES:
        setCurrentStep(SetupStep.CATEGORIES);
        break;
      default:
        break;
    }
  };

  const handleSkip = () => {
    Alert.alert(
      'Kurulumu Atla',
      'Kurulumu atlarsanız varsayılan ayarlar kullanılacak. Daha sonra ayarlardan değiştirebilirsiniz.',
      [
        { text: 'İptal', style: 'cancel' },
        { 
          text: 'Atla', 
          onPress: () => {
            Analytics.trackEvent(AnalyticsEvent.FIRST_TIME_SETUP_SKIPPED, {
              currentStep,
              timeSpent: Date.now() - setupStartTime,
            });
            handleComplete(true);
          }
        },
      ]
    );
  };

  const handleComplete = async (skipped = false) => {
    try {
      // Save setup completion
      await AsyncStorage.setItem('first_time_setup_completed', 'true');
      await AsyncStorage.setItem('setup_completion_date', new Date().toISOString());
      
      if (skipped) {
        await AsyncStorage.setItem('setup_skipped', 'true');
      } else {
        // Save selected categories
        const categoryArray = Array.from(selectedCategories);
        await AsyncStorage.setItem('selected_categories', JSON.stringify(categoryArray));
        
        // Save preferences
        await AsyncStorage.setItem('user_preferences', JSON.stringify(preferences));
      }

      Analytics.trackEvent(AnalyticsEvent.FIRST_TIME_SETUP_COMPLETED, {
        skipped,
        selectedCategoriesCount: selectedCategories.size,
        totalTime: Date.now() - setupStartTime,
        preferences,
      });

      Logger.info(LogCategory.USER_ACTION, 'First-time setup completed', {
        skipped,
        selectedCategories: selectedCategories.size,
      });

      if (onComplete) {
        onComplete();
      } else {
        navigation.replace('Main');
      }
    } catch (error) {
      Logger.error(LogCategory.USER_ACTION, 'Failed to complete setup', error);
      Alert.alert('Hata', 'Kurulum tamamlanırken bir hata oluştu.');
    }
  };

  const toggleCategory = (categoryId) => {
    const newSelected = new Set(selectedCategories);
    if (newSelected.has(categoryId)) {
      newSelected.delete(categoryId);
    } else {
      newSelected.add(categoryId);
    }
    setSelectedCategories(newSelected);
  };

  const updatePreference = (key, value) => {
    setPreferences(prev => ({ ...prev, [key]: value }));
  };

  const renderProgressBar = () => {
    const steps = Object.values(SetupStep).slice(0, -1); // Exclude COMPLETE
    const currentIndex = steps.indexOf(currentStep);
    const progress = ((currentIndex + 1) / steps.length) * 100;

    return (
      <View style={styles.progressContainer}>
        <View style={styles.progressBar}>
          <View style={[styles.progressFill, { width: `${progress}%` }]} />
        </View>
        <Text style={styles.progressText}>
          {currentIndex + 1} / {steps.length}
        </Text>
      </View>
    );
  };

  const renderWelcomeStep = () => (
    <View style={styles.stepContainer}>
      <View style={styles.iconContainer}>
        <Text style={styles.stepIcon}>🌾</Text>
      </View>
      
      <Text style={styles.stepTitle}>Hoş Geldiniz!</Text>
      <Text style={styles.stepDescription}>
        Çiftçi Not Defterim'i kullanmaya başlamadan önce birkaç basit ayar yapalım. 
        Bu sadece birkaç dakika sürecek.
      </Text>

      <View style={styles.featureList}>
        <View style={styles.featureItem}>
          <Ionicons name="checkmark-circle" size={20} color={Colors.success} />
          <Text style={styles.featureText}>Kişiselleştirilmiş kategoriler</Text>
        </View>
        <View style={styles.featureItem}>
          <Ionicons name="checkmark-circle" size={20} color={Colors.success} />
          <Text style={styles.featureText}>Tercihlerinize göre ayarlar</Text>
        </View>
        <View style={styles.featureItem}>
          <Ionicons name="checkmark-circle" size={20} color={Colors.success} />
          <Text style={styles.featureText}>Daha iyi kullanıcı deneyimi</Text>
        </View>
      </View>
    </View>
  );

  const renderCategoriesStep = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Kategorilerinizi Seçin</Text>
      <Text style={styles.stepDescription}>
        Hangi gider kategorilerini kullanmak istiyorsunuz? Daha sonra yenilerini ekleyebilirsiniz.
      </Text>

      <ScrollView style={styles.categoriesContainer} showsVerticalScrollIndicator={false}>
        {DefaultCategories.map((category) => (
          <TouchableOpacity
            key={category.id}
            style={[
              styles.categoryItem,
              selectedCategories.has(category.id) && styles.categoryItemSelected,
            ]}
            onPress={() => toggleCategory(category.id)}
          >
            <View style={styles.categoryInfo}>
              <Text style={styles.categoryEmoji}>{category.emoji}</Text>
              <View style={styles.categoryText}>
                <Text style={styles.categoryName}>{category.name}</Text>
                <Text style={styles.categoryDescription}>{category.description}</Text>
              </View>
            </View>
            <Ionicons
              name={selectedCategories.has(category.id) ? 'checkmark-circle' : 'ellipse-outline'}
              size={24}
              color={selectedCategories.has(category.id) ? Colors.primary : Colors.border}
            />
          </TouchableOpacity>
        ))}
      </ScrollView>

      <Text style={styles.selectionCount}>
        {selectedCategories.size} kategori seçildi
      </Text>
    </View>
  );

  const renderPreferencesStep = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Tercihlerinizi Ayarlayın</Text>
      <Text style={styles.stepDescription}>
        Uygulamayı tercihlerinize göre özelleştirin.
      </Text>

      <View style={styles.preferencesContainer}>
        <View style={styles.preferenceItem}>
          <View style={styles.preferenceInfo}>
            <Text style={styles.preferenceName}>Para Birimi</Text>
            <Text style={styles.preferenceDescription}>Gider tutarları için kullanılacak</Text>
          </View>
          <View style={styles.currencySelector}>
            {['TRY', 'USD', 'EUR'].map((currency) => (
              <TouchableOpacity
                key={currency}
                style={[
                  styles.currencyOption,
                  preferences.currency === currency && styles.currencyOptionSelected,
                ]}
                onPress={() => updatePreference('currency', currency)}
              >
                <Text
                  style={[
                    styles.currencyText,
                    preferences.currency === currency && styles.currencyTextSelected,
                  ]}
                >
                  {currency}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <View style={styles.preferenceItem}>
          <View style={styles.preferenceInfo}>
            <Text style={styles.preferenceName}>Bildirimler</Text>
            <Text style={styles.preferenceDescription}>Hatırlatmalar ve güncellemeler</Text>
          </View>
          <Switch
            value={preferences.notifications}
            onValueChange={(value) => updatePreference('notifications', value)}
            trackColor={{ false: Colors.border, true: Colors.primary }}
            thumbColor={Colors.surface}
          />
        </View>

        <View style={styles.preferenceItem}>
          <View style={styles.preferenceInfo}>
            <Text style={styles.preferenceName}>Otomatik Yedekleme</Text>
            <Text style={styles.preferenceDescription}>Verilerinizi bulutta yedekle</Text>
          </View>
          <Switch
            value={preferences.autoBackup}
            onValueChange={(value) => updatePreference('autoBackup', value)}
            trackColor={{ false: Colors.border, true: Colors.primary }}
            thumbColor={Colors.surface}
          />
        </View>

        <View style={styles.preferenceItem}>
          <View style={styles.preferenceInfo}>
            <Text style={styles.preferenceName}>Varsayılan Mevsim</Text>
            <Text style={styles.preferenceDescription}>Yeni giderler için başlangıç mevsimi</Text>
          </View>
          <View style={styles.seasonSelector}>
            {[
              { id: 'spring', name: 'İlkbahar', emoji: '🌱' },
              { id: 'summer', name: 'Yaz', emoji: '☀️' },
              { id: 'autumn', name: 'Sonbahar', emoji: '🍂' },
              { id: 'winter', name: 'Kış', emoji: '❄️' },
            ].map((season) => (
              <TouchableOpacity
                key={season.id}
                style={[
                  styles.seasonOption,
                  preferences.defaultSeason === season.id && styles.seasonOptionSelected,
                ]}
                onPress={() => updatePreference('defaultSeason', season.id)}
              >
                <Text style={styles.seasonEmoji}>{season.emoji}</Text>
                <Text
                  style={[
                    styles.seasonText,
                    preferences.defaultSeason === season.id && styles.seasonTextSelected,
                  ]}
                >
                  {season.name}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </View>
    </View>
  );

  const renderCurrentStep = () => {
    switch (currentStep) {
      case SetupStep.WELCOME:
        return renderWelcomeStep();
      case SetupStep.CATEGORIES:
        return renderCategoriesStep();
      case SetupStep.PREFERENCES:
        return renderPreferencesStep();
      default:
        return null;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.background} />
      
      {/* Header */}
      <View style={styles.header}>
        {renderProgressBar()}
        <TouchableOpacity style={styles.skipButton} onPress={handleSkip}>
          <Text style={styles.skipButtonText}>Atla</Text>
        </TouchableOpacity>
      </View>

      {/* Content */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderCurrentStep()}
      </ScrollView>

      {/* Footer */}
      <View style={styles.footer}>
        <View style={styles.navigationButtons}>
          {currentStep !== SetupStep.WELCOME && (
            <TouchableOpacity style={styles.backButton} onPress={handleBack}>
              <Ionicons name="chevron-back" size={20} color={Colors.textSecondary} />
              <Text style={styles.backButtonText}>Geri</Text>
            </TouchableOpacity>
          )}
          
          <View style={styles.spacer} />
          
          <TouchableOpacity
            style={[
              styles.nextButton,
              currentStep === SetupStep.CATEGORIES && selectedCategories.size === 0 && styles.nextButtonDisabled,
            ]}
            onPress={handleNext}
            disabled={currentStep === SetupStep.CATEGORIES && selectedCategories.size === 0}
          >
            <Text style={styles.nextButtonText}>
              {currentStep === SetupStep.PREFERENCES ? 'Tamamla' : 'İleri'}
            </Text>
            <Ionicons name="chevron-forward" size={20} color={Colors.surface} />
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  progressContainer: {
    flex: 1,
    marginRight: Spacing.lg,
  },
  progressBar: {
    height: 4,
    backgroundColor: Colors.border,
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: Colors.primary,
    borderRadius: 2,
  },
  progressText: {
    fontSize: FontSize.xs,
    color: Colors.textSecondary,
    marginTop: Spacing.xs,
  },
  skipButton: {
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.md,
  },
  skipButtonText: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
  },
  content: {
    flex: 1,
  },
  stepContainer: {
    padding: Spacing.xl,
  },
  iconContainer: {
    alignItems: 'center',
    marginBottom: Spacing.xl,
  },
  stepIcon: {
    fontSize: 64,
  },
  stepTitle: {
    fontSize: FontSize.xxl,
    fontWeight: FontWeight.bold,
    color: Colors.text,
    textAlign: 'center',
    marginBottom: Spacing.md,
  },
  stepDescription: {
    fontSize: FontSize.md,
    color: Colors.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: Spacing.xl,
  },
  featureList: {
    marginTop: Spacing.lg,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  featureText: {
    fontSize: FontSize.md,
    color: Colors.text,
    marginLeft: Spacing.md,
  },
  categoriesContainer: {
    maxHeight: 400,
    marginBottom: Spacing.lg,
  },
  categoryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: Spacing.md,
    borderRadius: BorderRadius.md,
    borderWidth: 1,
    borderColor: Colors.border,
    marginBottom: Spacing.sm,
    backgroundColor: Colors.surface,
  },
  categoryItemSelected: {
    borderColor: Colors.primary,
    backgroundColor: Colors.primaryLight,
  },
  categoryInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  categoryEmoji: {
    fontSize: 24,
    marginRight: Spacing.md,
  },
  categoryText: {
    flex: 1,
  },
  categoryName: {
    fontSize: FontSize.md,
    fontWeight: FontWeight.medium,
    color: Colors.text,
    marginBottom: Spacing.xs,
  },
  categoryDescription: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
  },
  selectionCount: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
    textAlign: 'center',
  },
  preferencesContainer: {
    marginTop: Spacing.lg,
  },
  preferenceItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: Spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  preferenceInfo: {
    flex: 1,
  },
  preferenceName: {
    fontSize: FontSize.md,
    fontWeight: FontWeight.medium,
    color: Colors.text,
    marginBottom: Spacing.xs,
  },
  preferenceDescription: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
  },
  currencySelector: {
    flexDirection: 'row',
  },
  currencyOption: {
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.md,
    borderRadius: BorderRadius.sm,
    borderWidth: 1,
    borderColor: Colors.border,
    marginLeft: Spacing.xs,
  },
  currencyOptionSelected: {
    borderColor: Colors.primary,
    backgroundColor: Colors.primary,
  },
  currencyText: {
    fontSize: FontSize.sm,
    color: Colors.text,
  },
  currencyTextSelected: {
    color: Colors.surface,
  },
  seasonSelector: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  seasonOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.md,
    borderRadius: BorderRadius.sm,
    borderWidth: 1,
    borderColor: Colors.border,
    margin: Spacing.xs,
  },
  seasonOptionSelected: {
    borderColor: Colors.primary,
    backgroundColor: Colors.primary,
  },
  seasonEmoji: {
    fontSize: 16,
    marginRight: Spacing.xs,
  },
  seasonText: {
    fontSize: FontSize.sm,
    color: Colors.text,
  },
  seasonTextSelected: {
    color: Colors.surface,
  },
  footer: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.lg,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  navigationButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.lg,
  },
  backButtonText: {
    fontSize: FontSize.md,
    color: Colors.textSecondary,
    marginLeft: Spacing.xs,
  },
  spacer: {
    flex: 1,
  },
  nextButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.primary,
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.xl,
    borderRadius: BorderRadius.lg,
  },
  nextButtonDisabled: {
    backgroundColor: Colors.border,
  },
  nextButtonText: {
    fontSize: FontSize.md,
    color: Colors.surface,
    fontWeight: FontWeight.medium,
    marginRight: Spacing.xs,
  },
});
