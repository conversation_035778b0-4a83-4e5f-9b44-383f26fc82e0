import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Switch,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as DocumentPicker from 'expo-document-picker';

import { Colors } from '../constants/Colors';
import { Spacing, FontSize, FontWeight } from '../constants/Dimensions';
import { useAuth } from '../context/AuthContext';
import AuthBasedBackupSection from '../components/AuthBasedBackupSection';
import { DataManager } from '../services/DataManager';
import { APIClient } from '../services/APIClient';

export default function SettingsScreen({ navigation }) {
  const { user, isAuthenticated, isGuestMode, signOut } = useAuth();

  // TWO-MODE SYSTEM: Tracking mode state
  const [trackingMode, setTrackingMode] = useState('simple');
  const [isLoadingMode, setIsLoadingMode] = useState(true);
  const [isSwitchingMode, setIsSwitchingMode] = useState(false);

  // Debug authentication state
  console.log('Settings Screen - Auth State:', {
    user,
    isAuthenticated,
    isGuestMode,
    userExists: !!user,
    shouldShowLogout: isAuthenticated && !isGuestMode
  });

  // TWO-MODE SYSTEM: Load current tracking mode
  useEffect(() => {
    loadTrackingMode();
  }, [isAuthenticated, isGuestMode]);

  const loadTrackingMode = async () => {
    try {
      setIsLoadingMode(true);

      if (isGuestMode) {
        // For guest users, get tracking mode from DataManager (local only)
        const mode = DataManager.getTrackingMode();
        setTrackingMode(mode);
        console.log('Guest mode: Tracking mode loaded locally:', mode);
      } else if (isAuthenticated) {
        // For authenticated users, get from backend
        try {
          const response = await DataManager.apiClient.getTrackingMode();
          if (response && response.success) {
            setTrackingMode(response.data.mode);
          } else {
            // Fallback to local mode if backend fails
            const mode = DataManager.getTrackingMode();
            setTrackingMode(mode);
          }
        } catch (error) {
          console.warn('Backend tracking mode failed, using local:', error);
          // Fallback to local mode
          const mode = DataManager.getTrackingMode();
          setTrackingMode(mode);
        }
      } else {
        // Default to simple mode for unauthenticated users
        setTrackingMode('simple');
      }
    } catch (error) {
      console.error('Error loading tracking mode:', error);
      setTrackingMode('simple');
    } finally {
      setIsLoadingMode(false);
    }
  };

  const handleTrackingModeToggle = async (newMode) => {
    if (isSwitchingMode) return;

    // Check if already in the requested mode
    if (trackingMode === newMode) {
      console.log(`Already in ${newMode} mode, no action needed`);
      return;
    }

    const modeText = newMode === 'detailed' ? 'detaylı' : 'basit';

    Alert.alert(
      'Takip Modunu Değiştir',
      `${modeText.charAt(0).toUpperCase() + modeText.slice(1)} moda geçmek istediğinizden emin misiniz?${
        newMode === 'detailed'
          ? '\n\nDetaylı modda tarlalarınızı ve ürünlerinizi ayrı ayrı takip edebilirsiniz.'
          : '\n\nBasit modda genel gider takibi yaparsınız.'
      }`,
      [
        {
          text: 'İptal',
          style: 'cancel',
        },
        {
          text: 'Değiştir',
          onPress: () => switchTrackingMode(newMode),
        },
      ]
    );
  };

  const switchTrackingMode = async (newMode, confirmed = false) => {
    try {
      setIsSwitchingMode(true);

      if (isGuestMode) {
        // For guest users, handle mode switching locally
        if (newMode === 'detailed') {
          const result = await DataManager.activateDetailedModeForGuest();
          if (result.success) {
            // Set explicit tracking mode preference
            await DataManager.setTrackingMode('detailed');
            setTrackingMode('detailed');
            Alert.alert(
              'Başarılı',
              result.message,
              [{ text: 'Tamam' }]
            );
          } else {
            throw new Error('Detaylı mod aktifleştirilemedi');
          }
        } else {
          // Switch to simple mode for guest
          await DataManager.setTrackingMode('simple');
          setTrackingMode('simple');
          Alert.alert(
            'Başarılı',
            'Basit moda geçildi',
            [{ text: 'Tamam' }]
          );
        }
      } else if (isAuthenticated) {
        // For authenticated users, use backend API
        try {
          const response = await DataManager.apiClient.switchTrackingMode(newMode, confirmed);

          if (response && response.success) {
            setTrackingMode(response.data.mode);

            Alert.alert(
              'Başarılı',
              response.message,
              [{ text: 'Tamam' }]
            );
          } else {
            throw new Error(response?.message || 'Mod değiştirilemedi');
          }
        } catch (error) {
          console.warn('Backend mode switch failed, using local fallback:', error);
          // Fallback to local mode switching for authenticated users
          setTrackingMode(newMode);
          Alert.alert(
            'Başarılı',
            `${newMode === 'detailed' ? 'Detaylı' : 'Basit'} moda geçildi (yerel)`,
            [{ text: 'Tamam' }]
          );
        }
      }
    } catch (error) {
      console.error('Error switching tracking mode:', error);

      // Check if this is a confirmation required error
      if (error.requiresConfirmation && error.validationData) {
        try {
          const confirmed = await handleConfirmationRequired(newMode, error.validationData);
          if (confirmed) {
            return; // User confirmed and switch was successful
          }
        } catch (confirmError) {
          console.error('Error handling confirmation:', confirmError);
        }
      } else if (error.message && error.message.includes('çok sık yapılamaz')) {
        // Rate limit error - show user-friendly message
        Alert.alert(
          'Çok Sık Değişiklik',
          'Takip modu değişikliği çok sık yapılamaz. Lütfen birkaç dakika bekleyip tekrar deneyin.',
          [{ text: 'Tamam' }]
        );
      } else {
        // Generic error
        const errorMessage = error.message || 'Takip modu değiştirilirken bir hata oluştu.';
        Alert.alert(
          'Hata',
          errorMessage,
          [{ text: 'Tamam' }]
        );
      }
    } finally {
      setIsSwitchingMode(false);
    }
  };

  const handleConfirmationRequired = async (newMode, validationData) => {
    const { validation } = validationData;
    const modeText = newMode === 'detailed' ? 'detaylı' : 'basit';

    // Build confirmation message
    let confirmationMessage = `${modeText.charAt(0).toUpperCase() + modeText.slice(1)} moda geçmek için onay gerekiyor.\n\n`;

    // Add warnings
    if (validation.warnings && validation.warnings.length > 0) {
      confirmationMessage += 'Uyarılar:\n';
      validation.warnings.forEach(warning => {
        confirmationMessage += `• ${warning.message}\n`;
      });
      confirmationMessage += '\n';
    }

    // Add data impact
    if (validation.dataImpact) {
      if (validation.dataImpact.existingExpenses > 0) {
        confirmationMessage += `• ${validation.dataImpact.existingExpenses} adet mevcut gideriniz varsayılan tarlaya atanacak\n`;
      }
    }

    // Add benefits
    if (validation.benefits && validation.benefits.length > 0) {
      confirmationMessage += '\nFaydalar:\n';
      validation.benefits.forEach(benefit => {
        confirmationMessage += `• ${benefit}\n`;
      });
    }

    return new Promise((resolve) => {
      Alert.alert(
        'Onay Gerekiyor',
        confirmationMessage,
        [
          {
            text: 'İptal',
            style: 'cancel',
            onPress: () => resolve(false)
          },
          {
            text: 'Onayla',
            onPress: async () => {
              try {
                await switchTrackingMode(newMode, true);
                resolve(true);
              } catch (error) {
                console.error('Error with confirmed switch:', error);
                Alert.alert(
                  'Hata',
                  'Onaylanan işlem sırasında bir hata oluştu.',
                  [{ text: 'Tamam' }]
                );
                resolve(false);
              }
            }
          }
        ]
      );
    });
  };

  const handleExportData = () => {
    Alert.alert(
      'Veri Dışa Aktarma',
      'Bu özellik yakında eklenecek.',
      [{ text: 'Tamam' }]
    );
  };

  const handleImportData = () => {
    Alert.alert(
      'Veri İçe Aktarma',
      'Bu özellik yakında eklenecek.',
      [{ text: 'Tamam' }]
    );
  };



  const handleAbout = () => {
    Alert.alert(
      'Hakkında',
      'Çiftçi Not Defterim v1.0.0\n\nTarımsal giderlerinizi kolayca takip edin.',
      [{ text: 'Tamam' }]
    );
  };

  // Import/Export Handlers
  const handleExcelImport = async () => {
    try {
      // Check authentication
      if (!isAuthenticated || isGuestMode) {
        Alert.alert(
          'Giriş Gerekli',
          'Excel içe aktarma özelliği için Google ile giriş yapmanız gerekiyor.',
          [{ text: 'Tamam' }]
        );
        return;
      }

      // Pick Excel file
      const result = await DocumentPicker.getDocumentAsync({
        type: [
          'application/vnd.ms-excel',
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        ],
        copyToCacheDirectory: true,
      });

      if (result.canceled) {
        return;
      }

      const file = result.assets[0];

      // Show loading
      Alert.alert(
        'Yükleniyor',
        'Excel dosyası işleniyor, lütfen bekleyin...',
        [],
        { cancelable: false }
      );

      // Upload file
      const response = await APIClient.uploadExcelFile(file.uri, file.name);

      Alert.alert(
        'Başarılı',
        `Excel dosyası başarıyla içe aktarıldı. ${response.data?.successCount || 0} kayıt eklendi.`,
        [{ text: 'Tamam' }]
      );

    } catch (error) {
      console.error('Excel import error:', error);
      Alert.alert(
        'Hata',
        'Excel dosyası içe aktarılırken bir hata oluştu. Lütfen dosya formatını kontrol edin.',
        [{ text: 'Tamam' }]
      );
    }
  };

  const handleDownloadTemplate = async () => {
    try {
      // Check authentication
      if (!isAuthenticated || isGuestMode) {
        Alert.alert(
          'Giriş Gerekli',
          'Template indirme özelliği için Google ile giriş yapmanız gerekiyor.',
          [{ text: 'Tamam' }]
        );
        return;
      }

      Alert.alert(
        'İndiriliyor',
        'Excel şablonu indiriliyor...',
        [],
        { cancelable: false }
      );

      await APIClient.downloadTemplate();

      Alert.alert(
        'Başarılı',
        'Excel şablonu başarıyla indirildi.',
        [{ text: 'Tamam' }]
      );
    } catch (error) {
      console.error('Template download error:', error);
      Alert.alert(
        'Hata',
        'Template indirilemedi. Lütfen daha sonra tekrar deneyin.',
        [{ text: 'Tamam' }]
      );
    }
  };

  const handleLogout = () => {
    Alert.alert(
      'Çıkış Yap',
      'Hesabınızdan çıkış yapmak istediğinizden emin misiniz?',
      [
        {
          text: 'İptal',
          style: 'cancel',
        },
        {
          text: 'Çıkış Yap',
          style: 'destructive',
          onPress: async () => {
            try {
              const result = await signOut();
              if (result.success) {
                Alert.alert(
                  'Çıkış Başarılı',
                  'Hesabınızdan güvenli bir şekilde çıkış yapıldı. Verileriniz Google hesabınızda güvenle saklanmaktadır ve tekrar giriş yaptığınızda erişebilirsiniz.',
                  [{ text: 'Tamam' }]
                );
              } else {
                Alert.alert('Hata', result.error || 'Çıkış yapılırken bir hata oluştu.');
              }
            } catch (error) {
              console.error('Logout error:', error);
              Alert.alert('Hata', 'Çıkış yapılırken bir hata oluştu.');
            }
          },
        },
      ]
    );
  };

  const SettingItem = ({ icon, title, subtitle, onPress, color = Colors.text }) => (
    <TouchableOpacity style={styles.settingItem} onPress={onPress}>
      <View style={styles.settingIcon}>
        <Ionicons name={icon} size={24} color={color} />
      </View>
      <View style={styles.settingContent}>
        <Text style={styles.settingTitle}>{title}</Text>
        {subtitle && <Text style={styles.settingSubtitle}>{subtitle}</Text>}
      </View>
      <Ionicons name="chevron-forward" size={20} color={Colors.textSecondary} />
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Ayarlar</Text>
        <TouchableOpacity
          style={styles.homeButton}
          onPress={() => navigation.navigate('Home')}
        >
          <Ionicons name="home-outline" size={24} color={Colors.primary} />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Veri Yönetimi */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Veri Yönetimi</Text>
          
          <SettingItem
            icon="download-outline"
            title="Veri Dışa Aktarma"
            subtitle="Verilerinizi Excel dosyası olarak kaydedin"
            onPress={handleExportData}
            color={Colors.success}
          />
          
          <SettingItem
            icon="cloud-upload-outline"
            title="Veri İçe Aktarma"
            subtitle="Daha önce dışa aktardığınız verileri geri yükleyin"
            onPress={handleImportData}
            color={Colors.info}
          />
          
          <AuthBasedBackupSection />
        </View>

        {/* TWO-MODE SYSTEM: Takip Modu - Tüm kullanıcılar için erişilebilir */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Takip Modu</Text>

          <View style={styles.trackingModeContainer}>
            <View style={styles.trackingModeInfo}>
              <View style={styles.trackingModeIcon}>
                <Ionicons
                  name={trackingMode === 'detailed' ? 'grid' : 'list'}
                  size={24}
                  color={Colors.primary}
                />
              </View>
              <View style={styles.trackingModeContent}>
                <Text style={styles.trackingModeTitle}>
                  Detaylı Tarla Takibi
                </Text>
                <Text style={styles.trackingModeSubtitle}>
                  {trackingMode === 'detailed'
                    ? 'Tarlalarınızı ve ürünlerinizi ayrı ayrı takip ediyorsunuz'
                    : 'Genel gider takibi yapıyorsunuz'
                  }
                  {isGuestMode && ' (Yerel Depolama)'}
                </Text>
              </View>
            </View>

            {isLoadingMode ? (
              <ActivityIndicator size="small" color={Colors.primary} />
            ) : (
              <Switch
                value={trackingMode === 'detailed'}
                onValueChange={(value) =>
                  handleTrackingModeToggle(value ? 'detailed' : 'simple')
                }
                disabled={isSwitchingMode}
                trackColor={{ false: Colors.border, true: Colors.primary }}
                thumbColor={trackingMode === 'detailed' ? Colors.surface : Colors.textSecondary}
              />
            )}
          </View>

          <Text style={styles.trackingModeDescription}>
            {trackingMode === 'detailed'
              ? 'Detaylı modda her tarlanız için ayrı gider takibi yapabilir, ürün bazlı maliyet analizi alabilirsiniz.'
              : 'Basit modda tüm giderlerinizi genel olarak takip edersiniz. İstediğiniz zaman detaylı moda geçebilirsiniz.'
            }
            {isGuestMode && ' Misafir modunda verileriniz sadece bu cihazda saklanır.'}
          </Text>

          {/* Detailed Mode Management Options - Tüm kullanıcılar için erişilebilir */}
          {trackingMode === 'detailed' && (
            <View style={styles.detailedModeOptions}>
              <SettingItem
                icon="grid-outline"
                title="Tarla Yönetimi"
                subtitle={`Tarlalarınızı ekleyin, düzenleyin ve yönetin${isGuestMode ? ' (Yerel)' : ''}`}
                onPress={() => navigation.navigate('FieldManagement')}
                color={Colors.primary}
              />

              <SettingItem
                icon="leaf-outline"
                title="Ürün Yönetimi"
                subtitle={`Yetiştirdiğiniz ürünleri tanımlayın${isGuestMode ? ' (Yerel)' : ''}`}
                onPress={() => navigation.navigate('CropManagement')}
                color={Colors.success}
              />
            </View>
          )}
        </View>

        {/* Sezon Yönetimi */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Sezon Yönetimi</Text>

          <SettingItem
            icon="calendar-outline"
            title="Sezonlarım"
            subtitle={`Sezonlarınızı oluşturun, düzenleyin ve yönetin${isGuestMode ? ' (Yerel)' : ''}`}
            onPress={() => navigation.navigate('SeasonManagement')}
            color={Colors.primary}
          />
        </View>

        {/* Veri İçe/Dışa Aktarma - Sadece Google ile giriş yapmış kullanıcılar için */}
        {isAuthenticated && !isGuestMode && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Veri İçe/Dışa Aktarma</Text>

            <SettingItem
              icon="chatbubble-ellipses-outline"
              title="AI Asistan ile İçe Aktar"
              subtitle="Giderlerinizi doğal dilde yazarak AI ile hızlıca ekleyin"
              onPress={() => navigation.navigate('AIChatImport')}
              color={Colors.primary}
            />

            <SettingItem
              icon="document-outline"
              title="Excel İçe Aktar"
              subtitle="Excel dosyasından toplu gider aktarımı yapın"
              onPress={() => handleExcelImport()}
              color={Colors.success}
            />

            <SettingItem
              icon="download-outline"
              title="Excel Template İndir"
              subtitle="Gider girişi için Excel şablonunu indirin"
              onPress={() => handleDownloadTemplate()}
              color={Colors.warning}
            />

            <SettingItem
              icon="share-outline"
              title="Verileri Dışa Aktar"
              subtitle="Giderlerinizi Excel formatında dışa aktarın"
              onPress={() => handleExportData()}
              color={Colors.info}
            />
          </View>
        )}

        {/* Hesap */}
        {isAuthenticated && !isGuestMode && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Hesap</Text>

            <View style={styles.userInfo}>
              <View style={styles.userIcon}>
                <Ionicons name="person" size={24} color={Colors.primary} />
              </View>
              <View style={styles.userDetails}>
                <Text style={styles.userName}>{user?.name || 'Kullanıcı'}</Text>
                <Text style={styles.userEmail}>{user?.email}</Text>
              </View>
            </View>

            <SettingItem
              icon="log-out-outline"
              title="Çıkış Yap"
              subtitle="Hesabınızdan çıkış yapın"
              onPress={handleLogout}
              color={Colors.error}
            />
          </View>
        )}

        {/* Uygulama */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Uygulama</Text>

          <SettingItem
            icon="information-circle-outline"
            title="Hakkında"
            subtitle="Uygulama bilgileri ve sürüm"
            onPress={handleAbout}
            color={Colors.primary}
          />
        </View>

        {/* Bilgi Kartı */}
        <View style={styles.infoCard}>
          <Ionicons name="leaf" size={32} color={Colors.primary} />
          <Text style={styles.infoTitle}>Çiftçi Not Defterim</Text>
          <Text style={styles.infoDescription}>
            Tarımsal giderlerinizi kolayca takip edin, kategorilere ayırın ve raporlarla analiz edin.
          </Text>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    backgroundColor: Colors.primary,
    paddingTop: 50,
    paddingBottom: Spacing.lg,
    paddingHorizontal: Spacing.md,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: FontSize.xxl,
    fontWeight: FontWeight.bold,
    color: Colors.surface,
    flex: 1,
    textAlign: 'center',
  },
  homeButton: {
    backgroundColor: Colors.surface,
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    padding: Spacing.md,
  },
  section: {
    marginBottom: Spacing.lg,
  },
  sectionTitle: {
    fontSize: FontSize.lg,
    fontWeight: FontWeight.semibold,
    color: Colors.text,
    marginBottom: Spacing.md,
  },
  settingItem: {
    backgroundColor: Colors.surface,
    borderRadius: 12,
    padding: Spacing.md,
    marginBottom: Spacing.sm,
    flexDirection: 'row',
    alignItems: 'center',
    elevation: 2,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  settingIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.background,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.md,
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    fontSize: FontSize.md,
    fontWeight: FontWeight.medium,
    color: Colors.text,
  },
  settingSubtitle: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
    marginTop: 2,
  },
  userInfo: {
    backgroundColor: Colors.surface,
    borderRadius: 12,
    padding: Spacing.md,
    marginBottom: Spacing.sm,
    flexDirection: 'row',
    alignItems: 'center',
    elevation: 2,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  userIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: Colors.background,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.md,
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    fontSize: FontSize.md,
    fontWeight: FontWeight.semibold,
    color: Colors.text,
  },
  userEmail: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
    marginTop: 2,
  },
  infoCard: {
    backgroundColor: Colors.surface,
    borderRadius: 12,
    padding: Spacing.xl,
    alignItems: 'center',
    marginTop: Spacing.lg,
    marginBottom: Spacing.xl,
    elevation: 2,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  infoTitle: {
    fontSize: FontSize.lg,
    fontWeight: FontWeight.bold,
    color: Colors.text,
    marginTop: Spacing.md,
    marginBottom: Spacing.sm,
  },
  infoDescription: {
    fontSize: FontSize.md,
    color: Colors.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
  },
  // TWO-MODE SYSTEM: Tracking mode styles
  trackingModeContainer: {
    backgroundColor: Colors.surface,
    borderRadius: 12,
    padding: Spacing.md,
    marginBottom: Spacing.sm,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    elevation: 2,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  trackingModeInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  trackingModeIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.background,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.md,
  },
  trackingModeContent: {
    flex: 1,
  },
  trackingModeTitle: {
    fontSize: FontSize.md,
    fontWeight: FontWeight.semibold,
    color: Colors.text,
    marginBottom: 2,
  },
  trackingModeSubtitle: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
  },
  trackingModeDescription: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
    lineHeight: 20,
    marginTop: Spacing.xs,
    paddingHorizontal: Spacing.sm,
  },
  detailedModeOptions: {
    marginTop: Spacing.lg,
    paddingTop: Spacing.lg,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
});
