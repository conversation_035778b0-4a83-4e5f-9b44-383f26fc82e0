import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { Spacing, FontSize, FontWeight } from '../constants/Dimensions';
import { useAuth } from '../context/AuthContext';
import { DataManager } from '../services/DataManager';

export default function AIChatImportScreen({ navigation }) {
  const { user, isAuthenticated, isGuestMode } = useAuth();
  const [messages, setMessages] = useState([]);
  const [inputText, setInputText] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [currentSession, setCurrentSession] = useState(null);
  const [extractedData, setExtractedData] = useState([]);
  const [showPreview, setShowPreview] = useState(false);
  
  const scrollViewRef = useRef(null);
  const inputRef = useRef(null);
  const fadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Check authentication
    if (!isAuthenticated || isGuestMode) {
      Alert.alert(
        'Giriş Gerekli',
        'AI asistan özelliği için Google ile giriş yapmanız gerekli.',
        [
          { text: 'Tamam', onPress: () => navigation.goBack() }
        ]
      );
      return;
    }

    // Initialize chat with welcome message
    initializeChat();
  }, []);

  useEffect(() => {
    // Scroll to bottom when new messages are added
    if (messages.length > 0) {
      setTimeout(() => {
        scrollViewRef.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  }, [messages]);

  const initializeChat = () => {
    const welcomeMessage = {
      id: Date.now(),
      type: 'ai',
      content: 'Merhaba! Ben tarım giderlerinizi analiz eden AI asistanınızım. Giderlerinizi doğal dilde yazabilirsiniz.',
      timestamp: new Date(),
      suggestions: [
        'Dün 50 kg gübre aldım 2000 lira',
        'Geçen hafta 3 işçi getirdim budama için',
        'Bu ay su faturası 800 lira ödedim'
      ]
    };
    setMessages([welcomeMessage]);
    
    // Fade in animation
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 500,
      useNativeDriver: true,
    }).start();
  };

  const sendMessage = async () => {
    if (!inputText.trim() || isProcessing) return;

    const userMessage = {
      id: Date.now(),
      type: 'user',
      content: inputText.trim(),
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    setIsProcessing(true);

    try {
      // Call AI service
      const response = await DataManager.apiClient.processAIText(inputText.trim(), {
        sessionId: currentSession?.id,
        previousData: extractedData
      });

      if (response.success) {
        handleAIResponse(response.data);
      } else {
        handleAIError(response.error);
      }
    } catch (error) {
      console.error('AI processing error:', error);
      handleAIError({ message: 'AI servisi şu anda kullanılamıyor' });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleAIResponse = (data) => {
    const { confidence, extractedData: newData, questions, warnings } = data;

    // Create AI response message
    const aiMessage = {
      id: Date.now(),
      type: 'ai',
      content: generateAIResponseText(data),
      timestamp: new Date(),
      confidence,
      data: newData,
      questions,
      warnings
    };

    setMessages(prev => [...prev, aiMessage]);

    // Update extracted data
    if (newData && newData.length > 0) {
      setExtractedData(prev => [...prev, ...newData]);
    }

    // Show questions if any
    if (questions && questions.length > 0) {
      setTimeout(() => {
        showQuestions(questions);
      }, 1000);
    }

    // Show preview if we have good data
    if (confidence >= 0.7 && newData && newData.length > 0) {
      setShowPreview(true);
    }
  };

  const handleAIError = (error) => {
    const errorMessage = {
      id: Date.now(),
      type: 'ai',
      content: `Üzgünüm, bir hata oluştu: ${error.message || 'Bilinmeyen hata'}`,
      timestamp: new Date(),
      isError: true,
      suggestions: [
        'Tekrar deneyin',
        'Daha basit bir cümle yazın',
        'Manuel girişe geç'
      ]
    };

    setMessages(prev => [...prev, errorMessage]);
  };

  const generateAIResponseText = (data) => {
    const { confidence, extractedData, questions, warnings } = data;

    if (confidence >= 0.8 && extractedData.length > 0) {
      return `Harika! ${extractedData.length} gider kaydı buldum. Güven oranı: %${Math.round(confidence * 100)}`;
    } else if (confidence >= 0.6 && questions.length > 0) {
      return `${extractedData.length} kayıt buldum ama birkaç sorum var. Güven oranı: %${Math.round(confidence * 100)}`;
    } else if (confidence < 0.4) {
      return 'Metninizi tam anlayamadım. Daha net bir şekilde yazabilir misiniz?';
    } else {
      return `${extractedData.length} kayıt buldum ama kontrol etmenizi öneririm. Güven oranı: %${Math.round(confidence * 100)}`;
    }
  };

  const showQuestions = (questions) => {
    questions.forEach((question, index) => {
      setTimeout(() => {
        const questionMessage = {
          id: Date.now() + index,
          type: 'ai',
          content: question,
          timestamp: new Date(),
          isQuestion: true
        };
        setMessages(prev => [...prev, questionMessage]);
      }, index * 500);
    });
  };

  const handleSuggestionPress = (suggestion) => {
    setInputText(suggestion);
    inputRef.current?.focus();
  };

  const handlePreviewConfirm = () => {
    if (extractedData.length === 0) {
      Alert.alert('Hata', 'Kaydedilecek veri bulunamadı');
      return;
    }

    navigation.navigate('AIDataPreview', {
      extractedData,
      onConfirm: handleDataConfirm
    });
  };

  const handleDataConfirm = async (confirmedData) => {
    try {
      // Save data using DataManager
      const results = await Promise.all(
        confirmedData.map(item => DataManager.createExpense(item))
      );

      const successCount = results.filter(r => r.success).length;
      
      Alert.alert(
        'Başarılı',
        `${successCount} gider kaydı başarıyla eklendi!`,
        [
          { text: 'Tamam', onPress: () => navigation.goBack() }
        ]
      );
    } catch (error) {
      console.error('Data save error:', error);
      Alert.alert('Hata', 'Veriler kaydedilirken bir hata oluştu');
    }
  };

  const renderMessage = (message) => {
    const isUser = message.type === 'user';
    const isError = message.isError;
    const isQuestion = message.isQuestion;

    return (
      <View key={message.id} style={[
        styles.messageContainer,
        isUser ? styles.userMessage : styles.aiMessage
      ]}>
        <View style={[
          styles.messageBubble,
          isUser ? styles.userBubble : styles.aiBubble,
          isError && styles.errorBubble,
          isQuestion && styles.questionBubble
        ]}>
          <Text style={[
            styles.messageText,
            isUser ? styles.userText : styles.aiText,
            isError && styles.errorText
          ]}>
            {message.content}
          </Text>
          
          {message.confidence && (
            <View style={styles.confidenceContainer}>
              <Text style={styles.confidenceText}>
                Güven: %{Math.round(message.confidence * 100)}
              </Text>
            </View>
          )}

          {message.suggestions && (
            <View style={styles.suggestionsContainer}>
              {message.suggestions.map((suggestion, index) => (
                <TouchableOpacity
                  key={index}
                  style={styles.suggestionButton}
                  onPress={() => handleSuggestionPress(suggestion)}
                >
                  <Text style={styles.suggestionText}>{suggestion}</Text>
                </TouchableOpacity>
              ))}
            </View>
          )}
        </View>
        
        <Text style={styles.timestamp}>
          {message.timestamp.toLocaleTimeString('tr-TR', { 
            hour: '2-digit', 
            minute: '2-digit' 
          })}
        </Text>
      </View>
    );
  };

  return (
    <KeyboardAvoidingView 
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={Colors.white} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>AI Asistan</Text>
        <View style={styles.headerRight}>
          {extractedData.length > 0 && (
            <TouchableOpacity
              style={styles.previewButton}
              onPress={handlePreviewConfirm}
            >
              <Text style={styles.previewButtonText}>
                Önizle ({extractedData.length})
              </Text>
            </TouchableOpacity>
          )}
        </View>
      </View>

      <Animated.View style={[styles.content, { opacity: fadeAnim }]}>
        <ScrollView
          ref={scrollViewRef}
          style={styles.messagesContainer}
          showsVerticalScrollIndicator={false}
        >
          {messages.map(renderMessage)}
          
          {isProcessing && (
            <View style={styles.processingContainer}>
              <ActivityIndicator size="small" color={Colors.primary} />
              <Text style={styles.processingText}>AI düşünüyor...</Text>
            </View>
          )}
        </ScrollView>

        <View style={styles.inputContainer}>
          <TextInput
            ref={inputRef}
            style={styles.textInput}
            placeholder="Giderlerinizi yazın... (örn: dün 50 kg gübre aldım 2000 lira)"
            placeholderTextColor={Colors.textSecondary}
            value={inputText}
            onChangeText={setInputText}
            multiline
            maxLength={2000}
            editable={!isProcessing}
          />
          <TouchableOpacity
            style={[
              styles.sendButton,
              (!inputText.trim() || isProcessing) && styles.sendButtonDisabled
            ]}
            onPress={sendMessage}
            disabled={!inputText.trim() || isProcessing}
          >
            <Ionicons 
              name="send" 
              size={20} 
              color={(!inputText.trim() || isProcessing) ? Colors.textSecondary : Colors.white} 
            />
          </TouchableOpacity>
        </View>
      </Animated.View>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.primary,
    paddingTop: 50,
    paddingBottom: 15,
    paddingHorizontal: Spacing.medium,
  },
  backButton: {
    padding: Spacing.small,
  },
  headerTitle: {
    flex: 1,
    fontSize: FontSize.large,
    fontWeight: FontWeight.bold,
    color: Colors.white,
    textAlign: 'center',
    marginRight: 40,
  },
  headerRight: {
    minWidth: 80,
    alignItems: 'flex-end',
  },
  previewButton: {
    backgroundColor: Colors.white,
    paddingHorizontal: Spacing.small,
    paddingVertical: 4,
    borderRadius: 12,
  },
  previewButtonText: {
    color: Colors.primary,
    fontSize: FontSize.small,
    fontWeight: FontWeight.medium,
  },
  content: {
    flex: 1,
  },
  messagesContainer: {
    flex: 1,
    paddingHorizontal: Spacing.medium,
    paddingTop: Spacing.medium,
  },
  messageContainer: {
    marginBottom: Spacing.medium,
  },
  userMessage: {
    alignItems: 'flex-end',
  },
  aiMessage: {
    alignItems: 'flex-start',
  },
  messageBubble: {
    maxWidth: '80%',
    padding: Spacing.medium,
    borderRadius: 16,
  },
  userBubble: {
    backgroundColor: Colors.primary,
    borderBottomRightRadius: 4,
  },
  aiBubble: {
    backgroundColor: Colors.white,
    borderBottomLeftRadius: 4,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  errorBubble: {
    backgroundColor: Colors.errorLight,
    borderColor: Colors.error,
  },
  questionBubble: {
    backgroundColor: Colors.warningLight,
    borderColor: Colors.warning,
  },
  messageText: {
    fontSize: FontSize.medium,
    lineHeight: 20,
  },
  userText: {
    color: Colors.white,
  },
  aiText: {
    color: Colors.textPrimary,
  },
  errorText: {
    color: Colors.error,
  },
  confidenceContainer: {
    marginTop: Spacing.small,
    alignSelf: 'flex-end',
  },
  confidenceText: {
    fontSize: FontSize.small,
    color: Colors.textSecondary,
    fontStyle: 'italic',
  },
  suggestionsContainer: {
    marginTop: Spacing.small,
    gap: Spacing.small,
  },
  suggestionButton: {
    backgroundColor: Colors.primaryLight,
    paddingHorizontal: Spacing.small,
    paddingVertical: 6,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  suggestionText: {
    color: Colors.primary,
    fontSize: FontSize.small,
  },
  timestamp: {
    fontSize: FontSize.small,
    color: Colors.textSecondary,
    marginTop: 4,
    alignSelf: 'flex-end',
  },
  processingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Spacing.medium,
    gap: Spacing.small,
  },
  processingText: {
    color: Colors.textSecondary,
    fontSize: FontSize.medium,
    fontStyle: 'italic',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingHorizontal: Spacing.medium,
    paddingVertical: Spacing.medium,
    backgroundColor: Colors.white,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
    gap: Spacing.small,
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: 20,
    paddingHorizontal: Spacing.medium,
    paddingVertical: Spacing.small,
    fontSize: FontSize.medium,
    maxHeight: 100,
    textAlignVertical: 'top',
  },
  sendButton: {
    backgroundColor: Colors.primary,
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  sendButtonDisabled: {
    backgroundColor: Colors.border,
  },
});
