import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { Colors } from '../constants/Colors';
import { Spacing, FontSize, FontWeight, BorderRadius } from '../constants/Dimensions';
import { useAuth } from '../context/AuthContext';
import { DataManager } from '../services/DataManager';

export default function FieldManagement({ navigation }) {
  const { user, isAuthenticated } = useAuth();
  const [fields, setFields] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadFields();
  }, []);

  useEffect(() => {
    // Listen for data changes
    const unsubscribe = DataManager.addDataChangeListener(() => {
      loadFields();
    });

    return unsubscribe;
  }, []);

  const loadFields = async () => {
    try {
      setLoading(true);
      const fieldsData = await DataManager.getFields();
      setFields(fieldsData);
    } catch (error) {
      console.error('Error loading fields:', error);
      Alert.alert('Hata', 'Tarlalar yüklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadFields();
    setRefreshing(false);
  };

  const handleAddField = () => {
    navigation.navigate('AddField');
  };

  const handleEditField = (field) => {
    navigation.navigate('EditField', { fieldId: field._id });
  };

  const handleDeleteField = (field) => {
    Alert.alert(
      'Tarla Sil',
      `"${field.name}" tarlasını silmek istediğinizden emin misiniz?`,
      [
        {
          text: 'İptal',
          style: 'cancel',
        },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: () => deleteField(field._id),
        },
      ]
    );
  };

  const deleteField = async (fieldId) => {
    try {
      await DataManager.deleteField(fieldId);
      Alert.alert('Başarılı', 'Tarla başarıyla silindi.');
      // Data will be refreshed automatically via listener
    } catch (error) {
      console.error('Error deleting field:', error);
      Alert.alert('Hata', error.message || 'Tarla silinirken bir hata oluştu.');
    }
  };

  const handleSetDefault = async (field) => {
    try {
      await DataManager.setDefaultField(field._id);
      Alert.alert('Başarılı', 'Varsayılan tarla ayarlandı.');
      // Data will be refreshed automatically via listener
    } catch (error) {
      console.error('Error setting default field:', error);
      Alert.alert('Hata', 'Varsayılan tarla ayarlanırken bir hata oluştu.');
    }
  };

  const renderFieldItem = ({ item }) => (
    <View style={styles.fieldCard}>
      <View style={styles.fieldHeader}>
        <View style={styles.fieldInfo}>
          <View style={styles.fieldNameContainer}>
            <Text style={styles.fieldName}>{item.name}</Text>
            {item.isDefault && (
              <View style={styles.defaultBadge}>
                <Text style={styles.defaultBadgeText}>Varsayılan</Text>
              </View>
            )}
          </View>
          {item.formattedSize && (
            <Text style={styles.fieldSize}>{item.formattedSize}</Text>
          )}
          {item.location?.address && (
            <Text style={styles.fieldLocation}>{item.location.address}</Text>
          )}
          {item.notes && (
            <Text style={styles.fieldNotes}>{item.notes}</Text>
          )}
        </View>
        
        <View style={styles.fieldActions}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => handleEditField(item)}
          >
            <Ionicons name="pencil" size={20} color={Colors.primary} />
          </TouchableOpacity>
          
          {!item.isDefault && (
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => handleSetDefault(item)}
            >
              <Ionicons name="star-outline" size={20} color={Colors.warning} />
            </TouchableOpacity>
          )}
          
          <TouchableOpacity
            style={[styles.actionButton, styles.deleteButton]}
            onPress={() => handleDeleteField(item)}
          >
            <Ionicons name="trash-outline" size={20} color={Colors.error} />
          </TouchableOpacity>
        </View>
      </View>
      
      {item.stats && (
        <View style={styles.fieldStats}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{item.stats.totalExpenses || 0}</Text>
            <Text style={styles.statLabel}>Gider</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>
              {item.stats.totalAmount ? `₺${item.stats.totalAmount.toLocaleString('tr-TR')}` : '₺0'}
            </Text>
            <Text style={styles.statLabel}>Toplam</Text>
          </View>
        </View>
      )}
    </View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="grid-outline" size={64} color={Colors.textSecondary} />
      <Text style={styles.emptyStateTitle}>Henüz tarla yok</Text>
      <Text style={styles.emptyStateText}>
        İlk tarlanızı ekleyerek detaylı takibe başlayın
      </Text>
      <TouchableOpacity style={styles.addFirstButton} onPress={handleAddField}>
        <Text style={styles.addFirstButtonText}>İlk Tarla Ekle</Text>
      </TouchableOpacity>
    </View>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.primary} />
        <Text style={styles.loadingText}>Tarlalar yükleniyor...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="chevron-back" size={24} color={Colors.surface} />
        </TouchableOpacity>
        
        <Text style={styles.headerTitle}>Tarla Yönetimi</Text>
        
        <TouchableOpacity style={styles.addButton} onPress={handleAddField}>
          <Ionicons name="add" size={24} color={Colors.surface} />
        </TouchableOpacity>
      </View>

      <View style={styles.content}>
        {fields.length === 0 ? (
          renderEmptyState()
        ) : (
          <FlatList
            data={fields}
            renderItem={renderFieldItem}
            keyExtractor={(item) => item._id}
            contentContainerStyle={styles.listContainer}
            showsVerticalScrollIndicator={false}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={onRefresh}
                colors={[Colors.primary]}
                tintColor={Colors.primary}
              />
            }
          />
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    backgroundColor: Colors.primary,
    paddingTop: 50,
    paddingBottom: Spacing.md,
    paddingHorizontal: Spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: FontSize.xl,
    fontWeight: FontWeight.bold,
    color: Colors.surface,
    flex: 1,
    textAlign: 'center',
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    padding: Spacing.md,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background,
  },
  loadingText: {
    marginTop: Spacing.md,
    fontSize: FontSize.md,
    color: Colors.textSecondary,
  },
  listContainer: {
    paddingBottom: Spacing.lg,
  },
  fieldCard: {
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.md,
    padding: Spacing.md,
    marginBottom: Spacing.md,
    elevation: 2,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  fieldHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  fieldInfo: {
    flex: 1,
    marginRight: Spacing.md,
  },
  fieldNameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.xs,
  },
  fieldName: {
    fontSize: FontSize.lg,
    fontWeight: FontWeight.semibold,
    color: Colors.text,
    marginRight: Spacing.sm,
  },
  defaultBadge: {
    backgroundColor: Colors.primary,
    paddingHorizontal: Spacing.xs,
    paddingVertical: 2,
    borderRadius: BorderRadius.sm,
  },
  defaultBadgeText: {
    fontSize: FontSize.xs,
    fontWeight: FontWeight.medium,
    color: Colors.surface,
  },
  fieldSize: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
    marginBottom: Spacing.xs,
  },
  fieldLocation: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
    marginBottom: Spacing.xs,
  },
  fieldNotes: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
    fontStyle: 'italic',
  },
  fieldActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: Colors.background,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: Spacing.xs,
  },
  deleteButton: {
    backgroundColor: Colors.errorLight,
  },
  fieldStats: {
    flexDirection: 'row',
    marginTop: Spacing.md,
    paddingTop: Spacing.md,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statValue: {
    fontSize: FontSize.md,
    fontWeight: FontWeight.semibold,
    color: Colors.text,
  },
  statLabel: {
    fontSize: FontSize.xs,
    color: Colors.textSecondary,
    marginTop: 2,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: Spacing.xl,
  },
  emptyStateTitle: {
    fontSize: FontSize.xl,
    fontWeight: FontWeight.semibold,
    color: Colors.text,
    marginTop: Spacing.md,
    marginBottom: Spacing.sm,
  },
  emptyStateText: {
    fontSize: FontSize.md,
    color: Colors.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: Spacing.xl,
  },
  addFirstButton: {
    backgroundColor: Colors.primary,
    paddingHorizontal: Spacing.xl,
    paddingVertical: Spacing.md,
    borderRadius: BorderRadius.md,
  },
  addFirstButtonText: {
    fontSize: FontSize.md,
    fontWeight: FontWeight.semibold,
    color: Colors.surface,
  },
});
