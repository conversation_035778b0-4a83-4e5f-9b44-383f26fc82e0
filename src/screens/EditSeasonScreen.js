import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Platform,
  Switch,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { Ionicons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';

import { DataManager } from '../services/DataManager';
import { Colors } from '../constants/Colors';
import { Spacing, FontSize, FontWeight } from '../constants/Dimensions';

const SEASON_COLORS = [
  '#4CAF50', '#2196F3', '#FF9800', '#E91E63',
  '#9C27B0', '#00BCD4', '#8BC34A', '#FF5722',
  '#795548', '#607D8B', '#FFC107', '#3F51B5'
];

const SEASON_EMOJIS = [
  '🌱', '🌿', '🍃', '🌾', '🌻', '🌺', '🌸', '🌼',
  '🍀', '🌳', '🌲', '🎋', '🌴', '🌵', '🌷', '🌹'
];

export default function EditSeasonScreen({ navigation, route }) {
  const { seasonId, season: initialSeason } = route.params;
  
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    startDate: null,
    endDate: null,
    color: SEASON_COLORS[0],
    emoji: SEASON_EMOJIS[0],
    isActive: false
  });
  
  const [showStartDatePicker, setShowStartDatePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [errors, setErrors] = useState({});

  useEffect(() => {
    loadSeasonData();
  }, []);

  const loadSeasonData = async () => {
    try {
      setInitialLoading(true);
      
      let seasonData = initialSeason;
      if (!seasonData) {
        seasonData = await DataManager.getSeasonById(seasonId);
      }

      if (seasonData) {
        setFormData({
          name: seasonData.name || '',
          description: seasonData.description || '',
          startDate: seasonData.startDate ? new Date(seasonData.startDate) : null,
          endDate: seasonData.endDate ? new Date(seasonData.endDate) : null,
          color: seasonData.color || SEASON_COLORS[0],
          emoji: seasonData.emoji || SEASON_EMOJIS[0],
          isActive: seasonData.isActive || false
        });
      } else {
        Alert.alert('Hata', 'Sezon bulunamadı.', [
          { text: 'Tamam', onPress: () => navigation.goBack() }
        ]);
      }
    } catch (error) {
      console.error('Error loading season data:', error);
      Alert.alert('Hata', 'Sezon verileri yüklenirken bir hata oluştu.');
    } finally {
      setInitialLoading(false);
    }
  };

  const validateForm = () => {
    const newErrors = {};

    // Name validation
    if (!formData.name.trim()) {
      newErrors.name = 'Sezon adı zorunludur';
    } else if (formData.name.trim().length < 2) {
      newErrors.name = 'Sezon adı en az 2 karakter olmalıdır';
    } else if (formData.name.trim().length > 100) {
      newErrors.name = 'Sezon adı en fazla 100 karakter olabilir';
    }

    // Description validation
    if (formData.description && formData.description.length > 500) {
      newErrors.description = 'Açıklama en fazla 500 karakter olabilir';
    }

    // Date validation
    if (formData.startDate && formData.endDate) {
      if (formData.endDate <= formData.startDate) {
        newErrors.endDate = 'Bitiş tarihi başlangıç tarihinden sonra olmalıdır';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);

      const updateData = {
        name: formData.name.trim(),
        description: formData.description.trim() || undefined,
        startDate: formData.startDate?.toISOString() || null,
        endDate: formData.endDate?.toISOString() || null,
        color: formData.color,
        emoji: formData.emoji
      };

      await DataManager.updateSeason(seasonId, updateData);

      // Handle active season change separately
      if (formData.isActive && !initialSeason?.isActive) {
        await DataManager.setActiveSeason(seasonId);
      }
      
      Alert.alert(
        'Başarılı',
        'Sezon başarıyla güncellendi.',
        [{ text: 'Tamam', onPress: () => navigation.goBack() }]
      );
    } catch (error) {
      console.error('Error updating season:', error);
      Alert.alert('Hata', error.message || 'Sezon güncellenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = () => {
    Alert.alert(
      'Sezon Silme',
      `"${formData.name}" sezonunu silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.`,
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            try {
              setLoading(true);
              await DataManager.deleteSeason(seasonId);
              Alert.alert(
                'Başarılı',
                'Sezon başarıyla silindi.',
                [{ text: 'Tamam', onPress: () => navigation.goBack() }]
              );
            } catch (error) {
              console.error('Error deleting season:', error);
              Alert.alert('Hata', error.message || 'Sezon silinirken bir hata oluştu.');
              setLoading(false);
            }
          }
        }
      ]
    );
  };

  const handleCancel = () => {
    navigation.goBack();
  };

  const formatDate = (date) => {
    if (!date) return 'Seçiniz';
    return date.toLocaleDateString('tr-TR');
  };

  const onStartDateChange = (event, selectedDate) => {
    setShowStartDatePicker(false);
    if (selectedDate) {
      setFormData(prev => ({ ...prev, startDate: selectedDate }));
      setErrors(prev => ({ ...prev, startDate: undefined }));
    }
  };

  const onEndDateChange = (event, selectedDate) => {
    setShowEndDatePicker(false);
    if (selectedDate) {
      setFormData(prev => ({ ...prev, endDate: selectedDate }));
      setErrors(prev => ({ ...prev, endDate: undefined }));
    }
  };

  const renderColorPicker = () => (
    <View style={styles.pickerContainer}>
      <Text style={styles.label}>Renk</Text>
      <View style={styles.colorGrid}>
        {SEASON_COLORS.map((color) => (
          <TouchableOpacity
            key={color}
            style={[
              styles.colorOption,
              { backgroundColor: color },
              formData.color === color && styles.selectedColorOption
            ]}
            onPress={() => setFormData(prev => ({ ...prev, color }))}
          >
            {formData.color === color && (
              <Ionicons name="checkmark" size={16} color={Colors.surface} />
            )}
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderEmojiPicker = () => (
    <View style={styles.pickerContainer}>
      <Text style={styles.label}>Emoji</Text>
      <View style={styles.emojiGrid}>
        {SEASON_EMOJIS.map((emoji) => (
          <TouchableOpacity
            key={emoji}
            style={[
              styles.emojiOption,
              formData.emoji === emoji && styles.selectedEmojiOption
            ]}
            onPress={() => setFormData(prev => ({ ...prev, emoji }))}
          >
            <Text style={styles.emojiText}>{emoji}</Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  if (initialLoading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <StatusBar style="light" backgroundColor={Colors.primary} />
        <ActivityIndicator size="large" color={Colors.primary} />
        <Text style={styles.loadingText}>Sezon yükleniyor...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar style="light" backgroundColor={Colors.primary} />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.cancelButton} onPress={handleCancel}>
          <Text style={styles.cancelButtonText}>İptal</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Sezon Düzenle</Text>
        <TouchableOpacity 
          style={[styles.saveButton, loading && styles.saveButtonDisabled]} 
          onPress={handleSave}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator size="small" color={Colors.surface} />
          ) : (
            <Text style={styles.saveButtonText}>Kaydet</Text>
          )}
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Active Season Toggle */}
        <View style={styles.toggleContainer}>
          <View style={styles.toggleInfo}>
            <Text style={styles.toggleLabel}>Aktif Sezon</Text>
            <Text style={styles.toggleDescription}>
              Bu sezonu aktif sezon olarak ayarla
            </Text>
          </View>
          <Switch
            value={formData.isActive}
            onValueChange={(value) => setFormData(prev => ({ ...prev, isActive: value }))}
            trackColor={{ false: Colors.border, true: Colors.primaryLight }}
            thumbColor={formData.isActive ? Colors.primary : Colors.textSecondary}
          />
        </View>

        {/* Season Name */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>
            Sezon Adı <Text style={styles.required}>*</Text>
          </Text>
          <TextInput
            style={[styles.textInput, errors.name && styles.inputError]}
            value={formData.name}
            onChangeText={(text) => {
              setFormData(prev => ({ ...prev, name: text }));
              setErrors(prev => ({ ...prev, name: undefined }));
            }}
            placeholder="Örn: 2025 Bahar Sezonu"
            placeholderTextColor={Colors.textSecondary}
            maxLength={100}
          />
          {errors.name && <Text style={styles.errorText}>{errors.name}</Text>}
        </View>

        {/* Description */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Açıklama</Text>
          <TextInput
            style={[styles.textArea, errors.description && styles.inputError]}
            value={formData.description}
            onChangeText={(text) => {
              setFormData(prev => ({ ...prev, description: text }));
              setErrors(prev => ({ ...prev, description: undefined }));
            }}
            placeholder="Sezon hakkında notlar..."
            placeholderTextColor={Colors.textSecondary}
            multiline
            numberOfLines={3}
            maxLength={500}
          />
          {errors.description && <Text style={styles.errorText}>{errors.description}</Text>}
        </View>

        {/* Start Date */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Başlangıç Tarihi</Text>
          <TouchableOpacity
            style={[styles.dateButton, errors.startDate && styles.inputError]}
            onPress={() => setShowStartDatePicker(true)}
          >
            <Text style={[
              styles.dateButtonText,
              !formData.startDate && styles.placeholderText
            ]}>
              {formatDate(formData.startDate)}
            </Text>
            <Ionicons name="calendar" size={20} color={Colors.textSecondary} />
          </TouchableOpacity>
          {errors.startDate && <Text style={styles.errorText}>{errors.startDate}</Text>}
        </View>

        {/* End Date */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Bitiş Tarihi</Text>
          <TouchableOpacity
            style={[styles.dateButton, errors.endDate && styles.inputError]}
            onPress={() => setShowEndDatePicker(true)}
          >
            <Text style={[
              styles.dateButtonText,
              !formData.endDate && styles.placeholderText
            ]}>
              {formatDate(formData.endDate)}
            </Text>
            <Ionicons name="calendar" size={20} color={Colors.textSecondary} />
          </TouchableOpacity>
          {errors.endDate && <Text style={styles.errorText}>{errors.endDate}</Text>}
        </View>

        {/* Color Picker */}
        {renderColorPicker()}

        {/* Emoji Picker */}
        {renderEmojiPicker()}

        {/* Delete Button */}
        <TouchableOpacity
          style={styles.deleteButton}
          onPress={handleDelete}
          disabled={loading}
        >
          <Ionicons name="trash" size={20} color={Colors.error} />
          <Text style={styles.deleteButtonText}>Sezonu Sil</Text>
        </TouchableOpacity>

        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Date Pickers */}
      {showStartDatePicker && (
        <DateTimePicker
          value={formData.startDate || new Date()}
          mode="date"
          display={Platform.OS === 'ios' ? 'spinner' : 'default'}
          onChange={onStartDateChange}
        />
      )}

      {showEndDatePicker && (
        <DateTimePicker
          value={formData.endDate || new Date()}
          mode="date"
          display={Platform.OS === 'ios' ? 'spinner' : 'default'}
          onChange={onEndDateChange}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    backgroundColor: Colors.primary,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Spacing.md,
    paddingTop: 50, // Status bar height
    paddingBottom: Spacing.md,
    elevation: 4,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  cancelButton: {
    padding: Spacing.xs,
  },
  cancelButtonText: {
    fontSize: FontSize.md,
    color: Colors.surface,
    fontWeight: FontWeight.medium,
  },
  headerTitle: {
    fontSize: FontSize.xl,
    fontWeight: FontWeight.bold,
    color: Colors.surface,
    flex: 1,
    textAlign: 'center',
  },
  saveButton: {
    backgroundColor: Colors.primaryDark,
    borderRadius: 6,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.xs,
    minWidth: 60,
    alignItems: 'center',
  },
  saveButtonDisabled: {
    opacity: 0.6,
  },
  saveButtonText: {
    fontSize: FontSize.md,
    color: Colors.surface,
    fontWeight: FontWeight.semibold,
  },
  loadingText: {
    fontSize: FontSize.md,
    color: Colors.textSecondary,
    marginTop: Spacing.md,
  },
  content: {
    flex: 1,
    padding: Spacing.md,
  },
  toggleContainer: {
    backgroundColor: Colors.surface,
    borderRadius: 8,
    padding: Spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: Spacing.lg,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  toggleInfo: {
    flex: 1,
  },
  toggleLabel: {
    fontSize: FontSize.md,
    fontWeight: FontWeight.medium,
    color: Colors.text,
    marginBottom: Spacing.xs,
  },
  toggleDescription: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
  },
  inputContainer: {
    marginBottom: Spacing.lg,
  },
  label: {
    fontSize: FontSize.md,
    fontWeight: FontWeight.medium,
    color: Colors.text,
    marginBottom: Spacing.sm,
  },
  required: {
    color: Colors.error,
  },
  textInput: {
    backgroundColor: Colors.surface,
    borderRadius: 8,
    padding: Spacing.md,
    fontSize: FontSize.md,
    color: Colors.text,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  textArea: {
    backgroundColor: Colors.surface,
    borderRadius: 8,
    padding: Spacing.md,
    fontSize: FontSize.md,
    color: Colors.text,
    borderWidth: 1,
    borderColor: Colors.border,
    minHeight: 80,
    textAlignVertical: 'top',
  },
  inputError: {
    borderColor: Colors.error,
  },
  errorText: {
    fontSize: FontSize.sm,
    color: Colors.error,
    marginTop: Spacing.xs,
  },
  dateButton: {
    backgroundColor: Colors.surface,
    borderRadius: 8,
    padding: Spacing.md,
    borderWidth: 1,
    borderColor: Colors.border,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  dateButtonText: {
    fontSize: FontSize.md,
    color: Colors.text,
  },
  placeholderText: {
    color: Colors.textSecondary,
  },
  pickerContainer: {
    marginBottom: Spacing.lg,
  },
  colorGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: Spacing.sm,
  },
  colorOption: {
    width: 40,
    height: 40,
    borderRadius: 20,
    margin: Spacing.xs,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 2,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  selectedColorOption: {
    borderWidth: 3,
    borderColor: Colors.text,
  },
  emojiGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: Spacing.sm,
  },
  emojiOption: {
    width: 40,
    height: 40,
    borderRadius: 20,
    margin: Spacing.xs,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.surface,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  selectedEmojiOption: {
    backgroundColor: Colors.primaryLight,
    borderColor: Colors.primary,
  },
  emojiText: {
    fontSize: 20,
  },
  deleteButton: {
    backgroundColor: Colors.errorLight,
    borderRadius: 8,
    padding: Spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: Spacing.lg,
    borderWidth: 1,
    borderColor: Colors.error,
  },
  deleteButtonText: {
    fontSize: FontSize.md,
    color: Colors.error,
    fontWeight: FontWeight.medium,
    marginLeft: Spacing.sm,
  },
  bottomSpacing: {
    height: Spacing.xl,
  },
});
