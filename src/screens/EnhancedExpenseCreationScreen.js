import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  StatusBar,
  Alert,
  Animated,
  Keyboard,
  TextInput,
  ActivityIndicator,
  Platform,
  Modal,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Colors } from '../constants/Colors';
import { Spacing, FontSize, FontWeight, BorderRadius } from '../constants/Dimensions';
import { validateExpense, formatAmount, sanitizeAmountInput, validateAmount } from '../utils/validation';
import Logger, { LogCategory } from '../utils/Logger';
import Analytics, { AnalyticsEvent } from '../utils/Analytics';
import { DataManager } from '../services/DataManager';
import { useAuth } from '../context/AuthContext';
import SeasonSelector from '../components/SeasonSelector';
// Step definitions for the expense creation flow
const ExpenseCreationStep = {
  CATEGORY_SELECTION: 'category_selection',
  SEASON_SELECTION: 'season_selection',   // SEASON INTEGRATION: New step
  FIELD_SELECTION: 'field_selection', // TWO-MODE SYSTEM: New step
  CROP_SELECTION: 'crop_selection',   // TWO-MODE SYSTEM: New step
  AMOUNT_INPUT: 'amount_input',
  DATE_SELECTION: 'date_selection',
  DESCRIPTION: 'description',
  REVIEW: 'review',
};
export default function EnhancedExpenseCreationScreen({ navigation, route }) {
  const { user } = useAuth();
  const [currentStep, setCurrentStep] = useState(ExpenseCreationStep.CATEGORY_SELECTION);
  const [expenseData, setExpenseData] = useState({
    categoryId: null,
    seasonId: null,    // SEASON INTEGRATION: New field
    fieldId: null,     // TWO-MODE SYSTEM: New field
    cropId: null,      // TWO-MODE SYSTEM: New field
    amount: '',
    date: new Date().toISOString().split('T')[0],
    description: '',
  });
  const [categories, setCategories] = useState([]);
  const [seasons, setSeasons] = useState([]);      // SEASON INTEGRATION: New state
  const [activeSeason, setActiveSeason] = useState(null); // SEASON INTEGRATION: New state
  const [fields, setFields] = useState([]);        // TWO-MODE SYSTEM: New state
  const [crops, setCrops] = useState([]);          // TWO-MODE SYSTEM: New state
  const [trackingMode, setTrackingMode] = useState('simple'); // TWO-MODE SYSTEM: New state
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [creationStartTime] = useState(Date.now());
  const [showDatePicker, setShowDatePicker] = useState(false);
  // Animation values
  const fadeAnim = useRef(new Animated.Value(1)).current;
  useEffect(() => {
    loadInitialData();
    Analytics.trackEvent(AnalyticsEvent.EXPENSE_CREATION_STARTED);
    Logger.info(LogCategory.USER_ACTION, 'Enhanced expense creation started');
  }, []);
  const loadInitialData = async () => {
    try {
      await DataManager.initialize();
      // Load categories
      const loadedCategories = await DataManager.getCategories();
      console.log('Loaded categories:', loadedCategories);
      if (loadedCategories && Array.isArray(loadedCategories)) {
        setCategories(loadedCategories);
      } else {
        console.error('Categories is not an array:', loadedCategories);
        setCategories([]);
      }
      // SEASON INTEGRATION: Load seasons and set active season
      await loadSeasons();
      // TWO-MODE SYSTEM: Load tracking mode and related data
      await loadTrackingMode();
    } catch (error) {
      Logger.error(LogCategory.DATABASE, 'Failed to load initial data', error);
      Alert.alert('Hata', 'Veriler yüklenirken bir hata oluştu.');
    }
  };
  const loadTrackingMode = async () => {
    try {
      // GUEST MODE: Use local tracking mode only
      if (DataManager.isGuestMode()) {
        const mode = DataManager.getTrackingMode();
        const preference = DataManager.trackingModePreference;
        const fieldsCount = DataManager.localData.fields?.length || 0;
        setTrackingMode(mode);
        console.log('Guest mode: Tracking mode loaded locally:', {
          mode,
          preference,
          fieldsCount,
          finalMode: mode
        });
        // If detailed mode, load fields and crops
        if (mode === 'detailed') {
          await loadFields();
          await loadCrops();
        }
        return;
      }
      // AUTHENTICATED MODE: Try backend first, fallback to local
      if (DataManager.shouldUseBackend()) {
        try {
          const response = await DataManager.apiClient.getTrackingMode();
          if (response && response.success) {
            setTrackingMode(response.data.mode);
            // If detailed mode, load fields and crops
            if (response.data.mode === 'detailed') {
              await loadFields();
              await loadCrops();
            }
            return;
          }
        } catch (error) {
          console.warn('Backend tracking mode failed, using local fallback:', error);
        }
      }
      // Fallback to local mode
      const mode = DataManager.getTrackingMode();
      const preference = DataManager.trackingModePreference;
      const fieldsCount = DataManager.localData.fields?.length || 0;
      setTrackingMode(mode);
      console.log('Authenticated mode (local fallback): Tracking mode loaded:', {
        mode,
        preference,
        fieldsCount,
        finalMode: mode
      });
      // If detailed mode, load fields and crops
      if (mode === 'detailed') {
        await loadFields();
        await loadCrops();
      }
    } catch (error) {
      console.error('Error loading tracking mode:', error);
      // Default to simple mode on error
      setTrackingMode('simple');
    }
  };
  const loadFields = async () => {
    try {
      const fields = await DataManager.getFields();
      console.log('Loaded fields:', fields);
      setFields(fields);
      // Auto-select default field if available
      const defaultField = fields.find(field => field.isDefault);
      if (defaultField) {
        updateExpenseData('fieldId', defaultField._id || defaultField.id);
      }
    } catch (error) {
      console.error('Error loading fields:', error);
      setFields([]);
    }
  };
  const loadCrops = async () => {
    try {
      const crops = await DataManager.getCrops();
      console.log('Loaded crops:', crops);
      setCrops(crops);
    } catch (error) {
      console.error('Error loading crops:', error);
      setCrops([]);
    }
  };
  // SEASON INTEGRATION: Load seasons and set active season
  const loadSeasons = async () => {
    try {
      const loadedSeasons = await DataManager.getSeasons();
      console.log('Loaded seasons:', loadedSeasons);
      setSeasons(loadedSeasons);
      // Get active season and auto-select it
      const currentActiveSeason = await DataManager.getActiveSeason();
      console.log('Active season:', currentActiveSeason);
      setActiveSeason(currentActiveSeason);
      if (currentActiveSeason) {
        updateExpenseData('seasonId', currentActiveSeason.id || currentActiveSeason._id);
      }
    } catch (error) {
      console.error('Error loading seasons:', error);
      setSeasons([]);
      setActiveSeason(null);
    }
  };
  const animateToStep = (step) => {
    // Basit fade animasyonu - slide animasyonunu kaldırdık
    Animated.timing(fadeAnim, {
      toValue: 0,
      duration: 150,
      useNativeDriver: true,
    }).start(() => {
      setCurrentStep(step);
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }).start();
    });
    Keyboard.dismiss();
  };
  // TWO-MODE SYSTEM: Get active steps based on tracking mode
  const getActiveSteps = () => {
    if (trackingMode === 'detailed') {
      return [
        ExpenseCreationStep.CATEGORY_SELECTION,
        ExpenseCreationStep.SEASON_SELECTION,   // SEASON INTEGRATION: Always include season selection
        ExpenseCreationStep.FIELD_SELECTION,
        ExpenseCreationStep.CROP_SELECTION,
        ExpenseCreationStep.AMOUNT_INPUT,
        ExpenseCreationStep.DATE_SELECTION,
        ExpenseCreationStep.DESCRIPTION,
        ExpenseCreationStep.REVIEW
      ];
    } else {
      // Simple mode - skip field and crop selection but include season
      return [
        ExpenseCreationStep.CATEGORY_SELECTION,
        ExpenseCreationStep.SEASON_SELECTION,   // SEASON INTEGRATION: Always include season selection
        ExpenseCreationStep.AMOUNT_INPUT,
        ExpenseCreationStep.DATE_SELECTION,
        ExpenseCreationStep.DESCRIPTION,
        ExpenseCreationStep.REVIEW
      ];
    }
  };
  // TWO-MODE SYSTEM: Get current step index within active steps only
  const getCurrentStepIndex = () => {
    const activeSteps = getActiveSteps();
    return activeSteps.indexOf(currentStep);
  };
  // Legacy function - kept for compatibility but now uses active steps
  const getStepIndex = (step) => {
    const activeSteps = getActiveSteps();
    return activeSteps.indexOf(step);
  };
  const handleNext = () => {
    const validation = validateCurrentStep();
    if (!validation.isValid) {
      setErrors(validation.errors);
      return;
    }
    setErrors({});
    const nextStep = getNextStep();
    if (nextStep) {
      animateToStep(nextStep);
    } else {
      handleSaveExpense();
    }
  };
  // TWO-MODE SYSTEM: Dynamic step navigation based on tracking mode
  const getNextStep = () => {
    switch (currentStep) {
      case ExpenseCreationStep.CATEGORY_SELECTION:
        return ExpenseCreationStep.SEASON_SELECTION; // SEASON INTEGRATION: Always go to season selection
      case ExpenseCreationStep.SEASON_SELECTION:
        if (trackingMode === 'detailed') {
          return ExpenseCreationStep.FIELD_SELECTION;
        } else {
          return ExpenseCreationStep.AMOUNT_INPUT;
        }
      case ExpenseCreationStep.FIELD_SELECTION:
        return ExpenseCreationStep.CROP_SELECTION;
      case ExpenseCreationStep.CROP_SELECTION:
        return ExpenseCreationStep.AMOUNT_INPUT;
      case ExpenseCreationStep.AMOUNT_INPUT:
        return ExpenseCreationStep.DATE_SELECTION;
      case ExpenseCreationStep.DATE_SELECTION:
        return ExpenseCreationStep.DESCRIPTION;
      case ExpenseCreationStep.DESCRIPTION:
        return ExpenseCreationStep.REVIEW;
      case ExpenseCreationStep.REVIEW:
        return null; // End of flow
      default:
        return null;
    }
  };
  const getPreviousStep = () => {
    switch (currentStep) {
      case ExpenseCreationStep.CATEGORY_SELECTION:
        return null; // Start of flow
      case ExpenseCreationStep.SEASON_SELECTION:
        return ExpenseCreationStep.CATEGORY_SELECTION; // SEASON INTEGRATION: Go back to category
      case ExpenseCreationStep.FIELD_SELECTION:
        return ExpenseCreationStep.SEASON_SELECTION; // SEASON INTEGRATION: Go back to season
      case ExpenseCreationStep.CROP_SELECTION:
        return ExpenseCreationStep.FIELD_SELECTION;
      case ExpenseCreationStep.AMOUNT_INPUT:
        if (trackingMode === 'detailed') {
          return ExpenseCreationStep.CROP_SELECTION;
        } else {
          return ExpenseCreationStep.SEASON_SELECTION; // SEASON INTEGRATION: Go back to season
        }
      case ExpenseCreationStep.DATE_SELECTION:
        return ExpenseCreationStep.AMOUNT_INPUT;
      case ExpenseCreationStep.DESCRIPTION:
        return ExpenseCreationStep.DATE_SELECTION;
      case ExpenseCreationStep.REVIEW:
        return ExpenseCreationStep.DESCRIPTION;
      default:
        return null;
    }
  };
  const handlePrevious = () => {
    const previousStep = getPreviousStep();
    if (previousStep) {
      animateToStep(previousStep);
    }
  };
  const validateCurrentStep = () => {
    const errors = {};
    switch (currentStep) {
      case ExpenseCreationStep.CATEGORY_SELECTION:
        if (!expenseData.categoryId) {
          errors.category = 'Kategori seçimi gereklidir';
        }
        break;
      // SEASON INTEGRATION: Season validation
      case ExpenseCreationStep.SEASON_SELECTION:
        if (!expenseData.seasonId) {
          errors.seasonId = 'Sezon seçimi gereklidir';
        }
        break;
      // TWO-MODE SYSTEM: Field validation
      case ExpenseCreationStep.FIELD_SELECTION:
        if (trackingMode === 'detailed' && !expenseData.fieldId) {
          errors.fieldId = 'Detaylı modda tarla seçimi gereklidir';
        }
        break;
      // TWO-MODE SYSTEM: Crop validation (optional)
      case ExpenseCreationStep.CROP_SELECTION:
        // Crop selection is optional, no validation needed
        break;
      case ExpenseCreationStep.AMOUNT_INPUT:
        const amountValidation = validateAmount(expenseData.amount);
        if (!amountValidation.isValid) {
          errors.amount = amountValidation.error;
        }
        break;
      case ExpenseCreationStep.DATE_SELECTION:
        if (!expenseData.date) {
          errors.date = 'Tarih seçimi gereklidir';
        }
        break;
      default:
        break;
    }
    return {
      isValid: Object.keys(errors).length === 0,
      errors,
    };
  };
  const handleSaveExpense = async () => {
    try {
      setLoading(true);
      // Final validation
      const validation = validateExpense(expenseData);
      if (!validation.isValid) {
        setErrors(validation.errors);
        return;
      }
      // Get selected category details for reports
      const selectedCategory = categories.find(cat => cat.id === expenseData.categoryId);
      // Save expense to database
      const expenseToSave = {
        categoryId: expenseData.categoryId,
        categoryName: selectedCategory?.name || 'Bilinmeyen',
        categoryEmoji: selectedCategory?.emoji || '📝',
        categoryColor: selectedCategory?.color || '#666',
        amount: validation.sanitizedData.amount,
        date: expenseData.date,
        description: expenseData.description || '',
        // SEASON INTEGRATION: Include season information
        seasonId: expenseData.seasonId,
        // TWO-MODE SYSTEM: Include new fields
        fieldId: expenseData.fieldId || null,
        cropId: expenseData.cropId || null,
        trackingMode: trackingMode,
        createdAt: new Date().toISOString(),
      };
      await DataManager.addExpense(expenseToSave);
      Analytics.trackEvent(AnalyticsEvent.EXPENSE_CREATED, {
        categoryId: expenseData.categoryId,
        categoryName: expenseToSave.categoryName,
        amount: validation.sanitizedData.amount,
        hasDescription: !!expenseData.description,
        creationTime: Date.now() - creationStartTime,
      });
      Logger.info(LogCategory.USER_ACTION, 'Expense created successfully', {
        categoryId: expenseData.categoryId,
        amount: validation.sanitizedData.amount,
      });
      Alert.alert(
        'Başarılı',
        'Gider başarıyla kaydedildi.',
        [
          {
            text: 'Tamam',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } catch (error) {
      Logger.error(LogCategory.USER_ACTION, 'Failed to save expense', error);
      Alert.alert('Hata', 'Gider kaydedilirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };
  const updateExpenseData = (field, value) => {
    setExpenseData(prev => ({ ...prev, [field]: value }));
    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };
  const handleDateChange = (selectedDate) => {
    if (selectedDate) {
      const dateString = selectedDate.toISOString().split('T')[0];
      updateExpenseData('date', dateString);
    }
    setShowDatePicker(false);
  };
  const openDatePicker = () => {
    // Basit tarih seçici - bugün, dün, önceki hafta seçenekleri
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    const lastWeek = new Date(today);
    lastWeek.setDate(lastWeek.getDate() - 7);
    Alert.alert(
      'Tarih Seçin',
      'Gider tarihini seçin',
      [
        {
          text: '📅 Takvim Aç',
          onPress: () => openCalendarModal(),
        },
        {
          text: 'Bugün',
          onPress: () => handleDateChange(today),
        },
        {
          text: 'Dün',
          onPress: () => handleDateChange(yesterday),
        },
        {
          text: 'Geçen Hafta',
          onPress: () => handleDateChange(lastWeek),
        },
        {
          text: 'İptal',
          style: 'cancel',
        },
      ]
    );
  };
  const openCalendarModal = () => {
    setShowDatePicker(true);
  };
  const closeDatePicker = () => {
    setShowDatePicker(false);
  };
  const selectCalendarDate = (day) => {
    const today = new Date();
    // Timezone sorununu önlemek için ISO string kullanıyoruz
    const year = today.getFullYear();
    const month = today.getMonth() + 1; // getMonth() 0-based, ISO string 1-based
    const dayStr = day.toString().padStart(2, '0');
    const monthStr = month.toString().padStart(2, '0');
    const isoString = `${year}-${monthStr}-${dayStr}T12:00:00.000Z`;
    const selectedDate = new Date(isoString);
    handleDateChange(selectedDate);
    setShowDatePicker(false);
  };
  // TWO-MODE SYSTEM: Dynamic progress bar based on tracking mode
  const renderProgressBar = () => {
    const activeSteps = getActiveSteps();
    const currentIndex = getCurrentStepIndex();
    const progress = ((currentIndex + 1) / activeSteps.length) * 100;
    return (
      <View style={styles.progressContainer}>
        <View style={styles.progressBar}>
          <View style={[styles.progressFill, { width: `${progress}%` }]} />
        </View>
        <Text style={styles.progressText}>
          {currentIndex + 1} / {activeSteps.length}
        </Text>
      </View>
    );
  };
  const renderCategorySelection = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Kategori Seçin</Text>
      <Text style={styles.stepDescription}>
        Önce giderinizin kategorisini seçin
      </Text>
      {errors.category && (
        <Text style={styles.errorText}>{errors.category}</Text>
      )}
      <ScrollView style={styles.categoriesGrid} showsVerticalScrollIndicator={false}>
        {categories && categories.length > 0 ? (
          categories.map((category) => (
            <TouchableOpacity
              key={category.id}
              style={[
                styles.categoryCard,
                { borderColor: category.color },
                expenseData.categoryId === category.id && [
                  styles.categoryCardSelected,
                  { backgroundColor: category.color }
                ],
              ]}
              onPress={() => updateExpenseData('categoryId', category.id)}
            >
              <Text style={styles.categoryEmoji}>{category.emoji}</Text>
              <Text
                style={[
                  styles.categoryName,
                  expenseData.categoryId === category.id && styles.categoryNameSelected,
                ]}
              >
                {category.name}
              </Text>
            </TouchableOpacity>
          ))
        ) : (
          <Text style={styles.noDataText}>Kategoriler yükleniyor...</Text>
        )}
      </ScrollView>
    </View>
  );
  // SEASON INTEGRATION: Season selection step
  const renderSeasonSelection = () => {
    const selectedSeason = seasons.find(season =>
      (season.id || season._id) === expenseData.seasonId
    );
    return (
      <View style={styles.stepContainer}>
        <Text style={styles.stepTitle}>Sezon Seçin</Text>
        <Text style={styles.stepDescription}>
          Giderin hangi sezona ait olduğunu seçin
        </Text>
        {errors.seasonId && (
          <Text style={styles.errorText}>{errors.seasonId}</Text>
        )}
        <View style={styles.seasonSelectorContainer}>
          <SeasonSelector
            seasons={seasons}
            selectedSeason={selectedSeason}
            onSelect={(season) => {
              updateExpenseData('seasonId', season.id || season._id);
            }}
            placeholder="Sezon seçiniz"
            showActiveBadge={true}
            error={errors.seasonId}
          />
        </View>
        {activeSeason && !expenseData.seasonId && (
          <TouchableOpacity
            style={styles.useActiveSeasonButton}
            onPress={() => {
              updateExpenseData('seasonId', activeSeason.id || activeSeason._id);
            }}
          >
            <Ionicons name="checkmark-circle" size={20} color={Colors.primary} />
            <Text style={styles.useActiveSeasonText}>
              Aktif Sezonu Kullan: {activeSeason.name}
            </Text>
          </TouchableOpacity>
        )}
        {selectedSeason && (
          <View style={styles.selectedSeasonInfo}>
            <Text style={styles.selectedSeasonTitle}>Seçili Sezon:</Text>
            <View style={styles.selectedSeasonCard}>
              <Text style={styles.selectedSeasonEmoji}>{selectedSeason.emoji || '🌱'}</Text>
              <Text style={styles.selectedSeasonName}>{selectedSeason.name}</Text>
              {selectedSeason.isActive && (
                <View style={styles.activeSeasonBadge}>
                  <Text style={styles.activeSeasonBadgeText}>Aktif</Text>
                </View>
              )}
            </View>
          </View>
        )}
      </View>
    );
  };
  // TWO-MODE SYSTEM: Field selection step
  const renderFieldSelection = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Tarla Seçin</Text>
      <Text style={styles.stepDescription}>
        Giderin hangi tarlaya ait olduğunu seçin
      </Text>
      {errors.fieldId && (
        <Text style={styles.errorText}>{errors.fieldId}</Text>
      )}
      <ScrollView style={styles.categoriesGrid} showsVerticalScrollIndicator={false}>
        {fields.map((field) => (
          <TouchableOpacity
            key={field._id}
            style={[
              styles.categoryCard,
              { borderColor: Colors.primary },
              expenseData.fieldId === field._id && [
                styles.categoryCardSelected,
                { backgroundColor: Colors.primary }
              ],
            ]}
            onPress={() => updateExpenseData('fieldId', field._id)}
          >
            <View style={styles.fieldCardContent}>
              <Text style={styles.categoryEmoji}>🏞️</Text>
              <View style={styles.fieldInfo}>
                <Text
                  style={[
                    styles.categoryName,
                    expenseData.fieldId === field._id && styles.categoryNameSelected,
                  ]}
                >
                  {field.name}
                </Text>
                {field.formattedSize && (
                  <Text
                    style={[
                      styles.fieldSize,
                      expenseData.fieldId === field._id && styles.fieldSizeSelected,
                    ]}
                  >
                    {field.formattedSize}
                  </Text>
                )}
                {field.isDefault && (
                  <Text
                    style={[
                      styles.defaultBadgeText,
                      expenseData.fieldId === field._id && styles.defaultBadgeTextSelected,
                    ]}
                  >
                    Varsayılan
                  </Text>
                )}
              </View>
            </View>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
  // TWO-MODE SYSTEM: Crop selection step
  const renderCropSelection = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Ürün Seçin (İsteğe Bağlı)</Text>
      <Text style={styles.stepDescription}>
        Giderin hangi ürünle ilgili olduğunu seçin
      </Text>
      <TouchableOpacity
        style={[
          styles.categoryCard,
          { borderColor: Colors.textSecondary },
          !expenseData.cropId && [
            styles.categoryCardSelected,
            { backgroundColor: Colors.textSecondary }
          ],
        ]}
        onPress={() => updateExpenseData('cropId', null)}
      >
        <Text style={styles.categoryEmoji}>❌</Text>
        <Text
          style={[
            styles.categoryName,
            !expenseData.cropId && styles.categoryNameSelected,
          ]}
        >
          Ürün Seçme
        </Text>
      </TouchableOpacity>
      <ScrollView style={styles.categoriesGrid} showsVerticalScrollIndicator={false}>
        {crops.map((crop) => (
          <TouchableOpacity
            key={crop._id}
            style={[
              styles.categoryCard,
              { borderColor: Colors.success },
              expenseData.cropId === crop._id && [
                styles.categoryCardSelected,
                { backgroundColor: Colors.success }
              ],
            ]}
            onPress={() => updateExpenseData('cropId', crop._id)}
          >
            <Text style={styles.categoryEmoji}>{crop.emoji}</Text>
            <Text
              style={[
                styles.categoryName,
                expenseData.cropId === crop._id && styles.categoryNameSelected,
              ]}
            >
              {crop.nameTr}
            </Text>
            {crop.isDefault && (
              <Text
                style={[
                  styles.cropBadgeText,
                  expenseData.cropId === crop._id && styles.cropBadgeTextSelected,
                ]}
              >
                Sistem
              </Text>
            )}
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
  const renderAmountInput = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Tutar Girin</Text>
      <Text style={styles.stepDescription}>
        Gider tutarını girin (virgül veya nokta kullanabilirsiniz)
      </Text>
      {errors.amount && (
        <Text style={styles.errorText}>{errors.amount}</Text>
      )}
      <View style={styles.amountInputContainer}>
        <Text style={styles.currencySymbol}>₺</Text>
        <TextInput
          style={styles.amountInput}
          value={expenseData.amount}
          onChangeText={(text) => updateExpenseData('amount', sanitizeAmountInput(text))}
          placeholder="0,00"
          keyboardType="numeric"
          autoFocus
        />
      </View>
      {expenseData.amount && (
        <Text style={styles.formattedAmount}>
          {formatAmount(expenseData.amount)}
        </Text>
      )}
    </View>
  );
  const renderDateSelection = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Tarih Seçin</Text>
      <Text style={styles.stepDescription}>
        Gider tarihini seçin veya değiştirin
      </Text>
      {errors.date && (
        <Text style={styles.errorText}>{errors.date}</Text>
      )}
      <TouchableOpacity
        style={styles.dateSelector}
        onPress={openDatePicker}
      >
        <Ionicons name="calendar-outline" size={24} color={Colors.primary} />
        <Text style={styles.dateText}>
          {new Date(expenseData.date).toLocaleDateString('tr-TR', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
          })}
        </Text>
        <Ionicons name="chevron-down" size={20} color={Colors.textSecondary} />
      </TouchableOpacity>
    </View>
  );
  const renderDescription = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Açıklama (İsteğe Bağlı)</Text>
      <Text style={styles.stepDescription}>
        Gideriniz hakkında detay ekleyebilirsiniz
      </Text>
      <TextInput
        style={styles.descriptionInput}
        value={expenseData.description}
        onChangeText={(text) => updateExpenseData('description', text)}
        placeholder="Örn: 25 kg NPK gübresi, Ahmet Bey'den alındı"
        multiline
        numberOfLines={4}
        maxLength={500}
      />
      <Text style={styles.characterCount}>
        {expenseData.description.length} / 500
      </Text>
    </View>
  );
  const renderReview = () => {
    const reviewCategory = categories.find(cat => cat.id === expenseData.categoryId);
    const reviewSeason = seasons.find(season =>
      (season.id || season._id) === expenseData.seasonId
    );
    // TWO-MODE SYSTEM: Get field and crop details for detailed mode
    const reviewField = trackingMode === 'detailed' && expenseData.fieldId
      ? fields.find(field => field._id === expenseData.fieldId)
      : null;
    const reviewCrop = trackingMode === 'detailed' && expenseData.cropId
      ? crops.find(crop => crop._id === expenseData.cropId)
      : null;

    return (
      <View style={styles.stepContainer}>
        <Text style={styles.stepTitle}>Gideri Kontrol Edin</Text>
        <Text style={styles.stepDescription}>
          Bilgileri kontrol edin ve kaydedin
        </Text>
        <View style={styles.reviewContainer}>
          <View style={styles.reviewItem}>
            <Text style={styles.reviewLabel}>Kategori</Text>
            <View style={styles.reviewCategoryValue}>
              <Text style={styles.reviewCategoryEmoji}>{reviewCategory?.emoji}</Text>
              <Text style={styles.reviewCategoryName}>{reviewCategory?.name}</Text>
            </View>
          </View>
          {/* SEASON INTEGRATION: Show season in review */}
          <View style={styles.reviewItem}>
            <Text style={styles.reviewLabel}>Sezon</Text>
            <View style={styles.reviewSeasonValue}>
              <View style={styles.reviewSeasonTextContainer}>
                  <Text style={styles.reviewSeasonEmoji}>{reviewSeason?.emoji || '🌱'}</Text>
                <Text style={styles.reviewSeasonName}>{reviewSeason?.name || 'Bilinmeyen'}</Text>
                {reviewSeason?.isActive && (
                  <View style={styles.reviewActiveSeasonBadge}>
                    <Text style={styles.reviewActiveSeasonBadgeText}>Aktif</Text>
                  </View>
                )}
              </View>
            </View>
          </View>
          {/* TWO-MODE SYSTEM: Show field in review for detailed mode */}
          {trackingMode === 'detailed' && (
            <View style={styles.reviewItem}>
              <Text style={styles.reviewLabel}>Tarla</Text>
              <View style={styles.reviewFieldValue}>
                <View style={styles.reviewFieldTextContainer}>
                  <Text style={styles.reviewFieldEmoji}>🏞️</Text>
                  <Text style={styles.reviewFieldName}>
                    {reviewField?.name || 'Seçilmedi'}
                  </Text>
                  {reviewField?.formattedSize && (
                    <Text style={styles.reviewFieldSize}>
                      ({reviewField.formattedSize})
                    </Text>
                  )}
                  {reviewField?.isDefault && (
                    <View style={styles.reviewDefaultFieldBadge}>
                      <Text style={styles.reviewDefaultFieldBadgeText}>Varsayılan</Text>
                    </View>
                  )}
                </View>
              </View>
            </View>
          )}
          {/* TWO-MODE SYSTEM: Show crop in review for detailed mode */}
          {trackingMode === 'detailed' && (
            <View style={styles.reviewItem}>
              <Text style={styles.reviewLabel}>Ürün</Text>
              <View style={styles.reviewCropValue}>
                <View style={styles.reviewCropTextContainer}>
                  <Text style={styles.reviewCropEmoji}>
                    {reviewCrop?.emoji || '❌'}
                  </Text>
                  <Text style={styles.reviewCropName}>
                    {reviewCrop?.nameTr || 'Seçilmedi'}
                  </Text>
                  {reviewCrop?.isDefault && (
                    <View style={styles.reviewDefaultCropBadge}>
                      <Text style={styles.reviewDefaultCropBadgeText}>Sistem</Text>
                    </View>
                  )}
                </View>
              </View>
            </View>
          )}
          <View style={styles.reviewItem}>
            <Text style={styles.reviewLabel}>Tutar</Text>
            <Text style={[styles.reviewValue, styles.reviewAmount]}>
              {formatAmount(expenseData.amount)}
            </Text>
          </View>
          <View style={styles.reviewItem}>
            <Text style={styles.reviewLabel}>Tarih</Text>
            <Text style={styles.reviewValue}>
              {new Date(expenseData.date).toLocaleDateString('tr-TR', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
              })}
            </Text>
          </View>
          {expenseData.description && (
            <View style={styles.reviewItem}>
              <Text style={styles.reviewLabel}>Açıklama</Text>
              <Text style={styles.reviewValue}>{expenseData.description}</Text>
            </View>
          )}
        </View>
      </View>
    );
  };
  const renderCurrentStep = () => {
    switch (currentStep) {
      case ExpenseCreationStep.CATEGORY_SELECTION:
        return renderCategorySelection();
      case ExpenseCreationStep.SEASON_SELECTION:
        return renderSeasonSelection(); // SEASON INTEGRATION: New render function
      case ExpenseCreationStep.FIELD_SELECTION:
        return renderFieldSelection();
      case ExpenseCreationStep.CROP_SELECTION:
        return renderCropSelection();
      case ExpenseCreationStep.AMOUNT_INPUT:
        return renderAmountInput();
      case ExpenseCreationStep.DATE_SELECTION:
        return renderDateSelection();
      case ExpenseCreationStep.DESCRIPTION:
        return renderDescription();
      case ExpenseCreationStep.REVIEW:
        return renderReview();
      default:
        return null;
    }
  };
  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.background} />
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => navigation.goBack()}>
          <Ionicons name="chevron-back" size={24} color={Colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Yeni Gider</Text>
        <TouchableOpacity
          style={styles.headerRight}
          onPress={() => navigation.navigate('MainTabs', { screen: 'Home' })}
        >
          <Ionicons name="home-outline" size={24} color={Colors.primary} />
        </TouchableOpacity>
      </View>
      {/* Progress */}
      {renderProgressBar()}
      {/* Content */}
      <Animated.View
        style={[
          styles.content,
          {
            opacity: fadeAnim,
            // translateX animasyonunu kaldırdık - sadece opacity kullanıyoruz
          },
        ]}
      >
        {renderCurrentStep()}
      </Animated.View>
      {/* Footer */}
      <View style={styles.footer}>
        <View style={styles.navigationButtons}>
          {currentStep !== ExpenseCreationStep.CATEGORY_SELECTION && (
            <TouchableOpacity style={styles.backStepButton} onPress={handlePrevious}>
              <Ionicons name="chevron-back" size={20} color={Colors.textSecondary} />
              <Text style={styles.backStepButtonText}>Geri</Text>
            </TouchableOpacity>
          )}
          <View style={styles.spacer} />
          <TouchableOpacity
            style={[
              styles.nextButton,
              loading && styles.nextButtonDisabled,
            ]}
            onPress={handleNext}
            disabled={loading}
          >
            {loading ? (
              <ActivityIndicator size="small" color={Colors.surface} />
            ) : (
              <>
                <Text style={styles.nextButtonText}>
                  {currentStep === ExpenseCreationStep.REVIEW ? 'Kaydet' : 'İleri'}
                </Text>
                <Ionicons 
                  name={currentStep === ExpenseCreationStep.REVIEW ? "checkmark" : "chevron-forward"} 
                  size={20} 
                  color={Colors.surface} 
                />
              </>
            )}
          </TouchableOpacity>
        </View>
      </View>
      {/* Simple Calendar Modal */}
      {showDatePicker && (
        <Modal
          transparent={true}
          animationType="slide"
          visible={showDatePicker}
          onRequestClose={closeDatePicker}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <TouchableOpacity
                  onPress={closeDatePicker}
                  style={styles.modalButton}
                >
                  <Text style={styles.modalButtonText}>İptal</Text>
                </TouchableOpacity>
                <Text style={styles.modalTitle}>Tarih Seçin</Text>
                <TouchableOpacity
                  onPress={closeDatePicker}
                  style={styles.modalButton}
                >
                  <Text style={[styles.modalButtonText, styles.modalButtonDone]}>Tamam</Text>
                </TouchableOpacity>
              </View>
              {/* Simple Calendar Grid */}
              <View style={styles.calendarContainer}>
                <Text style={styles.monthTitle}>
                  {new Date().toLocaleDateString('tr-TR', { month: 'long', year: 'numeric' })}
                </Text>
                <View style={styles.calendarGrid}>
                  {/* Generate calendar days for current month */}
                  {Array.from({ length: new Date().getDate() }, (_, i) => i + 1).map((day) => (
                    <TouchableOpacity
                      key={day}
                      style={[
                        styles.calendarDay,
                        day === new Date().getDate() && styles.calendarDayToday
                      ]}
                      onPress={() => selectCalendarDate(day)}
                    >
                      <Text style={[
                        styles.calendarDayText,
                        day === new Date().getDate() && styles.calendarDayTodayText
                      ]}>
                        {day}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
            </View>
          </View>
        </Modal>
      )}
    </SafeAreaView>
  );
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  backButton: {
    padding: Spacing.sm,
  },
  headerTitle: {
    fontSize: FontSize.lg,
    fontWeight: FontWeight.bold,
    color: Colors.text,
  },
  headerRight: {
    padding: Spacing.sm,
  },
  progressContainer: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  progressBar: {
    height: 4,
    backgroundColor: Colors.border,
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: Colors.primary,
    borderRadius: 2,
  },
  progressText: {
    fontSize: FontSize.xs,
    color: Colors.textSecondary,
    marginTop: Spacing.xs,
    textAlign: 'center',
  },
  content: {
    flex: 1,
  },
  stepContainer: {
    flex: 1,
    padding: Spacing.xl,
  },
  stepTitle: {
    fontSize: FontSize.xxl,
    fontWeight: FontWeight.bold,
    color: Colors.text,
    textAlign: 'center',
    marginBottom: Spacing.md,
  },
  stepDescription: {
    fontSize: FontSize.md,
    color: Colors.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: Spacing.xl,
  },
  errorText: {
    fontSize: FontSize.sm,
    color: Colors.error,
    textAlign: 'center',
    marginBottom: Spacing.md,
  },
  categoriesGrid: {
    flex: 1,
  },
  categoryCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: Spacing.lg,
    borderRadius: BorderRadius.lg,
    borderWidth: 2,
    borderColor: Colors.border,
    backgroundColor: Colors.surface,
    marginBottom: Spacing.md,
  },
  categoryCardSelected: {
    borderWidth: 2,
  },
  categoryEmoji: {
    fontSize: 32,
    marginRight: Spacing.lg,
  },
  categoryName: {
    fontSize: FontSize.lg,
    fontWeight: FontWeight.medium,
    color: Colors.text,
  },
  categoryNameSelected: {
    color: Colors.surface,
    fontWeight: FontWeight.bold,
  },
  amountInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: Spacing.lg,
  },
  currencySymbol: {
    fontSize: FontSize.xxxl,
    fontWeight: FontWeight.bold,
    color: Colors.primary,
    marginRight: Spacing.md,
  },
  amountInput: {
    fontSize: FontSize.xxxl,
    fontWeight: FontWeight.bold,
    color: Colors.text,
    textAlign: 'center',
    minWidth: 200,
    borderBottomWidth: 2,
    borderBottomColor: Colors.primary,
    paddingVertical: Spacing.md,
  },
  formattedAmount: {
    fontSize: FontSize.lg,
    color: Colors.textSecondary,
    textAlign: 'center',
  },
  dateSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: Spacing.lg,
    borderRadius: BorderRadius.lg,
    borderWidth: 1,
    borderColor: Colors.border,
    backgroundColor: Colors.surface,
  },
  dateText: {
    fontSize: FontSize.lg,
    color: Colors.text,
    flex: 1,
    marginLeft: Spacing.md,
  },
  descriptionInput: {
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    fontSize: FontSize.md,
    color: Colors.text,
    backgroundColor: Colors.surface,
    textAlignVertical: 'top',
    minHeight: 120,
    marginBottom: Spacing.sm,
  },
  characterCount: {
    fontSize: FontSize.xs,
    color: Colors.textSecondary,
    textAlign: 'right',
  },
  reviewContainer: {
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
  },
  reviewItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  reviewLabel: {
    fontSize: FontSize.md,
    color: Colors.textSecondary,
    fontWeight: FontWeight.medium,
  },
  reviewValue: {
    fontSize: FontSize.md,
    color: Colors.text,
    textAlign: 'right',
    flex: 1,
    marginLeft: Spacing.md,
  },
  reviewAmount: {
    fontSize: FontSize.lg,
    fontWeight: FontWeight.bold,
    color: Colors.primary,
  },
  reviewCategoryValue: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    justifyContent: 'flex-end',
  },
  reviewCategoryEmoji: {
    fontSize: 20,
    marginRight: Spacing.sm,
  },
  reviewCategoryName: {
    fontSize: FontSize.md,
    color: Colors.text,
    fontWeight: FontWeight.medium,
    flexShrink: 1,
    textAlign: 'right',
  },
  footer: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.lg,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  navigationButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backStepButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.lg,
  },
  backStepButtonText: {
    fontSize: FontSize.md,
    color: Colors.textSecondary,
    marginLeft: Spacing.xs,
  },
  spacer: {
    flex: 1,
  },
  nextButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.primary,
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.xl,
    borderRadius: BorderRadius.lg,
  },
  nextButtonDisabled: {
    backgroundColor: Colors.border,
  },
  nextButtonText: {
    fontSize: FontSize.md,
    color: Colors.surface,
    fontWeight: FontWeight.medium,
    marginRight: Spacing.xs,
  },
  // Modal styles for iOS date picker
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: Colors.surface,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 34, // Safe area bottom
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  modalTitle: {
    fontSize: FontSize.lg,
    fontWeight: FontWeight.semibold,
    color: Colors.text,
  },
  modalButton: {
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.md,
  },
  modalButtonText: {
    fontSize: FontSize.md,
    color: Colors.primary,
  },
  modalButtonDone: {
    fontWeight: FontWeight.semibold,
  },
  // Calendar styles
  calendarContainer: {
    padding: Spacing.lg,
  },
  monthTitle: {
    fontSize: FontSize.lg,
    fontWeight: FontWeight.semibold,
    color: Colors.text,
    textAlign: 'center',
    marginBottom: Spacing.lg,
    textTransform: 'capitalize',
  },
  calendarGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  calendarDay: {
    width: '13%',
    aspectRatio: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Spacing.sm,
    borderRadius: 8,
    backgroundColor: Colors.background,
  },
  calendarDayToday: {
    backgroundColor: Colors.primary,
  },
  calendarDayText: {
    fontSize: FontSize.md,
    color: Colors.text,
    fontWeight: FontWeight.medium,
  },
  calendarDayTodayText: {
    color: Colors.surface,
    fontWeight: FontWeight.bold,
  },
  // TWO-MODE SYSTEM: New styles for field and crop selection
  fieldCardContent: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
  },
  fieldInfo: {
    flex: 1,
    marginLeft: Spacing.sm,
  },
  fieldSize: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
    marginTop: 2,
  },
  fieldSizeSelected: {
    color: Colors.surface,
  },
  defaultBadgeText: {
    fontSize: FontSize.xs,
    color: Colors.primary,
    fontWeight: FontWeight.medium,
    marginTop: 2,
  },
  defaultBadgeTextSelected: {
    color: Colors.surface,
  },
  cropBadgeText: {
    fontSize: FontSize.xs,
    color: Colors.success,
    fontWeight: FontWeight.medium,
    marginTop: 2,
  },
  cropBadgeTextSelected: {
    color: Colors.surface,
  },
  // SEASON INTEGRATION: New styles for season selection
  seasonSelectorContainer: {
    marginVertical: Spacing.lg,
  },
  useActiveSeasonButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.background,
    padding: Spacing.md,
    borderRadius: BorderRadius.md,
    marginTop: Spacing.md,
    borderWidth: 1,
    borderColor: Colors.primary,
  },
  useActiveSeasonText: {
    marginLeft: Spacing.sm,
    fontSize: FontSize.md,
    color: Colors.primary,
    fontWeight: FontWeight.medium,
  },
  selectedSeasonInfo: {
    marginTop: Spacing.lg,
    padding: Spacing.md,
    backgroundColor: Colors.background,
    borderRadius: BorderRadius.md,
  },
  selectedSeasonTitle: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
    marginBottom: Spacing.sm,
    fontWeight: FontWeight.medium,
  },
  selectedSeasonCard: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  selectedSeasonEmoji: {
    fontSize: 24,
    marginRight: Spacing.sm,
  },
  selectedSeasonName: {
    fontSize: FontSize.md,
    color: Colors.text,
    fontWeight: FontWeight.medium,
    flex: 1,
  },
  activeSeasonBadge: {
    backgroundColor: Colors.success,
    paddingHorizontal: Spacing.sm,
    paddingVertical: 2,
    borderRadius: BorderRadius.sm,
  },
  activeSeasonBadgeText: {
    fontSize: FontSize.xs,
    color: Colors.surface,
    fontWeight: FontWeight.medium,
  },
  reviewSeasonValue: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flex: 1,
    justifyContent: 'flex-end',
  },
  reviewSeasonEmoji: {
    fontSize: 18,
    marginRight: Spacing.sm,
    marginTop: 2, // Align with text baseline
  },
  reviewSeasonTextContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    // flex: 1,
  },
  reviewSeasonName: {
    fontSize: FontSize.md,
    color: Colors.text,
    fontWeight: FontWeight.medium,
    textAlign: 'right',
    marginRight: Spacing.sm,
  },
  reviewActiveSeasonBadge: {
    backgroundColor: Colors.success,
    paddingHorizontal: Spacing.xs,
    paddingVertical: 1,
    borderRadius: BorderRadius.sm,
    marginTop: Spacing.xs,
    alignSelf: 'flex-end',
  },
  reviewActiveSeasonBadgeText: {
    fontSize: FontSize.xs,
    color: Colors.surface,
    fontWeight: FontWeight.medium,
  },
  // TWO-MODE SYSTEM: Field review styles
  reviewFieldValue: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flex: 1,
    justifyContent: 'flex-end',
  },
  reviewFieldEmoji: {
    fontSize: 18,
    marginRight: Spacing.sm,
    marginTop: 2, // Align with text baseline
  },
  reviewFieldTextContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  reviewFieldName: {
    fontSize: FontSize.md,
    color: Colors.text,
    fontWeight: FontWeight.medium,
    textAlign: 'right',
    marginRight: Spacing.sm,
  },
  reviewFieldSize: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
    textAlign: 'right',
    marginRight: Spacing.sm,
  },
  reviewDefaultFieldBadge: {
    backgroundColor: Colors.primary,
    paddingHorizontal: Spacing.xs,
    paddingVertical: 1,
    borderRadius: BorderRadius.sm,
    marginTop: Spacing.xs,
    alignSelf: 'flex-end',
  },
  reviewDefaultFieldBadgeText: {
    fontSize: FontSize.xs,
    color: Colors.surface,
    fontWeight: FontWeight.medium,
  },
  // TWO-MODE SYSTEM: Crop review styles
  reviewCropValue: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flex: 1,
    justifyContent: 'flex-end',
  },
  reviewCropEmoji: {
    fontSize: 18,
    marginRight: Spacing.sm,
    marginTop: 2, // Align with text baseline
  },
  reviewCropTextContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  reviewCropName: {
    fontSize: FontSize.md,
    color: Colors.text,
    fontWeight: FontWeight.medium,
    textAlign: 'right',
    marginRight: Spacing.sm,
  },
  reviewDefaultCropBadge: {
    backgroundColor: Colors.success,
    paddingHorizontal: Spacing.xs,
    paddingVertical: 1,
    borderRadius: BorderRadius.sm,
    marginTop: Spacing.xs,
    alignSelf: 'flex-end',
  },
  reviewDefaultCropBadgeText: {
    fontSize: FontSize.xs,
    color: Colors.surface,
    fontWeight: FontWeight.medium,
  },
});
