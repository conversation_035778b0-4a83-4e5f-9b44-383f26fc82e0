import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Dimensions,
  SafeAreaView,
  StatusBar,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { Colors } from '../constants/Colors';
import { Spacing, FontSize, FontWeight, BorderRadius } from '../constants/Dimensions';

const { width: screenWidth } = Dimensions.get('window');

const welcomeSlides = [
  {
    id: 1,
    title: 'Çiftçi Not Defterim\'e Hoş Geldiniz',
    subtitle: 'Tarımsal giderlerinizi kolayca takip edin',
    description: 'Gübre, ilaç, sulama ve diğer tarımsal giderlerinizi kategorilere ayırarak düzenli bir şekilde kaydedin.',
    icon: '🌾',
    color: Colors.primary,
  },
  {
    id: 2,
    title: 'Mevsimsel Takip',
    subtitle: '<PERSON>rım dönemlerine göre organize olun',
    description: 'Giderlerinizi ilkbahar ekimi, yaz bakımı, sonbahar hasadı ve kış hazırlığı dönemlerine göre takip edin.',
    icon: '🌱',
    color: Colors.success,
  },
  {
    id: 3,
    title: 'Akıllı Raporlama',
    subtitle: 'Detaylı analizler ve karşılaştırmalar',
    description: 'Kategori bazında harcama analizleri, mevsimsel karşılaştırmalar ve trend raporları oluşturun.',
    icon: '📊',
    color: Colors.info,
  },
  {
    id: 4,
    title: 'Bulut Senkronizasyonu',
    subtitle: 'Verileriniz her zaman güvende',
    description: 'Google hesabınızla giriş yaparak verilerinizi bulutta saklayın ve tüm cihazlarınızda erişin.',
    icon: '☁️',
    color: Colors.secondary,
  },
];

export default function WelcomeScreen({ navigation, onComplete }) {
  const [currentSlide, setCurrentSlide] = useState(0);
  const scrollViewRef = useRef(null);
  const fadeAnim = useRef(new Animated.Value(1)).current;

  const handleNext = () => {
    if (currentSlide < welcomeSlides.length - 1) {
      const nextSlide = currentSlide + 1;
      setCurrentSlide(nextSlide);
      
      // Animate to next slide
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 150,
        useNativeDriver: true,
      }).start(() => {
        scrollViewRef.current?.scrollTo({
          x: nextSlide * screenWidth,
          animated: false,
        });
        
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 150,
          useNativeDriver: true,
        }).start();
      });
    } else {
      handleGetStarted();
    }
  };

  const handlePrevious = () => {
    if (currentSlide > 0) {
      const prevSlide = currentSlide - 1;
      setCurrentSlide(prevSlide);
      
      // Animate to previous slide
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 150,
        useNativeDriver: true,
      }).start(() => {
        scrollViewRef.current?.scrollTo({
          x: prevSlide * screenWidth,
          animated: false,
        });
        
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 150,
          useNativeDriver: true,
        }).start();
      });
    }
  };

  const handleSkip = () => {
    if (onComplete) {
      onComplete(false); // false indicates skipped
    } else {
      navigation.replace('Auth');
    }
  };

  const handleGetStarted = () => {
    if (onComplete) {
      onComplete(true); // true indicates completed
    } else {
      navigation.replace('Auth');
    }
  };

  const renderSlide = (slide, index) => (
    <View key={slide.id} style={[styles.slide, { backgroundColor: slide.color }]}>
      <Animated.View style={[styles.slideContent, { opacity: fadeAnim }]}>
        <View style={styles.iconContainer}>
          <Text style={styles.slideIcon}>{slide.icon}</Text>
        </View>
        
        <Text style={styles.slideTitle}>{slide.title}</Text>
        <Text style={styles.slideSubtitle}>{slide.subtitle}</Text>
        <Text style={styles.slideDescription}>{slide.description}</Text>
      </Animated.View>
    </View>
  );

  const renderPagination = () => (
    <View style={styles.pagination}>
      {welcomeSlides.map((_, index) => (
        <View
          key={index}
          style={[
            styles.paginationDot,
            index === currentSlide && styles.paginationDotActive,
          ]}
        />
      ))}
    </View>
  );

  const currentSlideData = welcomeSlides[currentSlide];

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={currentSlideData.color} />
      
      {/* Header */}
      <View style={[styles.header, { backgroundColor: currentSlideData.color }]}>
        <TouchableOpacity style={styles.skipButton} onPress={handleSkip}>
          <Text style={styles.skipButtonText}>Atla</Text>
        </TouchableOpacity>
      </View>

      {/* Slides */}
      <ScrollView
        ref={scrollViewRef}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        scrollEnabled={false}
        style={styles.slidesContainer}
      >
        {welcomeSlides.map((slide, index) => renderSlide(slide, index))}
      </ScrollView>

      {/* Footer */}
      <View style={[styles.footer, { backgroundColor: currentSlideData.color }]}>
        {renderPagination()}
        
        <View style={styles.navigationButtons}>
          {currentSlide > 0 && (
            <TouchableOpacity style={styles.previousButton} onPress={handlePrevious}>
              <Ionicons name="chevron-back" size={20} color={Colors.surface} />
              <Text style={styles.previousButtonText}>Geri</Text>
            </TouchableOpacity>
          )}
          
          <View style={styles.spacer} />
          
          <TouchableOpacity style={styles.nextButton} onPress={handleNext}>
            <Text style={styles.nextButtonText}>
              {currentSlide === welcomeSlides.length - 1 ? 'Başlayalım' : 'İleri'}
            </Text>
            {currentSlide < welcomeSlides.length - 1 && (
              <Ionicons name="chevron-forward" size={20} color={Colors.surface} />
            )}
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.primary,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
  },
  skipButton: {
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.md,
  },
  skipButtonText: {
    fontSize: FontSize.md,
    color: Colors.surface,
    fontWeight: FontWeight.medium,
  },
  slidesContainer: {
    flex: 1,
  },
  slide: {
    width: screenWidth,
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: Spacing.xl,
  },
  slideContent: {
    alignItems: 'center',
    maxWidth: 320,
  },
  iconContainer: {
    marginBottom: Spacing.xl,
  },
  slideIcon: {
    fontSize: 80,
    textAlign: 'center',
  },
  slideTitle: {
    fontSize: FontSize.xxxl,
    fontWeight: FontWeight.bold,
    color: Colors.surface,
    textAlign: 'center',
    marginBottom: Spacing.md,
    lineHeight: 36,
  },
  slideSubtitle: {
    fontSize: FontSize.lg,
    color: Colors.surface,
    textAlign: 'center',
    marginBottom: Spacing.lg,
    opacity: 0.9,
    lineHeight: 24,
  },
  slideDescription: {
    fontSize: FontSize.md,
    color: Colors.surface,
    textAlign: 'center',
    lineHeight: 22,
    opacity: 0.8,
  },
  footer: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.xl,
  },
  pagination: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Spacing.xl,
  },
  paginationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: Colors.surface,
    opacity: 0.3,
    marginHorizontal: 4,
  },
  paginationDotActive: {
    opacity: 1,
    width: 24,
  },
  navigationButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  previousButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.lg,
  },
  previousButtonText: {
    fontSize: FontSize.md,
    color: Colors.surface,
    fontWeight: FontWeight.medium,
    marginLeft: Spacing.xs,
  },
  spacer: {
    flex: 1,
  },
  nextButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.surface,
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.xl,
    borderRadius: BorderRadius.lg,
    minWidth: 120,
    justifyContent: 'center',
  },
  nextButtonText: {
    fontSize: FontSize.md,
    color: Colors.primary,
    fontWeight: FontWeight.bold,
    marginRight: Spacing.xs,
  },
});
