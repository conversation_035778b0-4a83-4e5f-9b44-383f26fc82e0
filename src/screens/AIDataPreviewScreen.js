import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { Spacing, FontSize, FontWeight } from '../constants/Dimensions';

export default function AIDataPreviewScreen({ route, navigation }) {
  const { extractedData, onConfirm } = route.params;
  const [editableData, setEditableData] = useState([]);
  const [isValidating, setIsValidating] = useState(false);
  const [validationResults, setValidationResults] = useState(null);

  useEffect(() => {
    // Initialize editable data
    setEditableData(extractedData.map((item, index) => ({
      ...item,
      id: index,
      isEditing: false
    })));

    // Auto-validate data
    validateData(extractedData);
  }, [extractedData]);

  const validateData = async (data) => {
    setIsValidating(true);
    try {
      // This would call the validation API
      // For now, we'll do client-side validation
      const results = data.map((item, index) => {
        const errors = [];
        const warnings = [];

        // Validate date
        if (!item.date) {
          errors.push('Tarih eksik');
        } else {
          const date = new Date(item.date);
          if (isNaN(date.getTime())) {
            errors.push('Geçersiz tarih');
          }
        }

        // Validate category
        const validCategories = [
          'Gübre', 'İşçilik', 'İlaç', 'Su', 'Yakıt', 'Tohum', 'Makine', 'Depolama'
        ];
        if (!item.category) {
          errors.push('Kategori eksik');
        } else if (!validCategories.includes(item.category)) {
          errors.push('Geçersiz kategori');
        }

        // Validate amount
        if (!item.amount || item.amount <= 0) {
          errors.push('Geçerli tutar gerekli');
        } else if (item.amount > 100000) {
          warnings.push('Yüksek tutar');
        }

        // Validate description
        if (!item.description || item.description.trim().length === 0) {
          errors.push('Açıklama gerekli');
        }

        // Check confidence
        if (item.confidence < 0.7) {
          warnings.push('Düşük güven skoru');
        }

        return {
          index,
          valid: errors.length === 0,
          errors,
          warnings
        };
      });

      setValidationResults(results);
    } catch (error) {
      console.error('Validation error:', error);
    } finally {
      setIsValidating(false);
    }
  };

  const handleEdit = (index, field, value) => {
    setEditableData(prev => prev.map((item, i) => 
      i === index ? { ...item, [field]: value } : item
    ));
  };

  const toggleEdit = (index) => {
    setEditableData(prev => prev.map((item, i) => 
      i === index ? { ...item, isEditing: !item.isEditing } : item
    ));
  };

  const handleDelete = (index) => {
    Alert.alert(
      'Kaydı Sil',
      'Bu kaydı silmek istediğinizden emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        { 
          text: 'Sil', 
          style: 'destructive',
          onPress: () => {
            setEditableData(prev => prev.filter((_, i) => i !== index));
          }
        }
      ]
    );
  };

  const handleConfirm = () => {
    const validData = editableData.filter((_, index) => {
      const validation = validationResults?.[index];
      return validation?.valid !== false;
    });

    if (validData.length === 0) {
      Alert.alert('Hata', 'Kaydedilecek geçerli veri bulunamadı');
      return;
    }

    Alert.alert(
      'Onay',
      `${validData.length} kayıt kaydedilecek. Devam etmek istiyor musunuz?`,
      [
        { text: 'İptal', style: 'cancel' },
        { 
          text: 'Kaydet', 
          onPress: () => {
            onConfirm(validData);
            navigation.goBack();
          }
        }
      ]
    );
  };

  const renderDataItem = (item, index) => {
    const validation = validationResults?.[index];
    const hasErrors = validation?.errors?.length > 0;
    const hasWarnings = validation?.warnings?.length > 0;

    return (
      <View key={index} style={[
        styles.dataItem,
        hasErrors && styles.dataItemError,
        hasWarnings && !hasErrors && styles.dataItemWarning
      ]}>
        <View style={styles.dataHeader}>
          <View style={styles.confidenceContainer}>
            <Text style={styles.confidenceText}>
              Güven: %{Math.round((item.confidence || 0) * 100)}
            </Text>
            <View style={[
              styles.confidenceBar,
              { backgroundColor: item.confidence >= 0.7 ? Colors.success : 
                               item.confidence >= 0.5 ? Colors.warning : Colors.error }
            ]} />
          </View>
          
          <View style={styles.actionButtons}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => toggleEdit(index)}
            >
              <Ionicons 
                name={item.isEditing ? "checkmark" : "pencil"} 
                size={16} 
                color={Colors.primary} 
              />
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => handleDelete(index)}
            >
              <Ionicons name="trash" size={16} color={Colors.error} />
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.dataContent}>
          {/* Date */}
          <View style={styles.fieldContainer}>
            <Text style={styles.fieldLabel}>Tarih:</Text>
            {item.isEditing ? (
              <TextInput
                style={styles.fieldInput}
                value={item.date || ''}
                onChangeText={(value) => handleEdit(index, 'date', value)}
                placeholder="YYYY-MM-DD"
              />
            ) : (
              <Text style={styles.fieldValue}>{item.date || 'Belirtilmemiş'}</Text>
            )}
          </View>

          {/* Category */}
          <View style={styles.fieldContainer}>
            <Text style={styles.fieldLabel}>Kategori:</Text>
            {item.isEditing ? (
              <TextInput
                style={styles.fieldInput}
                value={item.category || ''}
                onChangeText={(value) => handleEdit(index, 'category', value)}
                placeholder="Kategori seçin"
              />
            ) : (
              <Text style={styles.fieldValue}>{item.category || 'Belirtilmemiş'}</Text>
            )}
          </View>

          {/* Description */}
          <View style={styles.fieldContainer}>
            <Text style={styles.fieldLabel}>Açıklama:</Text>
            {item.isEditing ? (
              <TextInput
                style={[styles.fieldInput, styles.multilineInput]}
                value={item.description || ''}
                onChangeText={(value) => handleEdit(index, 'description', value)}
                placeholder="Açıklama girin"
                multiline
              />
            ) : (
              <Text style={styles.fieldValue}>{item.description || 'Belirtilmemiş'}</Text>
            )}
          </View>

          {/* Amount */}
          <View style={styles.fieldContainer}>
            <Text style={styles.fieldLabel}>Tutar:</Text>
            {item.isEditing ? (
              <TextInput
                style={styles.fieldInput}
                value={item.amount?.toString() || ''}
                onChangeText={(value) => handleEdit(index, 'amount', parseFloat(value) || 0)}
                placeholder="0"
                keyboardType="numeric"
              />
            ) : (
              <Text style={styles.fieldValue}>
                {item.amount ? `${item.amount.toLocaleString('tr-TR')} ₺` : 'Belirtilmemiş'}
              </Text>
            )}
          </View>

          {/* Notes */}
          {item.notes && (
            <View style={styles.fieldContainer}>
              <Text style={styles.fieldLabel}>Notlar:</Text>
              <Text style={styles.fieldValue}>{item.notes}</Text>
            </View>
          )}
        </View>

        {/* Validation Messages */}
        {validation && (validation.errors.length > 0 || validation.warnings.length > 0) && (
          <View style={styles.validationContainer}>
            {validation.errors.map((error, i) => (
              <Text key={i} style={styles.errorText}>• {error}</Text>
            ))}
            {validation.warnings.map((warning, i) => (
              <Text key={i} style={styles.warningText}>• {warning}</Text>
            ))}
          </View>
        )}
      </View>
    );
  };

  const validCount = validationResults?.filter(r => r.valid).length || 0;
  const totalAmount = editableData.reduce((sum, item) => sum + (item.amount || 0), 0);

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={Colors.white} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Veri Önizleme</Text>
        <View style={styles.headerRight} />
      </View>

      <View style={styles.summaryContainer}>
        <View style={styles.summaryItem}>
          <Text style={styles.summaryLabel}>Toplam Kayıt</Text>
          <Text style={styles.summaryValue}>{editableData.length}</Text>
        </View>
        <View style={styles.summaryItem}>
          <Text style={styles.summaryLabel}>Geçerli</Text>
          <Text style={[styles.summaryValue, { color: Colors.success }]}>{validCount}</Text>
        </View>
        <View style={styles.summaryItem}>
          <Text style={styles.summaryLabel}>Toplam Tutar</Text>
          <Text style={styles.summaryValue}>{totalAmount.toLocaleString('tr-TR')} ₺</Text>
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {isValidating ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={Colors.primary} />
            <Text style={styles.loadingText}>Veriler doğrulanıyor...</Text>
          </View>
        ) : (
          editableData.map(renderDataItem)
        )}
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity
          style={styles.cancelButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.cancelButtonText}>İptal</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.confirmButton,
            validCount === 0 && styles.confirmButtonDisabled
          ]}
          onPress={handleConfirm}
          disabled={validCount === 0}
        >
          <Text style={styles.confirmButtonText}>
            Kaydet ({validCount})
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.primary,
    paddingTop: 50,
    paddingBottom: 15,
    paddingHorizontal: Spacing.medium,
  },
  backButton: {
    padding: Spacing.small,
  },
  headerTitle: {
    flex: 1,
    fontSize: FontSize.large,
    fontWeight: FontWeight.bold,
    color: Colors.white,
    textAlign: 'center',
    marginRight: 40,
  },
  headerRight: {
    width: 40,
  },
  summaryContainer: {
    flexDirection: 'row',
    backgroundColor: Colors.white,
    paddingVertical: Spacing.medium,
    paddingHorizontal: Spacing.medium,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  summaryItem: {
    flex: 1,
    alignItems: 'center',
  },
  summaryLabel: {
    fontSize: FontSize.small,
    color: Colors.textSecondary,
    marginBottom: 4,
  },
  summaryValue: {
    fontSize: FontSize.medium,
    fontWeight: FontWeight.bold,
    color: Colors.textPrimary,
  },
  content: {
    flex: 1,
    paddingHorizontal: Spacing.medium,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Spacing.large * 2,
  },
  loadingText: {
    marginTop: Spacing.medium,
    fontSize: FontSize.medium,
    color: Colors.textSecondary,
  },
  dataItem: {
    backgroundColor: Colors.white,
    borderRadius: 12,
    padding: Spacing.medium,
    marginVertical: Spacing.small,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  dataItemError: {
    borderColor: Colors.error,
    backgroundColor: Colors.errorLight,
  },
  dataItemWarning: {
    borderColor: Colors.warning,
    backgroundColor: Colors.warningLight,
  },
  dataHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.medium,
  },
  confidenceContainer: {
    flex: 1,
  },
  confidenceText: {
    fontSize: FontSize.small,
    color: Colors.textSecondary,
    marginBottom: 4,
  },
  confidenceBar: {
    height: 3,
    borderRadius: 2,
    width: '60%',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: Spacing.small,
  },
  actionButton: {
    padding: Spacing.small,
    borderRadius: 8,
    backgroundColor: Colors.background,
  },
  dataContent: {
    gap: Spacing.small,
  },
  fieldContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  fieldLabel: {
    fontSize: FontSize.small,
    fontWeight: FontWeight.medium,
    color: Colors.textSecondary,
    width: 80,
    marginTop: 2,
  },
  fieldValue: {
    flex: 1,
    fontSize: FontSize.medium,
    color: Colors.textPrimary,
  },
  fieldInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: 8,
    paddingHorizontal: Spacing.small,
    paddingVertical: 6,
    fontSize: FontSize.medium,
  },
  multilineInput: {
    minHeight: 60,
    textAlignVertical: 'top',
  },
  validationContainer: {
    marginTop: Spacing.medium,
    paddingTop: Spacing.small,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  errorText: {
    fontSize: FontSize.small,
    color: Colors.error,
    marginBottom: 2,
  },
  warningText: {
    fontSize: FontSize.small,
    color: Colors.warning,
    marginBottom: 2,
  },
  footer: {
    flexDirection: 'row',
    backgroundColor: Colors.white,
    paddingVertical: Spacing.medium,
    paddingHorizontal: Spacing.medium,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
    gap: Spacing.medium,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: Colors.background,
    paddingVertical: Spacing.medium,
    borderRadius: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.border,
  },
  cancelButtonText: {
    fontSize: FontSize.medium,
    fontWeight: FontWeight.medium,
    color: Colors.textSecondary,
  },
  confirmButton: {
    flex: 1,
    backgroundColor: Colors.primary,
    paddingVertical: Spacing.medium,
    borderRadius: 12,
    alignItems: 'center',
  },
  confirmButtonDisabled: {
    backgroundColor: Colors.border,
  },
  confirmButtonText: {
    fontSize: FontSize.medium,
    fontWeight: FontWeight.bold,
    color: Colors.white,
  },
});
