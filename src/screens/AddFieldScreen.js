/**
 * AddFieldScreen - Yeni tarla ekleme formu
 * Çiftçi Not Defterim - Tarla Yönetimi
 */
import React, { useState, useContext } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Collapsible from 'react-native-collapsible';
import { AuthContext } from '../context/AuthContext';
import { Colors } from '../constants/Colors';
import { Spacing, BorderRadius, FontSize, FontWeight } from '../constants/Dimensions';
import { validateField } from '../utils/validation';
import { DataManager } from '../services/DataManager';
const AddFieldScreen = ({ navigation }) => {
  const { user } = useContext(AuthContext);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [isDetailedInfoCollapsed, setIsDetailedInfoCollapsed] = useState(true);
  // Form state
  const [fieldData, setFieldData] = useState({
    name: '',
    size: {
      value: '',
      unit: 'dekar'
    },
    location: {
      address: ''
    },
    soilType: 'other',
    irrigationType: 'none',
    notes: ''
  });
  // Size units
  const sizeUnits = [
    { value: 'dekar', label: 'Dekar' },
    { value: 'dönüm', label: 'Dönüm' },
    { value: 'hectare', label: 'Hektar' },
    { value: 'acre', label: 'Acre' }
  ];
  // Soil types
  const soilTypes = [
    { value: 'clay', label: 'Killi', emoji: '🟤' },
    { value: 'sandy', label: 'Kumlu', emoji: '🟡' },
    { value: 'loamy', label: 'Balçıklı', emoji: '🟫' },
    { value: 'silty', label: 'Siltli', emoji: '🔸' },
    { value: 'peaty', label: 'Turbalı', emoji: '🟫' },
    { value: 'chalky', label: 'Kireçli', emoji: '⚪' },
    { value: 'other', label: 'Diğer', emoji: '❓' }
  ];
  // Irrigation types
  const irrigationTypes = [
    { value: 'drip', label: 'Damla Sulama', emoji: '💧' },
    { value: 'sprinkler', label: 'Yağmurlama', emoji: '🌧️' },
    { value: 'flood', label: 'Salma Sulama', emoji: '🌊' },
    { value: 'manual', label: 'Manuel', emoji: '🪣' },
    { value: 'none', label: 'Sulama Yok', emoji: '🚫' }
  ];
  const updateField = (field, value) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setFieldData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value
        }
      }));
    } else {
      setFieldData(prev => ({ ...prev, [field]: value }));
    }
    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };
  const toggleDetailedInfo = () => {
    setIsDetailedInfoCollapsed(!isDetailedInfoCollapsed);
  };
  // Enhanced horizontal scroll component with fade effects
  const EnhancedHorizontalScroll = ({ children }) => (
    <View style={styles.enhancedScrollContainer}>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={true}
        style={styles.enhancedHorizontalScroll}
        contentContainerStyle={styles.scrollContentContainer}
      >
        {children}
      </ScrollView>
      {/* Left fade effect using multiple overlapping views for smooth gradient */}
      <View style={styles.leftFadeGradient} pointerEvents="none">
        <View style={[styles.fadeLayer, { backgroundColor: 'rgba(245,245,245,0.95)', width: '10%' }]} />
        <View style={[styles.fadeLayer, { backgroundColor: 'rgba(245,245,245,0.9)', width: '20%' }]} />
        <View style={[styles.fadeLayer, { backgroundColor: 'rgba(245,245,245,0.8)', width: '30%' }]} />
        <View style={[styles.fadeLayer, { backgroundColor: 'rgba(245,245,245,0.65)', width: '45%' }]} />
        <View style={[styles.fadeLayer, { backgroundColor: 'rgba(245,245,245,0.5)', width: '60%' }]} />
        <View style={[styles.fadeLayer, { backgroundColor: 'rgba(245,245,245,0.35)', width: '75%' }]} />
        <View style={[styles.fadeLayer, { backgroundColor: 'rgba(245,245,245,0.2)', width: '85%' }]} />
        <View style={[styles.fadeLayer, { backgroundColor: 'rgba(245,245,245,0.1)', width: '95%' }]} />
        <View style={[styles.fadeLayer, { backgroundColor: 'rgba(245,245,245,0.05)', width: '100%' }]} />
      </View>
      {/* Right fade effect using multiple overlapping views for smooth gradient */}
      <View style={styles.rightFadeGradient} pointerEvents="none">
        <View style={[styles.fadeLayer, styles.fadeLayerRight, { backgroundColor: 'rgba(245,245,245,0.95)', width: '10%' }]} />
        <View style={[styles.fadeLayer, styles.fadeLayerRight, { backgroundColor: 'rgba(245,245,245,0.9)', width: '20%' }]} />
        <View style={[styles.fadeLayer, styles.fadeLayerRight, { backgroundColor: 'rgba(245,245,245,0.8)', width: '30%' }]} />
        <View style={[styles.fadeLayer, styles.fadeLayerRight, { backgroundColor: 'rgba(245,245,245,0.65)', width: '45%' }]} />
        <View style={[styles.fadeLayer, styles.fadeLayerRight, { backgroundColor: 'rgba(245,245,245,0.5)', width: '60%' }]} />
        <View style={[styles.fadeLayer, styles.fadeLayerRight, { backgroundColor: 'rgba(245,245,245,0.35)', width: '75%' }]} />
        <View style={[styles.fadeLayer, styles.fadeLayerRight, { backgroundColor: 'rgba(245,245,245,0.2)', width: '85%' }]} />
        <View style={[styles.fadeLayer, styles.fadeLayerRight, { backgroundColor: 'rgba(245,245,245,0.1)', width: '95%' }]} />
        <View style={[styles.fadeLayer, styles.fadeLayerRight, { backgroundColor: 'rgba(245,245,245,0.05)', width: '100%' }]} />
      </View>
    </View>
  );
  const validateForm = () => {
    const validation = validateField(fieldData);
    setErrors(validation.errors);
    return validation.isValid;
  };
  const handleSave = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);

      const payload = {
        name: fieldData.name.trim()
      };

      // Add size if provided
      if (fieldData.size.value && fieldData.size.value.trim()) {
        payload.size = {
          value: parseFloat(fieldData.size.value),
          unit: fieldData.size.unit
        };
      }

      // Add optional fields if provided
      if (fieldData.soilType) {
        payload.soilType = fieldData.soilType;
      }

      if (fieldData.irrigationType) {
        payload.irrigationType = fieldData.irrigationType;
      }

      if (fieldData.notes && fieldData.notes.trim()) {
        payload.notes = fieldData.notes.trim();
      }

      // Add location if provided
      if (fieldData.location.address.trim()) {
        payload.location = {
          address: fieldData.location.address.trim()
        };
      }

      await DataManager.addField(payload);

      Alert.alert('Başarılı', 'Tarla başarıyla eklendi.', [
        {
          text: 'Tamam',
          onPress: () => navigation.goBack()
        }
      ]);
    } catch (error) {
      console.error('Error creating field:', error);
      Alert.alert('Hata', error.message || 'Tarla eklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };
  const handleCancel = () => {
    navigation.goBack();
  };
  const renderSizeUnitSelector = () => (
    <View style={styles.selectorContainer}>
      <Text style={styles.selectorLabel}>Birim</Text>
      <EnhancedHorizontalScroll>
        {sizeUnits.map((unit) => (
          <TouchableOpacity
            key={unit.value}
            style={[
              styles.selectorOption,
              fieldData.size.unit === unit.value && styles.selectorOptionSelected
            ]}
            onPress={() => updateField('size.unit', unit.value)}
          >
            <Text style={[
              styles.selectorOptionText,
              fieldData.size.unit === unit.value && styles.selectorOptionTextSelected
            ]}>
              {unit.label}
            </Text>
          </TouchableOpacity>
        ))}
      </EnhancedHorizontalScroll>
    </View>
  );
  const renderSoilTypeSelector = () => (
    <View style={styles.selectorContainer}>
      <Text style={styles.selectorLabel}>Toprak Tipi</Text>
      <EnhancedHorizontalScroll>
        {soilTypes.map((soil) => (
          <TouchableOpacity
            key={soil.value}
            style={[
              styles.selectorOption,
              fieldData.soilType === soil.value && styles.selectorOptionSelected
            ]}
            onPress={() => updateField('soilType', soil.value)}
          >
            <Text style={styles.selectorEmoji}>{soil.emoji}</Text>
            <Text style={[
              styles.selectorOptionText,
              fieldData.soilType === soil.value && styles.selectorOptionTextSelected
            ]}>
              {soil.label}
            </Text>
          </TouchableOpacity>
        ))}
      </EnhancedHorizontalScroll>
    </View>
  );
  const renderIrrigationTypeSelector = () => (
    <View style={styles.selectorContainer}>
      <Text style={styles.selectorLabel}>Sulama Sistemi</Text>
      <EnhancedHorizontalScroll>
        {irrigationTypes.map((irrigation) => (
          <TouchableOpacity
            key={irrigation.value}
            style={[
              styles.selectorOption,
              fieldData.irrigationType === irrigation.value && styles.selectorOptionSelected
            ]}
            onPress={() => updateField('irrigationType', irrigation.value)}
          >
            <Text style={styles.selectorEmoji}>{irrigation.emoji}</Text>
            <Text style={[
              styles.selectorOptionText,
              fieldData.irrigationType === irrigation.value && styles.selectorOptionTextSelected
            ]}>
              {irrigation.label}
            </Text>
          </TouchableOpacity>
        ))}
      </EnhancedHorizontalScroll>
    </View>
  );
  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.background} />
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.headerButton} onPress={handleCancel}>
          <Ionicons name="close" size={24} color={Colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Yeni Tarla Ekle</Text>
        <View style={styles.headerButton} />
      </View>
      {/* Content */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* 1. Tarla Adı */}
        <View style={styles.fieldContainer}>
          <Text style={styles.fieldLabel}>Tarla Adı *</Text>
          {errors.name && (
            <Text style={styles.errorText}>{errors.name}</Text>
          )}
          <TextInput
            style={styles.textInput}
            value={fieldData.name}
            onChangeText={(text) => updateField('name', text)}
            placeholder="Örn: Kuzey Tarla, Ana Bağ..."
            maxLength={50}
          />
        </View>
        {/* 2. Konum */}
        <View style={styles.fieldContainer}>
          <Text style={styles.fieldLabel}>Konum</Text>
          {errors['location.address'] && (
            <Text style={styles.errorText}>{errors['location.address']}</Text>
          )}
          <TextInput
            style={styles.textInput}
            value={fieldData.location.address}
            onChangeText={(text) => updateField('location.address', text)}
            placeholder="İsteğe bağlı - Tarla konumu veya adresi..."
            maxLength={200}
          />
        </View>
        {/* 3. Detaylı Bilgiler Accordion */}
        <View style={styles.accordionContainer}>
          <TouchableOpacity
            style={styles.accordionHeader}
            onPress={toggleDetailedInfo}
            activeOpacity={0.7}
          >
            <View style={styles.accordionHeaderContent}>
              <Text style={styles.accordionTitle}>Detaylı Bilgiler (İsteğe Bağlı)</Text>
              <Ionicons
                name={isDetailedInfoCollapsed ? 'chevron-down' : 'chevron-up'}
                size={20}
                color={Colors.primary}
              />
            </View>
          </TouchableOpacity>
          <Collapsible collapsed={isDetailedInfoCollapsed}>
            <View style={styles.accordionContent}>
              {/* 3.1. Alan Büyüklüğü */}
              <View style={styles.fieldContainer}>
                <Text style={styles.fieldLabel}>Alan Büyüklüğü</Text>
                {errors['size.value'] && (
                  <Text style={styles.errorText}>{errors['size.value']}</Text>
                )}
                <TextInput
                  style={styles.textInput}
                  value={fieldData.size.value}
                  onChangeText={(text) => updateField('size.value', text)}
                  placeholder="İsteğe bağlı - Örn: 10"
                  keyboardType="numeric"
                />
              </View>
              {/* 3.2. Birim */}
              <View style={styles.fieldContainer}>
                {renderSizeUnitSelector()}
              </View>
              {/* 3.3. Sulama Sistemi */}
              <View style={styles.fieldContainer}>
                {renderIrrigationTypeSelector()}
              </View>
              {/* 3.4. Toprak Tipi */}
              <View style={styles.fieldContainer}>
                {renderSoilTypeSelector()}
              </View>
              {/* 3.5. Notlar */}
              <View style={styles.fieldContainer}>
                <Text style={styles.fieldLabel}>Notlar</Text>
                {errors.notes && (
                  <Text style={styles.errorText}>{errors.notes}</Text>
                )}
                <TextInput
                  style={styles.descriptionInput}
                  value={fieldData.notes}
                  onChangeText={(text) => updateField('notes', text)}
                  placeholder="İsteğe bağlı - Tarla hakkında ek bilgiler..."
                  multiline
                  numberOfLines={3}
                  maxLength={200}
                />
                <Text style={styles.characterCount}>
                  {fieldData.notes.length} / 200
                </Text>
              </View>
            </View>
          </Collapsible>
        </View>
      </ScrollView>
      {/* Bottom Save Button */}
      <View style={styles.bottomButtonContainer}>
        <TouchableOpacity
          style={[
            styles.bottomSaveButton,
            loading && styles.bottomSaveButtonDisabled,
          ]}
          onPress={handleSave}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator size="small" color={Colors.surface} />
          ) : (
            <>
              <Ionicons name="checkmark" size={20} color={Colors.surface} />
              <Text style={styles.bottomSaveButtonText}>Kaydet</Text>
            </>
          )}
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};
const styles = {
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    backgroundColor: Colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  headerButton: {
    padding: Spacing.sm,
    borderRadius: BorderRadius.md,
  },
  headerTitle: {
    fontSize: FontSize.lg,
    fontWeight: FontWeight.semibold,
    color: Colors.text,
  },
  saveButton: {
    backgroundColor: Colors.primary,
    paddingHorizontal: Spacing.md,
  },
  saveButtonDisabled: {
    backgroundColor: Colors.border,
  },
  saveButtonText: {
    fontSize: FontSize.sm,
    color: Colors.surface,
    fontWeight: FontWeight.medium,
  },
  content: {
    flex: 1,
    paddingHorizontal: Spacing.lg,
  },
  fieldContainer: {
    marginVertical: Spacing.lg,
  },
  fieldLabel: {
    fontSize: FontSize.md,
    fontWeight: FontWeight.medium,
    color: Colors.text,
    marginBottom: Spacing.sm,
  },
  errorText: {
    fontSize: FontSize.sm,
    color: Colors.error,
    marginBottom: Spacing.sm,
  },
  textInput: {
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    fontSize: FontSize.md,
    color: Colors.text,
    backgroundColor: Colors.surface,
  },
  descriptionInput: {
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    fontSize: FontSize.md,
    color: Colors.text,
    backgroundColor: Colors.surface,
    textAlignVertical: 'top',
    minHeight: 80,
  },
  characterCount: {
    fontSize: FontSize.xs,
    color: Colors.textSecondary,
    textAlign: 'right',
    marginTop: Spacing.xs,
  },
  selectorContainer: {
    marginTop: Spacing.md,
  },
  selectorLabel: {
    fontSize: FontSize.sm,
    fontWeight: FontWeight.medium,
    color: Colors.textSecondary,
    marginBottom: Spacing.sm,
  },
  horizontalScroll: {
    marginTop: Spacing.sm,
  },
  selectorOption: {
    alignItems: 'center',
    padding: Spacing.md,
    borderRadius: BorderRadius.md,
    borderWidth: 1,
    borderColor: Colors.border,
    marginRight: Spacing.sm,
    minWidth: 80,
    backgroundColor: Colors.surface,
  },
  selectorOptionSelected: {
    borderColor: Colors.primary,
    backgroundColor: Colors.primary,
    borderWidth: 2,
  },
  selectorEmoji: {
    fontSize: 20,
    marginBottom: Spacing.xs,
  },
  selectorOptionText: {
    fontSize: FontSize.sm,
    color: Colors.text,
    textAlign: 'center',
  },
  selectorOptionTextSelected: {
    color: Colors.surface,
    fontWeight: FontWeight.bold,
  },
  // Accordion Styles
  accordionContainer: {
    marginBottom: Spacing.lg,
  },
  accordionHeader: {
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.md,
    borderWidth: 1,
    borderColor: Colors.border,
    marginBottom: Spacing.sm,
  },
  accordionHeaderContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: Spacing.md,
  },
  accordionTitle: {
    fontSize: FontSize.md,
    fontWeight: FontWeight.medium,
    color: Colors.text,
  },
  accordionContent: {
    backgroundColor: Colors.background,
    borderRadius: BorderRadius.md,
    borderWidth: 1,
    borderColor: Colors.border,
    borderTopWidth: 0,
    borderTopLeftRadius: 0,
    borderTopRightRadius: 0,
    padding: Spacing.md,
    marginTop: -Spacing.sm,
  },
  // Enhanced Horizontal Scroll Styles
  enhancedScrollContainer: {
    position: 'relative',
    marginTop: Spacing.sm,
  },
  enhancedHorizontalScroll: {
    paddingBottom: 5, // Extra space for scroll indicator (2-3 pixels more)
  },
  scrollContentContainer: {
    paddingHorizontal: Spacing.xs,
  },
  leftFadeGradient: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 5, // Account for scroll indicator space
    width: 25,
    zIndex: 1,
    flexDirection: 'row',
  },
  rightFadeGradient: {
    position: 'absolute',
    right: 0,
    top: 0,
    bottom: 5, // Account for scroll indicator space
    width: 25,
    zIndex: 1,
    flexDirection: 'row',
  },
  fadeLayer: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
  },
  fadeLayerRight: {
    left: 'auto',
    right: 0,
  },
  rightFadeGradient: {
    position: 'absolute',
    right: 0,
    top: 0,
    bottom: 3, // Account for scroll indicator space
    width: 20,
    zIndex: 1,
  },
  // Bottom Button Styles
  bottomButtonContainer: {
    backgroundColor: Colors.surface,
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  bottomSaveButton: {
    backgroundColor: Colors.primary,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Spacing.md,
    borderRadius: BorderRadius.lg,
    elevation: 2,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  bottomSaveButtonDisabled: {
    backgroundColor: Colors.border,
    elevation: 0,
    shadowOpacity: 0,
  },
  bottomSaveButtonText: {
    fontSize: FontSize.md,
    color: Colors.surface,
    fontWeight: FontWeight.semibold,
    marginLeft: Spacing.xs,
  },
};
export default AddFieldScreen;
