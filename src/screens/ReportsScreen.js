import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { DataManager } from '../services/DataManager';
import SeasonSelector from '../components/SeasonSelector';
import { Colors } from '../constants/Colors';
import { Spacing, FontSize, FontWeight } from '../constants/Dimensions';

export default function ReportsScreen({ navigation }) {
  const [monthlyTotal, setMonthlyTotal] = useState(0);
  const [categoryTotals, setCategoryTotals] = useState([]);
  const [loading, setLoading] = useState(true);
  // SEASON INTEGRATION: Add season-related states
  const [seasons, setSeasons] = useState([]);
  const [selectedSeason, setSelectedSeason] = useState(null);
  const [activeSeason, setActiveSeason] = useState(null);
  const [filterMode, setFilterMode] = useState('season'); // 'month' | 'season'

  useEffect(() => {
    loadInitialData();

    // Listen for data changes (e.g., when new expense is added)
    const removeListener = DataManager.addDataChangeListener(() => {
      console.log('ReportsScreen: Data change detected, reloading reports...');
      loadReports();
    });

    return removeListener;
  }, []);

  // SEASON INTEGRATION: Reload reports when season selection changes
  useEffect(() => {
    if (selectedSeason) {
      loadReports();
    }
  }, [selectedSeason, filterMode]);

  // SEASON INTEGRATION: Load initial data including seasons
  const loadInitialData = async () => {
    try {
      await DataManager.initialize();

      // Load seasons
      const loadedSeasons = await DataManager.getSeasons();
      setSeasons(loadedSeasons);

      // Get active season and set as default
      const currentActiveSeason = await DataManager.getActiveSeason();
      setActiveSeason(currentActiveSeason);

      if (currentActiveSeason) {
        setSelectedSeason(currentActiveSeason);
        console.log('ReportsScreen: Default season set to active season:', currentActiveSeason.name);
      } else if (loadedSeasons.length > 0) {
        setSelectedSeason(loadedSeasons[0]);
        console.log('ReportsScreen: Default season set to first available season:', loadedSeasons[0].name);
      }

      // Load reports after setting default season
      await loadReports();
    } catch (error) {
      console.error('Error loading initial data:', error);
      Alert.alert('Hata', 'Veriler yüklenirken bir hata oluştu.');
    }
  };

  const loadReports = async () => {
    try {
      setLoading(true);
      console.log('ReportsScreen: Loading reports...');

      // DataManager'ı başlat
      await DataManager.initialize();

      // SEASON INTEGRATION: Filter expenses based on filterMode
      const allExpenses = await DataManager.getExpenses();
      console.log('ReportsScreen: All expenses loaded:', allExpenses.length);

      let filteredExpenses = [];

      if (filterMode === 'season' && selectedSeason) {
        // Filter by selected season
        const selectedSeasonId = selectedSeason.id || selectedSeason._id;

        filteredExpenses = allExpenses.filter(expense => {
          // Handle both string and object seasonId formats (same as HomeScreen)
          let expenseSeasonId = expense.seasonId;
          if (typeof expenseSeasonId === 'object' && expenseSeasonId !== null) {
            expenseSeasonId = expenseSeasonId._id || expenseSeasonId.id;
          }

          return expenseSeasonId === selectedSeasonId;
        });
        console.log('ReportsScreen: Season filtering applied:', filteredExpenses.length, 'expenses for season:', selectedSeason.name);
      } else {
        // Monthly filtering (fallback)
        const now = new Date();
        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);

        filteredExpenses = allExpenses.filter(expense => {
          const expenseDate = new Date(expense.date);
          return expenseDate >= startOfMonth && expenseDate <= endOfMonth;
        });
        console.log('ReportsScreen: Monthly filtering applied:', filteredExpenses.length, 'expenses for current month');
      }

      // Toplam hesapla
      const total = filteredExpenses.reduce((sum, expense) => sum + expense.amount, 0);
      console.log('ReportsScreen: Total calculated:', total);
      setMonthlyTotal(total);

      // Kategoriye göre toplamları hesapla
      const categoryMap = {};
      console.log('ReportsScreen: Processing categories...');
      filteredExpenses.forEach(expense => {
        console.log('ReportsScreen: Processing expense for category:', {
          expenseId: expense.id,
          categoryId: expense.categoryId,
          categoryName: expense.categoryName,
          categoryEmoji: expense.categoryEmoji,
          amount: expense.amount
        });
        const categoryName = expense.categoryName || 'Bilinmeyen';
        if (!categoryMap[categoryName]) {
          categoryMap[categoryName] = {
            name: categoryName,
            total: 0,
            emoji: expense.categoryEmoji || '📝',
            color: expense.categoryColor || '#666'
          };
        }
        categoryMap[categoryName].total += expense.amount;
      });

      const categoryData = Object.values(categoryMap);
      setCategoryTotals(categoryData.filter(cat => cat.total > 0));
      
    } catch (error) {
      console.error('Rapor yükleme hatası:', error);
      Alert.alert('Hata', 'Raporlar yüklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
    }).format(amount);
  };

  const getPercentage = (amount) => {
    if (monthlyTotal === 0) return 0;
    return ((amount / monthlyTotal) * 100).toFixed(1);
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <Text>Yükleniyor...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Raporlar</Text>
        <TouchableOpacity
          style={styles.homeButton}
          onPress={() => navigation.navigate('Home')}
        >
          <Ionicons name="home-outline" size={24} color={Colors.primary} />
        </TouchableOpacity>
      </View>

      {/* SEASON INTEGRATION: Filter Controls */}
      <View style={styles.filterContainer}>
        <View style={styles.filterModeToggle}>
          <TouchableOpacity
            style={[
              styles.filterModeButton,
              filterMode === 'month' && styles.filterModeButtonActive
            ]}
            onPress={() => setFilterMode('month')}
          >
            <Text style={[
              styles.filterModeButtonText,
              filterMode === 'month' && styles.filterModeButtonTextActive
            ]}>
              Bu Ay
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.filterModeButton,
              filterMode === 'season' && styles.filterModeButtonActive
            ]}
            onPress={() => setFilterMode('season')}
          >
            <Text style={[
              styles.filterModeButtonText,
              filterMode === 'season' && styles.filterModeButtonTextActive
            ]}>
              Sezon
            </Text>
          </TouchableOpacity>
        </View>

        {filterMode === 'season' && (
          <View style={styles.seasonSelectorContainer}>
            <SeasonSelector
              seasons={seasons}
              selectedSeason={selectedSeason}
              onSelect={setSelectedSeason}
              placeholder="Sezon seçiniz"
              showActiveBadge={true}
              style={styles.seasonSelector}
            />
          </View>
        )}
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* SEASON INTEGRATION: Dynamic summary title */}
        <View style={styles.summaryCard}>
          <View style={styles.summaryHeader}>
            <Ionicons
              name={filterMode === 'season' ? 'leaf' : 'calendar'}
              size={24}
              color={Colors.primary}
            />
            <Text style={styles.summaryTitle}>
              {filterMode === 'season' && selectedSeason
                ? `${selectedSeason.name} - Toplam Gider`
                : 'Bu Ayın Toplam Gideri'
              }
            </Text>
          </View>
          <Text style={styles.summaryAmount}>{formatCurrency(monthlyTotal)}</Text>
        </View>

        {/* Kategoriye Göre Dağılım */}
        <View style={styles.categorySection}>
          <Text style={styles.sectionTitle}>Kategoriye Göre Dağılım</Text>
          
          {categoryTotals.length === 0 ? (
            <View style={styles.emptyState}>
              <Ionicons name="pie-chart-outline" size={48} color={Colors.textSecondary} />
              <Text style={styles.emptyStateText}>
                {filterMode === 'season' && selectedSeason
                  ? `${selectedSeason.name} için henüz gider kaydı yok`
                  : 'Bu ay henüz gider kaydı yok'
                }
              </Text>
            </View>
          ) : (
            categoryTotals.map((category, index) => (
              <View key={`category-${index}-${category.name}`} style={styles.categoryItem}>
                <View style={styles.categoryInfo}>
                  <Text style={styles.categoryEmoji}>{category.emoji}</Text>
                  <View style={styles.categoryDetails}>
                    <Text style={styles.categoryName}>{category.name}</Text>
                    <Text style={styles.categoryPercentage}>
                      %{getPercentage(category.total)} - {formatCurrency(category.total)}
                    </Text>
                  </View>
                </View>
                <View style={styles.progressBarContainer}>
                  <View 
                    style={[
                      styles.progressBar, 
                      { 
                        width: `${getPercentage(category.total)}%`,
                        backgroundColor: category.color 
                      }
                    ]} 
                  />
                </View>
              </View>
            ))
          )}
        </View>

        {/* İstatistikler */}
        {categoryTotals.length > 0 && (
          <View style={styles.statsSection}>
            <Text style={styles.sectionTitle}>İstatistikler</Text>
            
            <View style={styles.statsGrid}>
              <View style={styles.statItem}>
                <Ionicons name="trending-up" size={24} color={Colors.success} />
                <Text style={styles.statLabel}>En Yüksek Kategori</Text>
                <Text style={styles.statValue}>
                  {categoryTotals[0]?.name || '-'}
                </Text>
              </View>
              
              <View style={styles.statItem}>
                <Ionicons name="list" size={24} color={Colors.info} />
                <Text style={styles.statLabel}>Toplam Kategori</Text>
                <Text style={styles.statValue}>{categoryTotals.length}</Text>
              </View>
              
              <View style={styles.statItem}>
                <Ionicons name="calculator" size={24} color={Colors.secondary} />
                <Text style={styles.statLabel}>Ortalama Gider</Text>
                <Text style={styles.statValue}>
                  {formatCurrency(monthlyTotal / (categoryTotals.length || 1))}
                </Text>
              </View>
            </View>
          </View>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    backgroundColor: Colors.primary,
    paddingTop: 50,
    paddingBottom: Spacing.lg,
    paddingHorizontal: Spacing.md,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: FontSize.xxl,
    fontWeight: FontWeight.bold,
    color: Colors.surface,
    flex: 1,
    textAlign: 'center',
  },
  homeButton: {
    backgroundColor: Colors.surface,
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    padding: Spacing.md,
  },
  summaryCard: {
    backgroundColor: Colors.surface,
    borderRadius: 12,
    padding: Spacing.lg,
    marginBottom: Spacing.lg,
    elevation: 2,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  summaryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  summaryTitle: {
    fontSize: FontSize.lg,
    fontWeight: FontWeight.medium,
    color: Colors.text,
    marginLeft: Spacing.sm,
  },
  summaryAmount: {
    fontSize: FontSize.xxxl,
    fontWeight: FontWeight.bold,
    color: Colors.primary,
    textAlign: 'center',
  },
  categorySection: {
    marginBottom: Spacing.lg,
  },
  sectionTitle: {
    fontSize: FontSize.lg,
    fontWeight: FontWeight.semibold,
    color: Colors.text,
    marginBottom: Spacing.md,
  },
  emptyState: {
    backgroundColor: Colors.surface,
    borderRadius: 12,
    padding: Spacing.xl,
    alignItems: 'center',
    elevation: 2,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  emptyStateText: {
    fontSize: FontSize.md,
    color: Colors.textSecondary,
    marginTop: Spacing.md,
  },
  categoryItem: {
    backgroundColor: Colors.surface,
    borderRadius: 12,
    padding: Spacing.md,
    marginBottom: Spacing.sm,
    elevation: 2,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  categoryInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  categoryEmoji: {
    fontSize: 24,
    marginRight: Spacing.md,
  },
  categoryDetails: {
    flex: 1,
  },
  categoryName: {
    fontSize: FontSize.md,
    fontWeight: FontWeight.semibold,
    color: Colors.text,
  },
  categoryPercentage: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
    marginTop: 2,
  },
  progressBarContainer: {
    height: 8,
    backgroundColor: Colors.border,
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    borderRadius: 4,
  },
  statsSection: {
    marginBottom: Spacing.xl,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statItem: {
    backgroundColor: Colors.surface,
    borderRadius: 12,
    padding: Spacing.md,
    width: '48%',
    marginBottom: Spacing.sm,
    alignItems: 'center',
    elevation: 2,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  statLabel: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
    marginTop: Spacing.xs,
    textAlign: 'center',
  },
  statValue: {
    fontSize: FontSize.md,
    fontWeight: FontWeight.semibold,
    color: Colors.text,
    marginTop: Spacing.xs,
    textAlign: 'center',
  },
  // SEASON INTEGRATION: Filter controls styles
  filterContainer: {
    backgroundColor: Colors.surface,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  filterModeToggle: {
    flexDirection: 'row',
    backgroundColor: Colors.background,
    borderRadius: 8,
    padding: 2,
  },
  filterModeButton: {
    flex: 1,
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.md,
    borderRadius: 6,
    alignItems: 'center',
  },
  filterModeButtonActive: {
    backgroundColor: Colors.primary,
  },
  filterModeButtonText: {
    fontSize: FontSize.sm,
    fontWeight: FontWeight.medium,
    color: Colors.textSecondary,
  },
  filterModeButtonTextActive: {
    color: Colors.surface,
  },
  seasonSelectorContainer: {
    marginTop: Spacing.md,
  },
  seasonSelector: {
    // Additional styles if needed
  },
});
