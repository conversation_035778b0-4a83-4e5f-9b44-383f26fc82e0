/**
 * Auth Choice Screen - İlk giriş seçim ekranı
 * Kullanıcıya Google ile giriş yap veya yerel veri saklama seçeneği sunar
 */

import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Image,
} from "react-native";
import { StatusBar } from "expo-status-bar";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { useAuth } from "../context/AuthContext";
import DataConflictModal from "../components/DataConflictModal";
import { Colors } from "../constants/Colors";
import Logger, { LogCategory } from "../utils/Logger";

const AuthChoiceScreen = ({ navigation }) => {
  const {
    signInWithGoogle,
    signInAsGuest,
    checkDataConflictAndSignIn,
    showDataConflictModal,
    setShowDataConflictModal,
    conflictData,
    signOut,
  } = useAuth();
  const [loading, setLoading] = useState(false);
  const [loadingType, setLoadingType] = useState(null); // 'google' or 'local'

  // Google ile giriş yap
  const handleGoogleSignIn = async () => {
    try {
      setLoading(true);
      setLoadingType("google");

      console.log("AuthChoiceScreen: Starting Google Sign-In...");

      // First check for data conflicts
      const result = await signInWithGoogle();

      console.log("AuthChoiceScreen: Google Sign-In result:", {
        success: result?.success,
        needsDataConflictCheck: result?.needsDataConflictCheck,
        guestDataCount: result?.guestDataCount,
        error: result?.error,
      });

      if (result && result.needsDataConflictCheck) {
        // Guest has data, need to check for conflicts
        console.log("AuthChoiceScreen: Data conflict check needed");
        await handleDataConflictCheck(result.guestDataCount);
      } else if (result && result.success) {
        // No conflicts, proceed with normal sign-in
        console.log("AuthChoiceScreen: No conflicts, proceeding to main app");
        await handleSuccessfulSignIn();
      } else if (result && result.cancelled) {
        // User cancelled - don't show error, just log it
        console.log("AuthChoiceScreen: User cancelled Google Sign-In");
      } else if (result && result.error) {
        console.log("AuthChoiceScreen: Showing error alert:", result.error);
        Alert.alert("Giriş Hatası", result.error);
      } else {
        console.log("AuthChoiceScreen: Unexpected result format:", result);
        Alert.alert(
          "Giriş Hatası",
          "Google ile giriş yapılamadı. Lütfen tekrar deneyin."
        );
      }
    } catch (error) {
      console.log("AuthChoiceScreen Google sign-in error:", error);
      Logger.error(LogCategory.AUTH, "Google sign-in failed", error);
      Alert.alert(
        "Giriş Hatası",
        "Google ile giriş yapılamadı. Lütfen tekrar deneyin."
      );
    } finally {
      setLoading(false);
      setLoadingType(null);
    }
  };

  const handleDataConflictCheck = async (guestCount) => {
    try {
      console.log("AuthChoiceScreen: Checking data conflict...");

      // Sign in and check user data - PASS guestCount parameter!
      const conflictResult = await checkDataConflictAndSignIn(guestCount);

      console.log(
        "AuthChoiceScreen: Data conflict check result:",
        conflictResult
      );

      if (conflictResult && conflictResult.success) {
        if (conflictResult.hasExistingData) {
          // Modal will be shown by AuthContext automatically
          console.log(
            "AuthChoiceScreen: Data conflict detected, modal should be shown by AuthContext"
          );
          // Don't navigate yet - wait for user decision in modal
          // Modal state will be handled by AuthContext
          // The modal will be rendered in this screen using AuthContext state
        } else {
          // No user data, proceed with migration
          console.log("AuthChoiceScreen: No user data conflict, proceeding");
          await handleSuccessfulSignIn();
        }
      } else {
        Alert.alert(
          "Hata",
          conflictResult?.error || "Veri kontrolü sırasında hata oluştu"
        );
      }
    } catch (error) {
      console.error("AuthChoiceScreen: Data conflict check error:", error);
      Alert.alert("Hata", "Veri kontrolü sırasında hata oluştu");
    }
  };

  const handleSuccessfulSignIn = async () => {
    try {
      // Check if modal is open - if so, don't navigate yet
      if (showDataConflictModal) {
        console.log("AuthChoiceScreen: Modal is open, not navigating yet");
        return;
      }

      // Kullanıcı tercihini kaydet
      await AsyncStorage.setItem("auth_choice", "google");
      await AsyncStorage.setItem("user_authenticated", "true");
      await AsyncStorage.setItem("user_has_made_auth_choice", "true");

      Logger.info(
        LogCategory.AUTH,
        "Google sign-in successful from AuthChoiceScreen"
      );

      // Ana uygulamaya yönlendir
      navigation.replace("MainApp");
    } catch (error) {
      console.error("AuthChoiceScreen: Error saving auth choice:", error);
      // Still navigate to main app even if saving fails (but only if modal is not open)
      if (!showDataConflictModal) {
        navigation.replace("MainApp");
      }
    }
  };

  // Data conflict modal callbacks
  const handleDataConflictContinue = async () => {
    try {
      setShowDataConflictModal(false);
      // User chose to continue - their guest data will be lost
      // The sign-in process is already completed, just navigate
      await handleSuccessfulSignIn();
    } catch (error) {
      console.error("AuthChoiceScreen: Continue with data loss error:", error);
      Alert.alert("Hata", "İşlem tamamlanırken hata oluştu");
    }
  };

  const handleDataConflictCancel = async () => {
    try {
      setShowDataConflictModal(false);
      // User cancelled - sign out and return to guest mode
      await signOut();
      console.log("AuthChoiceScreen: User cancelled data conflict resolution");
    } catch (error) {
      console.error("AuthChoiceScreen: Cancel error:", error);
    }
  };

  // Yerel veri saklama seçeneği
  const handleLocalStorage = async () => {
    try {
      setLoading(true);
      setLoadingType("local");

      // AuthService üzerinden misafir girişi yap
      const result = await signInAsGuest();

      if (result.success) {
        // Kullanıcı tercihini kaydet
        await AsyncStorage.setItem("auth_choice", "local");
        await AsyncStorage.setItem("user_has_made_auth_choice", "true");

        Logger.info(LogCategory.AUTH, "Local storage selected");

        // Kısa bir gecikme (kullanıcı deneyimi için)
        await new Promise((resolve) => setTimeout(resolve, 500));

        // Ana uygulamaya yönlendir
        navigation.replace("MainApp");
      } else {
        Alert.alert(
          "Hata",
          result.error || "Misafir moduna geçilirken hata oluştu."
        );
      }
    } catch (error) {
      Logger.error(LogCategory.AUTH, "Local storage setup failed", error);
      Alert.alert(
        "Hata",
        "Yerel veri saklama ayarlanamadı. Lütfen tekrar deneyin."
      );
    } finally {
      setLoading(false);
      setLoadingType(null);
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar style="light" backgroundColor={Colors.primary} />

      {/* Header */}
      <View style={styles.header}>
        <Image
          source={require("../../assets/icon.png")}
          style={styles.appIcon}
          resizeMode="contain"
        />
        <Text style={styles.appTitle}>Çiftçi Not Defterim</Text>
        <Text style={styles.appSubtitle}>Tarımsal Gider Takibi</Text>
      </View>

      {/* Content */}
      <View style={styles.content}>
        <Text style={styles.welcomeText}>Hoş Geldiniz!</Text>
        <Text style={styles.descriptionText}>
          Verilerinizi nasıl saklamak istiyorsunuz?
        </Text>

        {/* Google Sign-In Option */}
        <TouchableOpacity
          style={[styles.optionButton, styles.googleButton]}
          onPress={handleGoogleSignIn}
          disabled={loading}
        >
          {loadingType === "google" ? (
            <ActivityIndicator color="#FFFFFF" size="small" />
          ) : (
            <>
              <Text style={styles.googleIcon}>🔐</Text>
              <View style={styles.optionContent}>
                <Text style={styles.optionTitle}>Google ile Giriş Yap</Text>
                <Text style={styles.optionDescription}>
                  Verileriniz kaybolmaz, tüm cihazlarınızda senkronize olur
                </Text>
              </View>
            </>
          )}
        </TouchableOpacity>

        {/* Local Storage Option */}
        <TouchableOpacity
          style={[styles.optionButton, styles.localButton]}
          onPress={handleLocalStorage}
          disabled={loading}
        >
          {loadingType === "local" ? (
            <ActivityIndicator color={Colors.primary} size="small" />
          ) : (
            <>
              <Text style={styles.localIcon}>📱</Text>
              <View style={styles.optionContent}>
                <Text style={styles.optionTitle}>Verileri Burada Tut</Text>
                <Text style={styles.optionDescription}>
                  Veriler sadece bu cihazda saklanır
                </Text>
              </View>
            </>
          )}
        </TouchableOpacity>

        {/* Info Text */}
        <View style={styles.infoContainer}>
          <Text style={styles.infoIcon}>💡</Text>
          <Text style={styles.infoText}>
            Google ile giriş yaparsanız verileriniz kaybolmaz ve tüm
            cihazlarınızda erişebilirsiniz.
          </Text>
        </View>
      </View>

      {/* Data Conflict Modal */}
      <DataConflictModal
        visible={showDataConflictModal}
        onClose={handleDataConflictCancel}
        onContinue={handleDataConflictContinue}
        guestDataCount={conflictData.guestDataCount}
        userDataCount={conflictData.userDataCount}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    backgroundColor: Colors.primary,
    paddingTop: 60,
    paddingBottom: 40,
    paddingHorizontal: 20,
    alignItems: "center",
  },
  appIcon: {
    width: 64,
    height: 64,
    marginBottom: 16,
  },
  appTitle: {
    fontSize: 28,
    fontWeight: "bold",
    color: "#FFFFFF",
    textAlign: "center",
    marginBottom: 8,
  },
  appSubtitle: {
    fontSize: 16,
    color: "#FFFFFF",
    textAlign: "center",
    opacity: 0.9,
  },
  content: {
    flex: 1,
    padding: 24,
    justifyContent: "center",
  },
  welcomeText: {
    fontSize: 24,
    fontWeight: "bold",
    color: Colors.text,
    textAlign: "center",
    marginBottom: 12,
  },
  descriptionText: {
    fontSize: 16,
    color: Colors.textSecondary,
    textAlign: "center",
    marginBottom: 40,
    lineHeight: 24,
  },
  optionButton: {
    flexDirection: "row",
    alignItems: "center",
    padding: 20,
    borderRadius: 16,
    marginBottom: 16,
    elevation: 3,
    shadowColor: "#000000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  googleButton: {
    backgroundColor: Colors.primary,
  },
  localButton: {
    backgroundColor: "#FFFFFF",
    borderWidth: 2,
    borderColor: Colors.primary,
  },

  googleIcon: {
    fontSize: 32,
    marginRight: 16,
  },
  localIcon: {
    fontSize: 32,
    marginRight: 16,
  },

  optionContent: {
    flex: 1,
  },
  optionTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 4,
  },
  optionDescription: {
    fontSize: 14,
    opacity: 0.8,
    lineHeight: 20,
  },
  infoContainer: {
    flexDirection: "row",
    alignItems: "flex-start",
    backgroundColor: "#E8F5E8",
    padding: 16,
    borderRadius: 12,
    marginTop: 24,
  },
  infoIcon: {
    fontSize: 20,
    marginRight: 12,
    marginTop: 2,
  },
  infoText: {
    flex: 1,
    fontSize: 14,
    color: Colors.text,
    lineHeight: 20,
  },
});

export default AuthChoiceScreen;
