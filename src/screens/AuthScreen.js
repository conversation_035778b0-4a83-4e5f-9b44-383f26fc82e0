import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Image,
  SafeAreaView,
  StatusBar,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";

import AuthService from "../services/AuthService";
import { Colors } from "../constants/Colors";
import {
  Spacing,
  FontSize,
  FontWeight,
  BorderRadius,
} from "../constants/Dimensions";

export default function AuthScreen({ navigation }) {
  const [loading, setLoading] = useState(false);
  const [googleSignInLoading, setGoogleSignInLoading] = useState(false);
  const [guestSignInLoading, setGuestSignInLoading] = useState(false);

  useEffect(() => {
    // Initialize auth service
    initializeAuth();
  }, []);

  const initializeAuth = async () => {
    try {
      setLoading(true);
      await AuthService.initialize();

      // Check if user is already authenticated
      if (AuthService.isAuthenticated()) {
        navigation.replace("Main");
      }
    } catch (error) {
      console.error("Auth initialization error:", error);
      Alert.alert("Hata", "Uygulama başlatılırken bir hata oluştu.");
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    try {
      setGoogleSignInLoading(true);
      const result = await AuthService.signInWithGoogle();

      if (result.success) {
        Alert.alert(
          "Hoş Geldiniz!",
          `Merhaba ${result.user.name}, başarıyla giriş yaptınız.`,
          [
            {
              text: "Tamam",
              onPress: () => navigation.replace("Main"),
            },
          ]
        );
      } else {
        Alert.alert("Giriş Hatası", result.error);
      }
    } catch (error) {
      console.error("Google sign-in error:", error);
      Alert.alert("Hata", "Google ile giriş yapılırken bir hata oluştu.");
    } finally {
      setGoogleSignInLoading(false);
    }
  };

  const handleGuestSignIn = async () => {
    try {
      setGuestSignInLoading(true);
      const result = await AuthService.signInAsGuest();

      if (result.success) {
        Alert.alert(
          "Misafir Modu",
          "Misafir modunda devam ediyorsunuz. Verileriniz sadece bu cihazda saklanacak.",
          [
            {
              text: "Anladım",
              onPress: () => navigation.replace("Main"),
            },
          ]
        );
      } else {
        Alert.alert("Hata", result.error);
      }
    } catch (error) {
      console.error("Guest sign-in error:", error);
      Alert.alert("Hata", "Misafir moduna geçilirken bir hata oluştu.");
    } finally {
      setGuestSignInLoading(false);
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="light-content" backgroundColor={Colors.primary} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.primary} />
          <Text style={styles.loadingText}>Uygulama başlatılıyor...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={Colors.primary} />

      {/* Header */}
      <View style={styles.header}>
        <View style={styles.logoContainer}>
          <Image
            source={require("../../assets/icon.png")}
            style={styles.logoImage}
            resizeMode="contain"
          />
          <Text style={styles.logoText}>Çiftçi Not Defterim</Text>
          <Text style={styles.logoSubtext}>Tarımsal Gider Takibi</Text>
        </View>
      </View>

      {/* Content */}
      <View style={styles.content}>
        <View style={styles.welcomeSection}>
          <Text style={styles.welcomeTitle}>Hoş Geldiniz!</Text>
          <Text style={styles.welcomeDescription}>
            Tarımsal giderlerinizi kolayca takip edin, kategorilere ayırın ve
            mevsimsel raporlar oluşturun.
          </Text>
        </View>

        <View style={styles.featuresSection}>
          <View style={styles.featureItem}>
            <Ionicons
              name="add-circle-outline"
              size={24}
              color={Colors.primary}
            />
            <Text style={styles.featureText}>Hızlı gider ekleme</Text>
          </View>
          <View style={styles.featureItem}>
            <Ionicons
              name="bar-chart-outline"
              size={24}
              color={Colors.primary}
            />
            <Text style={styles.featureText}>Mevsimsel raporlar</Text>
          </View>
          <View style={styles.featureItem}>
            <Ionicons name="cloud-outline" size={24} color={Colors.primary} />
            <Text style={styles.featureText}>Bulut senkronizasyonu</Text>
          </View>
        </View>

        <View style={styles.authSection}>
          {/* Google Sign In Button */}
          <TouchableOpacity
            style={[styles.authButton, styles.googleButton]}
            onPress={handleGoogleSignIn}
            disabled={googleSignInLoading || guestSignInLoading}
          >
            {googleSignInLoading ? (
              <ActivityIndicator size="small" color={Colors.text} />
            ) : (
              <>
                <Ionicons name="logo-google" size={20} color={Colors.text} />
                <Text style={styles.googleButtonText}>
                  Google ile Giriş Yap
                </Text>
              </>
            )}
          </TouchableOpacity>

          {/* Guest Mode Button */}
          <TouchableOpacity
            style={[styles.authButton, styles.guestButton]}
            onPress={handleGuestSignIn}
            disabled={googleSignInLoading || guestSignInLoading}
          >
            {guestSignInLoading ? (
              <ActivityIndicator size="small" color={Colors.surface} />
            ) : (
              <>
                <Ionicons
                  name="person-outline"
                  size={20}
                  color={Colors.surface}
                />
                <Text style={styles.guestButtonText}>
                  Misafir Olarak Devam Et
                </Text>
              </>
            )}
          </TouchableOpacity>

          <Text style={styles.guestModeNote}>
            Misafir modunda verileriniz sadece bu cihazda saklanır
          </Text>
        </View>
      </View>

      {/* Footer */}
      <View style={styles.footer}>
        <Text style={styles.footerText}>
          Giriş yaparak Kullanım Koşulları ve Gizlilik Politikası'nı kabul etmiş
          olursunuz
        </Text>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: Colors.background,
  },
  loadingText: {
    marginTop: Spacing.md,
    fontSize: FontSize.md,
    color: Colors.textSecondary,
  },
  header: {
    backgroundColor: Colors.primary,
    paddingVertical: Spacing.xl,
    paddingHorizontal: Spacing.lg,
    alignItems: "center",
  },
  logoContainer: {
    alignItems: "center",
  },
  logoImage: {
    width: 80,
    height: 80,
    marginBottom: Spacing.sm,
  },
  logoText: {
    fontSize: FontSize.xxxl,
    fontWeight: FontWeight.bold,
    color: Colors.surface,
    textAlign: "center",
  },
  logoSubtext: {
    fontSize: FontSize.md,
    color: Colors.surface,
    opacity: 0.9,
    marginTop: Spacing.xs,
  },
  content: {
    flex: 1,
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.xl,
  },
  welcomeSection: {
    marginBottom: Spacing.xl,
  },
  welcomeTitle: {
    fontSize: FontSize.xxl,
    fontWeight: FontWeight.bold,
    color: Colors.text,
    textAlign: "center",
    marginBottom: Spacing.md,
  },
  welcomeDescription: {
    fontSize: FontSize.md,
    color: Colors.textSecondary,
    textAlign: "center",
    lineHeight: 24,
  },
  featuresSection: {
    marginBottom: Spacing.xl,
  },
  featureItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: Spacing.md,
    paddingHorizontal: Spacing.md,
  },
  featureText: {
    fontSize: FontSize.md,
    color: Colors.text,
    marginLeft: Spacing.md,
  },
  authSection: {
    marginTop: "auto",
  },
  authButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.lg,
    borderRadius: BorderRadius.lg,
    marginBottom: Spacing.md,
    minHeight: 50,
  },
  googleButton: {
    backgroundColor: Colors.surface,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  googleButtonText: {
    fontSize: FontSize.md,
    fontWeight: FontWeight.medium,
    color: Colors.text,
    marginLeft: Spacing.sm,
  },
  guestButton: {
    backgroundColor: Colors.primary,
  },
  guestButtonText: {
    fontSize: FontSize.md,
    fontWeight: FontWeight.medium,
    color: Colors.surface,
    marginLeft: Spacing.sm,
  },
  guestModeNote: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
    textAlign: "center",
    marginTop: Spacing.sm,
  },
  footer: {
    paddingHorizontal: Spacing.lg,
    paddingBottom: Spacing.lg,
  },
  footerText: {
    fontSize: FontSize.xs,
    color: Colors.textSecondary,
    textAlign: "center",
    lineHeight: 18,
  },
});
