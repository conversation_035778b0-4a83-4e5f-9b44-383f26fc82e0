import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  StatusBar,
  Alert,
  TextInput,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { Colors } from '../constants/Colors';
import { Spacing, FontSize, FontWeight, BorderRadius } from '../constants/Dimensions';
import EmojiPicker from '../components/EmojiPicker';
import { validateCategoryName, generateCategoryId } from '../utils/validation';
import Logger, { LogCategory } from '../utils/Logger';
import Analytics, { AnalyticsEvent } from '../utils/Analytics';

// Predefined color palette for categories
const CATEGORY_COLORS = [
  '#4CAF50', '#2196F3', '#FF9800', '#9C27B0', '#F44336',
  '#795548', '#607D8B', '#FF5722', '#8BC34A', '#03A9F4',
  '#FFC107', '#E91E63', '#673AB7', '#009688', '#CDDC39',
  '#3F51B5', '#FF6F00', '#C2185B', '#7B1FA2', '#388E3C',
];

// Predefined icons for categories
const CATEGORY_ICONS = [
  'leaf-outline', 'water-outline', 'flash-outline', 'hammer-outline',
  'car-outline', 'home-outline', 'people-outline', 'calculator-outline',
  'card-outline', 'time-outline', 'location-outline', 'document-outline',
  'settings-outline', 'star-outline', 'heart-outline', 'gift-outline',
];

export default function CategoryCreationScreen({ navigation, route }) {
  const { onCategoryCreated, editingCategory = null } = route.params || {};
  
  const [categoryData, setCategoryData] = useState({
    name: '',
    emoji: '📝',
    color: CATEGORY_COLORS[0],
    icon: CATEGORY_ICONS[0],
    description: '',
  });
  
  const [errors, setErrors] = useState({});
  const [loading, setSaving] = useState(false);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [creationStartTime] = useState(Date.now());

  const isEditing = !!editingCategory;

  useEffect(() => {
    if (isEditing) {
      setCategoryData({
        name: editingCategory.name || '',
        emoji: editingCategory.emoji || '📝',
        color: editingCategory.color || CATEGORY_COLORS[0],
        icon: editingCategory.icon || CATEGORY_ICONS[0],
        description: editingCategory.description || '',
      });
      
      Analytics.trackEvent(AnalyticsEvent.CATEGORY_EDIT_STARTED, {
        categoryId: editingCategory.id,
        categoryName: editingCategory.name,
      });
    } else {
      Analytics.trackEvent(AnalyticsEvent.CATEGORY_CREATION_STARTED);
    }

    Logger.info(LogCategory.USER_ACTION, isEditing ? 'Category edit started' : 'Category creation started');
  }, [editingCategory, isEditing]);

  const handleSave = async () => {
    try {
      setSaving(true);
      setErrors({});

      // Validate category data
      const validation = validateCategoryData();
      if (!validation.isValid) {
        setErrors(validation.errors);
        return;
      }

      // Create or update category
      const categoryToSave = {
        id: isEditing ? editingCategory.id : generateCategoryId(),
        ...categoryData,
        isDefault: false,
        createdAt: isEditing ? editingCategory.createdAt : new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      // Save to database
      // This would be replaced with actual database call
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call

      Analytics.trackEvent(
        isEditing ? AnalyticsEvent.CATEGORY_EDITED : AnalyticsEvent.CATEGORY_CREATED,
        {
          categoryId: categoryToSave.id,
          categoryName: categoryToSave.name,
          hasCustomEmoji: categoryToSave.emoji !== '📝',
          hasCustomColor: categoryToSave.color !== CATEGORY_COLORS[0],
          hasDescription: !!categoryToSave.description,
          creationTime: isEditing ? undefined : Date.now() - creationStartTime,
        }
      );

      Logger.info(LogCategory.USER_ACTION, isEditing ? 'Category updated' : 'Category created', {
        categoryId: categoryToSave.id,
        categoryName: categoryToSave.name,
      });

      Alert.alert(
        'Başarılı',
        isEditing ? 'Kategori başarıyla güncellendi.' : 'Kategori başarıyla oluşturuldu.',
        [
          {
            text: 'Tamam',
            onPress: () => {
              if (onCategoryCreated) {
                onCategoryCreated(categoryToSave);
              }
              navigation.goBack();
            },
          },
        ]
      );
    } catch (error) {
      Logger.error(LogCategory.USER_ACTION, 'Failed to save category', error);
      Alert.alert('Hata', 'Kategori kaydedilirken bir hata oluştu.');
    } finally {
      setSaving(false);
    }
  };

  const validateCategoryData = () => {
    const errors = {};
    
    // Validate name
    const nameValidation = validateCategoryName(categoryData.name);
    if (!nameValidation.isValid) {
      errors.name = nameValidation.error;
    }

    // Validate emoji
    if (!categoryData.emoji.trim()) {
      errors.emoji = 'Emoji seçimi gereklidir';
    }

    // Validate color
    if (!categoryData.color) {
      errors.color = 'Renk seçimi gereklidir';
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors,
    };
  };

  const updateField = (field, value) => {
    setCategoryData(prev => ({ ...prev, [field]: value }));
    
    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  const renderNameInput = () => (
    <View style={styles.fieldContainer}>
      <Text style={styles.fieldLabel}>Kategori Adı *</Text>
      {errors.name && (
        <Text style={styles.errorText}>{errors.name}</Text>
      )}
      <TextInput
        style={[styles.textInput, errors.name && styles.textInputError]}
        value={categoryData.name}
        onChangeText={(text) => updateField('name', text)}
        placeholder="Örn: Organik Gübre, Sera Malzemeleri"
        maxLength={50}
        autoFocus
      />
      <Text style={styles.characterCount}>
        {categoryData.name.length} / 50
      </Text>
    </View>
  );

  const renderEmojiSelector = () => (
    <View style={styles.fieldContainer}>
      <Text style={styles.fieldLabel}>Emoji *</Text>
      {errors.emoji && (
        <Text style={styles.errorText}>{errors.emoji}</Text>
      )}
      <TouchableOpacity
        style={[styles.emojiSelector, errors.emoji && styles.emojiSelectorError]}
        onPress={() => setShowEmojiPicker(true)}
      >
        <Text style={styles.selectedEmoji}>{categoryData.emoji}</Text>
        <Text style={styles.emojiSelectorText}>Emoji Seç</Text>
        <Ionicons name="chevron-forward" size={20} color={Colors.textSecondary} />
      </TouchableOpacity>
    </View>
  );

  const renderColorSelector = () => (
    <View style={styles.fieldContainer}>
      <Text style={styles.fieldLabel}>Renk *</Text>
      {errors.color && (
        <Text style={styles.errorText}>{errors.color}</Text>
      )}
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.colorScroll}>
        {CATEGORY_COLORS.map((color) => (
          <TouchableOpacity
            key={color}
            style={[
              styles.colorOption,
              { backgroundColor: color },
              categoryData.color === color && styles.colorOptionSelected,
            ]}
            onPress={() => updateField('color', color)}
          >
            {categoryData.color === color && (
              <Ionicons name="checkmark" size={20} color={Colors.surface} />
            )}
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  const renderIconSelector = () => (
    <View style={styles.fieldContainer}>
      <Text style={styles.fieldLabel}>İkon (İsteğe Bağlı)</Text>
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.iconScroll}>
        {CATEGORY_ICONS.map((icon) => (
          <TouchableOpacity
            key={icon}
            style={[
              styles.iconOption,
              categoryData.icon === icon && styles.iconOptionSelected,
            ]}
            onPress={() => updateField('icon', icon)}
          >
            <Ionicons
              name={icon}
              size={24}
              color={categoryData.icon === icon ? Colors.primary : Colors.textSecondary}
            />
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  const renderDescriptionInput = () => (
    <View style={styles.fieldContainer}>
      <Text style={styles.fieldLabel}>Açıklama (İsteğe Bağlı)</Text>
      <TextInput
        style={styles.descriptionInput}
        value={categoryData.description}
        onChangeText={(text) => updateField('description', text)}
        placeholder="Bu kategori hangi giderler için kullanılacak?"
        multiline
        numberOfLines={3}
        maxLength={200}
      />
      <Text style={styles.characterCount}>
        {categoryData.description.length} / 200
      </Text>
    </View>
  );

  const renderPreview = () => (
    <View style={styles.previewContainer}>
      <Text style={styles.previewTitle}>Önizleme</Text>
      <View
        style={[
          styles.previewCard,
          { borderColor: categoryData.color },
        ]}
      >
        <Text style={styles.previewEmoji}>{categoryData.emoji}</Text>
        <View style={styles.previewInfo}>
          <Text style={styles.previewName}>
            {categoryData.name || 'Kategori Adı'}
          </Text>
          {categoryData.description && (
            <Text style={styles.previewDescription}>
              {categoryData.description}
            </Text>
          )}
        </View>
        <Ionicons name={categoryData.icon} size={20} color={categoryData.color} />
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.background} />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.headerButton} onPress={() => navigation.goBack()}>
          <Ionicons name="close" size={24} color={Colors.text} />
        </TouchableOpacity>
        
        <Text style={styles.headerTitle}>
          {isEditing ? 'Kategoriyi Düzenle' : 'Yeni Kategori'}
        </Text>
        
        <TouchableOpacity
          style={[
            styles.headerButton,
            styles.saveButton,
            loading && styles.saveButtonDisabled,
          ]}
          onPress={handleSave}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator size="small" color={Colors.surface} />
          ) : (
            <Text style={styles.saveButtonText}>Kaydet</Text>
          )}
        </TouchableOpacity>
      </View>

      {/* Content */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderNameInput()}
        {renderEmojiSelector()}
        {renderColorSelector()}
        {renderIconSelector()}
        {renderDescriptionInput()}
        {renderPreview()}
      </ScrollView>

      {/* Emoji Picker Modal */}
      <EmojiPicker
        visible={showEmojiPicker}
        onClose={() => setShowEmojiPicker(false)}
        onEmojiSelect={(emoji) => updateField('emoji', emoji)}
        selectedEmoji={categoryData.emoji}
        title="Kategori Emojisi Seç"
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  headerButton: {
    padding: Spacing.sm,
    minWidth: 60,
  },
  headerTitle: {
    fontSize: FontSize.lg,
    fontWeight: FontWeight.bold,
    color: Colors.text,
  },
  saveButton: {
    backgroundColor: Colors.primary,
    borderRadius: BorderRadius.md,
    alignItems: 'center',
    justifyContent: 'center',
  },
  saveButtonDisabled: {
    backgroundColor: Colors.border,
  },
  saveButtonText: {
    fontSize: FontSize.sm,
    color: Colors.surface,
    fontWeight: FontWeight.medium,
  },
  content: {
    flex: 1,
    paddingHorizontal: Spacing.lg,
  },
  fieldContainer: {
    marginVertical: Spacing.lg,
  },
  fieldLabel: {
    fontSize: FontSize.md,
    fontWeight: FontWeight.medium,
    color: Colors.text,
    marginBottom: Spacing.sm,
  },
  errorText: {
    fontSize: FontSize.sm,
    color: Colors.error,
    marginBottom: Spacing.sm,
  },
  textInput: {
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
    fontSize: FontSize.md,
    color: Colors.text,
    backgroundColor: Colors.surface,
  },
  textInputError: {
    borderColor: Colors.error,
  },
  characterCount: {
    fontSize: FontSize.xs,
    color: Colors.textSecondary,
    textAlign: 'right',
    marginTop: Spacing.xs,
  },
  emojiSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.surface,
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
  },
  emojiSelectorError: {
    borderColor: Colors.error,
  },
  selectedEmoji: {
    fontSize: 32,
    marginRight: Spacing.md,
  },
  emojiSelectorText: {
    flex: 1,
    fontSize: FontSize.md,
    color: Colors.text,
  },
  colorScroll: {
    marginTop: Spacing.sm,
  },
  colorOption: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: Spacing.md,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'transparent',
  },
  colorOptionSelected: {
    borderColor: Colors.text,
    borderWidth: 3,
  },
  iconScroll: {
    marginTop: Spacing.sm,
  },
  iconOption: {
    width: 50,
    height: 50,
    borderRadius: BorderRadius.md,
    marginRight: Spacing.md,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.surface,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  iconOptionSelected: {
    borderColor: Colors.primary,
    backgroundColor: Colors.primaryLight,
  },
  descriptionInput: {
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
    fontSize: FontSize.md,
    color: Colors.text,
    backgroundColor: Colors.surface,
    textAlignVertical: 'top',
    minHeight: 80,
  },
  previewContainer: {
    marginVertical: Spacing.xl,
    paddingVertical: Spacing.lg,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  previewTitle: {
    fontSize: FontSize.md,
    fontWeight: FontWeight.bold,
    color: Colors.text,
    marginBottom: Spacing.md,
  },
  previewCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.lg,
    borderWidth: 2,
    padding: Spacing.lg,
  },
  previewEmoji: {
    fontSize: 32,
    marginRight: Spacing.md,
  },
  previewInfo: {
    flex: 1,
  },
  previewName: {
    fontSize: FontSize.md,
    fontWeight: FontWeight.bold,
    color: Colors.text,
    marginBottom: Spacing.xs,
  },
  previewDescription: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
  },
});
