import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  FlatList,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Share,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { Ionicons } from '@expo/vector-icons';
import { useFocusEffect } from '@react-navigation/native';

import { DataManager } from '../services/DataManager';
import SeasonSummary from '../components/SeasonSummary';
import ViewToggle from '../components/ViewToggle';
import { Colors } from '../constants/Colors';
import { Spacing, FontSize, FontWeight } from '../constants/Dimensions';

export default function SeasonDetailScreen({ navigation, route }) {
  const { seasonId, seasonName } = route.params;
  
  const [season, setSeason] = useState(null);
  const [expenses, setExpenses] = useState([]);  // Still needed for recent expenses preview
  const [categories, setCategories] = useState([]);  // Still needed for expense display
  const [loading, setLoading] = useState(true);
  const [viewMode, setViewMode] = useState('summary'); // 'summary' | 'analytics' - REMOVED 'expenses'
  // REMOVED: expenseLoading state - no longer needed

  useEffect(() => {
    loadSeasonData();

    // Listen for data changes
    const removeListener = DataManager.addDataChangeListener(() => {
      console.log('SeasonDetailScreen: Data change detected, reloading...');
      loadSeasonData();
    });

    return removeListener;
  }, []);

  // Reload data when screen comes into focus
  useFocusEffect(
    React.useCallback(() => {
      loadSeasonData();
    }, [])
  );

  const loadSeasonData = async () => {
    try {
      setLoading(true);

      // Initialize DataManager
      await DataManager.initialize();

      // Load season, expenses, and categories
      const [seasonData, allExpenses, allCategories] = await Promise.all([
        DataManager.getSeasonById(seasonId),
        DataManager.getExpenses(),
        DataManager.getCategories()
      ]);

      if (!seasonData) {
        Alert.alert('Hata', 'Sezon bulunamadı.', [
          { text: 'Tamam', onPress: () => navigation.goBack() }
        ]);
        return;
      }

      setSeason(seasonData);
      setCategories(allCategories || []);

      // Filter expenses for this season
      const seasonExpenses = allExpenses.filter(expense => {
        // Check if expense belongs to this season
        if (expense.seasonId === seasonId) {
          return true;
        }

        // If no seasonId, check date range (for legacy expenses)
        if (!expense.seasonId && seasonData.startDate) {
          const expenseDate = new Date(expense.date);
          const seasonStart = new Date(seasonData.startDate);
          const seasonEnd = seasonData.endDate ? new Date(seasonData.endDate) : new Date();
          
          return expenseDate >= seasonStart && expenseDate <= seasonEnd;
        }

        return false;
      });

      // Sort expenses by date (newest first)
      seasonExpenses.sort((a, b) => new Date(b.date) - new Date(a.date));
      setExpenses(seasonExpenses);
      
      console.log('SeasonDetailScreen - Data loaded:', {
        season: seasonData.name,
        expenseCount: seasonExpenses.length
      });
    } catch (error) {
      console.error('Error loading season data:', error);
      Alert.alert('Hata', 'Sezon verileri yüklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  // REMOVED: handleAddExpense - No longer needed as expense management is moved to HomeScreen

  // REMOVED: handleEditExpense - Expense editing moved to HomeScreen
  // Users should navigate to HomeScreen to edit expenses

  const handleEditSeason = () => {
    navigation.navigate('EditSeason', { 
      seasonId: seasonId,
      season: season 
    });
  };

  const handleShareSeason = async () => {
    try {
      const summary = calculateSeasonSummary();
      const shareContent = `
🌱 ${season.name} - Sezon Özeti

📊 Toplam Gider: ${formatCurrency(summary.totalAmount)}
📝 Gider Sayısı: ${summary.expenseCount}
📅 Tarih Aralığı: ${getDateRangeText()}

${summary.categoryBreakdown.length > 0 ? '📋 En Çok Harcanan Kategoriler:\n' + 
  summary.categoryBreakdown.slice(0, 3).map(cat => 
    `${cat.categoryEmoji} ${cat.categoryName}: ${formatCurrency(cat.amount)}`
  ).join('\n') : ''}

Çiftçi Not Defterim ile oluşturuldu 🚜
      `.trim();

      await Share.share({
        message: shareContent,
        title: `${season.name} - Sezon Özeti`
      });
    } catch (error) {
      console.error('Error sharing season:', error);
      Alert.alert('Hata', 'Paylaşım sırasında bir hata oluştu.');
    }
  };

  const calculateSeasonSummary = () => {
    if (!expenses || expenses.length === 0) {
      return {
        totalAmount: 0,
        expenseCount: 0,
        avgAmount: 0,
        categoryBreakdown: []
      };
    }

    const totalAmount = expenses.reduce((sum, expense) => sum + expense.amount, 0);
    const expenseCount = expenses.length;
    const avgAmount = totalAmount / expenseCount;

    // Calculate category breakdown
    const categoryMap = new Map();
    expenses.forEach(expense => {
      const categoryId = expense.categoryId;
      if (categoryMap.has(categoryId)) {
        const existing = categoryMap.get(categoryId);
        categoryMap.set(categoryId, {
          ...existing,
          amount: existing.amount + expense.amount,
          count: existing.count + 1
        });
      } else {
        const category = categories.find(cat => cat.id === categoryId || cat._id === categoryId);
        categoryMap.set(categoryId, {
          categoryId,
          categoryName: category?.name || 'Bilinmeyen',
          categoryEmoji: category?.emoji || '📝',
          amount: expense.amount,
          count: 1
        });
      }
    });

    const categoryBreakdown = Array.from(categoryMap.values())
      .sort((a, b) => b.amount - a.amount);

    return {
      totalAmount,
      expenseCount,
      avgAmount,
      categoryBreakdown
    };
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('tr-TR');
  };

  const getDateRangeText = () => {
    if (!season) return '';
    
    const startText = season.startDate 
      ? formatDate(season.startDate)
      : 'Başlangıç: Açık';
    
    const endText = season.endDate 
      ? formatDate(season.endDate)
      : 'Bitiş: Açık';
    
    return `${startText} - ${endText}`;
  };

  const renderExpenseItem = ({ item: expense }) => {
    const category = categories.find(cat => cat.id === expense.categoryId || cat._id === expense.categoryId);
    
    return (
      <TouchableOpacity
        style={styles.expenseItem}
        onPress={() => navigation.navigate('MainTabs', { screen: 'Home' })}
      >
        <View style={styles.expenseHeader}>
          <Text style={styles.categoryEmoji}>{category?.emoji || '📝'}</Text>
          <View style={styles.expenseInfo}>
            <Text style={styles.expenseDescription} numberOfLines={1}>
              {expense.description}
            </Text>
            <Text style={styles.expenseCategory}>
              {category?.name || 'Bilinmeyen Kategori'}
            </Text>
          </View>
          <View style={styles.expenseRight}>
            <Text style={styles.expenseAmount}>
              {formatCurrency(expense.amount)}
            </Text>
            <Text style={styles.expenseDate}>
              {formatDate(expense.date)}
            </Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const renderEmptyExpenses = () => (
    <View style={styles.emptyState}>
      <Ionicons name="receipt-outline" size={64} color={Colors.textSecondary} />
      <Text style={styles.emptyStateTitle}>Henüz Gider Yok</Text>
      <Text style={styles.emptyStateText}>
        Bu sezona ait henüz gider kaydı bulunmuyor.
      </Text>
      <TouchableOpacity
        style={styles.addFirstExpenseButton}
        onPress={() => navigation.navigate('MainTabs', { screen: 'Home' })}
      >
        <Text style={styles.addFirstExpenseButtonText}>Ana Sayfaya Git</Text>
      </TouchableOpacity>
    </View>
  );

  const viewOptions = [
    { key: 'summary', label: 'Özet', icon: 'stats-chart' },
    { key: 'analytics', label: 'Analiz', icon: 'analytics' }
    // REMOVED: expenses option - Gider yönetimi HomeScreen'e taşındı
  ];

  if (loading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <StatusBar style="light" backgroundColor={Colors.primary} />
        <ActivityIndicator size="large" color={Colors.primary} />
        <Text style={styles.loadingText}>Sezon yükleniyor...</Text>
      </View>
    );
  }

  if (!season) {
    return (
      <View style={[styles.container, styles.centered]}>
        <StatusBar style="light" backgroundColor={Colors.primary} />
        <Text style={styles.errorText}>Sezon bulunamadı</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar style="light" backgroundColor={Colors.primary} />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={Colors.surface} />
        </TouchableOpacity>
        <Text style={styles.headerTitle} numberOfLines={1}>
          {season.name}
        </Text>
        <View style={styles.headerActions}>
          <TouchableOpacity style={styles.headerAction} onPress={handleShareSeason}>
            <Ionicons name="share" size={20} color={Colors.surface} />
          </TouchableOpacity>
          <TouchableOpacity style={styles.headerAction} onPress={handleEditSeason}>
            <Ionicons name="create" size={20} color={Colors.surface} />
          </TouchableOpacity>
        </View>
      </View>

      {/* View Toggle */}
      <View style={styles.toggleContainer}>
        <ViewToggle
          mode={viewMode}
          onToggle={setViewMode}
          options={viewOptions}
          size="small"
        />
      </View>

      {/* Content */}
      <View style={styles.content}>
        {viewMode === 'summary' && (
          <ScrollView showsVerticalScrollIndicator={false}>
            <SeasonSummary
              season={season}
              expenses={expenses}
              compact={false}
              showProgress={true}
              showCategoryBreakdown={true}
              style={styles.summaryWidget}
            />

            {/* Quick Actions - REMOVED: Gider Ekle button */}
            <View style={styles.quickActions}>
              <TouchableOpacity style={styles.quickAction} onPress={handleEditSeason}>
                <Ionicons name="create" size={24} color={Colors.secondary} />
                <Text style={styles.quickActionText}>Sezon Düzenle</Text>
              </TouchableOpacity>

              <TouchableOpacity style={styles.quickAction} onPress={handleShareSeason}>
                <Ionicons name="share" size={24} color={Colors.success} />
                <Text style={styles.quickActionText}>Paylaş</Text>
              </TouchableOpacity>
            </View>

            {/* Recent Expenses Preview */}
            {expenses.length > 0 && (
              <View style={styles.recentExpensesSection}>
                <View style={styles.sectionHeader}>
                  <Text style={styles.sectionTitle}>Son Giderler</Text>
                  <TouchableOpacity onPress={() => navigation.navigate('MainTabs', { screen: 'Home' })}>
                    <Text style={styles.seeAllText}>Tümünü Gör</Text>
                  </TouchableOpacity>
                </View>

                {expenses.slice(0, 3).map((expense) => (
                  <View key={expense.id || expense._id}>
                    {renderExpenseItem({ item: expense })}
                  </View>
                ))}
              </View>
            )}
          </ScrollView>
        )}

        {/* REMOVED: expenses view mode - Gider yönetimi HomeScreen'e taşındı */}

        {viewMode === 'analytics' && (
          <ScrollView showsVerticalScrollIndicator={false}>
            <View style={styles.analyticsContainer}>
              <Text style={styles.analyticsTitle}>Analiz ve Raporlar</Text>
              <Text style={styles.analyticsSubtitle}>
                Gelişmiş analiz özellikleri yakında eklenecek
              </Text>

              {/* Placeholder for future analytics */}
              <View style={styles.analyticsPlaceholder}>
                <Ionicons name="analytics" size={48} color={Colors.textSecondary} />
                <Text style={styles.placeholderText}>
                  Grafik ve analiz özellikleri geliştiriliyor
                </Text>
              </View>
            </View>
          </ScrollView>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    backgroundColor: Colors.primary,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Spacing.md,
    paddingTop: 50, // Status bar height
    paddingBottom: Spacing.md,
    elevation: 4,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  backButton: {
    padding: Spacing.xs,
  },
  headerTitle: {
    fontSize: FontSize.xl,
    fontWeight: FontWeight.bold,
    color: Colors.surface,
    flex: 1,
    textAlign: 'center',
    marginHorizontal: Spacing.sm,
  },
  headerActions: {
    flexDirection: 'row',
  },
  headerAction: {
    padding: Spacing.xs,
    marginLeft: Spacing.xs,
  },
  loadingText: {
    fontSize: FontSize.md,
    color: Colors.textSecondary,
    marginTop: Spacing.md,
  },
  errorText: {
    fontSize: FontSize.md,
    color: Colors.error,
  },
  toggleContainer: {
    backgroundColor: Colors.surface,
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
    alignItems: 'center',
  },
  content: {
    flex: 1,
  },
  summaryWidget: {
    margin: Spacing.md,
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.lg,
    backgroundColor: Colors.surface,
    marginHorizontal: Spacing.md,
    marginBottom: Spacing.md,
    borderRadius: 12,
    elevation: 1,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  quickAction: {
    alignItems: 'center',
    flex: 1,
  },
  quickActionText: {
    fontSize: FontSize.sm,
    color: Colors.text,
    marginTop: Spacing.xs,
    textAlign: 'center',
  },
  recentExpensesSection: {
    backgroundColor: Colors.surface,
    marginHorizontal: Spacing.md,
    marginBottom: Spacing.md,
    borderRadius: 12,
    padding: Spacing.md,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  sectionTitle: {
    fontSize: FontSize.lg,
    fontWeight: FontWeight.semibold,
    color: Colors.text,
  },
  seeAllText: {
    fontSize: FontSize.sm,
    color: Colors.primary,
    fontWeight: FontWeight.medium,
  },
  // REMOVED: expensesContainer, expensesHeader, expensesTitle, addExpenseButton, addExpenseButtonText
  // These styles are no longer needed as expense management moved to HomeScreen
  expenseItem: {
    backgroundColor: Colors.surface,
    borderRadius: 8,
    padding: Spacing.md,
    marginBottom: Spacing.sm,
    elevation: 1,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  expenseHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryEmoji: {
    fontSize: 20,
    marginRight: Spacing.sm,
  },
  expenseInfo: {
    flex: 1,
  },
  expenseDescription: {
    fontSize: FontSize.md,
    fontWeight: FontWeight.medium,
    color: Colors.text,
    marginBottom: Spacing.xs,
  },
  expenseCategory: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
  },
  expenseRight: {
    alignItems: 'flex-end',
  },
  expenseAmount: {
    fontSize: FontSize.md,
    fontWeight: FontWeight.bold,
    color: Colors.primary,
    marginBottom: Spacing.xs,
  },
  expenseDate: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: Spacing.xl,
  },
  emptyListContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  emptyStateTitle: {
    fontSize: FontSize.xl,
    fontWeight: FontWeight.bold,
    color: Colors.text,
    marginTop: Spacing.lg,
    marginBottom: Spacing.md,
  },
  emptyStateText: {
    fontSize: FontSize.md,
    color: Colors.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: Spacing.xl,
    paddingHorizontal: Spacing.lg,
  },
  addFirstExpenseButton: {
    backgroundColor: Colors.primary,
    borderRadius: 8,
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.xl,
  },
  addFirstExpenseButtonText: {
    color: Colors.surface,
    fontSize: FontSize.md,
    fontWeight: FontWeight.semibold,
  },
  analyticsContainer: {
    padding: Spacing.md,
  },
  analyticsTitle: {
    fontSize: FontSize.xl,
    fontWeight: FontWeight.bold,
    color: Colors.text,
    marginBottom: Spacing.sm,
  },
  analyticsSubtitle: {
    fontSize: FontSize.md,
    color: Colors.textSecondary,
    marginBottom: Spacing.xl,
  },
  analyticsPlaceholder: {
    alignItems: 'center',
    paddingVertical: Spacing.xl,
  },
  placeholderText: {
    fontSize: FontSize.md,
    color: Colors.textSecondary,
    marginTop: Spacing.md,
    textAlign: 'center',
  },
});
