import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  StatusBar,
  Alert,
  TextInput,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { Colors } from '../constants/Colors';
import { Spacing, FontSize, FontWeight, BorderRadius } from '../constants/Dimensions';
import { validateExpense, formatAmount, sanitizeAmountInput } from '../utils/validation';
import EditableDatePicker from '../components/EditableDatePicker';
import SeasonSelector from '../components/SeasonSelector';
import Logger, { LogCategory } from '../utils/Logger';
import Analytics, { AnalyticsEvent } from '../utils/Analytics';
import { DataManager } from '../services/DataManager';

export default function ExpenseEditScreen({ navigation, route }) {
  const { expenseId } = route.params;
  const [expense, setExpense] = useState(null);
  const [editedExpense, setEditedExpense] = useState({
    categoryId: '',
    seasonId: '',      // SEASON INTEGRATION: Add seasonId field
    fieldId: '',       // TWO-MODE SYSTEM: Add fieldId field
    cropId: '',        // TWO-MODE SYSTEM: Add cropId field
    amount: '',
    date: '',
    description: '',
  });
  const [categories, setCategories] = useState([]);
  const [seasons, setSeasons] = useState([]);      // SEASON INTEGRATION: Add seasons state
  const [fields, setFields] = useState([]);        // TWO-MODE SYSTEM: Add fields state
  const [crops, setCrops] = useState([]);          // TWO-MODE SYSTEM: Add crops state
  const [trackingMode, setTrackingMode] = useState('simple'); // TWO-MODE SYSTEM: Add tracking mode state
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [errors, setErrors] = useState({});
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    loadExpenseData();
    loadCategories();
    loadSeasons();  // SEASON INTEGRATION: Load seasons
    loadTrackingMode(); // TWO-MODE SYSTEM: Load tracking mode
  }, [expenseId]);

  useEffect(() => {
    // Check if there are any changes
    if (expense) {
      const changes =
        editedExpense.categoryId !== expense.categoryId ||
        editedExpense.seasonId !== (expense.seasonId || '') ||  // SEASON INTEGRATION: Check season changes
        editedExpense.fieldId !== (expense.fieldId || '') ||    // TWO-MODE SYSTEM: Check field changes
        editedExpense.cropId !== (expense.cropId || '') ||      // TWO-MODE SYSTEM: Check crop changes
        editedExpense.amount !== expense.amount.toString() ||
        editedExpense.date !== expense.date ||
        editedExpense.description !== (expense.description || '');

      setHasChanges(changes);
    }
  }, [editedExpense, expense]);

  const loadExpenseData = async () => {
    try {
      setLoading(true);

      // Load expense from database
      await DataManager.initialize();
      const expenses = await DataManager.getExpenses();
      const foundExpense = expenses.find(exp => (exp._id || exp.id) === expenseId);

      if (!foundExpense) {
        Alert.alert('Hata', 'Gider bulunamadı.');
        navigation.goBack();
        return;
      }

      setExpense(foundExpense);
      setEditedExpense({
        categoryId: foundExpense.categoryId,
        seasonId: foundExpense.seasonId || '',  // SEASON INTEGRATION: Load season ID
        fieldId: foundExpense.fieldId || '',    // TWO-MODE SYSTEM: Load field ID
        cropId: foundExpense.cropId || '',      // TWO-MODE SYSTEM: Load crop ID
        amount: foundExpense.amount.toString(),
        date: foundExpense.date,
        description: foundExpense.description || '',
      });

      Analytics.trackEvent(AnalyticsEvent.EXPENSE_EDIT_STARTED, {
        expenseId,
        originalAmount: foundExpense.amount,
        originalCategory: foundExpense.categoryId,
      });

      Logger.info(LogCategory.USER_ACTION, 'Expense edit started', { expenseId });
    } catch (error) {
      Logger.error(LogCategory.DATABASE, 'Failed to load expense', error);
      Alert.alert('Hata', 'Gider bilgileri yüklenirken bir hata oluştu.');
      navigation.goBack();
    } finally {
      setLoading(false);
    }
  };

  const loadCategories = async () => {
    try {
      await DataManager.initialize();
      const loadedCategories = await DataManager.getCategories();
      setCategories(loadedCategories);
    } catch (error) {
      Logger.error(LogCategory.DATABASE, 'Failed to load categories', error);
      Alert.alert('Hata', 'Kategoriler yüklenirken bir hata oluştu.');
    }
  };

  // SEASON INTEGRATION: Load seasons function
  const loadSeasons = async () => {
    try {
      await DataManager.initialize();
      const loadedSeasons = await DataManager.getSeasons();
      setSeasons(loadedSeasons);
    } catch (error) {
      Logger.error(LogCategory.DATABASE, 'Failed to load seasons', error);
      console.error('Error loading seasons:', error);
    }
  };

  // TWO-MODE SYSTEM: Load tracking mode function
  const loadTrackingMode = async () => {
    try {
      // GUEST MODE: Use local tracking mode only
      if (DataManager.isGuestMode()) {
        const mode = DataManager.getTrackingMode();
        setTrackingMode(mode);
        console.log('Guest mode: Tracking mode loaded locally:', mode);
        // If detailed mode, load fields and crops
        if (mode === 'detailed') {
          await loadFields();
          await loadCrops();
        }
        return;
      }
      // AUTHENTICATED MODE: Try backend first, fallback to local
      if (DataManager.shouldUseBackend()) {
        try {
          const response = await DataManager.apiClient.getTrackingMode();
          if (response && response.success) {
            setTrackingMode(response.data.mode);
            // If detailed mode, load fields and crops
            if (response.data.mode === 'detailed') {
              await loadFields();
              await loadCrops();
            }
            return;
          }
        } catch (error) {
          console.warn('Backend tracking mode failed, using local fallback:', error);
        }
      }
      // Fallback to local mode
      const mode = DataManager.getTrackingMode();
      setTrackingMode(mode);
      console.log('Authenticated mode (local fallback): Tracking mode loaded:', mode);
      // If detailed mode, load fields and crops
      if (mode === 'detailed') {
        await loadFields();
        await loadCrops();
      }
    } catch (error) {
      console.error('Error loading tracking mode:', error);
      // Default to simple mode on error
      setTrackingMode('simple');
    }
  };

  // TWO-MODE SYSTEM: Load fields function
  const loadFields = async () => {
    try {
      await DataManager.initialize();
      const loadedFields = await DataManager.getFields();
      setFields(loadedFields);
    } catch (error) {
      Logger.error(LogCategory.DATABASE, 'Failed to load fields', error);
      console.error('Error loading fields:', error);
    }
  };

  // TWO-MODE SYSTEM: Load crops function
  const loadCrops = async () => {
    try {
      await DataManager.initialize();
      const loadedCrops = await DataManager.getCrops();
      setCrops(loadedCrops);
    } catch (error) {
      Logger.error(LogCategory.DATABASE, 'Failed to load crops', error);
      console.error('Error loading crops:', error);
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      setErrors({});

      // Validate changes
      const validation = validateExpense(editedExpense);
      if (!validation.isValid) {
        setErrors(validation.errors);
        return;
      }

      if (!hasChanges) {
        Alert.alert('Bilgi', 'Herhangi bir değişiklik yapılmadı.');
        return;
      }

      // Save changes to database
      const updatedExpense = {
        ...expense,
        categoryId: editedExpense.categoryId,
        seasonId: editedExpense.seasonId,  // SEASON INTEGRATION: Include season ID
        fieldId: editedExpense.fieldId || null,    // TWO-MODE SYSTEM: Include field ID
        cropId: editedExpense.cropId || null,      // TWO-MODE SYSTEM: Include crop ID
        amount: parseFloat(editedExpense.amount),
        date: editedExpense.date,
        description: editedExpense.description,
        updatedAt: new Date().toISOString(),
      };

      await DataManager.updateExpense(expense._id || expense.id, updatedExpense);

      const selectedCategory = categories.find(cat => cat.id === editedExpense.categoryId);
      const changes = getChanges();

      Analytics.trackEvent(AnalyticsEvent.EXPENSE_EDITED, {
        expenseId,
        changes: Object.keys(changes),
        changeCount: Object.keys(changes).length,
        newAmount: validation.sanitizedData.amount,
        newCategory: selectedCategory?.name,
      });

      Logger.info(LogCategory.USER_ACTION, 'Expense updated successfully', {
        expenseId,
        changes,
      });

      Alert.alert(
        'Başarılı',
        'Gider başarıyla güncellendi.',
        [
          {
            text: 'Tamam',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } catch (error) {
      Logger.error(LogCategory.USER_ACTION, 'Failed to update expense', error);
      Alert.alert('Hata', 'Gider güncellenirken bir hata oluştu.');
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    if (hasChanges) {
      Alert.alert(
        'Değişiklikleri Kaydet',
        'Yaptığınız değişiklikler kaydedilmedi. Çıkmak istediğinizden emin misiniz?',
        [
          { text: 'Kalmaya Devam Et', style: 'cancel' },
          {
            text: 'Çık',
            style: 'destructive',
            onPress: () => {
              Analytics.trackEvent(AnalyticsEvent.EXPENSE_EDIT_CANCELLED, {
                expenseId,
                hadChanges: hasChanges,
              });
              navigation.goBack();
            }
          },
        ]
      );
    } else {
      navigation.goBack();
    }
  };

  const handleDelete = () => {
    Alert.alert(
      'Gideri Sil',
      'Bu gideri silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: confirmDelete
        },
      ]
    );
  };

  const confirmDelete = async () => {
    try {
      setSaving(true);

      // Delete expense from database
      await DataManager.deleteExpense(expense._id || expense.id);

      const selectedCategory = categories.find(cat => cat.id === expense.categoryId);

      Analytics.trackEvent(AnalyticsEvent.EXPENSE_DELETED, {
        expenseId,
        amount: expense.amount,
        categoryId: expense.categoryId,
        categoryName: selectedCategory?.name,
      });

      Logger.info(LogCategory.USER_ACTION, 'Expense deleted successfully', {
        expenseId,
        amount: expense.amount,
      });

      Alert.alert(
        'Silindi',
        'Gider başarıyla silindi.',
        [
          {
            text: 'Tamam',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } catch (error) {
      Logger.error(LogCategory.USER_ACTION, 'Failed to delete expense', error);
      Alert.alert('Hata', 'Gider silinirken bir hata oluştu.');
    } finally {
      setSaving(false);
    }
  };

  const getChanges = () => {
    const changes = {};
    if (editedExpense.categoryId !== expense.categoryId) {
      changes.categoryId = { from: expense.categoryId, to: editedExpense.categoryId };
    }
    // SEASON INTEGRATION: Track season changes
    if (editedExpense.seasonId !== (expense.seasonId || '')) {
      changes.seasonId = { from: expense.seasonId || '', to: editedExpense.seasonId };
    }
    // TWO-MODE SYSTEM: Track field changes
    if (editedExpense.fieldId !== (expense.fieldId || '')) {
      changes.fieldId = { from: expense.fieldId || '', to: editedExpense.fieldId };
    }
    // TWO-MODE SYSTEM: Track crop changes
    if (editedExpense.cropId !== (expense.cropId || '')) {
      changes.cropId = { from: expense.cropId || '', to: editedExpense.cropId };
    }
    if (editedExpense.amount !== expense.amount.toString()) {
      changes.amount = { from: expense.amount, to: parseFloat(editedExpense.amount) };
    }
    if (editedExpense.date !== expense.date) {
      changes.date = { from: expense.date, to: editedExpense.date };
    }
    if (editedExpense.description !== (expense.description || '')) {
      changes.description = { from: expense.description || '', to: editedExpense.description };
    }
    return changes;
  };

  const updateField = (field, value) => {
    setEditedExpense(prev => ({ ...prev, [field]: value }));
    
    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  const renderCategorySelector = () => {
    const selectedCategory = categories.find(cat => cat.id === editedExpense.categoryId);
    
    return (
      <View style={styles.fieldContainer}>
        <Text style={styles.fieldLabel}>Kategori</Text>
        {errors.categoryId && (
          <Text style={styles.errorText}>{errors.categoryId}</Text>
        )}
        
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.categoriesScroll}>
          {categories.map((category) => (
            <TouchableOpacity
              key={category.id}
              style={[
                styles.categoryOption,
                { borderColor: category.color },
                editedExpense.categoryId === category.id && [
                  styles.categoryOptionSelected,
                  { backgroundColor: category.color }
                ],
              ]}
              onPress={() => updateField('categoryId', category.id)}
            >
              <Text style={styles.categoryEmoji}>{category.emoji}</Text>
              <Text
                style={[
                  styles.categoryOptionName,
                  editedExpense.categoryId === category.id && styles.categoryOptionNameSelected,
                ]}
              >
                {category.name}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
    );
  };

  // SEASON INTEGRATION: Season selector component
  const renderSeasonSelector = () => {
    const selectedSeason = seasons.find(season =>
      (season.id || season._id) === editedExpense.seasonId
    );

    return (
      <View style={styles.fieldContainer}>
        <Text style={styles.fieldLabel}>Sezon</Text>
        {errors.seasonId && (
          <Text style={styles.errorText}>{errors.seasonId}</Text>
        )}

        <SeasonSelector
          seasons={seasons}
          selectedSeason={selectedSeason}
          onSelect={(season) => {
            updateField('seasonId', season.id || season._id);
          }}
          placeholder="Sezon seçiniz"
          showActiveBadge={true}
          error={errors.seasonId}
          style={styles.seasonSelector}
        />
      </View>
    );
  };

  // TWO-MODE SYSTEM: Determine if field and crop info should be shown
  const shouldShowFieldAndCropInfo = () => {
    // If user is currently in simple mode, never show field/crop info
    if (trackingMode === 'simple') {
      return false;
    }

    // If user is currently in detailed mode, always show field/crop info
    // This covers both cases:
    // 1. Expense was created in detailed mode (has fieldId/cropId)
    // 2. Expense was created in simple mode but user is now in detailed mode (show fallback values)
    if (trackingMode === 'detailed') {
      return true;
    }

    return false;
  };

  // TWO-MODE SYSTEM: Determine if expense was created in detailed mode
  const wasExpenseCreatedInDetailedMode = () => {
    return !!(expense?.fieldId || expense?.cropId);
  };

  // TWO-MODE SYSTEM: Field display component
  const renderFieldDisplay = () => {
    // Check if field and crop info should be shown based on current tracking mode
    if (!shouldShowFieldAndCropInfo()) {
      return null;
    }

    const selectedField = fields.find(field => field._id === editedExpense.fieldId);
    const wasCreatedInDetailedMode = wasExpenseCreatedInDetailedMode();

    return (
      <View style={styles.fieldContainer}>
        <Text style={styles.fieldLabel}>Tarla</Text>
        <View style={styles.fieldDisplayContainer}>
          <View style={styles.fieldDisplayContent}>
            <Text style={styles.fieldDisplayEmoji}>🏞️</Text>
            <View style={styles.fieldDisplayTextContainer}>
              <Text style={styles.fieldDisplayName}>
                {selectedField?.name || (wasCreatedInDetailedMode ? 'Bilinmiyor' : 'Seçilmedi')}
              </Text>
              {selectedField?.formattedSize && (
                <Text style={styles.fieldDisplaySize}>
                  ({selectedField.formattedSize})
                </Text>
              )}
              {selectedField?.isDefault && (
                <View style={styles.fieldDisplayBadge}>
                  <Text style={styles.fieldDisplayBadgeText}>Varsayılan</Text>
                </View>
              )}
            </View>
          </View>
        </View>
      </View>
    );
  };

  // TWO-MODE SYSTEM: Crop display component
  const renderCropDisplay = () => {
    // Check if field and crop info should be shown based on current tracking mode
    if (!shouldShowFieldAndCropInfo()) {
      return null;
    }

    const selectedCrop = crops.find(crop => crop._id === editedExpense.cropId);
    const wasCreatedInDetailedMode = wasExpenseCreatedInDetailedMode();

    return (
      <View style={styles.fieldContainer}>
        <Text style={styles.fieldLabel}>Ürün</Text>
        <View style={styles.cropDisplayContainer}>
          <View style={styles.cropDisplayContent}>
            <Text style={styles.cropDisplayEmoji}>
              {selectedCrop?.emoji || '🌱'}
            </Text>
            <View style={styles.cropDisplayTextContainer}>
              <Text style={styles.cropDisplayName}>
                {selectedCrop?.nameTr || (wasCreatedInDetailedMode ? 'Bilinmiyor' : 'Seçilmedi')}
              </Text>
              {selectedCrop?.isDefault && (
                <View style={styles.cropDisplayBadge}>
                  <Text style={styles.cropDisplayBadgeText}>Sistem</Text>
                </View>
              )}
            </View>
          </View>
        </View>
      </View>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor={Colors.background} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.primary} />
          <Text style={styles.loadingText}>Gider bilgileri yükleniyor...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!expense) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor={Colors.background} />
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle-outline" size={64} color={Colors.error} />
          <Text style={styles.errorTitle}>Gider Bulunamadı</Text>
          <Text style={styles.errorDescription}>
            Düzenlemek istediğiniz gider bulunamadı.
          </Text>
          <TouchableOpacity style={styles.errorButton} onPress={() => navigation.goBack()}>
            <Text style={styles.errorButtonText}>Geri Dön</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.background} />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.headerButton} onPress={handleCancel}>
          <Ionicons name="close" size={24} color={Colors.text} />
        </TouchableOpacity>
        
        <Text style={styles.headerTitle}>Gideri Düzenle</Text>
        
        <TouchableOpacity
          style={[
            styles.headerButton,
            styles.saveButton,
            (!hasChanges || saving) && styles.saveButtonDisabled,
          ]}
          onPress={handleSave}
          disabled={!hasChanges || saving}
        >
          {saving ? (
            <ActivityIndicator size="small" color={Colors.surface} />
          ) : (
            <Text style={styles.saveButtonText}>Kaydet</Text>
          )}
        </TouchableOpacity>
      </View>

      {/* Content */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Category Selection */}
        {renderCategorySelector()}

        {/* SEASON INTEGRATION: Season Selection */}
        {renderSeasonSelector()}

        {/* TWO-MODE SYSTEM: Field Display */}
        {renderFieldDisplay()}

        {/* TWO-MODE SYSTEM: Crop Display */}
        {renderCropDisplay()}

        {/* Amount Input */}
        <View style={styles.fieldContainer}>
          <Text style={styles.fieldLabel}>Tutar</Text>
          {errors.amount && (
            <Text style={styles.errorText}>{errors.amount}</Text>
          )}
          
          <View style={styles.amountInputContainer}>
            <Text style={styles.currencySymbol}>₺</Text>
            <TextInput
              style={styles.amountInput}
              value={editedExpense.amount}
              onChangeText={(text) => updateField('amount', sanitizeAmountInput(text))}
              placeholder="0,00"
              keyboardType="numeric"
            />
          </View>
          
          {editedExpense.amount && (
            <Text style={styles.formattedAmount}>
              {formatAmount(editedExpense.amount)}
            </Text>
          )}
        </View>

        {/* Date Selection */}
        <View style={styles.fieldContainer}>
          <Text style={styles.fieldLabel}>Tarih</Text>
          <EditableDatePicker
            date={editedExpense.date}
            onDateChange={(date) => updateField('date', date)}
            error={errors.date}
          />
        </View>

        {/* Description */}
        <View style={styles.fieldContainer}>
          <Text style={styles.fieldLabel}>Açıklama (İsteğe Bağlı)</Text>
          <TextInput
            style={styles.descriptionInput}
            value={editedExpense.description}
            onChangeText={(text) => updateField('description', text)}
            placeholder="Gider hakkında detay ekleyin..."
            multiline
            numberOfLines={3}
            maxLength={500}
          />
          <Text style={styles.characterCount}>
            {editedExpense.description.length} / 500
          </Text>
        </View>

        {/* Change Summary */}
        {hasChanges && (
          <View style={styles.changesContainer}>
            <Text style={styles.changesTitle}>Yapılan Değişiklikler:</Text>
            {Object.entries(getChanges()).map(([field, change]) => (
              <View key={field} style={styles.changeItem}>
                <Text style={styles.changeField}>
                  {field === 'categoryId' ? 'Kategori' :
                   field === 'seasonId' ? 'Sezon' :  // SEASON INTEGRATION: Add season field label
                   field === 'fieldId' ? 'Tarla' :   // TWO-MODE SYSTEM: Add field label
                   field === 'cropId' ? 'Ürün' :     // TWO-MODE SYSTEM: Add crop label
                   field === 'amount' ? 'Tutar' :
                   field === 'date' ? 'Tarih' : 'Açıklama'}:
                </Text>
                <Text style={styles.changeValue}>
                  {field === 'categoryId'
                    ? `${categories.find(c => c.id === change.from)?.name} → ${categories.find(c => c.id === change.to)?.name}`
                    : field === 'seasonId'  // SEASON INTEGRATION: Add season change display
                    ? `${seasons.find(s => (s.id || s._id) === change.from)?.name || 'Seçilmemiş'} → ${seasons.find(s => (s.id || s._id) === change.to)?.name || 'Seçilmemiş'}`
                    : field === 'fieldId'   // TWO-MODE SYSTEM: Add field change display
                    ? `${fields.find(f => f._id === change.from)?.name || 'Bilinmiyor'} → ${fields.find(f => f._id === change.to)?.name || 'Bilinmiyor'}`
                    : field === 'cropId'    // TWO-MODE SYSTEM: Add crop change display
                    ? `${crops.find(c => c._id === change.from)?.nameTr || 'Bilinmiyor'} → ${crops.find(c => c._id === change.to)?.nameTr || 'Bilinmiyor'}`
                    : field === 'amount'
                    ? `${formatAmount(change.from)} → ${formatAmount(change.to)}`
                    : `${change.from || '(boş)'} → ${change.to || '(boş)'}`
                  }
                </Text>
              </View>
            ))}
          </View>
        )}

        {/* Original Expense Info */}
        <View style={styles.originalInfoContainer}>
          <Text style={styles.originalInfoTitle}>Orijinal Bilgiler:</Text>
          <Text style={styles.originalInfoText}>
            Oluşturulma: {new Date(expense.createdAt).toLocaleDateString('tr-TR')}
          </Text>
          {expense.updatedAt !== expense.createdAt && (
            <Text style={styles.originalInfoText}>
              Son Güncelleme: {new Date(expense.updatedAt).toLocaleDateString('tr-TR')}
            </Text>
          )}
        </View>

        {/* Delete Button */}
        <View style={styles.dangerZone}>
          <Text style={styles.dangerZoneTitle}>Tehlikeli İşlemler</Text>
          <TouchableOpacity
            style={[styles.deleteButton, saving && styles.deleteButtonDisabled]}
            onPress={handleDelete}
            disabled={saving}
          >
            <Ionicons name="trash-outline" size={20} color={Colors.error} />
            <Text style={styles.deleteButtonText}>Gideri Sil</Text>
          </TouchableOpacity>
          <Text style={styles.deleteWarning}>
            Bu işlem geri alınamaz. Gider kalıcı olarak silinecektir.
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: FontSize.md,
    color: Colors.textSecondary,
    marginTop: Spacing.md,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: Spacing.xl,
  },
  errorTitle: {
    fontSize: FontSize.xl,
    fontWeight: FontWeight.bold,
    color: Colors.text,
    marginTop: Spacing.lg,
    marginBottom: Spacing.md,
  },
  errorDescription: {
    fontSize: FontSize.md,
    color: Colors.textSecondary,
    textAlign: 'center',
    marginBottom: Spacing.xl,
  },
  errorButton: {
    backgroundColor: Colors.primary,
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.xl,
    borderRadius: BorderRadius.lg,
  },
  errorButtonText: {
    fontSize: FontSize.md,
    color: Colors.surface,
    fontWeight: FontWeight.medium,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  headerButton: {
    padding: Spacing.sm,
    minWidth: 60,
  },
  headerTitle: {
    fontSize: FontSize.lg,
    fontWeight: FontWeight.bold,
    color: Colors.text,
  },
  saveButton: {
    backgroundColor: Colors.primary,
    borderRadius: BorderRadius.md,
    alignItems: 'center',
    justifyContent: 'center',
  },
  saveButtonDisabled: {
    backgroundColor: Colors.border,
  },
  saveButtonText: {
    fontSize: FontSize.sm,
    color: Colors.surface,
    fontWeight: FontWeight.medium,
  },
  content: {
    flex: 1,
    paddingHorizontal: Spacing.lg,
  },
  fieldContainer: {
    marginVertical: Spacing.lg,
  },
  fieldLabel: {
    fontSize: FontSize.md,
    fontWeight: FontWeight.medium,
    color: Colors.text,
    marginBottom: Spacing.sm,
  },
  errorText: {
    fontSize: FontSize.sm,
    color: Colors.error,
    marginBottom: Spacing.sm,
  },
  categoriesScroll: {
    marginTop: Spacing.sm,
  },
  categoryOption: {
    alignItems: 'center',
    padding: Spacing.md,
    borderRadius: BorderRadius.md,
    borderWidth: 1,
    marginRight: Spacing.sm,
    minWidth: 80,
    backgroundColor: Colors.surface,
  },
  categoryOptionSelected: {
    borderWidth: 2,
  },
  categoryEmoji: {
    fontSize: 24,
    marginBottom: Spacing.xs,
  },
  categoryOptionName: {
    fontSize: FontSize.sm,
    color: Colors.text,
    textAlign: 'center',
  },
  categoryOptionNameSelected: {
    color: Colors.surface,
    fontWeight: FontWeight.bold,
  },
  amountInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.lg,
    borderWidth: 1,
    borderColor: Colors.border,
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
  },
  currencySymbol: {
    fontSize: FontSize.xl,
    fontWeight: FontWeight.bold,
    color: Colors.primary,
    marginRight: Spacing.md,
  },
  amountInput: {
    flex: 1,
    fontSize: FontSize.xl,
    fontWeight: FontWeight.bold,
    color: Colors.text,
  },
  formattedAmount: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
    marginTop: Spacing.xs,
    textAlign: 'center',
  },
  descriptionInput: {
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    fontSize: FontSize.md,
    color: Colors.text,
    backgroundColor: Colors.surface,
    textAlignVertical: 'top',
    minHeight: 80,
  },
  characterCount: {
    fontSize: FontSize.xs,
    color: Colors.textSecondary,
    textAlign: 'right',
    marginTop: Spacing.xs,
  },
  changesContainer: {
    backgroundColor: Colors.primaryLight,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    marginVertical: Spacing.lg,
    borderLeftWidth: 4,
    borderLeftColor: Colors.primary,
  },
  changesTitle: {
    fontSize: FontSize.md,
    fontWeight: FontWeight.bold,
    color: Colors.text,
    marginBottom: Spacing.md,
  },
  changeItem: {
    marginBottom: Spacing.sm,
  },
  changeField: {
    fontSize: FontSize.sm,
    fontWeight: FontWeight.medium,
    color: Colors.text,
  },
  changeValue: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
    marginTop: Spacing.xs,
  },
  originalInfoContainer: {
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    marginVertical: Spacing.lg,
  },
  originalInfoTitle: {
    fontSize: FontSize.sm,
    fontWeight: FontWeight.medium,
    color: Colors.text,
    marginBottom: Spacing.sm,
  },
  originalInfoText: {
    fontSize: FontSize.xs,
    color: Colors.textSecondary,
    marginBottom: Spacing.xs,
  },
  dangerZone: {
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    marginVertical: Spacing.lg,
    borderWidth: 1,
    borderColor: Colors.error,
  },
  dangerZoneTitle: {
    fontSize: FontSize.md,
    fontWeight: FontWeight.bold,
    color: Colors.error,
    marginBottom: Spacing.md,
  },
  deleteButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.surface,
    borderWidth: 1,
    borderColor: Colors.error,
    borderRadius: BorderRadius.lg,
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.lg,
    marginBottom: Spacing.sm,
  },
  deleteButtonDisabled: {
    opacity: 0.5,
  },
  deleteButtonText: {
    fontSize: FontSize.md,
    color: Colors.error,
    fontWeight: FontWeight.medium,
    marginLeft: Spacing.sm,
  },
  deleteWarning: {
    fontSize: FontSize.xs,
    color: Colors.textSecondary,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  // SEASON INTEGRATION: Season selector styles
  seasonSelector: {
    marginTop: Spacing.sm,
  },
  // TWO-MODE SYSTEM: Field display styles
  fieldDisplayContainer: {
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.lg,
    borderWidth: 1,
    borderColor: Colors.border,
    padding: Spacing.lg,
  },
  fieldDisplayContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  fieldDisplayEmoji: {
    fontSize: 24,
    marginRight: Spacing.md,
  },
  fieldDisplayTextContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  fieldDisplayName: {
    fontSize: FontSize.md,
    fontWeight: FontWeight.medium,
    color: Colors.text,
    marginRight: Spacing.sm,
  },
  fieldDisplaySize: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
    marginRight: Spacing.sm,
  },
  fieldDisplayBadge: {
    backgroundColor: Colors.primary,
    borderRadius: BorderRadius.sm,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
  },
  fieldDisplayBadgeText: {
    fontSize: FontSize.xs,
    color: Colors.surface,
    fontWeight: FontWeight.medium,
  },
  // TWO-MODE SYSTEM: Crop display styles
  cropDisplayContainer: {
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.lg,
    borderWidth: 1,
    borderColor: Colors.border,
    padding: Spacing.lg,
  },
  cropDisplayContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  cropDisplayEmoji: {
    fontSize: 24,
    marginRight: Spacing.md,
  },
  cropDisplayTextContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  cropDisplayName: {
    fontSize: FontSize.md,
    fontWeight: FontWeight.medium,
    color: Colors.text,
    marginRight: Spacing.sm,
  },
  cropDisplayBadge: {
    backgroundColor: Colors.success,
    borderRadius: BorderRadius.sm,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
  },
  cropDisplayBadgeText: {
    fontSize: FontSize.xs,
    color: Colors.surface,
    fontWeight: FontWeight.medium,
  },
});
