import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  RefreshControl,
  ActivityIndicator,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { Ionicons } from '@expo/vector-icons';
import { useFocusEffect } from '@react-navigation/native';

import { DataManager } from '../services/DataManager';
import SeasonCard from '../components/SeasonCard';
import { Colors } from '../constants/Colors';
import { Spacing, FontSize, FontWeight } from '../constants/Dimensions';

export default function SeasonManagementScreen({ navigation }) {
  const [seasons, setSeasons] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [activeSeason, setActiveSeason] = useState(null);

  useEffect(() => {
    loadSeasons();

    // Listen for data changes
    const removeListener = DataManager.addDataChangeListener(() => {
      console.log('SeasonManagementScreen: Data change detected, reloading...');
      loadSeasons();
    });

    return removeListener;
  }, []);

  // Reload data when screen comes into focus
  useFocusEffect(
    React.useCallback(() => {
      loadSeasons();
    }, [])
  );

  const loadSeasons = async () => {
    try {
      setLoading(true);

      // Initialize DataManager
      await DataManager.initialize();

      // Load seasons and active season
      const [allSeasons, currentActiveSeason] = await Promise.all([
        DataManager.getSeasons(),
        DataManager.getActiveSeason()
      ]);

      setSeasons(allSeasons || []);
      setActiveSeason(currentActiveSeason);
      
      console.log('SeasonManagementScreen - Seasons loaded:', allSeasons?.length || 0);
    } catch (error) {
      console.error('Error loading seasons:', error);
      Alert.alert('Hata', 'Sezonlar yüklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadSeasons();
    setRefreshing(false);
  };

  const handleSeasonPress = (season) => {
    navigation.navigate('SeasonDetail', { 
      seasonId: season.id || season._id,
      seasonName: season.name 
    });
  };

  const handleAddSeason = () => {
    navigation.navigate('AddSeason');
  };

  const handleEditSeason = (season) => {
    navigation.navigate('EditSeason', { 
      seasonId: season.id || season._id,
      season: season 
    });
  };

  const handleActivateSeason = async (season) => {
    try {
      if (season.isActive) {
        Alert.alert('Bilgi', 'Bu sezon zaten aktif.');
        return;
      }

      Alert.alert(
        'Sezon Aktivasyonu',
        `"${season.name}" sezonunu aktif yapmak istediğinizden emin misiniz?`,
        [
          { text: 'İptal', style: 'cancel' },
          {
            text: 'Aktif Yap',
            onPress: async () => {
              try {
                await DataManager.setActiveSeason(season.id || season._id);
                Alert.alert('Başarılı', `"${season.name}" sezonu aktif yapıldı.`);
                loadSeasons(); // Refresh the list
              } catch (error) {
                console.error('Error activating season:', error);
                Alert.alert('Hata', 'Sezon aktif yapılırken bir hata oluştu.');
              }
            }
          }
        ]
      );
    } catch (error) {
      console.error('Error in handleActivateSeason:', error);
    }
  };

  const handleDeleteSeason = async (season) => {
    try {
      Alert.alert(
        'Sezon Silme',
        `"${season.name}" sezonunu silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.`,
        [
          { text: 'İptal', style: 'cancel' },
          {
            text: 'Sil',
            style: 'destructive',
            onPress: async () => {
              try {
                await DataManager.deleteSeason(season.id || season._id);
                Alert.alert('Başarılı', 'Sezon başarıyla silindi.');
                loadSeasons(); // Refresh the list
              } catch (error) {
                console.error('Error deleting season:', error);
                Alert.alert('Hata', error.message || 'Sezon silinirken bir hata oluştu.');
              }
            }
          }
        ]
      );
    } catch (error) {
      console.error('Error in handleDeleteSeason:', error);
    }
  };

  const renderSeasonItem = ({ item: season }) => (
    <View style={styles.seasonItemContainer}>
      <SeasonCard
        season={season}
        onPress={handleSeasonPress}
        showDetails={true}
        compact={false}
        style={styles.seasonCard}
      />
      
      {/* Action Buttons */}
      <View style={styles.actionButtons}>
        {!season.isActive && (
          <TouchableOpacity
            style={[styles.actionButton, styles.activateButton]}
            onPress={() => handleActivateSeason(season)}
          >
            <Ionicons name="checkmark-circle" size={20} color={Colors.primary} />
            <Text style={styles.activateButtonText}>Aktif Yap</Text>
          </TouchableOpacity>
        )}
        
        <TouchableOpacity
          style={[styles.actionButton, styles.editButton]}
          onPress={() => handleEditSeason(season)}
        >
          <Ionicons name="create" size={20} color={Colors.secondary} />
          <Text style={styles.editButtonText}>Düzenle</Text>
        </TouchableOpacity>
        
        {seasons.length > 1 && (
          <TouchableOpacity
            style={[styles.actionButton, styles.deleteButton]}
            onPress={() => handleDeleteSeason(season)}
          >
            <Ionicons name="trash" size={20} color={Colors.error} />
            <Text style={styles.deleteButtonText}>Sil</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="leaf-outline" size={64} color={Colors.textSecondary} />
      <Text style={styles.emptyStateTitle}>Henüz Sezon Yok</Text>
      <Text style={styles.emptyStateText}>
        İlk sezonunuzu oluşturarak tarımsal giderlerinizi organize etmeye başlayın.
      </Text>
      <TouchableOpacity style={styles.addFirstSeasonButton} onPress={handleAddSeason}>
        <Text style={styles.addFirstSeasonButtonText}>İlk Sezonumu Oluştur</Text>
      </TouchableOpacity>
    </View>
  );

  if (loading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <StatusBar style="light" backgroundColor={Colors.primary} />
        <ActivityIndicator size="large" color={Colors.primary} />
        <Text style={styles.loadingText}>Sezonlar yükleniyor...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar style="light" backgroundColor={Colors.primary} />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={Colors.surface} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Sezon Yönetimi</Text>
        <TouchableOpacity style={styles.addButton} onPress={handleAddSeason}>
          <Ionicons name="add" size={24} color={Colors.surface} />
        </TouchableOpacity>
      </View>

      {/* Season List */}
      <FlatList
        data={seasons}
        renderItem={renderSeasonItem}
        keyExtractor={(item) => item.id || item._id}
        contentContainerStyle={[
          styles.listContainer,
          seasons.length === 0 && styles.emptyListContainer
        ]}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[Colors.primary]}
            tintColor={Colors.primary}
          />
        }
        ListEmptyComponent={renderEmptyState}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    backgroundColor: Colors.primary,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Spacing.md,
    paddingTop: 50, // Status bar height
    paddingBottom: Spacing.md,
    elevation: 4,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  backButton: {
    padding: Spacing.xs,
  },
  headerTitle: {
    fontSize: FontSize.xl,
    fontWeight: FontWeight.bold,
    color: Colors.surface,
    flex: 1,
    textAlign: 'center',
  },
  addButton: {
    padding: Spacing.xs,
  },
  loadingText: {
    fontSize: FontSize.md,
    color: Colors.textSecondary,
    marginTop: Spacing.md,
  },
  listContainer: {
    padding: Spacing.md,
  },
  emptyListContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  seasonItemContainer: {
    marginBottom: Spacing.lg,
  },
  seasonCard: {
    marginBottom: Spacing.sm,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    paddingHorizontal: Spacing.sm,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: 6,
    marginLeft: Spacing.xs,
  },
  activateButton: {
    backgroundColor: Colors.primaryLight,
  },
  activateButtonText: {
    fontSize: FontSize.sm,
    color: Colors.primary,
    fontWeight: FontWeight.medium,
    marginLeft: Spacing.xs,
  },
  editButton: {
    backgroundColor: Colors.secondaryLight,
  },
  editButtonText: {
    fontSize: FontSize.sm,
    color: Colors.secondary,
    fontWeight: FontWeight.medium,
    marginLeft: Spacing.xs,
  },
  deleteButton: {
    backgroundColor: Colors.errorLight,
  },
  deleteButtonText: {
    fontSize: FontSize.sm,
    color: Colors.error,
    fontWeight: FontWeight.medium,
    marginLeft: Spacing.xs,
  },
  emptyState: {
    alignItems: 'center',
    paddingHorizontal: Spacing.xl,
  },
  emptyStateTitle: {
    fontSize: FontSize.xl,
    fontWeight: FontWeight.bold,
    color: Colors.text,
    marginTop: Spacing.lg,
    marginBottom: Spacing.md,
  },
  emptyStateText: {
    fontSize: FontSize.md,
    color: Colors.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: Spacing.xl,
  },
  addFirstSeasonButton: {
    backgroundColor: Colors.primary,
    borderRadius: 8,
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.xl,
  },
  addFirstSeasonButtonText: {
    color: Colors.surface,
    fontSize: FontSize.md,
    fontWeight: FontWeight.semibold,
  },
});
