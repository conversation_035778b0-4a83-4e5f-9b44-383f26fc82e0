import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Alert,
  BackHandler,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';

import WelcomeScreen from './WelcomeScreen';
import { Colors } from '../constants/Colors';
import { Spacing, FontSize, FontWeight, BorderRadius } from '../constants/Dimensions';
import Logger, { LogCategory } from '../utils/Logger';
import Analytics, { AnalyticsEvent } from '../utils/Analytics';

// Onboarding steps
const OnboardingStep = {
  WELCOME: 'welcome',
  TUTORIAL: 'tutorial',
  CATEGORY_SETUP: 'category_setup',
  FIRST_EXPENSE: 'first_expense',
  COMPLETE: 'complete',
};

export default function OnboardingScreen({ navigation }) {
  const [currentStep, setCurrentStep] = useState(OnboardingStep.WELCOME);
  const [onboardingStartTime] = useState(Date.now());
  const [canSkip, setCanSkip] = useState(true);

  useEffect(() => {
    // Track onboarding start
    Analytics.trackEvent(AnalyticsEvent.ONBOARDING_STARTED);
    Logger.info(LogCategory.USER_ACTION, 'Onboarding started');

    // Handle back button
    const backHandler = BackHandler.addEventListener('hardwareBackPress', handleBackPress);
    
    return () => backHandler.remove();
  }, []);

  const handleBackPress = () => {
    if (currentStep === OnboardingStep.WELCOME) {
      // Show exit confirmation
      Alert.alert(
        'Uygulamadan Çık',
        'Onboarding\'i tamamlamadan çıkmak istediğinizden emin misiniz?',
        [
          { text: 'Hayır', style: 'cancel' },
          { text: 'Evet', onPress: () => BackHandler.exitApp() },
        ]
      );
      return true;
    } else {
      // Go back to previous step
      handlePreviousStep();
      return true;
    }
  };

  const handleWelcomeComplete = (completed) => {
    if (completed) {
      Analytics.trackEvent(AnalyticsEvent.WELCOME_COMPLETED);
      setCurrentStep(OnboardingStep.TUTORIAL);
    } else {
      Analytics.trackEvent(AnalyticsEvent.WELCOME_SKIPPED);
      handleSkipOnboarding();
    }
  };

  const handleTutorialComplete = (completed) => {
    if (completed) {
      Analytics.trackEvent(AnalyticsEvent.TUTORIAL_COMPLETED, {
        timeSpent: Date.now() - onboardingStartTime,
      });
      setCurrentStep(OnboardingStep.CATEGORY_SETUP);
    } else {
      Analytics.trackEvent(AnalyticsEvent.TUTORIAL_SKIPPED);
      handleSkipOnboarding();
    }
  };

  const handleCategorySetupComplete = () => {
    Analytics.trackEvent(AnalyticsEvent.CATEGORY_SETUP_COMPLETED);
    setCurrentStep(OnboardingStep.FIRST_EXPENSE);
  };

  const handleFirstExpenseComplete = () => {
    Analytics.trackEvent(AnalyticsEvent.FIRST_EXPENSE_COMPLETED);
    setCurrentStep(OnboardingStep.COMPLETE);
    completeOnboarding();
  };

  const handleSkipOnboarding = () => {
    Alert.alert(
      'Onboarding\'i Atla',
      'Uygulamanın temel özelliklerini öğrenmek için onboarding\'i tamamlamanızı öneririz. Yine de atlamak istiyor musunuz?',
      [
        { text: 'Hayır', style: 'cancel' },
        { 
          text: 'Evet, Atla', 
          onPress: () => {
            Analytics.trackEvent(AnalyticsEvent.ONBOARDING_SKIPPED, {
              currentStep,
              timeSpent: Date.now() - onboardingStartTime,
            });
            completeOnboarding(true);
          }
        },
      ]
    );
  };

  const handlePreviousStep = () => {
    switch (currentStep) {
      case OnboardingStep.TUTORIAL:
        setCurrentStep(OnboardingStep.WELCOME);
        break;
      case OnboardingStep.CATEGORY_SETUP:
        setCurrentStep(OnboardingStep.TUTORIAL);
        break;
      case OnboardingStep.FIRST_EXPENSE:
        setCurrentStep(OnboardingStep.CATEGORY_SETUP);
        break;
      default:
        break;
    }
  };

  const completeOnboarding = async (skipped = false) => {
    try {
      // Mark onboarding as completed
      await AsyncStorage.setItem('onboarding_completed', 'true');
      await AsyncStorage.setItem('onboarding_completion_date', new Date().toISOString());
      
      if (skipped) {
        await AsyncStorage.setItem('onboarding_skipped', 'true');
      }

      Analytics.trackEvent(AnalyticsEvent.ONBOARDING_COMPLETED, {
        skipped,
        totalTime: Date.now() - onboardingStartTime,
        completedSteps: getCurrentStepIndex(),
      });

      Logger.info(LogCategory.USER_ACTION, 'Onboarding completed', {
        skipped,
        currentStep,
      });

      // Navigate to auth screen
      navigation.replace('Auth');
    } catch (error) {
      Logger.error(LogCategory.USER_ACTION, 'Failed to complete onboarding', error);
      Alert.alert('Hata', 'Onboarding tamamlanırken bir hata oluştu.');
    }
  };

  const getCurrentStepIndex = () => {
    const steps = Object.values(OnboardingStep);
    return steps.indexOf(currentStep);
  };

  const renderProgressBar = () => {
    const totalSteps = Object.values(OnboardingStep).length - 1; // Exclude COMPLETE
    const currentStepIndex = getCurrentStepIndex();
    const progress = (currentStepIndex / totalSteps) * 100;

    return (
      <View style={styles.progressContainer}>
        <View style={styles.progressBar}>
          <View style={[styles.progressFill, { width: `${progress}%` }]} />
        </View>
        <Text style={styles.progressText}>
          {currentStepIndex + 1} / {totalSteps}
        </Text>
      </View>
    );
  };

  const renderSkipButton = () => {
    if (!canSkip) return null;

    return (
      <TouchableOpacity style={styles.skipButton} onPress={handleSkipOnboarding}>
        <Text style={styles.skipButtonText}>Tümünü Atla</Text>
        <Ionicons name="arrow-forward" size={16} color={Colors.textSecondary} />
      </TouchableOpacity>
    );
  };

  const renderCurrentStep = () => {
    switch (currentStep) {
      case OnboardingStep.WELCOME:
        return (
          <WelcomeScreen
            navigation={navigation}
            onComplete={handleWelcomeComplete}
          />
        );
      
      case OnboardingStep.TUTORIAL:
        return (
          <TutorialScreen
            navigation={navigation}
            onComplete={handleTutorialComplete}
            onSkip={() => handleSkipOnboarding()}
          />
        );
      
      case OnboardingStep.CATEGORY_SETUP:
        return (
          <CategorySetupScreen
            navigation={navigation}
            onComplete={handleCategorySetupComplete}
            onSkip={() => handleSkipOnboarding()}
          />
        );
      
      case OnboardingStep.FIRST_EXPENSE:
        return (
          <FirstExpenseScreen
            navigation={navigation}
            onComplete={handleFirstExpenseComplete}
            onSkip={() => handleSkipOnboarding()}
          />
        );
      
      default:
        return null;
    }
  };

  // For now, render welcome screen as other screens are not implemented yet
  if (currentStep === OnboardingStep.WELCOME) {
    return (
      <WelcomeScreen
        navigation={navigation}
        onComplete={handleWelcomeComplete}
      />
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.background} />
      
      {/* Header with progress and skip */}
      <View style={styles.header}>
        {renderProgressBar()}
        {renderSkipButton()}
      </View>

      {/* Current step content */}
      <View style={styles.content}>
        {renderCurrentStep()}
      </View>
    </SafeAreaView>
  );
}

// Placeholder components for other onboarding steps
const TutorialScreen = ({ onComplete, onSkip }) => (
  <View style={styles.placeholderContainer}>
    <Text style={styles.placeholderTitle}>Tutorial Screen</Text>
    <Text style={styles.placeholderText}>Interactive tutorial will be implemented here</Text>
    <TouchableOpacity style={styles.placeholderButton} onPress={() => onComplete(true)}>
      <Text style={styles.placeholderButtonText}>Continue</Text>
    </TouchableOpacity>
  </View>
);

const CategorySetupScreen = ({ onComplete, onSkip }) => (
  <View style={styles.placeholderContainer}>
    <Text style={styles.placeholderTitle}>Category Setup</Text>
    <Text style={styles.placeholderText}>Category introduction will be implemented here</Text>
    <TouchableOpacity style={styles.placeholderButton} onPress={onComplete}>
      <Text style={styles.placeholderButtonText}>Continue</Text>
    </TouchableOpacity>
  </View>
);

const FirstExpenseScreen = ({ onComplete, onSkip }) => (
  <View style={styles.placeholderContainer}>
    <Text style={styles.placeholderTitle}>First Expense</Text>
    <Text style={styles.placeholderText}>Guided first expense creation will be implemented here</Text>
    <TouchableOpacity style={styles.placeholderButton} onPress={onComplete}>
      <Text style={styles.placeholderButtonText}>Complete</Text>
    </TouchableOpacity>
  </View>
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  progressContainer: {
    flex: 1,
    marginRight: Spacing.lg,
  },
  progressBar: {
    height: 4,
    backgroundColor: Colors.border,
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: Colors.primary,
    borderRadius: 2,
  },
  progressText: {
    fontSize: FontSize.xs,
    color: Colors.textSecondary,
    marginTop: Spacing.xs,
  },
  skipButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.md,
  },
  skipButtonText: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
    marginRight: Spacing.xs,
  },
  content: {
    flex: 1,
  },
  // Placeholder styles
  placeholderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: Spacing.xl,
  },
  placeholderTitle: {
    fontSize: FontSize.xxl,
    fontWeight: FontWeight.bold,
    color: Colors.text,
    marginBottom: Spacing.md,
  },
  placeholderText: {
    fontSize: FontSize.md,
    color: Colors.textSecondary,
    textAlign: 'center',
    marginBottom: Spacing.xl,
  },
  placeholderButton: {
    backgroundColor: Colors.primary,
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.xl,
    borderRadius: BorderRadius.lg,
  },
  placeholderButtonText: {
    fontSize: FontSize.md,
    color: Colors.surface,
    fontWeight: FontWeight.medium,
  },
});
