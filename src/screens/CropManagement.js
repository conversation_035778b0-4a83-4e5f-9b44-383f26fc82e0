import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';

import { Colors } from '../constants/Colors';
import { Spacing, FontSize, FontWeight, BorderRadius } from '../constants/Dimensions';
import { useAuth } from '../context/AuthContext';

const CATEGORY_COLORS = {
  'tahil': Colors.warning,
  'sebze': Colors.success,
  'meyve': Colors.error,
  'baklagil': Colors.info,
  'endüstriyel': Colors.secondary,
  'diğer': Colors.textSecondary,
};

const PRODUCTION_TYPE_ICONS = {
  'seasonal': 'calendar-outline',
  'continuous': 'infinite-outline',
};

export default function CropManagement({ navigation }) {
  const { user, isAuthenticated, isGuestMode } = useAuth();
  const [crops, setCrops] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('all');

  const categories = [
    { key: 'all', name: 'Tümü' },
    { key: 'tahil', name: 'Tahıl' },
    { key: 'sebze', name: 'Sebze' },
    { key: 'meyve', name: 'Meyve' },
    { key: 'baklagil', name: 'Baklagil' },
    { key: 'endüstriyel', name: 'Endüstriyel' },
    { key: 'diğer', name: 'Diğer' },
  ];

  useEffect(() => {
    loadCrops();
  }, [selectedCategory]);

  const loadCrops = async () => {
    try {
      setLoading(true);

      if (isGuestMode) {
        // Guest mode: Load crops from local storage
        await loadCropsFromLocal();
      } else {
        // Authenticated mode: Load crops from API
        await loadCropsFromAPI();
      }
    } catch (error) {
      console.error('Error loading crops:', error);
      if (isGuestMode) {
        Alert.alert('Hata', 'Yerel ürünler yüklenirken bir hata oluştu.');
      } else {
        Alert.alert('Hata', 'Ürünler yüklenirken bir hata oluştu.');
      }
    } finally {
      setLoading(false);
    }
  };

  const loadCropsFromLocal = async () => {
    try {
      const storedCrops = await AsyncStorage.getItem('crops_guest');
      let localCrops = storedCrops ? JSON.parse(storedCrops) : [];

      // Add some default crops for guest users if none exist
      if (localCrops.length === 0) {
        localCrops = getDefaultCrops();
        await AsyncStorage.setItem('crops_guest', JSON.stringify(localCrops));
      }

      // Filter by category if needed
      if (selectedCategory !== 'all') {
        localCrops = localCrops.filter(crop => crop.category === selectedCategory);
      }

      setCrops(localCrops);
    } catch (error) {
      console.error('Error loading local crops:', error);
      setCrops([]);
    }
  };

  const loadCropsFromAPI = async () => {
    try {
      // Backend henüz hazır değilse, authenticated kullanıcılar için de varsayılan ürünleri göster
      console.log('API çağrısı deneniyor...');

      const url = selectedCategory === 'all'
        ? '/api/v1/crops'
        : `/api/v1/crops?category=${selectedCategory}`;

      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${user?.accessToken}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setCrops(data.data);
        console.log('API\'den ürünler yüklendi:', data.data.length);
      } else {
        console.warn('API yanıtı başarısız, varsayılan ürünler yükleniyor');
        // API başarısız olursa varsayılan ürünleri göster
        await loadDefaultCropsForAuthenticated();
      }
    } catch (error) {
      console.error('API hatası, varsayılan ürünler yükleniyor:', error);
      // Network hatası durumunda varsayılan ürünleri göster
      await loadDefaultCropsForAuthenticated();
    }
  };

  const loadDefaultCropsForAuthenticated = async () => {
    try {
      let defaultCrops = getDefaultCrops();

      // Filter by category if needed
      if (selectedCategory !== 'all') {
        defaultCrops = defaultCrops.filter(crop => crop.category === selectedCategory);
      }

      setCrops(defaultCrops);
      console.log('Authenticated kullanıcı için varsayılan ürünler yüklendi:', defaultCrops.length);
    } catch (error) {
      console.error('Error loading default crops for authenticated user:', error);
      setCrops([]);
    }
  };

  const getDefaultCrops = () => {
    return [
      {
        _id: 'default_1',
        name: 'Wheat',
        nameTr: 'Buğday',
        emoji: '🌾',
        category: 'tahil',
        categoryDisplay: 'Tahıl',
        productionType: 'seasonal',
        description: 'Temel tahıl ürünü',
        isDefault: true,
        stats: { usageCount: 0, totalAmount: 0 }
      },
      {
        _id: 'default_2',
        name: 'Tomato',
        nameTr: 'Domates',
        emoji: '🍅',
        category: 'sebze',
        categoryDisplay: 'Sebze',
        productionType: 'seasonal',
        description: 'Yaygın sebze ürünü',
        isDefault: true,
        stats: { usageCount: 0, totalAmount: 0 }
      },
      {
        _id: 'default_3',
        name: 'Apple',
        nameTr: 'Elma',
        emoji: '🍎',
        category: 'meyve',
        categoryDisplay: 'Meyve',
        productionType: 'continuous',
        description: 'Popüler meyve ürünü',
        isDefault: true,
        stats: { usageCount: 0, totalAmount: 0 }
      },
      {
        _id: 'default_4',
        name: 'Grape',
        nameTr: 'Üzüm',
        emoji: '🍇',
        category: 'meyve',
        categoryDisplay: 'Meyve',
        productionType: 'continuous',
        description: 'Bağcılık ürünü',
        isDefault: true,
        stats: { usageCount: 0, totalAmount: 0 }
      }
    ];
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadCrops();
    setRefreshing(false);
  };

  const handleAddCrop = () => {
    if (isGuestMode) {
      Alert.alert(
        'Misafir Modu',
        'Misafir modunda özel ürün ekleyemezsiniz. Google ile giriş yaparak bu özelliği kullanabilirsiniz.',
        [{ text: 'Tamam' }]
      );
      return;
    }

    // Google kullanıcıları için de şimdilik bilgilendirme
    Alert.alert(
      'Özellik Geliştiriliyor',
      'Özel ürün ekleme özelliği yakında kullanıma sunulacak. Şimdilik varsayılan ürünleri kullanabilirsiniz.',
      [{ text: 'Tamam' }]
    );
    // navigation.navigate('AddCrop'); // Henüz hazır değil
  };

  const handleEditCrop = (crop) => {
    if (crop.isDefault) {
      Alert.alert(
        'Bilgi',
        'Varsayılan ürünler düzenlenemez. Özel ürün ekleme özelliği yakında kullanıma sunulacak.',
        [{ text: 'Tamam' }]
      );
      return;
    }

    if (isGuestMode) {
      Alert.alert(
        'Misafir Modu',
        'Misafir modunda ürün düzenleyemezsiniz. Google ile giriş yaparak bu özelliği kullanabilirsiniz.',
        [{ text: 'Tamam' }]
      );
      return;
    }

    // Google kullanıcıları için de şimdilik bilgilendirme
    Alert.alert(
      'Özellik Geliştiriliyor',
      'Ürün düzenleme özelliği yakında kullanıma sunulacak.',
      [{ text: 'Tamam' }]
    );
    // navigation.navigate('EditCrop', { cropId: crop._id }); // Henüz hazır değil
  };

  const handleDeleteCrop = (crop) => {
    if (crop.isDefault) {
      Alert.alert(
        'Bilgi',
        'Varsayılan ürünler silinemez.',
        [{ text: 'Tamam' }]
      );
      return;
    }

    if (isGuestMode) {
      Alert.alert(
        'Misafir Modu',
        'Misafir modunda ürün silemezsiniz. Google ile giriş yaparak bu özelliği kullanabilirsiniz.',
        [{ text: 'Tamam' }]
      );
      return;
    }

    // Google kullanıcıları için de şimdilik bilgilendirme
    Alert.alert(
      'Özellik Geliştiriliyor',
      'Ürün silme özelliği yakında kullanıma sunulacak.',
      [{ text: 'Tamam' }]
    );

    // Alert.alert(
    //   'Ürün Sil',
    //   `"${crop.nameTr}" ürününü silmek istediğinizden emin misiniz?`,
    //   [
    //     {
    //       text: 'İptal',
    //       style: 'cancel',
    //     },
    //     {
    //       text: 'Sil',
    //       style: 'destructive',
    //       onPress: () => deleteCrop(crop._id),
    //     },
    //   ]
    // );
  };

  const deleteCrop = async (cropId) => {
    try {
      const response = await fetch(`/api/v1/crops/${cropId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${user?.accessToken}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        setCrops(crops.filter(crop => crop._id !== cropId));
        Alert.alert('Başarılı', 'Ürün başarıyla silindi.');
      } else {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Ürün silinemedi');
      }
    } catch (error) {
      console.error('Error deleting crop:', error);
      Alert.alert('Hata', error.message || 'Ürün silinirken bir hata oluştu.');
    }
  };

  const renderCategoryFilter = () => (
    <View style={styles.categoryFilter}>
      <FlatList
        data={categories}
        horizontal
        showsHorizontalScrollIndicator={false}
        keyExtractor={(item) => item.key}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={[
              styles.categoryButton,
              selectedCategory === item.key && styles.categoryButtonActive,
            ]}
            onPress={() => setSelectedCategory(item.key)}
          >
            <Text
              style={[
                styles.categoryButtonText,
                selectedCategory === item.key && styles.categoryButtonTextActive,
              ]}
            >
              {item.name}
            </Text>
          </TouchableOpacity>
        )}
        contentContainerStyle={styles.categoryList}
      />
    </View>
  );

  const renderCropItem = ({ item }) => (
    <View style={styles.cropCard}>
      <View style={styles.cropHeader}>
        <View style={styles.cropInfo}>
          <View style={styles.cropNameContainer}>
            <Text style={styles.cropEmoji}>{item.emoji}</Text>
            <View style={styles.cropNames}>
              <Text style={styles.cropName}>{item.nameTr}</Text>
              <Text style={styles.cropNameEn}>{item.name}</Text>
            </View>
          </View>
          
          <View style={styles.cropMeta}>
            <View style={[styles.categoryBadge, { backgroundColor: CATEGORY_COLORS[item.category] }]}>
              <Text style={styles.categoryBadgeText}>{item.categoryDisplay || item.category}</Text>
            </View>
            
            <View style={styles.productionTypeBadge}>
              <Ionicons 
                name={PRODUCTION_TYPE_ICONS[item.productionType]} 
                size={14} 
                color={Colors.textSecondary} 
              />
              <Text style={styles.productionTypeText}>
                {item.productionType === 'seasonal' ? 'Sezonluk' : 'Sürekli'}
              </Text>
            </View>
            
            {item.isDefault && (
              <View style={styles.defaultBadge}>
                <Text style={styles.defaultBadgeText}>Sistem</Text>
              </View>
            )}
          </View>
          
          {item.description && (
            <Text style={styles.cropDescription}>{item.description}</Text>
          )}
        </View>
        
        <View style={styles.cropActions}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => handleEditCrop(item)}
          >
            <Ionicons 
              name={item.isDefault ? "eye-outline" : "pencil"} 
              size={20} 
              color={Colors.primary} 
            />
          </TouchableOpacity>
          
          {!item.isDefault && (
            <TouchableOpacity
              style={[styles.actionButton, styles.deleteButton]}
              onPress={() => handleDeleteCrop(item)}
            >
              <Ionicons name="trash-outline" size={20} color={Colors.error} />
            </TouchableOpacity>
          )}
        </View>
      </View>
      
      {item.stats && (
        <View style={styles.cropStats}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{item.stats.usageCount || 0}</Text>
            <Text style={styles.statLabel}>Kullanım</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>
              {item.stats.totalAmount ? `₺${item.stats.totalAmount.toLocaleString('tr-TR')}` : '₺0'}
            </Text>
            <Text style={styles.statLabel}>Toplam</Text>
          </View>
        </View>
      )}
    </View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="leaf-outline" size={64} color={Colors.textSecondary} />
      <Text style={styles.emptyStateTitle}>
        {selectedCategory === 'all' ? 'Bu kategoride ürün yok' : `${categories.find(c => c.key === selectedCategory)?.name} kategorisinde ürün yok`}
      </Text>
      <Text style={styles.emptyStateText}>
        {isGuestMode
          ? 'Google ile giriş yaparak özel ürün ekleyebilirsiniz'
          : 'Özel ürün ekleme özelliği yakında kullanıma sunulacak'
        }
      </Text>
    </View>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.primary} />
        <Text style={styles.loadingText}>Ürünler yükleniyor...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="chevron-back" size={24} color={Colors.surface} />
        </TouchableOpacity>
        
        <Text style={styles.headerTitle}>Ürün Yönetimi</Text>
        
        <TouchableOpacity style={styles.addButton} onPress={handleAddCrop}>
          <Ionicons name="add" size={24} color={Colors.surface} />
        </TouchableOpacity>
      </View>

      {renderCategoryFilter()}

      <View style={styles.content}>
        {crops.length === 0 ? (
          renderEmptyState()
        ) : (
          <FlatList
            data={crops}
            renderItem={renderCropItem}
            keyExtractor={(item) => item._id}
            contentContainerStyle={styles.listContainer}
            showsVerticalScrollIndicator={false}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={onRefresh}
                colors={[Colors.primary]}
                tintColor={Colors.primary}
              />
            }
          />
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    backgroundColor: Colors.primary,
    paddingTop: 50,
    paddingBottom: Spacing.md,
    paddingHorizontal: Spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: FontSize.xl,
    fontWeight: FontWeight.bold,
    color: Colors.surface,
    flex: 1,
    textAlign: 'center',
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  categoryFilter: {
    backgroundColor: Colors.surface,
    paddingVertical: Spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  categoryList: {
    paddingHorizontal: Spacing.md,
  },
  categoryButton: {
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    marginRight: Spacing.sm,
    borderRadius: BorderRadius.md,
    backgroundColor: Colors.background,
  },
  categoryButtonActive: {
    backgroundColor: Colors.primary,
  },
  categoryButtonText: {
    fontSize: FontSize.sm,
    fontWeight: FontWeight.medium,
    color: Colors.text,
  },
  categoryButtonTextActive: {
    color: Colors.surface,
  },
  content: {
    flex: 1,
    padding: Spacing.md,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background,
  },
  loadingText: {
    marginTop: Spacing.md,
    fontSize: FontSize.md,
    color: Colors.textSecondary,
  },
  listContainer: {
    paddingBottom: Spacing.lg,
  },
  cropCard: {
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.md,
    padding: Spacing.md,
    marginBottom: Spacing.md,
    elevation: 2,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  cropHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  cropInfo: {
    flex: 1,
    marginRight: Spacing.md,
  },
  cropNameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  cropEmoji: {
    fontSize: 32,
    marginRight: Spacing.md,
  },
  cropNames: {
    flex: 1,
  },
  cropName: {
    fontSize: FontSize.lg,
    fontWeight: FontWeight.semibold,
    color: Colors.text,
  },
  cropNameEn: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
    fontStyle: 'italic',
  },
  cropMeta: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: Spacing.sm,
  },
  categoryBadge: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: 2,
    borderRadius: BorderRadius.sm,
    marginRight: Spacing.sm,
    marginBottom: Spacing.xs,
  },
  categoryBadgeText: {
    fontSize: FontSize.xs,
    fontWeight: FontWeight.medium,
    color: Colors.surface,
  },
  productionTypeBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.background,
    paddingHorizontal: Spacing.sm,
    paddingVertical: 2,
    borderRadius: BorderRadius.sm,
    marginRight: Spacing.sm,
    marginBottom: Spacing.xs,
  },
  productionTypeText: {
    fontSize: FontSize.xs,
    color: Colors.textSecondary,
    marginLeft: 2,
  },
  defaultBadge: {
    backgroundColor: Colors.info,
    paddingHorizontal: Spacing.sm,
    paddingVertical: 2,
    borderRadius: BorderRadius.sm,
    marginBottom: Spacing.xs,
  },
  defaultBadgeText: {
    fontSize: FontSize.xs,
    fontWeight: FontWeight.medium,
    color: Colors.surface,
  },
  cropDescription: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
    lineHeight: 18,
  },
  cropActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: Colors.background,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: Spacing.xs,
  },
  deleteButton: {
    backgroundColor: Colors.errorLight,
  },
  cropStats: {
    flexDirection: 'row',
    marginTop: Spacing.md,
    paddingTop: Spacing.md,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statValue: {
    fontSize: FontSize.md,
    fontWeight: FontWeight.semibold,
    color: Colors.text,
  },
  statLabel: {
    fontSize: FontSize.xs,
    color: Colors.textSecondary,
    marginTop: 2,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: Spacing.xl,
  },
  emptyStateTitle: {
    fontSize: FontSize.xl,
    fontWeight: FontWeight.semibold,
    color: Colors.text,
    marginTop: Spacing.md,
    marginBottom: Spacing.sm,
    textAlign: 'center',
  },
  emptyStateText: {
    fontSize: FontSize.md,
    color: Colors.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: Spacing.xl,
  },
  addFirstButton: {
    backgroundColor: Colors.primary,
    paddingHorizontal: Spacing.xl,
    paddingVertical: Spacing.md,
    borderRadius: BorderRadius.md,
  },
  addFirstButtonText: {
    fontSize: FontSize.md,
    fontWeight: FontWeight.semibold,
    color: Colors.surface,
  },
});
