/**
 * Authentication Context for Çiftçi Not Defterim
 * Provides authentication state management throughout the app
 */
import React, { createContext, useContext, useEffect, useState } from 'react';
import { Alert } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import AuthService from '../services/AuthService';
import { DataManager } from '../services/DataManager';
// Create the context
const AuthContext = createContext({
  user: null,
  isAuthenticated: false,
  isGuestMode: false,
  isFirstTime: false,
  loading: true,
  signInWithGoogle: () => {},
  signInAsGuest: () => {},
  signOut: () => {},
  updateProfile: () => {},
  getUserPreferences: () => {},
  saveUserPreferences: () => {},
  setNavigationRef: () => {},
});
// Custom hook to use the auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
// Auth Provider Component
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [isGuestMode, setIsGuestMode] = useState(false);
  const [isFirstTime, setIsFirstTime] = useState(false);
  const [loading, setLoading] = useState(true);
  const [initialized, setInitialized] = useState(false);
  const [navigationRef, setNavigationRef] = useState(null);
  // Data conflict modal state
  const [showDataConflictModal, setShowDataConflictModal] = useState(false);
  const [conflictData, setConflictData] = useState({
    guestDataCount: 0,
    userDataCount: 0
  });
  // Migration state to prevent duplicate migrations
  const [migrationInProgress, setMigrationInProgress] = useState(false);
  useEffect(() => {
    console.log('AuthContext useEffect triggered - calling initializeAuth');
    initializeAuth();
  }, []);
  // Periyodik olarak auth state'ini kontrol et
  useEffect(() => {
    if (!initialized) return;
    const checkAuthState = () => {
      // AuthService'in initialization'ının tamamlanmasını bekle
      if (!AuthService.initialized) {
        console.log('AuthService not initialized yet, skipping auth state check');
        return;
      }
      const currentUser = AuthService.getCurrentUser();
      const currentGuestMode = AuthService.isInGuestMode();
      // Sadece state değişmişse güncelle
      if (currentUser !== user || currentGuestMode !== isGuestMode) {
        console.log('Periodic auth state check - updating state:', {
          currentUser: currentUser?.email || currentUser?.name,
          currentGuestMode,
          previousUser: user?.email || user?.name,
          previousGuestMode: isGuestMode
        });
        setUser(currentUser);
        setIsGuestMode(currentGuestMode);
      }
    };
    // İlk kontrol
    checkAuthState();
    // Her 2 saniyede bir kontrol et
    const interval = setInterval(checkAuthState, 2000);
    return () => clearInterval(interval);
  }, [initialized, user, isGuestMode]);
  // Check if this is the first time user opens the app
  const checkFirstTimeUser = async () => {
    try {
      // Check auth data
      // console.log('🔧 TEMP: Clearing auth data for guest mode test');
      // await AsyncStorage.multiRemove(['user_has_made_auth_choice', 'user_authenticated', 'guest_mode']);
      const userHasMadeAuthChoice = await AsyncStorage.getItem('user_has_made_auth_choice');
      const userAuthenticated = await AsyncStorage.getItem('user_authenticated');
      const guestMode = await AsyncStorage.getItem('guest_mode');
      console.log('First time check:', {
        userHasMadeAuthChoice,
        userAuthenticated,
        guestMode
      });
      // EĞER kullanıcı daha önce auth choice yapmamışsa
      // O ZAMAN: İlk kez kullanıcı
      const isFirstTimeUser = !userHasMadeAuthChoice;
      setIsFirstTime(isFirstTimeUser);
      if (isFirstTimeUser) {
        console.log('🆕 First time user detected - will show auth choice screen');
      } else {
        console.log('👤 Returning user detected');
      }
    } catch (error) {
      console.error('Error checking first time user:', error);
      // Default to first time if error occurs
      setIsFirstTime(true);
    }
  };
  const initializeAuth = async () => {
    try {
      console.log('AuthContext initializeAuth started...');
      console.log('Setting loading to true');
      setLoading(true);
      // Check if this is the first time user opens the app
      await checkFirstTimeUser();
      // Initialize AuthService
      console.log('Calling AuthService.initialize()...');
      await AuthService.initialize();
      console.log('AuthService.initialize() completed');
      // Initialize DataManager
      await DataManager.initialize();
      // Set up auth state listener
      const unsubscribe = AuthService.addAuthStateListener(async (currentUser, guestMode) => {
        console.log('AuthContext received auth state change:', {
          currentUser: currentUser?.email || currentUser?.name,
          guestMode,
          wasAuthenticated: user && !isGuestMode,
          isNowAuthenticated: currentUser && !guestMode
        });
        const wasGuestMode = isGuestMode;
        const wasAuthenticated = user && !wasGuestMode;
        const isNowAuthenticated = currentUser && !guestMode;
        setUser(currentUser);
        setIsGuestMode(guestMode);
        // Check for first time user condition
        // EĞER: currentUser === undefined/null VE isGuestMode === false
        // O ZAMAN: İlk kez kullanıcı - auth choice screen'e yönlendir
        if (!currentUser && !guestMode && isFirstTime) {
          console.log('🆕 First time user condition met - should show auth choice screen');
          // Navigation will be handled by AppNavigator based on isFirstTime state
          setIsFirstTime(true);
        } else if (currentUser || guestMode) {
          // User is authenticated or in guest mode - not first time anymore
          setIsFirstTime(false);
          // Save that user has made a choice (async operation without await)
          AsyncStorage.setItem('user_has_made_auth_choice', 'true').catch(error => {
            console.error('Error saving auth choice flag:', error);
          });
        }
        // Handle authentication state changes
        if (wasGuestMode && isNowAuthenticated) {
          // User switched from guest mode to authenticated mode
          console.log('User switched from guest to authenticated mode, checking for data conflicts...');
          try {
            // Reset DataManager for new user context
            await DataManager.resetForUserChange();
            // Check if there's a data conflict that needs user decision
            // If modal is showing, don't auto-migrate yet - wait for user decision
            if (!showDataConflictModal && !migrationInProgress) {
              console.log('No data conflict modal showing and no migration in progress, proceeding with migration...');
              setMigrationInProgress(true);
              const migrationResult = await DataManager.migrateGuestDataToUser();
              console.log('Data migration completed:', migrationResult);
              setMigrationInProgress(false);
              // Show success message for data migration
              if (migrationResult && migrationResult.success && migrationResult.migratedCount > 0) {
                Alert.alert(
                  'Veriler Yedeklendi!',
                  `${migrationResult.migratedCount} adet gider kaydınız başarıyla Google hesabınıza yedeklendi. Artık verileriniz güvende ve diğer cihazlarınızdan erişebilirsiniz.`,
                  [{ text: 'Tamam' }]
                );
              }
            } else {
              console.log('Data conflict modal is showing or migration in progress, waiting...');
            }
        } catch (error) {
          console.error('Data migration failed:', error);
          setMigrationInProgress(false);
        }
        } else if (isNowAuthenticated && !wasAuthenticated) {
          // User signed in (first time or after logout)
          console.log('User authenticated, resetting DataManager for new user...');
          // First reset DataManager to set up API client with new user token
          try {
            await DataManager.resetForUserChange();
          } catch (error) {
            console.error('DataManager reset failed:', error);
          }
          // Note: Migration is handled by the wasGuestMode && isNowAuthenticated condition above
          // to avoid duplicate migrations. This section only handles DataManager reset.
          console.log('DataManager reset completed for authenticated user');
        } else if (!isNowAuthenticated && wasAuthenticated) {
          // User signed out - switch to guest mode with clean data
          console.log('User signed out, switching to clean guest mode...');
          try {
            // Clear guest data to ensure fresh start
            await DataManager.clearUserData('guest');
            // Reset DataManager for guest mode
            await DataManager.resetForUserChange();
          } catch (error) {
            console.error('Failed to reset to guest mode:', error);
          }
        }
      });
      // Get initial auth state after listener is set up
      const currentUser = AuthService.getCurrentUser();
      const isGuestMode = AuthService.isInGuestMode();
      console.log('Initial auth state after listener setup:', {
        currentUser: currentUser?.email || currentUser?.name,
        isGuestMode
      });
      if (currentUser || isGuestMode) {
        setUser(currentUser);
        setIsGuestMode(isGuestMode);
      }
      setInitialized(true);
      console.log('Setting loading to false - initialization complete');
      setLoading(false);
      // Return cleanup function
      return unsubscribe;
    } catch (error) {
      console.error('Auth initialization error:', error);
      console.log('Setting loading to false - initialization failed');
      setLoading(false);
    }
  };
  const signInWithGoogle = async () => {
    try {
      console.log('AuthContext: Starting Google Sign-In process...');
      setLoading(true);
      // Misafir modunda veri olup olmadığını kontrol et
      const isGuest = AuthService.isInGuestMode();
      let guestDataCount = 0;
      if (isGuest) {
        const guestData = await DataManager.getGuestDataSummary();
        guestDataCount = guestData.totalCount;
        console.log(`Guest mode active with ${guestDataCount} records.`);
      }
      // Google ile giriş yap
      const signInResult = await AuthService.signInWithGoogle();
      if (!signInResult.success) {
        return signInResult; // Giriş başarısız oldu
      }
      // Yeni kullanıcı için DataManager'ı sıfırla
      await DataManager.resetForUserChange();
      // Kullanıcının sunucuda verisi var mı kontrol et
      const userData = await DataManager.checkUserHasExistingData();
      console.log('Authenticated user data check:', userData);
      // Veri çakışması durumunu yönet
      if (userData.hasData && guestDataCount > 0) {
        console.log('Data conflict detected. Showing modal.');
        setConflictData({
          guestDataCount,
          userDataCount: userData.expenseCount,
        });
        setShowDataConflictModal(true);
      } else if (guestDataCount > 0) {
        // Misafir verisi var, kullanıcı verisi yok -> Otomatik taşıma
        console.log('No data conflict, migrating guest data to user account.');
        setMigrationInProgress(true);
        const migrationResult = await DataManager.migrateGuestDataToUser();
        console.log('Data migration completed:', migrationResult);
        setMigrationInProgress(false);
        if (migrationResult && migrationResult.success && migrationResult.migratedCount > 0) {
          Alert.alert(
            'Verileriniz Taşındı',
            `${migrationResult.migratedCount} adet kaydınız başarıyla yeni hesabınıza taşındı.`,
            [{ text: 'Harika!' }]
          );
        }
      }
      return {
        success: true,
        user: signInResult.user,
        hasExistingData: userData.hasData,
        userDataCount: userData.expenseCount,
      };
    } catch (error) {
      console.error('AuthContext: Google sign-in error:', error);
      return { success: false, error: 'Google ile giriş yapılırken bir hata oluştu.' };
    } finally {
      setLoading(false);
    }
  };
  const signInAsGuest = async () => {
    try {
      setLoading(true);
      const result = await AuthService.signInAsGuest();
      return result;
    } catch (error) {
      console.error('Guest sign-in error:', error);
      return { success: false, error: 'Misafir moduna geçilirken hata oluştu' };
    } finally {
      setLoading(false);
    }
  };
  const signOut = async () => {
    try {
      setLoading(true);
      const result = await AuthService.signOut();
      if (result.success) {
        // Clear auth choice flag so user sees auth choice screen again
        await AsyncStorage.removeItem('user_has_made_auth_choice');
        await AsyncStorage.removeItem('user_authenticated');
        await AsyncStorage.removeItem('guest_mode');
        // Set first time to true to show auth choice screen
        setIsFirstTime(true);
        console.log('✅ Sign out successful - user will see auth choice screen');
        // Navigate to auth choice screen if navigation ref is available
        if (navigationRef && navigationRef.current) {
          console.log('🔄 Navigating to auth choice screen after sign out');
          navigationRef.current.reset({
            index: 0,
            routes: [{ name: 'AuthChoice' }],
          });
        }
      }
      return result;
    } catch (error) {
      console.error('Sign out error:', error);
      return { success: false, error: 'Çıkış yapılırken hata oluştu' };
    } finally {
      setLoading(false);
    }
  };
  const updateProfile = async (updates) => {
    try {
      const result = await AuthService.updateProfile(updates);
      return result;
    } catch (error) {
      console.error('Profile update error:', error);
      return { success: false, error: 'Profil güncellenirken hata oluştu' };
    }
  };
  const getUserPreferences = async () => {
    try {
      const preferences = await AuthService.getUserPreferences();
      return preferences;
    } catch (error) {
      console.error('Get preferences error:', error);
      return {};
    }
  };
  const saveUserPreferences = async (preferences) => {
    try {
      const result = await AuthService.saveUserPreferences(preferences);
      return result;
    } catch (error) {
      console.error('Save preferences error:', error);
      return { success: false, error: 'Tercihler kaydedilirken hata oluştu' };
    }
  };
  const deleteAccount = async () => {
    try {
      setLoading(true);
      const result = await AuthService.deleteAccount();
      if (result.success) {
        // Clear all auth-related data
        await AsyncStorage.removeItem('user_has_made_auth_choice');
        await AsyncStorage.removeItem('user_authenticated');
        await AsyncStorage.removeItem('guest_mode');
        // Set first time to true to show auth choice screen
        setIsFirstTime(true);
        console.log('✅ Account deleted successfully - user will see auth choice screen');
        // Navigate to auth choice screen if navigation ref is available
        if (navigationRef && navigationRef.current) {
          console.log('🔄 Navigating to auth choice screen after account deletion');
          navigationRef.current.reset({
            index: 0,
            routes: [{ name: 'AuthChoice' }],
          });
        }
      }
      return result;
    } catch (error) {
      console.error('Delete account error:', error);
      return { success: false, error: 'Hesap silinirken hata oluştu' };
    } finally {
      setLoading(false);
    }
  };
  const reauthenticate = async () => {
    try {
      const result = await AuthService.reauthenticate();
      return result;
    } catch (error) {
      console.error('Re-authentication error:', error);
      return { success: false, error: 'Yeniden kimlik doğrulama başarısız' };
    }
  };
  // Context value
  const value = {
    // State
    user,
    isAuthenticated: user !== null,
    isGuestMode,
    isFirstTime,
    loading,
    initialized,
    // Data conflict modal state
    showDataConflictModal,
    setShowDataConflictModal,
    conflictData,
    setConflictData,
    // Methods
    signInWithGoogle,
    signInAsGuest,
    signOut,
    updateProfile,
    getUserPreferences,
    saveUserPreferences,
    deleteAccount,
    reauthenticate,
    setNavigationRef,
    // Utility methods
    getCurrentUser: () => AuthService.getCurrentUser(),
    isInGuestMode: () => AuthService.isInGuestMode(),
  };
  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
// Higher-order component for protected routes
export const withAuth = (Component) => {
  return (props) => {
    const { isAuthenticated, loading } = useAuth();
    if (loading) {
      return null; // Or a loading component
    }
    if (!isAuthenticated) {
      return null; // Or redirect to auth screen
    }
    return <Component {...props} />;
  };
};
// Hook for guest mode specific functionality
export const useGuestMode = () => {
  const { isGuestMode, user } = useAuth();
  return {
    isGuestMode,
    canSync: !isGuestMode,
    canBackup: !isGuestMode,
    canShare: !isGuestMode,
    storageLocation: isGuestMode ? 'local' : 'cloud',
    userId: user?.id || 'guest',
  };
};
// Hook for user preferences
export const useUserPreferences = () => {
  const { getUserPreferences, saveUserPreferences } = useAuth();
  const [preferences, setPreferences] = useState({
    currency: 'TRY',
    language: 'tr',
    theme: 'light',
    notifications: true,
    defaultSeason: 'spring'
  });
  const [loading, setLoading] = useState(true);
  useEffect(() => {
    loadPreferences();
  }, []);
  const loadPreferences = async () => {
    try {
      setLoading(true);
      const userPrefs = await getUserPreferences();
      setPreferences(prev => ({ ...prev, ...userPrefs }));
    } catch (error) {
      console.error('Error loading preferences:', error);
    } finally {
      setLoading(false);
    }
  };
  const updatePreferences = async (newPreferences) => {
    try {
      const updatedPrefs = { ...preferences, ...newPreferences };
      const result = await saveUserPreferences(updatedPrefs);
      if (result.success) {
        setPreferences(updatedPrefs);
      }
      return result;
    } catch (error) {
      console.error('Error updating preferences:', error);
      return { success: false, error: 'Tercihler güncellenirken hata oluştu' };
    }
  };
  return {
    preferences,
    loading,
    updatePreferences,
    refreshPreferences: loadPreferences,
  };
};
export { AuthContext };
export default AuthContext;
