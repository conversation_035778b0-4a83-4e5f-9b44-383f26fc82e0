/**
 * Performance and Stress Test for Migration System
 * Tests system performance with large datasets
 */

console.log('🧪 Starting Performance and Stress Tests...\n');

// Performance monitoring utilities
class PerformanceMonitor {
  constructor() {
    this.startTime = null;
    this.endTime = null;
    this.memoryStart = null;
    this.memoryEnd = null;
  }

  start() {
    this.startTime = Date.now();
    this.memoryStart = process.memoryUsage();
  }

  end() {
    this.endTime = Date.now();
    this.memoryEnd = process.memoryUsage();
  }

  getDuration() {
    return this.endTime - this.startTime;
  }

  getMemoryUsage() {
    return {
      heapUsed: this.memoryEnd.heapUsed - this.memoryStart.heapUsed,
      heapTotal: this.memoryEnd.heapTotal - this.memoryStart.heapTotal,
      external: this.memoryEnd.external - this.memoryStart.external
    };
  }

  getReport() {
    return {
      duration: this.getDuration(),
      memory: this.getMemoryUsage()
    };
  }
}

// Mock storage for performance testing
const mockStorage = {
  data: {},
  getItem: async (key) => mockStorage.data[key] || null,
  setItem: async (key, value) => { mockStorage.data[key] = value; },
  removeItem: async (key) => { delete mockStorage.data[key]; }
};

// Validation logic for performance testing
function validateExpense(expense) {
  const errors = [];
  
  if (!expense.amount || expense.amount <= 0) {
    errors.push('Invalid amount');
  }
  if (expense.amount > 1000000) {
    errors.push('Amount too high');
  }
  if (!expense.categoryId) {
    errors.push('Missing category');
  }
  if (!expense.date) {
    errors.push('Missing date');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

function validateField(field) {
  const errors = [];
  
  if (!field.name || field.name.trim().length === 0) {
    errors.push('Missing name');
  }
  if (field.name && field.name.length > 50) {
    errors.push('Name too long');
  }
  if (field.size && field.size.value < 0) {
    errors.push('Negative area');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

// Data generators
function generateExpenses(count) {
  const expenses = [];
  const categories = ['gübre', 'işçilik', 'ilaç', 'su', 'yakıt'];
  
  for (let i = 0; i < count; i++) {
    expenses.push({
      id: `expense_${i}`,
      amount: Math.floor(Math.random() * 10000) + 1,
      description: `Test expense ${i}`,
      categoryId: categories[Math.floor(Math.random() * categories.length)],
      date: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString()
    });
  }
  
  return expenses;
}

function generateFields(count) {
  const fields = [];
  const soilTypes = ['clay', 'sandy', 'loamy'];
  const units = ['dekar', 'dönüm', 'hectare'];
  
  for (let i = 0; i < count; i++) {
    fields.push({
      id: `field_${i}`,
      name: `Test Field ${i}`,
      size: {
        value: Math.floor(Math.random() * 100) + 1,
        unit: units[Math.floor(Math.random() * units.length)]
      },
      soilType: soilTypes[Math.floor(Math.random() * soilTypes.length)],
      notes: `Notes for field ${i}`
    });
  }
  
  return fields;
}

// Performance tests
async function runPerformanceTests() {
  let passedTests = 0;
  let totalTests = 0;

  async function performanceTest(name, testFn, maxDuration = 5000, maxMemory = 50 * 1024 * 1024) {
    totalTests++;
    console.log(`\n📋 Performance Test: ${name}`);
    
    const monitor = new PerformanceMonitor();
    
    try {
      monitor.start();
      const result = await testFn();
      monitor.end();
      
      const report = monitor.getReport();
      
      console.log(`   Duration: ${report.duration}ms`);
      console.log(`   Memory: ${Math.round(report.memory.heapUsed / 1024 / 1024 * 100) / 100}MB`);
      
      if (report.duration <= maxDuration && report.memory.heapUsed <= maxMemory && result) {
        console.log(`✅ ${name}: PASSED`);
        passedTests++;
      } else {
        console.log(`❌ ${name}: FAILED (Duration: ${report.duration}ms, Memory: ${Math.round(report.memory.heapUsed / 1024 / 1024)}MB)`);
      }
      
    } catch (error) {
      monitor.end();
      console.log(`❌ ${name}: ERROR - ${error.message}`);
    }
  }

  // Test 1: Large expense validation (1000 expenses)
  await performanceTest(
    'Validate 1000 expenses',
    async () => {
      const expenses = generateExpenses(1000);
      await mockStorage.setItem('expenses_guest', JSON.stringify(expenses));
      
      let validCount = 0;
      let invalidCount = 0;
      
      for (const expense of expenses) {
        const validation = validateExpense(expense);
        if (validation.isValid) {
          validCount++;
        } else {
          invalidCount++;
        }
      }
      
      console.log(`   Validated: ${validCount} valid, ${invalidCount} invalid`);
      return validCount > 0;
    },
    3000, // 3 seconds max
    30 * 1024 * 1024 // 30MB max
  );

  // Test 2: Large field validation (500 fields)
  await performanceTest(
    'Validate 500 fields',
    async () => {
      const fields = generateFields(500);
      await mockStorage.setItem('fields_guest', JSON.stringify(fields));
      
      let validCount = 0;
      let invalidCount = 0;
      
      for (const field of fields) {
        const validation = validateField(field);
        if (validation.isValid) {
          validCount++;
        } else {
          invalidCount++;
        }
      }
      
      console.log(`   Validated: ${validCount} valid, ${invalidCount} invalid`);
      return validCount > 0;
    },
    2000, // 2 seconds max
    20 * 1024 * 1024 // 20MB max
  );

  // Test 3: Batch validation performance
  await performanceTest(
    'Batch validation (2000 items)',
    async () => {
      const expenses = generateExpenses(1000);
      const fields = generateFields(1000);
      
      await mockStorage.setItem('expenses_guest', JSON.stringify(expenses));
      await mockStorage.setItem('fields_guest', JSON.stringify(fields));
      
      // Simulate batch validation
      const batchSize = 100;
      let totalProcessed = 0;
      
      // Process expenses in batches
      for (let i = 0; i < expenses.length; i += batchSize) {
        const batch = expenses.slice(i, i + batchSize);
        batch.forEach(expense => validateExpense(expense));
        totalProcessed += batch.length;
        
        // Small delay to prevent blocking
        await new Promise(resolve => setTimeout(resolve, 1));
      }
      
      // Process fields in batches
      for (let i = 0; i < fields.length; i += batchSize) {
        const batch = fields.slice(i, i + batchSize);
        batch.forEach(field => validateField(field));
        totalProcessed += batch.length;
        
        // Small delay to prevent blocking
        await new Promise(resolve => setTimeout(resolve, 1));
      }
      
      console.log(`   Processed: ${totalProcessed} items in batches`);
      return totalProcessed === 2000;
    },
    5000, // 5 seconds max
    50 * 1024 * 1024 // 50MB max
  );

  // Test 4: Large backup creation
  await performanceTest(
    'Create backup with large dataset',
    async () => {
      const expenses = generateExpenses(2000);
      const fields = generateFields(500);
      
      await mockStorage.setItem('expenses_guest', JSON.stringify(expenses));
      await mockStorage.setItem('fields_guest', JSON.stringify(fields));
      
      // Simulate backup creation
      const backup = {
        backupId: `backup_${Date.now()}`,
        timestamp: new Date().toISOString(),
        data: {
          expenses_guest: JSON.stringify(expenses),
          fields_guest: JSON.stringify(fields)
        },
        metadata: {
          expenseCount: expenses.length,
          fieldCount: fields.length
        }
      };
      
      await mockStorage.setItem('migration_backup', JSON.stringify(backup));
      
      console.log(`   Backup size: ${Math.round(JSON.stringify(backup).length / 1024)}KB`);
      return true;
    },
    3000, // 3 seconds max
    40 * 1024 * 1024 // 40MB max
  );

  // Test 5: Memory stress test
  await performanceTest(
    'Memory stress test (5000 expenses)',
    async () => {
      const expenses = generateExpenses(5000);
      
      // Process in chunks to test memory management
      const chunkSize = 500;
      let processedChunks = 0;
      
      for (let i = 0; i < expenses.length; i += chunkSize) {
        const chunk = expenses.slice(i, i + chunkSize);
        
        // Simulate processing
        chunk.forEach(expense => {
          validateExpense(expense);
          // Simulate some data transformation
          const processed = {
            ...expense,
            processed: true,
            timestamp: Date.now()
          };
        });
        
        processedChunks++;
        
        // Force garbage collection hint
        if (global.gc) {
          global.gc();
        }
        
        // Small delay
        await new Promise(resolve => setTimeout(resolve, 1));
      }
      
      console.log(`   Processed: ${processedChunks} chunks`);
      return processedChunks === Math.ceil(expenses.length / chunkSize);
    },
    8000, // 8 seconds max
    100 * 1024 * 1024 // 100MB max
  );

  // Summary
  console.log('\n📊 Performance Test Results:');
  console.log(`✅ Passed: ${passedTests}/${totalTests}`);
  console.log(`❌ Failed: ${totalTests - passedTests}/${totalTests}`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All performance tests passed!');
    console.log('🚀 System is ready for production with large datasets!');
    return true;
  } else {
    console.log('⚠️ Some performance tests failed!');
    console.log('🔧 Consider optimization before production deployment.');
    return false;
  }
}

// Run the performance tests
runPerformanceTests().then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('❌ Performance test suite failed:', error);
  process.exit(1);
});
