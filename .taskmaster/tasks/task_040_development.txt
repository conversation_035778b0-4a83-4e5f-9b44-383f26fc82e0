# Task ID: 40
# Title: Backend Bellek Kullanımı Optimizasyonu
# Status: pending
# Dependencies: None
# Priority: medium
# Description: Node.js uygulamasının bellek kullanımını analiz etmek ve olası bellek sızıntılarını veya verimsiz kullanımı tespit edip düzeltmek.
# Details:
Uygulamayı `node --inspect` flag'i ile çalıştırın ve Chrome DevTools'un 'Memory' sekmesini kullanarak heap snapshot'ları alın. Özellikle büyük veri işleme (Excel import gibi) senaryolarından önce ve sonra snapshot alarak bellek artışını gözlemleyin. Global değişkenlerin, kapanmayan bağlantıların veya büyük nesnelerin gereksiz yere bellekte tutulup tutulmadığını kontrol edin. Gerekirse, büyük dosyaları işlerken stream'leri kullanarak bellek kullanımını optimize edin.

# Test Strategy:
Yük testi araçları (ör. `autocannon`) ile uygulamaya yük bindirerek bellek kullanımını izleyin. Bellek kullanımının zamanla sürekli artıp artmadığını (bellek sızıntısı belirtisi) kontrol edin. Hedeflenen <150MB bellek kullanımını aşıp aşmadığını doğrulayın.
