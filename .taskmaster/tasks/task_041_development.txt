# Task ID: 41
# Title: Frontend Global Yükleme Durumu (Loading State) Yönetimi
# Status: pending
# Dependencies: None
# Priority: high
# Description: <PERSON>y<PERSON><PERSON><PERSON> genelinde, veri yüklenirken veya bir işlem gerçekleştirilirken kullanıcıya görsel geri bildirim sağlamak için tutarlı yükleme durumu göstergeleri (loading states) eklenmesi.
# Details:
React Context API veya bir state management kütüphanesi (Zustand, Redux) kullanarak global bir `isLoading` state'i oluşturun. API isteği başladığında bu state'i `true`, bittiğinde veya hata verdiğinde `false` yapın. Bu global state'e bağlı olarak, ekranın üzerinde ortaya çıkan bir overlay ve içinde `ActivityIndicator` bileşeni gösteren bir `GlobalLoader` bileşeni oluşturun ve bunu `App.js`'in en üst seviyesine ekleyin.

# Test Strategy:
Uygulamanın farklı ekranlarında API isteği tetikleyen işlemleri (veri listeleme, form gönderme) gerçekleştirin. Her işlem sırasında global yükleme göstergesinin göründüğünü ve işlem tamamlandığında kaybolduğunu doğrulayın. %100 görünürlük hedefine ulaşıldığını kontrol edin.
