# Task ID: 38
# Title: API Yanıtları iç<PERSON> (Caching) Mekanizması
# Status: pending
# Dependencies: 26
# Priority: high
# Description: Sık istenen ve nadiren değişen veriler için API yanıtlarını önbelleğe alarak veritabanı yükünü ve yanıt süresini azaltmak.
# Details:
`node-cache` kütüphanesini kullanarak bir `cache.service.js` oluşturun. Bu servis `get`, `set` ve `del` metodları sunmalıdır. Sık erişilen ama statik olan verileri döndüren API endpoint'leri i<PERSON><PERSON> (ör. ürün listesi, kategori listesi) bir middleware yazın. Bu middleware, istek geldiğinde önce cache'i kontrol etmeli, veri varsa cache'den döndürmeli, yoksa veritabanından alıp cache'e ekledikten sonra döndürmelidir.

# Test Strategy:
Önbelleğe alınan bir endpoint'e Postman ile iki kez ardışık istek atın. <PERSON><PERSON> isteğin daha ya<PERSON>, ikinci isteğin ise çok daha hızlı (<50ms) olduğunu ve veritabanı sorgusu oluşturmadığını (loglardan kontrol ederek) doğrulayın. Veritabanında bir değişiklik yaptıktan sonra ilgili cache'in temizlendiğini ve endpoint'in güncel veriyi döndürdüğünü test edin.
