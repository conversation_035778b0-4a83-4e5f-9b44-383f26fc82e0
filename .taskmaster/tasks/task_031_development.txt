# Task ID: 31
# Title: Frontend Excel İşlevselliği Entegrasyonu
# Status: pending
# Dependencies: 30
# Priority: high
# Description: Frontend'de, Excel şablonunu indirme ve seçilen dosyayı içe aktarma işlemlerini yönetecek butonların ve ilgili API çağrılarının entegrasyonu.
# Details:
Şablon indirme butonu için `/api/v1/data/template` endpoint'ine bir istek yapın ve gelen dosyayı kaydetmek için `expo-file-system` kullanın. İçe aktarma butonu için, `expo-document-picker` ile seçilen dosyayı `FormData` kullanarak `/api/v1/data/import` endpoint'ine POST isteği ile gönderin. API'den gelen yanıtı işleyerek kullanıcıya başarı veya hata mesajı gösterin.

# Test Strategy:
Şablon indirme butonuna tıklandığında dosyanın başarıyla indirilip kaydedildiğini doğrulayın. Geçerli bir Excel dosyası seçip 'İçe Aktar' butonuna basıldığında API'ye isteğin gittiğini ve başarılı yanıt alındığında kullanıcıya bildirim gösterildiğini test edin.

# Subtasks:
## 1. Gerekli Bağımlılıkların Kurulumu ve Yapılandırılması [pending]
### Dependencies: None
### Description: Excel işlemleri için `expo-file-system` ve `expo-document-picker` kütüphanelerinin projeye eklenmesi ve yapılandırılması.
### Details:
Projenin `frontend` dizininde `npm install expo-file-system expo-document-picker` komutunu çalıştırarak gerekli paketleri kurun. Kurulumun `frontend/package.json` dosyasına yansıdığını doğrulayın. Bu adım, dosya indirme ve seçme işlevselliği için temel altyapıyı oluşturur. Tahmini Süre: 1 saat. Kabul Kriterleri: Bağımlılıklar başarıyla kurulmalı ve proje hatasız bir şekilde derlenmelidir. Risk Faktörleri: Expo SDK sürümü ile kütüphane sürümleri arasında uyumsuzluk yaşanması.

## 2. Excel İşlemleri İçin Arayüz Bileşeninin Oluşturulması [pending]
### Dependencies: 31.1
### Description: 'Şablon İndir' ve 'Veri İçe Aktar' butonlarını içerecek olan React Native arayüz bileşeninin oluşturulması ve ilgili ekrana yerleştirilmesi.
### Details:
`frontend/src/components/` dizini altında `ExcelActions.js` adında yeni bir bileşen oluşturun. Bu bileşen, 'Şablon İndir' ve 'Veri İçe Aktar' için iki adet `TouchableOpacity` içermelidir. Butonları projenin tasarım sistemine uygun şekilde stillendirin ve ilgili ekrana (örneğin, `frontend/src/screens/DataImportScreen.js`) entegre edin. Tahmini Süre: 2 saat. Kabul Kriterleri: Butonlar arayüzde doğru şekilde görüntülenmeli, tıklanabilir olmalı ve henüz işlevsellikleri atanmamış olmalıdır.

## 3. Excel Şablonu İndirme İşlevselliğinin Uygulanması [pending]
### Dependencies: 31.2
### Description: 'Şablon İndir' butonuna tıklandığında `/api/v1/data/template` endpoint'inden şablon dosyasını indiren ve `expo-file-system` ile cihaza kaydeden mantığın eklenmesi.
### Details:
`ExcelActions.js` bileşenindeki 'Şablon İndir' butonunun `onPress` olayına, `/api/v1/data/template` endpoint'ine GET isteği yapan bir fonksiyon bağlayın. Gelen yanıtı `expo-file-system` kullanarak `FileSystem.documentDirectory` altına `sablom.xlsx` adıyla kaydedin. İşlem sırasında bir yükleme göstergesi (ActivityIndicator) gösterin. Tahmini Süre: 3 saat. Kabul Kriterleri: Butona tıklandığında dosya başarıyla indirilmeli ve kullanıcıya dosyanın kaydedildiğine dair bir bildirim gösterilmelidir.

## 4. Excel Dosyası İçe Aktarma İşlevselliğinin Uygulanması [pending]
### Dependencies: 31.2
### Description: 'Veri İçe Aktar' butonuna tıklandığında `expo-document-picker` ile dosya seçtiren ve seçilen dosyayı `/api/v1/data/import` endpoint'ine gönderen mantığın eklenmesi.
### Details:
`ExcelActions.js` bileşenindeki 'Veri İçe Aktar' butonunun `onPress` olayına, `DocumentPicker.getDocumentAsync()` metodunu çağıran bir fonksiyon bağlayın. Kullanıcının seçtiği dosyayı bir `FormData` nesnesine ekleyerek `/api/v1/data/import` endpoint'ine `multipart/form-data` olarak POST isteği gönderin. Tahmini Süre: 4 saat. Kabul Kriterleri: Kullanıcı bir Excel dosyası seçtiğinde, dosya başarıyla API'ye gönderilmelidir. Dosya seçimi iptal edildiğinde hata oluşmamalıdır.

## 5. API Yanıtlarının İşlenmesi ve Kullanıcı Bildirimleri [pending]
### Dependencies: 31.3, 31.4
### Description: İndirme ve içe aktarma işlemlerinden dönen API yanıtlarına göre kullanıcıya başarı, hata veya bilgilendirme mesajlarının gösterilmesi ve yükleme durumlarının yönetilmesi.
### Details:
`ExcelActions.js` bileşenine `loading`, `error` ve `success` durumlarını yönetmek için bir state (örn: `useState`) ekleyin. API çağrılarını `try...catch` blokları içine alın. Başarılı yanıtlarda (örn: 200 OK) kullanıcıya `Alert` veya bir toast bildirimi ile başarı mesajı gösterin. Hata durumlarında (örn: 400, 500) API'den gelen hata mesajını kullanıcıya gösterin. API isteği sırasında butonları devre dışı bırakın ve bir yükleme göstergesi gösterin. Tahmini Süre: 2 saat. Kabul Kriterleri: Tüm senaryolarda (başarı, sunucu hatası, geçersiz dosya, ağ hatası) kullanıcıya anlamlı ve net geri bildirimler verilmelidir.

