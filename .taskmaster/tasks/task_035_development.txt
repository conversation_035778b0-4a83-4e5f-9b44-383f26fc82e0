# Task ID: 35
# Title: AI API Zaman Aşımı ve Yeniden Deneme Ayarları
# Status: pending
# Dependencies: 33
# Priority: high
# Description: AI API çağrıları için zaman aşımı (timeout) süresinin PRD'de belirtilen 15 saniyeye çıkarılması ve yeniden deneme (retry) mantığı eklenmesi.
# Details:
Gemini API çağrısını yaparken, `AbortController` ve `setTimeout` kullanarak bir 15 saniyelik zaman aşımı mekanizması uygulayın. Eğer 15 saniye içinde yanıt gelmezse, isteği iptal edin ve bir timeout hatası fırlatın. Geçici ağ hataları veya kısa süreli API sorunları için `async-retry` gibi bir kütüphane kullanarak üstel geri çekilme (exponential backoff) ile 1-2 kez yeniden deneme mantığı ekleyin.

# Test Strategy:
Ağ trafiğ<PERSON> yavaşlatan bir araç (ör. Chrome DevTools network throttling) kullanarak veya API'yi gecikmeli yanıt verecek şekilde mock'layarak timeout senaryosunu test edin. 15 saniye sonra isteğin iptal edildiğini ve hata yakalandığını doğrulayın. Geçici bir hata senaryosunda yeniden deneme mekanizmasının çalıştığını loglardan kontrol edin.
