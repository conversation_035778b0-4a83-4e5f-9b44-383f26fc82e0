# Task ID: 48
# Title: AI Sistemi Yanıt Süresi ve Başarı Oranı Testi
# Status: pending
# Dependencies: 35
# Priority: high
# Description: AI sisteminin yanıt süresini ve başarı oranını ölçmek için testler yapılması.
# Details:
AI endpoint'ine 100 adet ardışık (aralarda küçük bekleme süreleri ile) test isteği gönderen bir script yazın. Her isteğin yanıt süresini ve başarı durumunu (HTTP 200 veya hata) kaydedin. Ortalama ve maksimum yanıt sürelerini hesaplayın. Başarılı isteklerin sayısını toplam istek sayısına bölerek başarı oranını bulun.

# Test Strategy:
Test scriptini çalıştırdıktan sonra sonuçları analiz edin. Ortalama yanıt süresinin 15 saniyenin altında olduğunu ve başarı oranının %95'in üzerinde olduğunu doğrulayın. Başarısız olan isteklerin nedenlerini loglardan inceleyin.
