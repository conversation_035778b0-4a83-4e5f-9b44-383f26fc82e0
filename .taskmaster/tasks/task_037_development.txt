# Task ID: 37
# Title: MongoDB Sorgu Optimizasyonu ve İndeksleme
# Status: pending
# Dependencies: None
# Priority: high
# Description: Veritabanı sorgu performansını iyileştirmek için sık kullanılan alanlara (örneğin, kullanıcı ID'si, tarih a<PERSON>ıkları) MongoDB üzerinde indeksler eklenmesi.
# Details:
Uygulamada en sık çalıştırılan sorguları belirleyin. Özellikle `find()` ve `aggregate()` sorgularında `filter` ve `sort` işlemlerinde kullanılan alanları tespit edin. `user_id`, `createdAt`, `date` gibi alanlar için MongoDB Atlas UI veya `createIndex()` komutu ile indeksler oluşturun. Örneğin: `db.collection.createIndex({ userId: 1, date: -1 })`.

# Test Strategy:
İndeks eklenmeden önce ve sonra, `explain('executionStats')` komutunu kullanarak yavaş bir sorgunun performansını karşılaştırın. `totalDocsExamined` sayısının önemli ölçüde azaldığını ve sorgu süresinin <100ms hedefine yaklaştığını doğrulayın.
