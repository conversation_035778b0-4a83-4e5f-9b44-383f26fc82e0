# Task ID: 47
# Title: Excel İçe Aktarma Performans Testi
# Status: pending
# Dependencies: 31
# Priority: high
# Description: Excel içe aktarma sürecinin performansını test ederek 10MB'a kadar olan dosyaların 30 saniye içinde işlenip işlenmediğini doğrulamak.
# Details:
<PERSON><PERSON><PERSON><PERSON> boyutlard<PERSON> (1MB, 5MB, 10MB) ve farklı satır sayılarına sahip (1.000, 10.000, 50.000 satır) test Excel dosyaları oluşturun. Bu dosyaları kullanarak içe aktarma API'sini test edin ve isteğin başlangıcından veritabanına yazmanın tamamlanmasına kadar geçen süreyi ölçün. Süreyi loglayın ve 30 saniye hedefini aşıp aşmadığını kontrol edin.

# Test Strategy:
10MB boyutunda bir test dosyası ile içe aktarma işlemini başlatın. Sunucu loglarından veya API yanıt süresinden toplam işlem süresinin 30 saniyenin altında olduğunu doğrulayın. İşlem sırasında sunucu kaynaklarının (CPU, bellek) aşırı tüketilmediğini izleyin.
