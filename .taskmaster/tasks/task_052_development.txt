# Task ID: 52
# Title: Frontend Excel Şablon İndirme İşlevselliği
# Status: pending
# Dependencies: 31, 41
# Priority: high
# Description: Ayarlar ekranında bulunan Excel şablonu indirme butonunu işlevsel hale getirmek. <PERSON><PERSON> g<PERSON><PERSON><PERSON>, backend'den şablon dosyasını indirmeyi, cihaza kaydetmeyi ve kullanıcıyı bilgilendirmeyi kapsar.
# Details:
SettingsScreen bileşenindeki 'Şablonu İndir' butonuna bir onPress olayı ekleyin. Bu olay tetiklendiğinde, mevcut APIClient.downloadTemplate metodunu çağırarak '/api/v1/import/template' endpoint'ine bir GET isteği gönderin. İstek sırasında kullanıcıya geri bildirim sağlamak için global yükleme durumunu (Task 41) aktif hale getirin. API'den dönen blob verisini, 'expo-file-system' ve 'expo-sharing' kütüphanelerini kullanarak 'veri-sablonu.xlsx' gibi anlamlı bir isimle kullanıcının cihazına kaydetmesini sağlayın. İndirme işlemi başarıyla tamamlandığında bir başarı mesajı (toast/alert) gösterin. Olası ağ veya API hatalarını yakalayıp kullanıcıya anlaşılır bir hata mesajı göstererek işlemi sonlandırın.

# Test Strategy:
Uygulamayı açıp Ayarlar ekranına gidin. 'Şablonu İndir' butonuna tıklayın. Butona tıklandığında global yükleme göstergesinin göründüğünü doğrulayın. Ağ trafiğini izleyerek '/api/v1/import/template' endpoint'ine doğru bir istek yapıldığını kontrol edin. İstek başarılı olduğunda, sistemin dosya kaydetme diyaloğunun açıldığını ve dosyayı kaydettikten sonra bir başarı bildirimi gösterildiğini doğrulayın. İndirilen dosyanın geçerli bir Excel dosyası olduğunu teyit edin. Ağı kapatarak veya geçersiz bir endpoint ile hata senaryosunu test edin ve kullanıcıya uygun bir hata mesajının gösterildiğini kontrol edin.
