# Task ID: 45
# Title: Kullanıcı Dostu Hata Mesajları Standardizasyonu
# Status: pending
# Dependencies: None
# Priority: medium
# Description: Uygulama genelindeki tüm hata mesajlarının (API hataları, doğrulama hataları, vb.) kullan<PERSON><PERSON><PERSON> dostu, anla<PERSON><PERSON><PERSON><PERSON>r ve tutarlı bir dilde olmasının sağlanması.
# Details:
Tüm hata mesajlarını tek bir dosyada (`constants/messages.js`) toplayın. Teknik jargondan arındırılmış, basit ve çözüm odaklı mesajlar yazın. <PERSON><PERSON><PERSON><PERSON>, 'Internal Server Error' yerine 'Sun<PERSON>uda beklenmedik bir hata oluştu. Lütfen daha sonra tekrar deneyin.' gibi bir mesaj kullanın. Hata mesajlarını gösteren merkezi bir `Toast` veya `Alert` bileşeni oluşturarak tüm uygulamanın aynı stili kullan<PERSON>ını sağlayın.

# Test Strategy:
Far<PERSON><PERSON><PERSON> hata senaryolarını (geçersiz form girişi, sunuc<PERSON> hatası, yetkilendirme hatası) manuel olarak tetikleyin. Her senaryoda gösterilen mesajın merkezi mesajlar dosyasından geldiğini, anlaşılır ve kullanıcı dostu olduğunu doğrulayın. Mesajların görsel tutarlılığını kontrol edin.
