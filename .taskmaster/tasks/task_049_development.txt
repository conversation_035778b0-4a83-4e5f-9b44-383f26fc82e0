# Task ID: 49
# Title: Uygulama Başlangıç Süresi Performans Testi
# Status: pending
# Dependencies: None
# Priority: medium
# Description: Uygulamanın ba<PERSON><PERSON><PERSON><PERSON> (startup) süresini ölçmek ve <3 saniye hedefine uygunluğunu kontrol etmek.
# Details:
React Native'in dahili performans araçlarını veya `react-native-startup-time` gibi bir kütüphaneyi kullanarak uygulamanın TTI (Time to Interactive) süresini ölçün. Hem 'cold start' (uygulama kapalıyken ilk açılış) hem de 'warm start' (uygulama arkaplandayken açılış) sürelerini ölçün. Özellikle 'cold start' süresini optimize etmek için gereksiz kütüphanelerin veya senkron işlemlerin başlangıçtan kaldırılmasını değerlendirin.

# Test Strategy:
Uygulamayı gerçek bir cihazda (hem Android hem iOS) birkaç kez tamamen kapatıp açarak 'cold start' süresini ölçün. Ortalama sürenin 3 saniyenin altında olduğunu doğrulayın. Gerekirse, bundle boyutunu analiz etmek için `react-native-bundle-visualizer` kullanın.
