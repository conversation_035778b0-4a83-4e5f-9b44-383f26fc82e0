# Task ID: 39
# Title: API Yanıt Süresi İzleme (Monitoring) Middleware'i
# Status: pending
# Dependencies: None
# Priority: medium
# Description: Her bir API isteğinin ne kadar sürede yanıt verdiğini izlemek için bir middleware eklenmesi. B<PERSON>, performans darboğazlarını tespit etmeye yardımcı olacaktır.
# Details:
Basit bir Express middleware'i yazın. Middleware, istek başladığında `process.hrtime()` veya `Date.now()` ile başlangıç zamanını kaydetmelidir. İstek tamamlandığında (`res.on('finish', ...)`), geçen süreyi hesaplayıp konsola veya bir log servisine yazdırmalıdır. Örneğin: `[GET] /api/v1/data - 25ms`. Daha gelişmiş bir çözüm için `express-status-monitor` gibi bir paket de kullanılabilir.

# Test Strategy:
Uygulamayı çalıştırıp birkaç farklı endpoint'e istek gönderin. Konsolda her istek için metod, URL ve yanıt süresini içeren logların göründüğünü doğrulayın. Yavaş bir endpoint'in daha yüksek bir süre ile loglandığını kontrol edin.
