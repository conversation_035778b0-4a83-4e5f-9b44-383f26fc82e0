{"version": "1.0.0", "metadata": {"created": "2025-07-12T15:37:34.160Z", "updated": "2025-07-12T15:37:34.160Z", "project": "Çiftçi Not Defterim", "description": "Teknik iyileştirme görevleri"}, "tags": {"master": {"name": "master", "description": "<PERSON> gel<PERSON><PERSON>e dalı", "metadata": {"created": "2025-07-12T15:37:34.160Z"}, "tasks": []}}, "currentTag": "master", "development": {"tasks": [{"id": 55, "title": "Ortam Değişkenleri Kurulumu ve Güvenli Yönetimi", "description": "Hardcoded kimlik bilgilerini (API anahtarları, client ID'ler) koddan çıkarmak ve güvenli bir şekilde yönetmek için .env dosyaları ve ilgili kütüphane kurulumu.", "details": "`react-native-dotenv` (v5.7.3) kütüphanesini projeye ekleyin. Proje kök dizininde `.env` dosyası oluşturun ve hassas verileri (GOOGLE_CLIENT_ID, API_KEY vb.) buraya taşıyın. `.gitignore` dosyasına `.env` dosyasını ekleyerek repoya gönderilmesini engelleyin. Android için `android/app/build.gradle` ve iOS için `Info.plist` dosyalarında bu değişkenleri kullanacak şekilde yapılandırma yapın.", "testStrategy": "Uygulamanın farklı ortamlarda (development, production) doğru konfigürasyonlarla çalıştığını doğrulayın. Kod tabanında `grep` veya benzeri bir araçla hardcoded anahtar kalmadığını kontrol edin.", "priority": "high", "dependencies": [], "status": "in-progress", "subtasks": []}, {"id": 56, "title": "Android için Production Keystore Oluşturma ve Yapılandırma", "description": "Google Play Store'a yüklenecek production build'lerini imzalamak için gü<PERSON>li bir production keystore oluşturma ve build sürecine entegre etme.", "details": "`keytool` komutunu kull<PERSON>rak bir `production.keystore` dosyası oluşturun. Oluşturulan keystore dosyasını projenin dışında güvenli bir yerde saklayın. Keystore şifrelerini ve alias bilgilerini `.env` dosyasından okuyacak şekilde `android/app/build.gradle` dosyasını yapılandırın. CI/CD pipeline'ı varsa, keystore dosyasını ve şifreleri güvenli bir şekilde (örn: GitHub Secrets, Vault) pipeline'a tanıtın.", "testStrategy": "`gradlew assembleRelease` komutuyla imzalı bir release APK/AAB oluşturun. `apks<PERSON>er` aracıyla build'in başarıyla imzalandığını doğrulayın.", "priority": "high", "dependencies": [55], "status": "pending", "subtasks": []}, {"id": 57, "title": "<PERSON><PERSON><PERSON> için Güvenli Depolama Katmanı Oluşturma", "description": "Cihaz üzerinde saklanan PII (Kişisel Tanımlanabilir Bilgiler) ve diğer hassas verilerin şifrelenmesi için `react-native-keychain` kullanarak güvenli bir depolama çözümü implemente etme.", "details": "`react-native-keychain` (v8.1.2) kütü<PERSON>nesini kurun ve native modülleri linkleyin. AsyncStorage yerine kullanılacak bir `SecureStorage` sarmalayıcı (wrapper) servisi oluşturun. Bu servis `setGenericPassword` ile veri yazma ve `getGenericPassword` ile veri okuma işlemlerini gerçekleştirecektir. Mevcut AsyncStorage kullanımlarını bu yeni güvenli servise migrate edin.", "testStrategy": "Bir test senaryosu ile hassas bir verinin (örn: auth token) `SecureStorage`'a yazılıp okunduğunu doğrulayın. Cihaz dosya sistemini inceleyerek verinin şifrelenmiş olarak saklandığını manuel olarak kontrol edin.", "priority": "high", "dependencies": [], "status": "pending", "subtasks": []}, {"id": 58, "title": "<PERSON><PERSON> <PERSON><PERSON> (<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>)", "description": "<PERSON>jede kod kalitesini artırmak, tutarl<PERSON> bir stil sağlamak ve yaygın hataları önlemek için ESLint, <PERSON><PERSON><PERSON> <PERSON> (git hooks) araçlarının kurulumu ve yapılandırılması.", "details": "`eslint`, `prettier`, `eslint-config-prettier`, `eslint-plugin-react`, `eslint-plugin-react-native` pake<PERSON><PERSON> kurun. Proje kök dizininde `.eslintrc.js` ve `.prettierrc.js` konfigürasyon dosyalarını oluşturun. `husky` (v8.0.3) ve `lint-staged` k<PERSON><PERSON> pre-commit hook'u oluşturun. Bu hook, her commit öncesi değiştirilen dosyalarda ESLint ve Prettier'ı otomatik olarak çalıştıracaktır.", "testStrategy": "Kasıtlı olarak hatalı veya formatlanmamış bir kod yazıp commit etmeye çalışın. Husky hook'unun devreye girip commit'i engellediğini veya dosyaları otomatik düzelttiğini doğrulayın.", "priority": "high", "dependencies": [], "status": "pending", "subtasks": []}, {"id": 59, "title": "Kritik Performans Optimizasyonları (Memoization)", "description": "Uygulama genelinde `React.memo`, `useMemo` ve `useCallback` kullanarak gereksiz component re-render'larını ve hesaplamaları önleyerek performansı artırma.", "details": "`React Profiler` veya `react-native-performance-monitor` gibi araçlar kullanarak en çok yeniden render olan component'leri tespit edin. Fonksiyonel component'leri `React.memo` ile sarmalayın. Prop olarak geçirilen fonksiyonları `useCallback` ile, pahalı hesaplamaları ise `useMemo` ile memoize edin. Özellikle listelerdeki item component'lerine odak<PERSON>ın.", "testStrategy": "React Profiler k<PERSON><PERSON><PERSON> optimizasyon öncesi ve sonrası re-render sayıların<PERSON> karşılaştırın. Belirgin bir azalma olduğunu doğrulayın. Kullanıcı arayüzünde akıcılığın arttığını gözlemleyin.", "priority": "high", "dependencies": [], "status": "pending", "subtasks": []}, {"id": 60, "title": "Büyük Component'lerin Parçalanması", "description": "500+ satırlık büyük component'leri, okunabilirliği ve yeniden kullanılabilirliği artırmak için maksimum 300 satırlık daha küçük, yönetilebilir ve tek sorumluluğu olan alt component'lere bölme.", "details": "Projedeki en büyük component'leri (örn: `find . -name \"*.js\" -o -name \"*.tsx\" | xargs wc -l | sort -n`) bularak işe başlayın. Mantıksal olarak ayrıştırılabilecek UI parçalarını (örn: Header, Form, ItemList) kendi component dosyalarına taşıyın. State yönetimi için prop drilling yerine Context API veya state management kütüphanesi (Zustand, Redux) kullanımını değerlendirin.", "testStrategy": "Refactor edilen ekranların görsel ve işlevsel olarak eskisiyle birebir aynı çalıştığını doğrulayın. Oluşturulan yeni alt component'ler için temel unit testler yazın.", "priority": "medium", "dependencies": [59], "status": "pending", "subtasks": []}, {"id": 61, "title": "<PERSON><PERSON><PERSON><PERSON>ızıntıları<PERSON><PERSON><PERSON> (Memory Leak Fixes)", "description": "Özellikle animasyonlar ve asenkron işlemlerden kaynaklanan hafıza sızıntılarını tespit edip giderme. Component unmount olduğunda listener'ların ve timer'ların temizlenmesini sağlama.", "details": "`useEffect` hook'unun cleanup fonksiyonunu kull<PERSON>rak a<PERSON>, timer'la<PERSON><PERSON> (`setTimeout`, `setInterval`) ve event listener'ları kaldırın. `useRef` kullanarak animasyon referanslarını saklayın ve cleanup fonksiyonunda animasyonları durdurun (`animation.stop()`). Android Studio Profiler veya Xcode Instruments gibi native araçlarla memory kullanımını izleyerek sızıntıların giderildiğini doğrulayın.", "testStrategy": "Ha<PERSON><PERSON>za sızıntısı olan ekranlara tekrar tekrar girip çıktıktan sonra memory kullanımını profiler ile izleyin. Memory kullanımının başlangıç seviyesine geri döndüğünü ve sürekli artmadığını doğrulayın.", "priority": "high", "dependencies": [], "status": "pending", "subtasks": []}, {"id": 62, "title": "Unit Test Altyapısı<PERSON><PERSON><PERSON> (Jest & RNTL)", "description": "Unit testleri yazmak için Jest ve React Native Testing Library (RNTL) altyapısını kurma ve yapılandırma.", "details": "`jest`, `react-native-testing-library` (@testing-library/react-native v12.4.3) ve gerekli preset'leri (`jest-expo` veya `react-native` preset) kurun. `jest.config.js` dosyasını oluşturarak transform, moduleNameMapper ve setup dosyalarını (`jest-setup.js`) yapılandırın. `jest-setup.js` içinde RNTL için gerekli global ayarları yapın.", "testStrategy": "Basit bir component için bir \"smoke test\" (render olup olmadığını kontrol eden) yazın ve `npm test` veya `yarn test` komutuyla testlerin başarıyla çalıştığını doğrulayın.", "priority": "high", "dependencies": [58], "status": "pending", "subtasks": []}, {"id": 63, "title": "Hata Ta<PERSON> ve Raporlama Servisi Entegrasyonu", "description": "Production ortamında oluşan çökmeleri ve hataları anında tespit edip analiz edebilmek için Sentry veya Firebase Crashlytics gibi bir servisin entegrasyonu.", "details": "Sentry (`@sentry/react-native` v5.20.0) SDK'sını projeye ekleyin. `App.js` veya uygulamanın giriş noktasında Sentry'yi DSN anahtarınızla başlatın. Source map'lerin otomatik olarak Sentry'e yüklenmesi için build script'le<PERSON> (`sentry-cli`) yapılandırın. Kullanıcı bilgilerini (ID, email vb.) PII kurallarına uygun olarak Sentry event'lerine ekleyin.", "testStrategy": "`Sentry.captureException(new Error('Test Hatası'))` ile manuel bir hata gönderin ve Sentry dashboard'unda göründüğünü doğrulayın. `Sentry.nativeCrash()` ile kasıtlı bir çökme yaratarak native crash raporlamasının çalıştığını test edin.", "priority": "high", "dependencies": [55], "status": "pending", "subtasks": []}, {"id": 64, "title": "Merkezi Loglama Altyapısı Kurulumu", "description": "Uygulama genelinde <PERSON> log<PERSON> (info, warning, error) merkezi bir yerde toplamak ve analiz etmek için Winston gibi bir kütüphane ile yapılandırılmış (structured) loglama altyapısı kurma.", "details": "Bu görev backend tarafında yapılır. Node.js or<PERSON>ı i<PERSON><PERSON> `winston` (v3.11.0) kütüphanesini kurun. Farklı transport'lar (örn: `Console`, `File`, ve Sentry/Datadog için `Http`) yapılandırın. Log formatını JSON olarak ayarlayarak `timestamp`, `level`, `message`, `service`, `traceId` gibi alanları ekleyin. React Native tarafında `react-native-logs` kullanarak logları Sentry'e \"breadcrumb\" olarak veya özel bir endpoint'e gönderebilirsiniz.", "testStrategy": "Uygulamanın farklı noktalarından çeşitli seviyelerde (info, warn, error) loglar oluşturun. Bu logların yapılandırılmış formatta ve doğru seviyede merkezi loglama sistemine ulaştığını doğrulayın.", "priority": "medium", "dependencies": [63], "status": "pending", "subtasks": []}, {"id": 65, "title": "FlatList Performans Optimizasyonları", "description": "Uzun listelerde akıcı bir kaydırma deneyimi sağlamak ve memory kullanımını azaltmak için `FlatList` component'inde `getItemLayout`, `windowSize`, `keyExtractor` gibi optimizasyon tekniklerini uygulama.", "details": "Tüm `FlatList` implementasyonlarında `keyExtractor` prop'unun benzersiz ve string bir değer döndürdüğünden emin olun. Liste elemanlarının yüks<PERSON><PERSON><PERSON><PERSON> sabitse, performansı ciddi şekilde artıran `getItemLayout` prop'unu implemente edin. `windowSize`, `maxToRenderPerBatch` ve `initialNumToRender` proplarını listenin yapısına göre ayarlayarak render performansını optimize edin. Liste elemanı component'ini `React.memo` ile sarmalayın.", "testStrategy": "Optimizasyon öncesi ve sonrası uzun bir listede kaydırma performansını hem Android hem de iOS cihazlarda test edin. \"Blank items\" sorununun yaşanmadığını ve kaydırmanın akıcı olduğunu doğrulayın. React Profiler ile render sayılarındaki düşüşü gözlemleyin.", "priority": "high", "dependencies": [59], "status": "pending", "subtasks": []}, {"id": 66, "title": "`DataManager.js`'in Servis Katmanı Desenine Göre Refactor Edilmesi", "description": "2000+ satırlık monolithic `DataManager.js` <PERSON><PERSON><PERSON><PERSON><PERSON>, her biri tek bir sorumluluğa sahip (örn: `AuthService`, `UserService`, `ProductService`) daha küçük ve yönetilebilir servis dosyalarına ayırma.", "details": "`DataManager.js` içindeki fonksiyonları mantıksal olarak gruplandırın (kullanıcı işlemleri, ürün işlemleri, ayarlar vb.). Her grup için ayrı bir `services/UserService.js`, `services/ProductService.js` dosyası oluşturun. İlgili fonksiyonları bu yeni dosyalara taşıyın. Servisler arası sıkı bağımlılıkları (tight coupling) azaltmak için doğrudan import yerine dependency injection (bağımlılık enjeksiyonu) mekanizması hazırlayın.", "testStrategy": "Refactor edilen her bir servis için unit testler yazın. Uygulamanın bu servisleri kullanan ekranlarının eskisi gibi çalışmaya devam ettiğini doğrulayan entegrasyon testleri veya manuel testler yapın.", "priority": "high", "dependencies": [58], "status": "pending", "subtasks": []}, {"id": 67, "title": "Bağımlılık Enjeksiyonu (Dependency Injection) Kurulumu", "description": "Servisler ve diğer katmanlar arasındaki bağımlılıkları yönetmek, test edilebilirliği artırmak ve sıkı bağımlılıkları (tight coupling) ortadan kaldırmak için bir dependency injection mekanizması kurma.", "details": "`tsyringe` (v4.8.0) veya `inversify` gibi hafif bir DI kütüphanesi kurun. Bir `di-container.ts` dosyası oluşturarak tüm servisleri (`UserService`, `ProductService` vb.) bu konteynere kaydedin. Component'lerde veya diğer servislerde, ihtiyaç duyulan servisleri doğrudan `import` etmek yerine bu konteyner üzerinden `resolve` ederek alın. Bu, özellikle testlerde sahte (mock) servisler enjekte etmeyi kolaylaştırır.", "testStrategy": "Bir servisin mock versiyonunu oluşturun ve DI konteyneri aracılığıyla bir test ortamına enjekte edin. Unit testin gerçek servis yerine mock servisi kullandığını doğrulayın. Uygulamanın DI konteyneri ile düzgün bir şekilde başlayıp çalıştığını kontrol edin.", "priority": "medium", "dependencies": [66], "status": "pending", "subtasks": []}, {"id": 68, "title": "Controller'lardaki Business Logic'in Servis Katmanına Taşınması", "description": "Controller (veya React component'leri) içindeki iş mantığını (business logic) ilgili servis katman<PERSON>ına taşıyarak controller'ların sadece istek/cevap yönlendirme ve veri bağlama (data binding) gibi UI sorumluluklarına odaklanmasını sağlama.", "details": "Component'ler içindeki veri <PERSON>, validasyon, API çağrıları gibi mantıksal işlemleri tespit edin. <PERSON><PERSON> mantığı, ilgi<PERSON> servise (örn: `UserService.registerUser`) taşıyın. Component artık sadece `userService.registerUser(data)` gibi bir metodu çağırıp dönen sonucu state'e set etmelidir. Bu, component'leri basitleştirir ve iş mantığını yeniden kullanılabilir ve test edilebilir hale getirir.", "testStrategy": "İş mantığı taşınan component'lerin UI'da aynı şekilde çalışmaya devam ettiğini doğrulayın. Servis katmanına taşınan iş mantığı için kapsamlı unit testler yazın.", "priority": "medium", "dependencies": [66, 67], "status": "pending", "subtasks": []}, {"id": 69, "title": "Veritabanı Geçiş (Migration) Altyapısı Kurulumu", "description": "Veritabanı şemasında yapılacak değişiklikleri sistematik, versiyonlanabilir ve geri alınabilir bir şekilde yönetmek için bir migration framework'ü (örn: TypeORM Migrations, Knex.js) kurma.", "details": "Bu görev backend'i ilgilendirir. Projenin ORM'ine (eğer varsa) uygun bir migration aracı seçin. Örneğin, TypeORM kullanılıyorsa kendi migration aracını, Knex kullanılıyorsa `knex migrate` komutlarını kullanın. İlk migration dosyasını mevcut şemayı yansıtacak şekilde oluşturun. Yeni şema değişiklikleri için yeni migration dosyaları (`up` ve `down` metodları ile) oluşturma sürecini tanımlayın.", "testStrategy": "Boş bir veritabanı üzerinde `migrate:latest` komutunu çalıştırarak tüm migration'ların hatasız uygulandığını ve şemanın doğru oluşturulduğunu doğrulayın. Bir migration'ı `migrate:rollback` ile geri alıp tekrar uygulayarak `up` ve `down` fonksiyonlarının doğru çalıştığını test edin.", "priority": "high", "dependencies": [], "status": "pending", "subtasks": []}, {"id": 70, "title": "Veritabanında İlişkisel Bütünlüğün Sağlanması", "description": "Veritabanı tabloları arasında mantıksal bütünlüğü ve veri tutarlılığını sağlamak için eksik olan foreign key (yabancı anahtar) kısıtlamalarını ekleme.", "details": "Veritabanı şemasını analiz ederek tablolar arasındaki ilişkileri (örn: `users` ve `orders` tabloları arasında `userId`) belirleyin. Her ilişki için bir önceki görevde kurulan migration framework'ünü kullanarak yeni bir migration dosyası oluşturun. Bu dosyada `ADD CONSTRAINT ... FOREIGN KEY ... REFERENCES ...` SQL komutlarını veya ORM'in ilgili metodlarını kullanarak kısıtlamaları ekleyin. `ON DELETE` ve `ON UPDATE` kurallarını (örn: `CASCADE`, `SET NULL`) iş mantığına uygun şekilde belirleyin.", "testStrategy": "Bir ana kaydı (örn: bir kullanıcı) silmeye çalıştığınızda, iliş<PERSON>li alt kayıtlar (örn: kullanıcının siparişleri) varsa veritabanının bu işlemi engellediğini veya tanımlanan kurala göre (örn: cascade delete) davrandığını doğrulayan bir test yazın.", "priority": "high", "dependencies": [69], "status": "pending", "subtasks": []}, {"id": 71, "title": "Kritik Operasyonlar için Transaction Desteği", "description": "Birden fazla veritabanı yazma işlemi gerektiren kritik operasyonların (örn: para transferi, sipariş oluşturma) atomikliğini sağlamak için transaction blokları içine alınması.", "details": "Uygulama servislerinde birden fazla tabloyu güncelleyen veya yazan kritik iş akışlarını tespit edin. Bu işlemleri veritabanı kütüphanesinin (örn: TypeORM `transaction`, Knex `transaction`) sağladığı transaction yönetimi metodları ile sarmalayın. Bir `try...catch` b<PERSON><PERSON><PERSON>, işlem sırasında herhangi bir hata olursa `rollback` işleminin çağrıldığından, başarıl<PERSON> olursa `commit` işleminin yapıldığından emin olun.", "testStrategy": "Bir transaction içindeki adımlardan birinin kasıtlı olarak başarısız olmasını sağlayan bir test senaryosu yazın. İşlem sonunda, transaction'dan önceki hiçbir verinin değişmediğini (rollback'in başarılı olduğunu) doğrulayın. Başarılı senaryoda ise tüm değişikliklerin veritabanına yansıdığını kontrol edin.", "priority": "high", "dependencies": [66], "status": "pending", "subtasks": []}, {"id": 72, "title": "API Dokümantasyonu Oluşturma (OpenAPI/Swagger)", "description": "Backend API'sinin tüm endpoint'<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, istek ve cevap formatlarını açıklayan, interaktif ve otomatik olarak güncellenen bir dokümantasyon oluşturmak için OpenAPI (Swagger) entegrasyonu.", "details": "Backend projesine `swagger-jsdoc` ve `swagger-ui-express` gibi kütüphanel<PERSON> ekleyin. Controller dosyalar<PERSON><PERSON><PERSON> her bir endpoint'in üzerine JSDoc formatında OpenAPI/Swagger açıklamaları ekleyin (`@openapi`, `@swagger`, `@param`, `@returns` vb.). Bu açıklamalardan dinamik olarak bir `swagger.json` dosyası üreten bir script veya endpoint oluşturun. `swagger-ui-express` ile bu JSON dosyasını kullanarak `/api-docs` gibi bir yolda interaktif bir UI sunun.", "testStrategy": "`/api-docs` endpoint'ine giderek Swagger UI'ın doğru bir şekilde yüklendiğini kontrol edin. Dokümantasyonda listelenen tüm endpoint'lerin, parametrelerin ve şemaların kod ile tutarlı olduğunu doğrulayın. UI üzerinden test bir API çağrısı yapın.", "priority": "medium", "dependencies": [68], "status": "pending", "subtasks": []}, {"id": 73, "title": "Unit Test Kapsamını Artırma", "description": "Refactor edile<PERSON> ser<PERSON>, helper fon<PERSON><PERSON><PERSON><PERSON> ve karmaşık component logiği için unit testler yazarak proje genelindeki test kapsamını (test coverage) minimum %70 seviyesine çıkarma.", "details": "Jest'in `--coverage` flag'ini kullanarak mevcut test kapsamını ölçün. Kapsamı en düşük ve en kritik olan mod<PERSON> (özellikle servis katmanı) başlayarak test yazmaya başlayın. Her fonksiyonun normal çalışma, kenar durumlar (edge cases) ve hata durumları için test senaryoları oluşturun. Bağımlılıkları `jest.mock()` ile mock'layarak testlerin izole ve hızlı çalışmasını sağlayın.", "testStrategy": "CI/CD pipeline'ına test kapsamı kontrolü ekleyin. Kapsam %70'in altına düşerse build'in başarısız olmasını sağlayın. `npm test -- --coverage` komutuyla düzenli olarak raporları kontrol edin.", "priority": "medium", "dependencies": [62, 66, 68], "status": "pending", "subtasks": []}, {"id": 74, "title": "API Entegrasyon Testleri Yazma", "description": "API endpoint'le<PERSON>n beklendiği gibi çalıştığını, doğru validasyonları yaptığını ve veritabanı ile doğru etkileşime girdiğini doğrulamak için `supertest` gibi bir kütüphane kullanarak entegrasyon testleri yazma.", "details": "Test için ayrı bir veritabanı ve konfigürasyon kullanın. `supertest` k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kurun. Her bir API endpoint'i i<PERSON><PERSON> (GET, POST, PUT, DELETE) test senaryoları oluşturun. Başarılı istekler (200, 201), client hataları (400, 401, 403, 404) ve sunucu hataları (500) için testler yazın. Testlerden önce veritabanını temizleyip gerekli başlangıç verisini (seed data) ekleyen bir mekanizma kurun.", "testStrategy": "Test suit'ini çalıştırarak tüm API endpoint'lerinin tanımlanan senaryolara göre doğru HTTP status kodları ve cevap body'leri döndürdüğünü doğrulayın. Veritabanını kontrol ederek testlerin veriler üzerinde beklenen değişiklikleri yaptığını (veya yapmadığını) teyit edin.", "priority": "medium", "dependencies": [71, 72], "status": "pending", "subtasks": []}, {"id": 75, "title": "E2E Test Altyapısı Kurulumu (Detox)", "description": "Uygulamanın ana kullanıcı akışlarının (örn: kay<PERSON>t olma, giri<PERSON> yapma, not ekleme) baştan sona doğru çalıştığını otomatize bir şekilde test etmek için Detox framework'ünü kurma ve yapılandırma.", "details": "`detox` (v20.14.8) kütü<PERSON>nesini kurun ve `detox init` komutuyla başlangıç yapılandırmasını oluşturun. `.detoxrc.json` dosyasın<PERSON> projenin build konfigürasyonlarına göre düzenleyin. Test edilecek elemanlara `testID` prop'u ekleyin. İlk E2E testini (örn: login akışı) `e2e/` klasörü altında yazın. Testler, \"kullanıcı gibi\" etkileşimleri (`element(by.id('...')).tap()`, `element(by.id('...')).typeText('...')`) simüle etmelidir.", "testStrategy": "`detox build` ve `detox test` komutlarını çalıştırarak testlerin hem iOS simülatöründe hem de Android emülatöründe başarıyla koştuğunu doğrulayın. Test<PERSON>in, tanımlanan kullanıcı akışını hatasız bir şekilde tamamladığını video kaydı veya loglar üzerinden izleyin.", "priority": "low", "dependencies": [56, 74], "status": "pending", "subtasks": []}, {"id": 76, "title": "GDPR Uyum ve <PERSON>", "description": "Kullanıcılardan kişisel verilerinin işlenmesi için açık ve net bir şekilde onay alınmasını sağlayan bir UI ve bu onayı yöneten bir mekanizma geliştirme.", "details": "Uygulamanın ilk açılışında veya kayıt sırasında gösterilecek bir \"Gizlilik Politikası ve Onay\" modal/ekranı tasarlayın. Bu ekranda hangi verilerin ne amaçla toplandığı açıkça belirtilmelidir. Kullanıcının onayı (`true`/`false` ve `timestamp`) `SecureStorage` veya backend veritabanında saklanmalıdır. Veri işleyen tüm servisler, işlem yapmadan önce bu onayın varlığını kontrol etmelidir. Kullanıcının ayarlar ekranından onayını geri çekebilmesine olanak tanıyın.", "testStrategy": "Yeni bir kullanıcı olarak uygulamayı ilk kez açtığınızda onay ekranının geldiğini doğrulayın. Onay vermeden kritik özelliklerin kullanılamadığını test edin. Onay verdikten sonra verinin kaydedildiğini ve özelliklerin çalıştığını kontrol edin. Ayarlardan onayı geri çekme işleminin başarılı olduğunu doğrulayın.", "priority": "medium", "dependencies": [57], "status": "pending", "subtasks": []}, {"id": 77, "title": "<PERSON><PERSON><PERSON><PERSON> (Certificate Pinning) Implementasyonu", "description": "API isteklerinin sadece beklenen sunucu sertifikasıyla güvenli bir şekilde yapılmasını sağlamak ve ortadaki adam (MITM) saldırılarını önlemek için certificate pinning uygulama.", "details": "`react-native-cert-pinner` veya `react-native-ssl-pinning` gibi bir kü<PERSON><PERSON><PERSON><PERSON> kull<PERSON>n. Sunucunuzun SSL sertifikasının public key hash'ini (SPKI hash) alın. Bu hash'i uygulama koduna veya güvenli bir konfigürasyon dosyasına ekleyin. API isteklerini yapan HTTP client'ı (örn: fetch, axios) bu kütüphanenin sağladığı sarmalayıcı ile kullanacak şekilde yapılandırın. Farklı bir sertifika ile karşılaşıldığında isteğin başarısız olmasını sağlayın.", "testStrategy": "Charles veya mitmproxy gibi bir proxy aracı kullanarak uygulamanın trafiğini kendi sahte sertifikanızla dinlemeye çalışın. Certificate pinning doğru yapılandı<PERSON><PERSON><PERSON>, uygulamanın API isteklerinin başarısız olduğunu ve proxy'nin trafiği çözemediğini doğrulayın.", "priority": "low", "dependencies": [55], "status": "pending", "subtasks": []}, {"id": 78, "title": "Veritabanı Sorgu Optimizasyonu", "description": "Veritabanı performansını artırmak için yavaş çalışan sorguları tespit etme, `EXPLAIN ANALYZE` gibi araçlarla analiz etme ve eksik index'leri ekleyerek veya sorguları yeniden yazarak optimize etme.", "details": "Veritabanı sisteminin sağladığı \"slow query log\" özelliğini aktif hale getirin. En yavaş ve en sık çalışan sorguları belirleyin. Bu sorgular için `EXPLAIN ANALYZE` komutunu çalıştırarak execution plan'ını inceleyin. \"Sequential Scan\" gibi verimsiz operasyonları tespit edin. `WHERE`, `JOIN` ve `ORDER BY` klaçlarında kullanılan kolonlar için uygun index'ler (`CREATE INDEX`) oluşturun. `over-indexing`'den kaçınmak için sadece gerekli index'leri ekleyin.", "testStrategy": "Optimizasyon öncesi ve sonrası yavaş sorguların execution time'ını karşılaştırın. %40 veya daha fazla bir iyileşme hedeflenmelidir. `EXPLAIN ANALYZE` çıktısında \"Index Scan\" kullanıldığını ve maliyetin (cost) düştüğünü doğrulayın.", "priority": "medium", "dependencies": [70], "status": "pending", "subtasks": []}, {"id": 79, "title": "Performans <PERSON> (APM) ve Dashboard Kurulumu", "description": "Uygulamanın performans metriklerini (API response time, transaction süreleri, veritabanı sorgu süreleri) gerçek zamanlı olarak izlemek ve analiz etmek için Sentry Performance, New Relic veya Datadog gibi bir APM aracı entegre etme ve dashboard'lar oluşturma.", "details": "Sentry Performance'ı (`@sentry/react-native` içinde gelir) veya seçilen başka bir APM aracını yapılandırın. Backend'de de ilgili APM SDK'sını kurun. Otomatik transaction takibini (HTTP istekleri, DB sorguları) etkinleştirin. Önemli kullanıcı akışları için özel transaction'lar (`Sentry.startTransaction`) oluşturun. Sentry (veya diğer araç) üzerinde API response time, en yavaş transaction'lar, hata oranları gibi metrikleri gösteren bir dashboard oluşturun.", "testStrategy": "Uygulamayı kullanarak çeşitli işlemleri gerçekleştirin. APM aracının dashboard'unda bu işlemlere karşılık gelen transaction'ların ve span'lerin (veritabanı sorgusu, http isteği vb.) göründüğünü doğrulayın. Performans metriklerinin (örn: LCP, FCP, response time) makul değerlerde olduğunu kontrol edin.", "priority": "low", "dependencies": [63, 78], "status": "pending", "subtasks": []}], "metadata": {"created": "2025-07-12T15:48:03.874Z", "updated": "2025-07-27T15:31:50.616Z", "description": "Tasks for development context"}}, "master": {"tasks": [{"id": 26, "title": "Backend Bağımlılıklarının <PERSON>u", "description": "Projenin backend'i için PRD'de belirtilen eksik NPM paketlerinin kurulması ve yapılandırılması. Bu, projenin temel işlevselliği için kritik bir adımdır.", "details": "Proje kök dizininde `npm install @google/generative-ai exceljs node-cache express-slow-down` komutunu çalıştırın. Paketlerin en son kararlı sürümlerinin yüklendiğinden emin olun. Kurulum sonrası `package.json` ve `package-lock.json` dosyalarını kontrol ederek bağımlılıkların doğru bir şekilde eklendiğini doğrulayın.", "testStrategy": "<PERSON>ü<PERSON> bağımlılıkların başar<PERSON>yla kurulduğunu ve `npm start` komutuyla uygulamanın hatasız başladığını doğrulayın. Her paketin temel bir fonksiyonunu (<PERSON><PERSON><PERSON><PERSON>, `node-cache` için bir değer set/get etme) test eden basit bir script yazın.", "priority": "high", "dependencies": [], "status": "pending", "subtasks": [{"id": 1, "title": "Temel NPM Paketlerinin Kurulumu", "description": "<PERSON><PERSON>nin backend'i <PERSON><PERSON><PERSON> g<PERSON> @google/generative-ai, exceljs, node-cache ve express-slow-down NPM paketlerinin kurulumunu gerçekleştirir.", "dependencies": [], "details": "Projenin `backend/` dizininde `npm install @google/generative-ai exceljs node-cache express-slow-down` komutunu çalıştırın. B<PERSON><PERSON><PERSON>, paketlerin en son kararlı sürümlerini `node_modules` dizinine yükleyecek ve `package.json` dosyasını güncelleyecektir. Tahmini Süre: 10 dakika. Kabul Kriterleri: Komutun hatasız bir şekilde tamamlanması ve `backend/node_modules` dizininde ilgili paket klasörlerinin oluşturulması. Risk Faktörleri: NPM registry'ye erişim sorunları, internet bağlantısı kesintileri.", "status": "pending", "testStrategy": "<PERSON><PERSON><PERSON> tamamlandıktan sonra `backend/` dizininde `npm list @google/generative-ai exceljs node-cache express-slow-down` komutunu çalıştırarak paketlerin ve sürümlerinin listelendiğini doğrulayın."}, {"id": 2, "title": "Bağımlılık Kurulumunun Doğrulanması ve Versiyon Kontrolü", "description": "Kurulum sonrası `package.json` ve `package-lock.json` dosyalarını kontrol ederek bağımlılıkların doğru bir şekilde eklendiğini ve sürümlerin kilitlendiğini doğrular.", "dependencies": [1], "details": "`backend/package.json` dosyasının `dependencies` bölümünü inceleyerek 4 yeni paketin eklendiğini kontrol edin. `backend/package-lock.json` dosyasında yapılan değişiklikleri gözden geçirerek kurulumun deterministik olduğundan emin olun. Ta<PERSON>ini <PERSON>üre: 5 dakika. Kabul Kriterleri: `package.json` dosyasında ilgili bağımlılıkların `^x.y.z` formatında yer alması. `package-lock.json` dosyasının güncellenmiş olması ve bu değişikliklerin versiyon kontrol sistemine (örn: Git) commit edilmeye hazır olması. Risk Faktörleri: `package-lock.json` dosyasındaki olası çakışmalar (conflicts).", "status": "pending", "testStrategy": "`backend/` dizininde `npm audit` komutunu çalıştırarak yeni eklenen paketlerde bilinen güvenlik açıkları olup olmadığını kontrol edin."}, {"id": 3, "title": "Rate Limiting için `express-slow-down` Yapılandırması", "description": "API'ye yönelik brute-force saldırılarını önlemek ve kaynak kullanımını dengelemek amacıyla `express-slow-down` paketini bir middleware olarak Express uygulamasına entegre eder.", "dependencies": [1], "details": "`backend/src/app.js` (veya ana sunuc<PERSON>) içine `const slowDown = require('express-slow-down');` satırını ekleyin. Ardından, `const apiLimiter = slowDown({ windowMs: 15 * 60 * 1000, delayAfter: 100, delayMs: () => 500 });` gibi bir yapılandırma oluşturun ve `app.use('/api/', apiLimiter);` ile belirli route'lara uygulayın. Tahmini Süre: 20 dakika. Kabul Kriterleri: Belirlenen istek limitini (delayAfter) aşan API çağrılarının yanıt süresinde gözle görülür bir gecikme (delayMs) olması. Risk Faktörleri: Yanlış yapılandırma sonucu tüm isteklerin yavaşlaması veya limitlendirmenin hiç çalışmaması.", "status": "pending", "testStrategy": "Bir test aracı (Postman, JMeter) veya script kullanarak bir API endpoint'ine kısa sürede 100'den fazla istek gönderin ve 101. istekten itibaren yanıt sürelerinin arttığını gözlemleyin."}, {"id": 4, "title": "`node-cache` <PERSON><PERSON><PERSON>ş Önbellek Servisi Oluşturma", "description": "Sık erişilen verileri bellekte tutarak veritabanı yükünü azaltmak için `node-cache` paketini kullanan yeniden kullanılabilir bir servis modülü oluşturur.", "dependencies": [1], "details": "`backend/src/services/cacheService.js` adında yeni bir dosya oluşturun. İçerisinde `const NodeCache = require('node-cache');` ile paketi import edin ve `const cache = new NodeCache({ stdTTL: 3600 });` gibi bir instance oluşturun. Bu instance'ı kullanarak `get`, `set`, `del` gibi temel önbellek fonksiyonlarını export eden bir modül tasarlayın. Tahmini Süre: 25 dakika. Kabul Kriterleri: `cacheService` mod<PERSON>lünün `set` ile veri ekleyip `get` ile bu veriyi başarıyla geri döndürmesi. Belirlenen TTL (Time-To-Live) süresi sonunda verinin otomatik olarak silinmesi. Risk Faktörleri: Bellek sızıntıları, yanlış TTL yapılandırması.", "status": "pending", "testStrategy": "<PERSON><PERSON><PERSON><PERSON><PERSON>n `cacheService` için Jest veya Mocha gibi bir test çatısı kullanarak birim testleri yazın. `set` ve `get` fonksiyonlarının doğru çalıştığını, `del` fonksiyonunun veriyi sildiğini ve TTL süresi dolunca `get` fonksiyonunun `undefined` döndürdüğünü test edin."}, {"id": 5, "title": "AI ve Excel Servisleri için <PERSON>langıç İskeletlerinin Oluşturulması", "description": "`@google/generative-ai` ve `exceljs` paketleri için gelecekteki geliştirmelere zemin hazırlayacak temel servis dosyalarını ve başlangıç yapılandırmalarını oluşturur.", "dependencies": [1], "details": "`backend/src/services/` dizini altında `aiService.js` ve `excelService.js` adında iki yeni dosya oluşturun. `aiService.js` içinde, `@google/generative-ai` paketini import edin ve API anahtarını `.env` dosyasından alacak şekilde temel bir client instance'ı oluşturun. `excelService.js` içinde, `exceljs` paketini import edin ve boş bir Excel dosyası oluşturup kaydedecek basit bir fonksiyon iskeleti (`createReport`) yazın. Tahmini Süre: 20 dakika. Kabul Kriterleri: Her iki servis dosyasının da ilgili paketleri hatasız bir şekilde import etmesi ve temel başlangıç yapılandırmalarını içermesi. Risk Faktörleri: API anahtarı gibi hassas bilgilerin koda yazılması (hard-coding).", "status": "pending", "testStrategy": "Projenin ana giriş noktasında (örn: `app.js`) bu yeni servis modüllerini `require` ile çağırarak import işleminin başarılı olduğunu ve herhangi bir başlangıç hatası vermediğini doğrulayın."}]}, {"id": 30, "title": "Frontend <PERSON><PERSON><PERSON> (File Picker) Arayüzü", "description": "React Native (Expo) uygulamasında, kullanıcının cihazından bir Excel dosyası seçmesini sağlayacak arayüz bileşeninin oluşturulması.", "details": "`expo-document-picker` k<PERSON><PERSON><PERSON><PERSON><PERSON>ini projeye ekleyin (`npx expo install expo-document-picker`). Kullanıcının dosya seçmesini tetikleyecek bir buton oluşturun. `DocumentPicker.getDocumentAsync()` fonksiyonunu kullanarak dosya seçiciyi açın. Yalnızca Excel dosyalarının (`application/vnd.ms-excel`, `application/vnd.openxmlformats-officedocument.spreadsheetml.sheet`) seçilmesine izin verin. Seçilen dosyanın URI'sini state'te saklayın.", "testStrategy": "Butona tıklandığında sistem dosya seçicisinin açıldığını doğrulayın. Bir Excel dosyası seçildiğinde, uygulamanın dosya bilgilerini (URI, isim, boyut) başarıyla aldığını kontrol edin. Excel dışı bir dosya seçmeye çalışıldığında ne olduğunu gözle<PERSON>in (ideal olarak filtrelenmelidir).", "priority": "high", "dependencies": [], "status": "pending", "subtasks": [{"id": 1, "title": "Bağımlılık Ku<PERSON>lumu: expo-document-picker", "description": "Projenin dosya seçici işlevselliği için gerek<PERSON> o<PERSON> `expo-document-picker` kütüphanesini kurmak ve projenin bağımlılıklarını güncellemek.", "dependencies": [], "details": "Projenin kök dizininde `npx expo install expo-document-picker` komutunu çalıştırın. Bu komut, `frontend/package.json` dosyasını güncelleyecektir. <PERSON><PERSON><PERSON> sonrası `npx expo start` ile projenin hatasız çalıştığını doğrulayın. Tahmini Süre: 15 dakika. Kabul Kriterleri: Kütüphane `package.json` içinde listelenmeli ve uygulama başarıyla derlenmelidir. Risk Faktörleri: Kütüphane sürüm uyumsuzlukları.", "status": "pending", "testStrategy": "`npx expo start` komutunu çalıştırarak uygulamanın başarıyla açıldığını ve kütüphane kurulumunun mevcut yapıyı bozmadığını kontrol edin."}, {"id": 2, "title": "Arayüz Bileşeni ve Buton Oluşturma", "description": "Kullanıcının dosya seçme işlemini başlatacağı bir buton içeren temel React Native bileşenini oluşturmak.", "dependencies": [1], "details": "`frontend/src/components/FilePicker.js` adında yeni bir dosya oluşturun. <PERSON>u dosya<PERSON>, `<PERSON><PERSON>` ve `View` kullanarak 'Excel Dosyası Seç' etiketli bir buton oluşturun. Bu bileşeni ana uygulama ekranına (`App.js` veya ilgili ekran) import ederek görünür hale getirin. Tahmini Süre: 30 dakika. Kabul Kriterleri: Buton, uygulama ekranında görünür ve tıklanabilir olmalıdır. Bu aşamada butona basıldığında bir işlevin çalışması beklenmez.", "status": "pending", "testStrategy": "Uygulamayı bir emülatörde veya fiziksel cihazda çalıştırın. Oluşturulan butonun ekranda doğru şekilde göründüğünü ve tıklandığında görsel bir geri bildirim verdiğini manuel olarak test edin."}, {"id": 3, "title": "Dosya Seçici Fonksiyonunun Entegrasyonu", "description": "Oluşturulan butona tıklandığında `expo-document-picker` k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> `getDocumentAsync` fonksiyonunu çağırarak cihazın dosya yöneticisini açmak ve Excel dosyalarını filtrelemek.", "dependencies": [2], "details": "`frontend/src/components/FilePicker.js` dosyasında, butonun `onPress` olay<PERSON>na `async` bir fonksiyon bağlayın. Bu fonksiyon içinde `DocumentPicker.getDocumentAsync()` fonksiyonunu `type` parametresi `['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet']` olacak şekilde çağırın. Tahmini Süre: 45 dakika. Kabul Kriterleri: Butona tıklandığında sistemin dosya seçici arayüzü açılmalı ve yalnızca Excel dosyaları (.xls, .xlsx) seçilebilir olmalıdır.", "status": "pending", "testStrategy": "Emülatör/cihaz üzerinde butona tıklayın. Dosya seçicinin açıldığını ve sadece Excel dosyalarının seçilebilir olduğunu doğrulayın. Diğer dosya türlerinin seçilemez (gri) olduğunu kontrol edin."}, {"id": 4, "title": "Seçilen Dosya Bilgisini State'te Saklama", "description": "Kullanıcı bir dos<PERSON>, `getDocumentAsync`'tan dönen dosya bilgi<PERSON>ini (URI, isim vb.) bileşenin state'inde saklamak.", "dependencies": [3], "details": "`FilePicker.js` bileşeninde `useState` hook'u ile `selectedFile` adında bir state değişkeni (`const [selectedFile, setSelectedFile] = useState(null);`) oluşturun. `getDocumentAsync`'tan dönen sonucun `canceled` alan<PERSON> `false` ise, dönen `assets[0]` nes<PERSON><PERSON> (URI, isim, boyut vb. içeren) `setSelectedFile` ile state'e kaydedin. Tahmini Süre: 30 dakika. Kabul Kriterleri: Bir Excel dosyası seçildiğinde, `selectedFile` state'i seçilen dosyanın bilgilerini içeren bir nesne ile güncellenmelidir.", "status": "pending", "testStrategy": "Bir dosya seçtikten sonra, React DevTools kullanarak veya ekrana geçici olarak `selectedFile` state'inin içeriğini yazdırarak state'in doğru URI ve dosya adı ile güncellendiğini doğrulayın. Seçim iptal edildiğinde state'in `null` kaldığını kontrol edin."}, {"id": 5, "title": "Seçilen Dosya Bilgisini Arayüzde Gösterme", "description": "<PERSON><PERSON><PERSON> seç<PERSON>i başarılı olduğunda, seç<PERSON>n dos<PERSON>ın adını arayüzde bir metin bileşeni ile kullanıcıya göstermek.", "dependencies": [4], "details": "`FilePicker.js` bileşeninin JSX kısmına koşull<PERSON> render mantığı ekleyin. `selectedFile` state'i dolu ise, bir `<Text>` bileşeni içinde `Seçilen Dosya: {selectedFile.name}` gibi bir metin gösterin. Tahmini <PERSON>üre: 20 dakika. Kabul Kriterleri: Kullanıcı bir dosya seçtiğinde, butonun altında veya yanında seçilen dosyanın adı görünmelidir. Başlangıçta veya seçim iptal edildiğinde bu metin görünmemelidir.", "status": "pending", "testStrategy": "Bir dosya seçin. Seçilen dosyanın adının ekranda doğru bir şekilde göründüğünü manuel olarak test edin. Uzun bir dosya adıyla test ederek arayüzün bozulmadığından emin olun."}]}, {"id": 31, "title": "Frontend Excel İşlevselliği Entegrasyonu", "description": "Frontend'de, Excel <PERSON>u indirme ve seçilen dosyayı içe aktarma işlemlerini yönetecek butonların ve ilgili API çağrılarının entegrasyonu.", "details": "Şablon indirme butonu için `/api/v1/data/template` endpoint'ine bir istek yapın ve gelen dosyayı kaydetmek için `expo-file-system` kullanın. İçe aktarma butonu için, `expo-document-picker` ile seçilen dosyayı `FormData` kullanarak `/api/v1/data/import` endpoint'ine POST isteği ile gönderin. API'den gelen yanıtı işleyerek kullanıcıya başarı veya hata mesajı gösterin.", "testStrategy": "Şablon indirme butonuna tıklandığında dosyanın başarıyla indirilip kaydedildiğini doğrulayın. Geçerli bir Excel dosyası seçip 'İçe Aktar' butonuna basıldığında API'ye isteğin gittiğini ve başarılı yanıt alındığında kullanıcıya bildirim gösterildiğini test edin.", "priority": "high", "dependencies": [30], "status": "pending", "subtasks": [{"id": 1, "title": "Gerekli Bağımlılıkların Kurulumu ve Yapılandırılması", "description": "Excel işlemleri için `expo-file-system` ve `expo-document-picker` kütüphanelerinin projeye eklenmesi ve yapılandırılması.", "dependencies": [], "details": "Projenin `frontend` dizininde `npm install expo-file-system expo-document-picker` komutunu çalıştırarak gerekli paketleri kurun. Kurulumun `frontend/package.json` dosyasına yansıdığını doğrulayın. B<PERSON> adım, dosya indirme ve seçme işlevselliği için temel altyapıyı oluşturur. Tahmini Süre: 1 saat. Kabul Kriterleri: Bağımlılıklar başarıyla kurulmalı ve proje hatasız bir şekilde derlenmelidir. Risk Faktörleri: Expo SDK sürümü ile kütüphane sürümleri arasında uyumsuzluk yaşanması.", "status": "pending", "testStrategy": "Bağımlılıklar kurulduktan sonra projenin iOS ve Android emülatörlerinde hatasız bir şekilde başlatılabildiğini doğrulayın."}, {"id": 2, "title": "Excel İşlemleri İçin Arayüz Bileşeninin Oluşturulması", "description": "'Şablon İndir' ve 'Veri İçe Aktar' butonlarını içerecek olan React Native arayüz bileşeninin oluşturulması ve ilgili ekrana yerleştirilmesi.", "dependencies": [1], "details": "`frontend/src/components/` dizini altında `ExcelActions.js` adında yeni bir bileşen oluşturun. Bu bileşen, 'Şablon İndir' ve 'Veri İçe Aktar' için iki adet `TouchableOpacity` içermelidir. Butonları projenin tasarım sistemine uygun şekilde stillendirin ve ilgili ekrana (örneğin, `frontend/src/screens/DataImportScreen.js`) entegre edin. Tahmini Süre: 2 saat. Kabul Kriterleri: Butonlar arayüzde doğru şekilde görü<PERSON>ülenmeli, tıklanabilir olmalı ve henüz işlevsellikleri atanmamış olmalıdır.", "status": "pending", "testStrategy": "Oluşturulan bileşenin farklı ekran boyutlarında doğru göründüğünü manuel olarak kontrol edin. Jest ve React Native Testing Library kullanarak bir snapshot testi oluşturun."}, {"id": 3, "title": "Excel Şablonu İndirme İşlevselliğinin Uygulanması", "description": "'Şablon İndir' butonuna tıklandığında `/api/v1/data/template` endpoint'inden şablon dosyasını indiren ve `expo-file-system` ile cihaza kaydeden mantığın eklenmesi.", "dependencies": [2], "details": "`ExcelActions.js` bileşenindeki 'Şablon İndir' butonunun `onPress` olay<PERSON>na, `/api/v1/data/template` endpoint'ine GET isteği yapan bir fonksiyon bağlayın. Gelen yanıtı `expo-file-system` kullanarak `FileSystem.documentDirectory` altına `sablom.xlsx` adıyla kaydedin. İşlem sırasında bir yükleme göstergesi (ActivityIndicator) gösterin. Tahmini Süre: 3 saat. Kabul Kriterleri: Butona tıklandığında dosya başarıyla indirilmeli ve kullanıcıya dosyanın kaydedildiğine dair bir bildirim gösterilmelidir.", "status": "pending", "testStrategy": "Manuel test: <PERSON>ona tıklayarak dosyanın cihazın dosya sistemine indiğini doğrulayın. API çağrısını `msw` veya `jest.mock` ile mock'layarak indirme fonksiyonu için birim testi yazın."}, {"id": 4, "title": "Excel Dosyası İçe Aktarma İşlevselliğinin Uygulanması", "description": "'Veri İçe Aktar' butonuna tıklandığında `expo-document-picker` ile dosya seçtiren ve seçilen dosyayı `/api/v1/data/import` endpoint'ine gönderen mantığın eklenmesi.", "dependencies": [2], "details": "`ExcelActions.js` bileşenindeki 'Veri İçe Aktar' butonunun `onPress` <PERSON><PERSON><PERSON><PERSON>, `DocumentPicker.getDocumentAsync()` metodunu çağıran bir fonksiyon bağlayın. Kullanıcının seçtiği dosyayı bir `FormData` nesnesine ekley<PERSON>k `/api/v1/data/import` endpoint'ine `multipart/form-data` olarak POST isteği gönderin. Tahmini Süre: 4 saat. Kabul Kriterleri: Kullanıcı bir Excel dosyası seçtiğinde, dosya başarıyla API'ye gönderilmelidir. Dosya seçimi iptal edildiğinde hata oluşmamalıdır.", "status": "pending", "testStrategy": "Manuel test: Geçerli ve geçersiz formatlardaki dosyaları seçerek içe aktarma işlemini deneyin. `expo-document-picker` ve API çağrısını mock'layarak dosya seçme ve gönderme mantığı için birim testi yazın."}, {"id": 5, "title": "API Yanıtlarının İşlenmesi ve Kullanıcı Bildirimleri", "description": "İndirme ve içe aktarma işlemlerinden dönen API yanıtlarına göre kullanıcıya başarı, hata veya bilgilendirme mesajlarının gösterilmesi ve yükleme durumlarının yönetilmesi.", "dependencies": [3, 4], "details": "`ExcelActions.js` bileşenine `loading`, `error` ve `success` durumlarını yönetmek için bir state (örn: `useState`) ekleyin. API çağrılarını `try...catch` blokları içine alın. Başarılı yanıtlarda (örn: 200 OK) kullanıcıya `Alert` veya bir toast bildirimi ile başarı mesajı gösterin. Hata durumlarında (örn: 400, 500) API'den gelen hata mesajını kullanıcıya gösterin. API isteği sırasında butonları devre dışı bırakın ve bir yükleme göstergesi gösterin. Tahmini Süre: 2 saat. Kabul Kriterleri: Tüm senaryolarda (başarı, sunucu hatası, geçersiz dosya, ağ hatası) kullanıcıya anlamlı ve net geri bildirimler verilmelidir.", "status": "pending", "testStrategy": "API yanıtlarını (başarılı, hatalı, beklenmedik format) mock'layarak tüm bildirim senaryolarının doğru çalıştığını doğrulayan birim testleri yazın. <PERSON> olarak cihazın internet bağlantısını keserek ağ hatası durumunu test edin."}]}, {"id": 32, "title": "Frontend İçe Aktarma İlerleme Göstergesi", "description": "Excel dosyasının yüklenmesi ve işlenmesi sırasında kullanıcıya görsel geri bildirim sağlamak için bir ilerleme göstergesi (progress indicator) eklenmesi.", "details": "<PERSON><PERSON>a yükleme is<PERSON>ğ<PERSON>, `axios` veya `fetch`'in yükleme ilerlemesini takip etme özel<PERSON>ğini (`onUploadProgress`) kullanın. Bu ilerleme verisini bir state'e kaydedin ve `react-native-progress` gibi bir kütü<PERSON>ne kull<PERSON> bir `Progress.Bar` bileşeni ile kullanıcıya gösterin. Yükleme tamamlandıktan sonra, backend'in dosyayı işlemesi için belirsiz bir yükleme animasyonu (ActivityIndicator) gösterin.", "testStrategy": "Büyük bir dosya yüklerken ilerleme çubuğunun %0'dan %100'e doğru dolduğunu görsel olarak doğrulayın. Yükleme bittikten sonra ve API yanıtı gelene kadar yükleme animasyonunun göründüğünü kontrol edin. İşlem tamamlandığında veya hata oluştuğunda tüm göstergelerin kaybolduğunu test edin.", "priority": "medium", "dependencies": [31], "status": "pending", "subtasks": [{"id": 1, "title": "react-native-progress Kütüphanesinin Kurulumu ve Yapılandırılması", "description": "Excel dosya yükleme ilerlemesini görsel olarak göstermek için kullanılacak olan `react-native-progress` kütüphanesinin frontend projesine eklenmesi.", "dependencies": [], "details": "<PERSON><PERSON> g<PERSON><PERSON>, il<PERSON><PERSON><PERSON> (progress bar) bileşenini kullanabilmek için gerekli altyapıyı hazırlar. \n- **<PERSON><PERSON><PERSON>:** `frontend/package.json`\n- **Kod Değişiklikleri:** <PERSON><PERSON><PERSON> ana dizininde `cd frontend` komutu çalıştırıldıktan sonra `npm install react-native-progress --save` veya `yarn add react-native-progress` komutu ile kütüphane kurulmalıdır. \n- **Tahmini Süre:** 0.5 saat\n- **Kabul Kriterleri:** Kurulum sonrası `frontend/package.json` dosyasının `dependencies` bölümünde `react-native-progress` ve versiyonu listelenmelidir. `npm install` veya `yarn` komutu hatasız çalışmalıdır.", "status": "pending", "testStrategy": "<PERSON><PERSON><PERSON><PERSON> başarılı olduğunu doğrulamak için, herhangi bir deneme ekranına `<Progress.Bar progress={0.3} width={200} />` bileşeni eklenir ve uygulamanın hata vermeden çalışıp bileşeni doğru şekilde render ettiği gözlemlenir."}, {"id": 2, "title": "İlerleme Göstergesi Arayüz Bileşenlerinin Eklenmesi", "description": "<PERSON><PERSON><PERSON> yükleme ekranına, yü<PERSON><PERSON> ilerlemesini gösterecek `Progress.Bar` ve backend işlemesini beklerken gösterilecek `ActivityIndicator` bileşenlerinin eklenmesi.", "dependencies": [1], "details": "Bu <PERSON><PERSON><PERSON><PERSON>, kullanı<PERSON>ı arayüzü ilerleme durumlarını yansıtacak şekilde güncellenir. \n- **<PERSON><PERSON><PERSON>:** `frontend/src/screens/ExcelImportScreen.js` (veya ilgili ekran bileşeni)\n- **Kod Değişiklikleri:** Ekrana `Progress.Bar` ve `ActivityIndicator` bileşenleri eklenir. Bu bileşenlerin görünürlüğünü ve ilerleme çubuğunun doluluk oranını kontrol etmek için `useState` hook'ları ile `progress` (0-1 arası sayı), `isUploading` (boolean) ve `isProcessing` (boolean) state'leri oluşturulur. JSX içinde bu state'lere bağlı koşullu render mantığı kurulur. \n- **Tahmini Süre:** 2 saat\n- **Kabul Kriterleri:** `isUploading` state'i `true` olduğunda `Progress.Bar` görün<PERSON><PERSON>, `isProcessing` state'i `true` olduğunda `ActivityIndicator` görünmelidir. State'ler `false` olduğunda bu bileşenler gizlenmelidir.", "status": "pending", "testStrategy": "React Developer Tools kullanılarak veya ekrana geçici butonlar eklenerek `isUploading`, `isProcessing` ve `progress` state'leri manuel olarak değiştirilir. Arayüzün bu değişikliklere doğru ve anlık olarak tepki verdiği doğrulanır."}, {"id": 3, "title": "Axios ile Dosya Yükleme İlerlemesinin Takibi ve State'e Aktarılması", "description": "Dosyayı backend'e gö<PERSON>en `axi<PERSON>` <PERSON><PERSON><PERSON><PERSON>, yü<PERSON><PERSON> ilerlemesini gerçek zamanlı olarak takip eden `onUploadProgress` konfigürasyonunun eklenmesi.", "dependencies": [2], "details": "Bu g<PERSON><PERSON><PERSON>, dosya yükleme işleminin teknik altyapısını oluşturur. \n- **<PERSON><PERSON><PERSON>:** `frontend/src/services/fileUploadService.js` (veya API çağrısının yapıldığı dosya)\n- **Kod <PERSON>ğişiklikleri:** Dosya yükleme fonksiyonundaki `axios.post` çağr<PERSON>s<PERSON>, `onUploadProgress` callback'ini içeren bir config nesnesi eklenir. Bu callback, `progressEvent` parametresinden `loaded` ve `total` değerlerini alarak ilerlemeyi `(progressEvent.loaded / progressEvent.total)` formülüyle hesaplar. Hesaplanan bu değer, bir callback fonksiyonu aracılığıyla `ExcelImportScreen` bileşenindeki `progress` state'ini günceller. İstek başlamadan önce `isUploading` state'i `true` olarak ayarlanır. \n- **<PERSON><PERSON><PERSON>:** 3 saat\n- **Kabul Kriterleri:** Kullanıcı dosyayı yüklemeye başladığında, `Progress.Bar` bileşeni ağ üzerinden gönderilen veri miktarına göre gerçek zamanlı olarak dolmalıdır.", "status": "pending", "testStrategy": "Büyük bir test dosyası (örn: 5-10 MB) kullanılır. Tarayıcı geliştirici araçlarının 'Network' sekmesindeki 'throttling' özelliği ile ağ hızı yavaşlatılarak ilerleme çubuğunun akıcı bir şekilde güncellendiği gözlemlenir. `onUploadProgress` içindeki hesaplanan değer `console.log` ile yazdırılarak doğruluğu kontrol edilir."}, {"id": 4, "title": "Yükleme Sonrası Backend İşleme Durumunun Yönetimi", "description": "Dosya yüklemesi %100'e ulaştığında `Progress.Bar`'ı gizleyip, backend'in dosyayı işlediğini belirten `ActivityIndicator`'ı gösterme mantığının implementasyonu.", "dependencies": [3], "details": "<PERSON>u ad<PERSON><PERSON>, yü<PERSON><PERSON> ve işleme adımları arasındaki geçişi yönetir. \n- **<PERSON><PERSON><PERSON>:** `frontend/src/screens/ExcelImportScreen.js`\n- **<PERSON><PERSON>şiklikleri:** `axios` iste<PERSON>i başar<PERSON>yla tama<PERSON>landığında (promise'in `.then()` bloğunda), `isUploading` state'i `false` ve `isProcessing` state'i `true` olarak ayarlanır. Bu durum değişikliği, arayüzde `Progress.Bar`'ın gizlenip `ActivityIndicator`'ın gösterilmesini tetikler. Backend'den işlemin bittiğine dair nihai yanıt gel<PERSON>, `isProcessing` state'i `false` yapılır ve `ActivityIndicator` gizlenir. \n- **Tahmini Süre:** 1.5 saat\n- **Kabul Kriterleri:** Dosya yüklemesi tamamlandığı anda ilerleme çubuğu kaybolmalı ve yerine dönen yükleme animasyonu (ActivityIndicator) görünmelidir. Backend'den yanıt geldiğinde bu animasyon da kaybolmalıdır.", "status": "pending", "testStrategy": "Backend API'sinde, yanıt göndermeden önce `setTimeout` ile 3-5 saniyelik yapay bir gecikme oluşturulur. Frontend'in bu bekleme süresi boyunca `ActivityIndicator`'ı doğru bir şekilde gö<PERSON>ip, yanıt geldikten sonra gizlediği kontrol edilir."}, {"id": 5, "title": "Hata Yönetimi ve Arayüzün Sıfırlanması", "description": "Dosya yükleme veya backend işleme sırasında oluşabilecek ağ veya sunucu hatalarını yakalayarak kullanıcıya anlamlı bir geri bildirim sunmak ve arayüzü temizlemek.", "dependencies": [4], "details": "Uygulamanın kararlılığını ve kullanıcı deneyimini artırmaya yönelik son adımdır. \n- **<PERSON><PERSON><PERSON>:** `frontend/src/screens/ExcelImportScreen.js`, `frontend/src/services/fileUploadService.js`\n- **<PERSON><PERSON>iklikleri:** `axios` iste<PERSON><PERSON> `try...catch` bloğu içine alınır. `catch` blo<PERSON><PERSON>, `isUploading` ve `isProcessing` state'leri `false` o<PERSON><PERSON> a<PERSON>ı<PERSON>, `progress` state'i `0`'a sıfırlanır. `react-native`'in `Alert` modülü veya bir toast/snackbar bileşeni kullanılarak kullanıcıya hatanın kaynağı hakkında bilgi verilir (örn: 'Dosya yüklenemedi, lütfen internet bağlantınızı kontrol edin.'). \n- **<PERSON><PERSON><PERSON>:** 2 saat\n- **Kabul Kriterleri:** Geçersiz bir <PERSON>yla, internet bağlantısı olmadan veya backend bir hata (örn: 500) döndürdüğünde işlem iptal edilmeli, tüm ilerleme göstergeleri gizlenmeli ve kullanıcıya bir hata mesajı gösterilmelidir. Arayüz, yeni bir denemeye hazır hale gelmelidir.", "status": "pending", "testStrategy": "İki senaryo test edilir: 1) Cihazın internet bağlantısı kesilerek yükleme başlatılır ve ağ hatasının doğru şekilde yakalandığı doğrulanır. 2) Backend endpoint'i geçici olarak 500 Internal Server <PERSON><PERSON><PERSON> döndürecek şekilde değiştirilir ve sunucu hatasının arayüzde doğru yönetildiği kontrol edilir."}]}, {"id": 33, "title": "AI Sistemi Hata Yö<PERSON>n (<PERSON><PERSON><PERSON>) İyileştirilmesi", "description": "Gemini AI API çağrıları etrafında kapsamlı hata yönetimi mekanizmaları eklenmesi. Bu, API hatalarını, ağ sorunlarını ve diğer beklenmedik durumları ele alacaktır.", "details": "`@google/generative-ai` k<PERSON><PERSON><PERSON><PERSON>nesi ile yapılan tüm API çağrılarını `try...catch` blokları içine alın. API'den dönen spesifik hata kodlarını (örn. rate limit, invalid API key, content safety) yakalayıp loglayın ve istemciye anlamlı hata mesajları döndürün. <PERSON><PERSON><PERSON><PERSON>, `e.message` içeriğini analiz ederek hatanın nedenini belirleyin.", "testStrategy": "<PERSON><PERSON><PERSON> test<PERSON>, Gemini API servisini mock'layarak farklı hata senaryoları (API hatası, timeout) oluşturun. Bu durumlarda sistemin çökmediğini ve beklenen hata yanıtını ürettiğini doğrulayın. Geçersiz bir API anahtarı ile test yaparak yetkilendirme hatasının doğru yönetildiğini kontrol edin.", "priority": "high", "dependencies": [26], "status": "pending", "subtasks": [{"id": 1, "title": "Özelleştirilmiş Hata Sınıflarının (Custom Error Classes) Oluşturulması", "description": "Uygulama genelinde standart bir hata yapısı oluşturmak için API, ağ ve doğrulama hatalarını temsil eden özelleştirilmiş hata sınıfları (örn: ApiError, NetworkError, ValidationError) tanımlanacaktır. Bu, hata yakalama ve işleme süreçlerini standartlaştıracaktır.", "dependencies": [], "details": "Yeni bir `utils/errorClasses.js` dosyası oluşturun. Bu dosyada, JavaScript'in temel `Error` sınıfından türeyen `ApiError`, `RateLimitError`, `ContentSafetyError` ve `GenericApiError` gibi sınıflar tanımlayın. Her sınıf, bir `message` ve bir `statusCode` (HTTP durum kodu) parametresi almalıdır. Bu, middleware katmanında hatanın türüne göre uygun HTTP yanıtını göndermeyi kolaylaştıracaktır.", "status": "pending", "testStrategy": "Her bir hata sını<PERSON><PERSON><PERSON><PERSON>n doğru `message` ve `statusCode` ile örneklenip örneklenemediğini kontrol eden birim testleri (unit tests) yazın."}, {"id": 2, "title": "aiService Katmanında Gemini API Hatalarının Yakalanması ve Yeniden Fırlatılması", "description": "`aiService.js` <PERSON><PERSON><PERSON><PERSON><PERSON>i Gemini API çağrıları, `try...catch` blokları ile sarmalanacak ve yakalanan hatalar, 34 numaralı görevde oluşturulan özelleştirilmiş hata sınıfları kullanılarak yeniden fırlatılacaktır. Bu, API'ye özgü hataları soyutlayarak uygulamanın geri kalanından gizler.", "dependencies": [], "details": "`aiService.js` dosyasındaki `@google/generative-ai` k<PERSON><PERSON><PERSON><PERSON><PERSON>ini kullanan tüm fonksiyonları güncelleyin. `catch (error)` b<PERSON><PERSON><PERSON>, `error.message` veya API'den dönen diğer hata detaylarını analiz edin. Örneğin, mesaj 'rate limit' içeriyorsa `throw new RateLimitError(...)`, 'API key not valid' içeriyorsa `throw new ApiError(...)`, içerik güvenliği ile ilgiliyse `throw new ContentSafetyError(...)` şeklinde ilgili özelleştirilmiş hatayı fırlatın. Diğer tüm beklenmedik API hataları için `GenericApiError` kullanın.", "status": "pending", "testStrategy": "`aiService`'in API çağrısını mock'layarak farklı hata senaryolarını (rate limit, invalid key vb.) simüle edin. Servisin bu durumlarda doğru özelleştirilmiş hata sınıfını fırlattığını doğrulayan birim testleri yazın."}, {"id": 3, "title": "Merkezi Hata Yönetimi Middleware'inin Oluşturulması", "description": "Uygulamadaki tüm hataları tek bir yerden yakalayıp işlemek için bir Express.js (veya kullanılan framework) error handling middleware'i oluşturulacaktır. Bu middleware, `aiController` ve diğer katmanlardan gelen hataları yönetecektir.", "dependencies": [], "details": "Projenin `middlewares` klasöründe `errorHandler.js` adında yeni bir dosya oluşturun. <PERSON><PERSON> dosya, `(err, req, res, next)` imzasını taşıyan bir fonksiyon ihraç etmelidir. Bu fonksiyon, uygulamanın ana dosyasına (örn: `app.js` veya `server.js`) tüm diğer route'lardan ve middleware'lerden sonra `app.use(errorHandler);` şeklinde eklenecektir. Başlangıçta, sadece gelen hatayı konsola loglayan ve genel bir 500 hatası döndüren temel bir iskelet oluşturun.", "status": "pending", "testStrategy": "Bu aşamada doğrudan test zordur. Bir sonraki görevle birlikte entegrasyon testi yapılacaktır."}, {"id": 4, "title": "Hata Middleware'i İçinde Hata Türüne Göre Yanıt Mantığının Geliştirilmesi", "description": "Oluşturulan merkezi hata middleware'i, gelen hatanın türü<PERSON> (`instanceof` ile) kontrol ederek uygun HTTP durum kodunu ve anlamlı bir JSON yanıtını istemciye döndürecek şekilde geliştirilecektir.", "dependencies": [], "details": "`errorHandler.js` middleware'ini güncelleyin. <PERSON><PERSON><PERSON> `err` parametresini kontrol edin:\n- `if (err instanceof ApiError)` ise, `res.status(err.statusCode).json({ success: false, message: err.message })` gibi bir yanıt döndürün.\n- `RateLimitError`, `ContentSafetyError` gibi diğer özel hatalar için benzer `if/else if` blokları ekleyin.\n- Tanımlı hatalardan hiçbiri değilse, genel bir 500 Internal Server Error yanıtı (`{ message: 'Sunucuda beklenmedik bir hata oluştu.' }`) döndürün. Ayrıca, üretim ortamı dışındayken hatanın `stack trace`'ini de loglayın.", "status": "pending", "testStrategy": "Middleware'i test etmek için sahte `req`, `res` ve `next` objeleri kullanan birim testleri yazın. Farklı türde hata nesneleri (ApiError, RateLimitError, standart Error) geçirerek middleware'in doğru `statusCode` ve JSON yanıtını oluşturduğunu doğrulayın."}, {"id": 5, "title": "aiController'ın Güncellenmesi ve Uçtan Uca Entegrasyon Testi", "description": "`aiController.js` <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> `try...catch` b<PERSON><PERSON><PERSON><PERSON>, hat<PERSON><PERSON><PERSON> do<PERSON><PERSON> `next(error)` fonksiyonuna yönlendirecek şekilde basitleştirilecektir. Bu, hataların merkezi middleware tarafından yakalanmasını sağlar. <PERSON>, tüm akışın doğru çalıştığını doğrulamak için entegrasyon testleri eklenecektir.", "dependencies": [], "details": "`aiController.js` içindeki tüm endpoint fonksiyonların<PERSON> gü<PERSON>n. `try` bloğu içinde `aiService`'ten gelen çağrıyı yapın. `catch (error)` bloğunda ise `res.status().json()` ile yanıt vermek yerine sadece `next(error);` çağrısı yapın. Bu, hatayı Express'in hata işleme zincirine devreder. Ardından, API endpoint'ine istek atarak hata senaryolarını (örn. geçersiz bir prompt göndererek `ContentSafetyError` tetiklemek) test eden entegrasyon testleri (Supertest gibi bir kütüphane ile) yazın ve dönen HTTP yanıtının ve içeriğinin beklendiği gibi olduğunu doğrulayın.", "status": "pending", "testStrategy": "Supertest veya benzeri bir kütüphane kullanarak API endpoint'lerine yönelik entegrasyon testleri yazın. API servisinin hata fırlatmasını mock'layarak, controller'ın hatayı `next()` ile doğ<PERSON> şekilde il<PERSON>ğini ve sonuç olarak merkezi `errorHandler`'ın beklenen HTTP yanıtını döndürdüğünü doğrulayın."}]}, {"id": 34, "title": "AI Sistemi i<PERSON> (Fallback) Mekanizması", "description": "AI servisinin yanıt vermemesi veya hata vermesi durumunda devreye girecek bir yedek (fallback) mekanizması oluşturulması.", "details": "Gemini API çağrısı başar<PERSON><PERSON><PERSON><PERSON>uğunda, `catch` bloğu içinde bir yedek yanıt oluşturun. B<PERSON>, önceden tanımlanmış statik bir cevap olabilir (ör. '<PERSON><PERSON><PERSON> z<PERSON> anda hizmet veremiyor, lü<PERSON><PERSON> daha sonra tekrar deneyin.') veya daha basit, kural tabanlı bir mantıkla üretilmiş bir sonuç olabilir. Bu, uygulamanın AI hatası nedeniyle tamamen işlevsiz kalmasını önler.", "testStrategy": "AI API'sini kasıtlı olarak başarısız olacak şekilde (ör. yanlış endpoint) yapılandırın. API çağrısı yapıldığında, sistemin ç<PERSON>ini ve bunun yerine tanımlanmış yedek yanıtı kullanıcıya döndürdüğünü doğrulayın.", "priority": "high", "dependencies": [33], "status": "pending", "subtasks": []}, {"id": 35, "title": "AI API Zaman Aşımı ve Yeniden Deneme Ayarları", "description": "AI API çağrıları için zaman a<PERSON>ı<PERSON>ı (timeout) süresinin PRD'de belirtilen 15 saniyeye çıkarılması ve yeniden deneme (retry) mantığı eklenmesi.", "details": "Gemini API çağrısını yaparken, `AbortController` ve `setTimeout` kullanarak bir 15 saniyelik zaman aşımı mekanizması uygulayın. Eğer 15 saniye içinde yanıt gelmezse, isteği iptal edin ve bir timeout hatası fırlatın. Geçici ağ hataları veya kısa süreli API sorunları için `async-retry` gibi bir kütüphane kullanarak üstel geri çekilme (exponential backoff) ile 1-2 kez yeniden deneme mantığı ekleyin.", "testStrategy": "Ağ trafiğini yavaşlatan bir araç (ör. Chrome DevTools network throttling) kullanarak veya API'yi gecikmeli yanıt verecek şekilde mock'layarak timeout senaryosunu test edin. 15 saniye sonra isteğin iptal edildiğini ve hata yakalandığını doğrulayın. Geçici bir hata senaryosunda yeniden deneme mekanizmasının çalıştığını loglardan kontrol edin.", "priority": "high", "dependencies": [33], "status": "pending", "subtasks": []}, {"id": 36, "title": "API Hız <PERSON> (Rate Limiting) Yapılandırması", "description": "API'ye yapılan istekleri yavaşlatmak ve kötüye kullanımı önlemek için `express-slow-down` middleware'inin yapılandırılması. AI endpoint'leri için daha esnek limitler belirlenmesi.", "details": "Uygulamanın ana `app.js` veya `server.js` dosyasına `express-slow-down` middleware'ini ekleyin. Genel bir kural belirleyin (ör. `windowMs: 15 * 60 * 1000`, `delayAfter: 100`, `delayMs: 500`). AI ile ilgili endpoint'ler (`/api/v1/ai/*`) için daha yüksek bir `delayAfter` de<PERSON><PERSON> (ör. 20) ile ayrı bir `slowDown` instance'ı uygulayarak bu endpoint'lerin daha az kısıtlanmasını sağlayın.", "testStrategy": "Bir test scripti veya Postman kullanarak kısa süre içinde bir endpoint'e çok sayıda istek gönderin. Belirlenen `delayAfter` limitini aştıktan sonra API yanıt sürelerinin `X-SlowDown-Delay` başlığı ile birlikte arttığını doğrulayın. AI endpoint'lerinin genel endpoint'lere göre daha toleranslı olduğunu test edin.", "priority": "medium", "dependencies": [26], "status": "pending", "subtasks": []}, {"id": 37, "title": "MongoDB Sorgu Optimizasyonu ve İndeksleme", "description": "Veritabanı sorgu performansını iyileştirmek için sık kullanılan alanlara (örneğin, kullanıcı ID'si, tarih a<PERSON>ıkları) MongoDB üzerinde indeksler eklenmesi.", "details": "Uygulamada en sık çalıştırılan sorguları belirleyin. Özellikle `find()` ve `aggregate()` sorgularında `filter` ve `sort` işlemlerinde kullanılan alanları tespit edin. `user_id`, `createdAt`, `date` gibi alanlar için MongoDB Atlas UI veya `createIndex()` komutu ile indeksler oluşturun. Örneğin: `db.collection.createIndex({ userId: 1, date: -1 })`.", "testStrategy": "İndeks eklenmeden önce ve sonra, `explain('executionStats')` komutunu kullanarak yavaş bir sorgunun performansını karşılaştırın. `totalDocsExamined` sayısının önemli ölçüde azaldığını ve sorgu süresinin <100ms hedefine yaklaştığını doğrulayın.", "priority": "high", "dependencies": [], "status": "pending", "subtasks": []}, {"id": 38, "title": "API Yanıtları için <PERSON>e (Caching) Mekanizması", "description": "Sık istenen ve nadiren değişen veriler için API yanıtlarını önbelleğe alarak veritabanı yükünü ve yanıt süresini azaltmak.", "details": "`node-cache` k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kullana<PERSON> bir `cache.service.js` oluşturun. Bu servis `get`, `set` ve `del` metodları sunmalıdır. Sık erişilen ama statik olan verileri döndüren API endpoint'le<PERSON> i<PERSON> (ör. <PERSON><PERSON><PERSON><PERSON> list<PERSON>, kate<PERSON><PERSON> listesi) bir middleware yazın. Bu middleware, istek geldiğinde önce cache'i kontrol etmeli, veri varsa cache'den döndürmeli, yoksa veritabanından alıp cache'e ekledikten sonra döndürmelidir.", "testStrategy": "Önbelleğe alınan bir endpoint'e Postman ile iki kez ardışık istek atın. İlk isteğin daha yavaş, ikinci isteğin ise çok daha hızlı (<50ms) olduğunu ve veritabanı sorgusu oluşturmadığını (loglardan kontrol ederek) doğrulayın. Veritabanında bir değişiklik yaptıktan sonra ilgili cache'in temizlendiğini ve endpoint'in güncel veriyi döndürdüğünü test edin.", "priority": "high", "dependencies": [26], "status": "pending", "subtasks": []}, {"id": 39, "title": "API Yanıt Süresi İzleme (Monitoring) Middleware'i", "description": "Her bir API isteğinin ne kadar sürede yanıt verdiğini izlemek için bir middleware eklenmesi. Bu, performans darboğazlarını tespit etmeye yardımcı olacaktır.", "details": "Basit bir Express middleware'i yazın. Middleware, istek başladığında `process.hrtime()` veya `Date.now()` ile ba<PERSON><PERSON><PERSON>ç zamanını kaydetmelidir. İstek tamamlandığında (`res.on('finish', ...)`), geçen süreyi hesaplayıp konsola veya bir log servisine yazdırmalıdır. Örneğin: `[GET] /api/v1/data - 25ms`. Daha gelişmiş bir çözüm için `express-status-monitor` gibi bir paket de kullanılabilir.", "testStrategy": "Uygulamayı çalıştırıp birkaç farklı endpoint'e istek gönderin. <PERSON><PERSON><PERSON> her istek için metod, URL ve yanıt süresini içeren logların göründüğünü doğrulayın. Yavaş bir endpoint'in daha yüksek bir süre ile loglandığını kontrol edin.", "priority": "medium", "dependencies": [], "status": "pending", "subtasks": []}, {"id": 40, "title": "Backend Bellek Kullanımı Optimizasyonu", "description": "Node.js uygulamasının bellek kullanımını analiz etmek ve olası bellek sızıntılarını veya verimsiz kullanımı tespit edip düzeltmek.", "details": "Uygulamayı `node --inspect` flag'i ile çalıştırın ve Chrome DevTools'un 'Memory' sek<PERSON><PERSON> kullanarak heap snapshot'ları alın. Özellikle büyük veri işleme (Excel import gibi) senaryolarından önce ve sonra snapshot alarak bellek artışını gözlemleyin. Global değişkenlerin, kapanmayan bağlantıların veya büyük nesnelerin gereksiz yere bellekte tutulup tutulmadığını kontrol edin. Gerekirse, büyük dosyaları işlerken stream'leri kullanarak bellek kullanımını optimize edin.", "testStrategy": "Yük testi araçları (ör. `autocannon`) ile uygulamaya yük bindirerek bellek kullanımını izleyin. Bellek kullanımının zamanla sürekli artıp artmadığını (bellek sızıntısı belirtisi) kontrol edin. Hedeflenen <150MB bellek kullanımını aşıp aşmadığını doğrulayın.", "priority": "medium", "dependencies": [], "status": "pending", "subtasks": []}, {"id": 41, "title": "Frontend Global Yükleme Durumu (Loading State) Yönetimi", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> gene<PERSON>, veri yüklenir<PERSON> veya bir işlem gerçekleştirilirken kullanıcıya görsel geri bildirim sağlamak için tutarlı yükleme durumu göstergeleri (loading states) eklenmesi.", "details": "React Context API veya bir state management kütüphanesi (Zustand, Redux) kullanarak global bir `isLoading` state'i oluşturun. API isteği başladığında bu state'i `true`, bittiğinde veya hata verdiğinde `false` yapın. Bu global state'e bağlı olarak, ekranın üzerinde ortaya çıkan bir overlay ve içinde `ActivityIndicator` bileşeni gösteren bir `GlobalLoader` bileşeni oluşturun ve bunu `App.js`'in en üst seviyesine ekleyin.", "testStrategy": "Uygulamanın farklı ekranlarında API isteği tetikleyen işlemleri (veri listeleme, form gönderme) gerçekleştirin. Her işlem sırasında global yükleme göstergesinin göründüğünü ve işlem tamamlandığında kaybolduğunu doğrulayın. %100 görünürlük hedefine ulaşıldığını kontrol edin.", "priority": "high", "dependencies": [], "status": "pending", "subtasks": []}, {"id": 42, "title": "Ağ Bağlantısı Dinleyicisi (NetInfo) Entegrasyonu", "description": "Cihazın internet bağlantısını dinleyerek çevrimdışı ve çevrimiçi durumları tespit etmek ve uygulama genelinde bu bilgiye erişim sağlamak.", "details": "`@react-native-community/netinfo` kütüphanesini projeye ekleyin. `App.js` içinde `NetInfo.addEventListener` kullanarak ağ durumundaki değişiklikleri dinleyin. Bağlantı durumunu (`isConnected`) global bir state'e (React Context veya Zustand) kaydedin. Bu, uygulamanın herhangi bir bileşeninden mevcut ağ durumunu kontrol etmeyi sağlar.", "testStrategy": "Emülatör/cihaz üzerinde Wi-Fi ve mobil veriyi açıp kapatarak test edin. Uygulamanın ağ durumu değişikliğini anında algıladığını ve global state'in güncellendiğini React DevTools ile doğrulayın.", "priority": "high", "dependencies": [], "status": "pending", "subtasks": []}, {"id": 43, "title": "Çevrimdışı (Offline) Gösterge Banner'ı", "description": "Cihaz çevrimdışı olduğunda kullanıcıyı bilgilendirmek için ekranın üst kısmında kalıcı bir bildirim (banner) gösterilmesi.", "details": "Ağ durumunu tutan global state'i dinleyen bir `OfflineIndicator` bileşeni oluşturun. Cihaz çevrimdışı olduğunda (`isConnected === false`), ekranın en üstünde 'İnternet bağlantısı yok' gibi bir mesaj içeren, dikkat çekici ama rahatsız etmeyen bir banner gösterin. Bağlantı geri geldiğinde bu banner otomatik olarak kaybolmalıdır.", "testStrategy": "Cihazın internet bağlantısını kesin. Çevrimdışı göstergesinin hemen göründüğünü doğrulayın. İnternet bağlantısını geri getirin ve göstergenin kaybolduğunu kontrol edin. Bu banner'ın uygulamanın kullanılabilirliğini engellemediğinden emin olun.", "priority": "medium", "dependencies": [42], "status": "pending", "subtasks": []}, {"id": 44, "title": "<PERSON><PERSON> (Network Error Recovery)", "description": "<PERSON>ğ hataları veya sunucuya ulaşılamaması durumunda, kullanıcıya sorunun ne olduğunu anlatan ve bir 'Tekrar Dene' seçeneği sunan bir mekanizma oluşturulması.", "details": "API çağrılarını yapan servis ka<PERSON>, `catch` b<PERSON><PERSON><PERSON> içinde ağ hatalarını (`TypeError: Network request failed`) tespit edin. Bu tür bir hata alındığında, kullanıcıya 'Bağlantı hatası oluştu. Lütfen internet bağlantınızı kontrol edip tekrar deneyin.' gibi bir mesaj gösteren bir modal veya tam ekran bir hata bileşeni tetikleyin. Bu bileşen, başarısız olan işlemi yeniden başlatan bir 'Tekrar Dene' butonu içermelidir.", "testStrategy": "Cihazı uçak moduna alıp veri getirmeye çalışan bir ekranı açın. Ağ hatası mesajının ve 'Tekrar Dene' butonunun göründüğünü doğrulayın. Uçak modunu kapatıp 'Tekrar Dene' butonuna bastığınızda verilerin başarıyla yüklendiğini test edin.", "priority": "high", "dependencies": [42], "status": "pending", "subtasks": []}, {"id": 45, "title": "Kullanıcı Dostu Hata Mesajları Standardizasyonu", "description": "Uygulama genelindeki tüm hata mesajlarının (API hataları, doğrulama hataları, vb.) kullanıcı dostu, anlaşılır ve tutarlı bir dilde olmasının sağlanması.", "details": "Tüm hata mesajlarını tek bir dosyada (`constants/messages.js`) toplayın. Teknik jargondan arındır<PERSON>lmış, basit ve çözüm odaklı mesajlar yazın. <PERSON><PERSON><PERSON><PERSON>, 'Internal Server Error' yerine '<PERSON><PERSON><PERSON> beklenmedik bir hata oluştu. Lütfen daha sonra tekrar deneyin.' gibi bir mesaj kullanın. Hata mesajlarını gösteren merkezi bir `Toast` veya `Alert` bileşeni oluşturarak tüm uygulamanın aynı stili kullanmasını sağlayın.", "testStrategy": "Farklı hata senaryolarını (geçersiz form giri<PERSON><PERSON>, sunucu hatası, yetkilendirme hatası) manuel olarak tetikleyin. Her senaryoda gösterilen mesajın merkezi mesajlar dosyasından gel<PERSON>, an<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ve kullanıcı dostu olduğunu doğrulayın. Mesajların görsel tutarlılığını kontrol edin.", "priority": "medium", "dependencies": [], "status": "pending", "subtasks": []}, {"id": 46, "title": "Veritabanı Sorgu Performans Testi ve Doğrulaması", "description": "Veritabanı sorgularının performansını ölçmek ve <100ms hedefine ulaşıldığını doğrulamak için testler yapılması.", "details": "MongoDB Atlas'taki 'Profiler' aracını veya `db.setProfilingLevel(2)` komutunu kullanarak yavaş sorguları loglayın. İndeks<PERSON>e sonrası, anahtar sorguları (kullanıcı verilerini listeleme, arama yapma vb.) manuel olarak veya bir script ile çalıştırarak `explain('executionStats')` ile performanslarını ölçün. Sonuçları belgeleyin ve 100ms'yi aşan sorguları tekrar optimize edin.", "testStrategy": "Test senaryoları çalıştırılırken MongoDB Profiler'dan gelen sonuçları analiz edin. `executionTimeMillis` değ<PERSON>nin tüm kritik sorgular için 100ms'nin altında olduğunu doğrulayın.", "priority": "high", "dependencies": [37], "status": "pending", "subtasks": []}, {"id": 47, "title": "Excel İçe Aktarma Performans Testi", "description": "Excel içe aktarma sürecinin performansını test ederek 10MB'a kadar olan dosyaların 30 saniye içinde işlenip işlenmediğini doğrulamak.", "details": "Fark<PERSON><PERSON> boyutlarda (1MB, 5MB, 10MB) ve farklı satır sayılarına sahip (1.000, 10.000, 50.000 satır) test Excel dosyaları oluşturun. Bu dosyaları kullanarak içe aktarma API'sini test edin ve isteğin başlangıcından veritabanına yazmanın tamamlanmasına kadar geçen süreyi ölçün. Süreyi loglayın ve 30 saniye hedefini aşıp aşmadığını kontrol edin.", "testStrategy": "10MB boyutunda bir test dosyası ile içe aktarma işlemini başlatın. Sunucu loglarından veya API yanıt süresinden toplam işlem süresinin 30 saniyenin altında olduğunu doğrulayın. İşlem sırasında sunucu kaynaklarının (CPU, bellek) aşırı tüketilmediğini izleyin.", "priority": "high", "dependencies": [31], "status": "pending", "subtasks": []}, {"id": 48, "title": "AI Sistemi Yanıt Süresi ve Başarı Oranı Testi", "description": "AI sisteminin yanıt süresini ve başarı oranını ölçmek için testler yapılması.", "details": "AI endpoint'ine 100 adet ardışık (aralarda küçük bekleme süreleri ile) test isteği gönderen bir script yazın. Her isteğin yanıt süresini ve başarı durumunu (HTTP 200 veya hata) kaydedin. Ortalama ve maksimum yanıt sürelerini hesaplayın. Başarılı isteklerin sayısını toplam istek sayısına bölerek başarı oranını bulun.", "testStrategy": "Test scriptini çalıştırdıktan sonra sonuçları analiz edin. Ortalama yanıt süresinin 15 saniyenin altında olduğunu ve başarı oranının %95'in üzerinde olduğunu doğrulayın. Başarısız olan isteklerin nedenlerini loglardan inceleyin.", "priority": "high", "dependencies": [35], "status": "pending", "subtasks": []}, {"id": 49, "title": "Uygulama Başlangıç Süresi Performans Testi", "description": "Uygulamanın başlangıç (startup) süresini ölçmek ve <3 saniye hedefine uygunluğunu kontrol etmek.", "details": "React Native'in dahili performans araçlarını veya `react-native-startup-time` gibi bir kütüphaneyi kullanarak uygulamanın TTI (Time to Interactive) süresini ölçün. Hem 'cold start' (uygulama kapalıyken ilk açılış) hem de 'warm start' (uygulama arkaplandayken açılış) sürelerini ölçün. Özellikle 'cold start' süresini optimize etmek için gereksiz kütüphanelerin veya senkron işlemlerin başlangıçtan kaldırılmasını değerlendirin.", "testStrategy": "Uygulamayı gerçek bir cihazda (hem Android hem iOS) birkaç kez tamamen kapatıp açarak 'cold start' süresini ölçün. Ortalama sürenin 3 saniyenin altında olduğunu doğrulayın. Gerekirse, bundle boyutunu analiz etmek için `react-native-bundle-visualizer` kullanın.", "priority": "medium", "dependencies": [], "status": "pending", "subtasks": []}, {"id": 50, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON> Son Test ve Kalite Kontrol", "description": "Tüm kritik ve yüksek öncelikli geliştirmeler tamamlandıktan sonra, projenin genel bir kalite kontrolünden geçirilmesi ve sürüm adayının hazırlanması.", "details": "<PERSON><PERSON><PERSON> ka<PERSON> (Excel, AI, Performans, UX) kapsayan bir test senaryosu listesi hazırlayın. Bu senaryoları hem Android hem de iOS platformlarında, farklı cihazlarda manuel olarak test edin. Bulunan hataları (bug) kaydedin ve önceliklendirin. Tüm kritik hatalar çözüldükten sonra, uygulama mağazalarına gönderilecek sürüm paketini (build) oluşturun.", "testStrategy": "Test ekibi veya geliştiriciler tarafından tüm test senaryolarının başarıyla tamamlandığını doğrulayın. Hata oranının <%1 olduğunu ve kullanıcı memnuniyeti anketlerinde (eğer varsa) hedeflenen puana ulaşıldığını kontrol edin. Son sürüm adayının kararlı olduğunu ve yayınlanmaya hazır olduğunu onaylayın.", "priority": "medium", "dependencies": [26, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49], "status": "pending", "subtasks": []}], "metadata": {"created": "2025-07-12T15:48:03.874Z", "updated": "2025-07-12T17:12:01.182Z", "description": "Tasks for development context"}}}