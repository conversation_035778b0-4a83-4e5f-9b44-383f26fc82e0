# Task ID: 53
# Title: Frontend Excel Dışa Aktarma İşlevselliği
# Status: pending
# Dependencies: 52, 41, 45
# Priority: medium
# Description: Ayarlar ekranındaki dışa aktarma butonunu işlevsel hale getirmek. <PERSON><PERSON> g<PERSON><PERSON><PERSON>, kullanı<PERSON>ının belirlediği tarih aralığı ve kategorilere göre gider verilerini backend'den alıp Excel formatında cihaza indirmesini sağlar.
# Details:
SettingsScreen bileşenine tarih aralığı seçimi için iki adet 'DatePicker' ve kategori filtrelemesi için bir 'MultiSelect' bileşeni ekleyin. Bu bileşenlerin durumlarını (state) yönetin. 'Dışa Aktar' butonunun 'onPress' olayında, seçilen tarih aralığı ve kategorileri query parametreleri olarak içeren bir API isteği oluşturun (örn: /api/v1/expenses/export?startDate=...&endDate=...&categories=...). İstek gönderilirken Task 41'de oluşturulan global yükleme durumunu aktif hale getirin. Backend'den dönen dosya blob'unu, Task 52'de kullanılan yönteme benzer şekilde, 'expo-file-system' ile geçici bir dosyaya yazın ve 'expo-sharing' ile kullanıcıya paylaşma/kaydetme seçeneği sunun. Dosya adını dinamik olarak oluşturun (örn: 'gider-raporu-2023-10-26.xlsx'). API çağrısı sırasında oluşabilecek hataları yakalamak için 'try...catch' bloğu kullanın ve Task 45'te standartlaştırılan kullanıcı dostu hata mesajlarını gösterin.

# Test Strategy:
Ayarlar ekranına gidin ve tarih aralığı ile kategori filtreleme bileşenlerinin doğru şekilde göründüğünü doğrulayın. Bir tarih aralığı ve birkaç kategori seçin. 'Dışa Aktar' butonuna tıklayın. Global yükleme göstergesinin (Task 41) belirdiğini kontrol edin. Ağ trafiğini izleyerek, doğru endpoint'e seçilen filtre parametreleriyle birlikte bir GET isteği yapıldığını doğrulayın. İstek başarılı olduğunda, sistemin dosyayı kaydetme/paylaşma diyaloğunu açtığını ve indirilen Excel dosyasının yalnızca filtrelenmiş verileri içerdiğini kontrol edin. Backend'i durdurarak veya geçersiz parametreler göndererek bir hata senaryosu oluşturun ve Task 45'e uygun, anlaşılır bir hata mesajının gösterildiğini doğrulayın.
