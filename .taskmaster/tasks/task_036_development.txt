# Task ID: 36
# Title: API Hız Sı<PERSON>rl<PERSON> (Rate Limiting) Yapılandırması
# Status: pending
# Dependencies: 26
# Priority: medium
# Description: API'ye yapılan istekleri yavaşlatmak ve kötüye kullanımı önlemek için `express-slow-down` middleware'inin yapılandırılması. AI endpoint'leri için daha esnek limitler belirlenmesi.
# Details:
Uygulamanın ana `app.js` veya `server.js` dosyasına `express-slow-down` middleware'ini ekleyin. Genel bir kural belirleyin (ör. `windowMs: 15 * 60 * 1000`, `delayAfter: 100`, `delayMs: 500`). AI ile ilgili endpoint'ler (`/api/v1/ai/*`) için daha yüksek bir `delayAfter` değeri (ör. 20) ile ayrı bir `slowDown` instance'ı uygulayarak bu endpoint'lerin daha az kısıtlanmasını sağlayın.

# Test Strategy:
Bir test scripti veya Postman kullanarak kısa süre içinde bir endpoint'e çok sayıda istek gönderin. Belirlenen `delayAfter` limitini aştıktan sonra API yanıt sürelerinin `X-SlowDown-Delay` başlığı ile birlikte arttığını doğrulayın. AI endpoint'lerinin genel endpoint'lere göre daha toleranslı olduğunu test edin.
