# Task ID: 42
# Title: <PERSON><PERSON>si (NetInfo) Entegrasyonu
# Status: pending
# Dependencies: None
# Priority: high
# Description: Cihazın internet bağlantısını dinleyerek çevrimdışı ve çevrimiçi durumları tespit etmek ve uygulama genelinde bu bilgiye erişim sağlamak.
# Details:
`@react-native-community/netinfo` kütüphanesini projeye ekleyin. `App.js` içinde `NetInfo.addEventListener` kullanarak ağ durumundaki değişiklikleri dinleyin. Bağlantı durumunu (`isConnected`) global bir state'e (React Context veya Zustand) kaydedin. Bu, uygulamanın herhangi bir bileşeninden mevcut ağ durumunu kontrol etmeyi sağlar.

# Test Strategy:
Emülatör/cihaz üzerinde Wi-Fi ve mobil veriyi açıp kapatarak test edin. Uygulamanın ağ durumu değişikliğini anında algıladığını ve global state'in güncellendiğini React DevTools ile doğrulayın.
