# Task ID: 33
# Title: AI Sistemi Hata Yönetiminin (Error <PERSON>ling) İyileştirilmesi
# Status: pending
# Dependencies: 26
# Priority: high
# Description: Gemini AI API çağrıları etrafında kapsamlı hata yönetimi mekanizmaları eklenmesi. Bu, API hatalarını, ağ sorunlarını ve diğer beklenmedik durumları ele alacaktır.
# Details:
`@google/generative-ai` kütüphanesi ile yapılan tüm API çağrılarını `try...catch` blokları içine alın. API'den dönen spesifik hata kodlarını (örn. rate limit, invalid API key, content safety) yakalayıp loglayın ve istemciye anlamlı hata mesajları döndürün. <PERSON><PERSON><PERSON><PERSON>, `e.message` içeriğini analiz ederek hatanın nedenini belirleyin.

# Test Strategy:
<PERSON><PERSON><PERSON> test<PERSON>, Gemini API servisini mock'layarak farklı hata senaryoları (API hatası, timeout) oluşturun. Bu durumlarda sistemin çökmediğini ve beklenen hata yanıtını ürettiğini doğrulayın. Geçersiz bir API anahtarı ile test yaparak yetkilendirme hatasının doğru yönetildiğini kontrol edin.
