# Task ID: 44
# Title: <PERSON><PERSON> (Network Error Recovery)
# Status: pending
# Dependencies: 42
# Priority: high
# Description: A<PERSON> hataları veya sunucuya ulaşılamaması durumunda, kullanıcıya sorunun ne olduğunu anlatan ve bir 'Tekrar Dene' seçeneği sunan bir mekanizma oluşturulması.
# Details:
API çağrılarını yapan servis katmanında, `catch` bloğu içinde ağ hatalarını (`TypeError: Network request failed`) tespit edin. Bu tür bir hata alındığında, kullanıcıya 'Bağlantı hatası oluştu. Lütfen internet bağlantınızı kontrol edip tekrar deneyin.' gibi bir mesaj gösteren bir modal veya tam ekran bir hata bileşeni tetikleyin. Bu bileşen, başarıs<PERSON>z olan işlemi yeniden başlatan bir 'Tekrar Dene' butonu içermelidir.

# Test Strategy:
Cihazı uçak moduna alıp veri getirmeye çalışan bir ekranı açın. Ağ hatası mesajının ve 'Tekrar Dene' butonunun göründüğünü doğrulayın. Uçak modunu kapatıp 'Tekrar Dene' butonuna bastığınızda verilerin başarıyla yüklendiğini test edin.
