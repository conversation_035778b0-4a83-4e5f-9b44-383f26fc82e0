# Task ID: 30
# Title: Frontend Do<PERSON>a Seçici (File Picker) Arayüzü
# Status: pending
# Dependencies: None
# Priority: high
# Description: React Native (Expo) uygulamasında, kullanıcının cihazından bir Excel dosyası seçmesini sağlayacak arayüz bileşeninin oluşturulması.
# Details:
`expo-document-picker` kütüphanesini projeye ekleyin (`npx expo install expo-document-picker`). Kullanıcının dosya seçmesini tetikleyecek bir buton oluşturun. `DocumentPicker.getDocumentAsync()` fonksiyonunu kullanarak dosya seçiciyi açın. Yalnızca Excel dosyalarının (`application/vnd.ms-excel`, `application/vnd.openxmlformats-officedocument.spreadsheetml.sheet`) seçilmesine izin verin. Seçilen dosyanın URI'sini state'te saklayın.

# Test Strategy:
Butona tıklandığında sistem dosya seçicisinin açıldığını doğrulayın. Bir Excel dosyası seçildiğinde, uygulamanın dosya bilgilerini (URI, isim, boyut) başarıyla aldığını kontrol edin. Excel dışı bir dosya seçmeye çalışıldığında ne olduğunu gözlemleyin (ideal olarak filtrelenmelidir).

# Subtasks:
## 1. Bağımlılık Kurulumu: expo-document-picker [pending]
### Dependencies: None
### Description: Projenin dosya seçici işlevselliği için gerekli olan `expo-document-picker` kütüphanesini kurmak ve projenin bağımlılıklarını güncellemek.
### Details:
Projenin kök dizininde `npx expo install expo-document-picker` komutunu çalıştırın. Bu komut, `frontend/package.json` dosyasını güncelleyecektir. Kurulum sonrası `npx expo start` ile projenin hatasız çalıştığını doğrulayın. Tahmini Süre: 15 dakika. Kabul Kriterleri: Kütüphane `package.json` içinde listelenmeli ve uygulama başarıyla derlenmelidir. Risk Faktörleri: Kütüphane sürüm uyumsuzlukları.

## 2. Arayüz Bileşeni ve Buton Oluşturma [pending]
### Dependencies: 30.1
### Description: Kullanıcının dosya seçme işlemini başlatacağı bir buton içeren temel React Native bileşenini oluşturmak.
### Details:
`frontend/src/components/FilePicker.js` adında yeni bir dosya oluşturun. Bu dosyada, `Button` ve `View` kullanarak 'Excel Dosyası Seç' etiketli bir buton oluşturun. Bu bileşeni ana uygulama ekranına (`App.js` veya ilgili ekran) import ederek görünür hale getirin. Tahmini Süre: 30 dakika. Kabul Kriterleri: Buton, uygulama ekranında görünür ve tıklanabilir olmalıdır. Bu aşamada butona basıldığında bir işlevin çalışması beklenmez.

## 3. Dosya Seçici Fonksiyonunun Entegrasyonu [pending]
### Dependencies: 30.2
### Description: Oluşturulan butona tıklandığında `expo-document-picker` kütüphanesinin `getDocumentAsync` fonksiyonunu çağırarak cihazın dosya yöneticisini açmak ve Excel dosyalarını filtrelemek.
### Details:
`frontend/src/components/FilePicker.js` dosyasında, butonun `onPress` olayına `async` bir fonksiyon bağlayın. Bu fonksiyon içinde `DocumentPicker.getDocumentAsync()` fonksiyonunu `type` parametresi `['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet']` olacak şekilde çağırın. Tahmini Süre: 45 dakika. Kabul Kriterleri: Butona tıklandığında sistemin dosya seçici arayüzü açılmalı ve yalnızca Excel dosyaları (.xls, .xlsx) seçilebilir olmalıdır.

## 4. Seçilen Dosya Bilgisini State'te Saklama [pending]
### Dependencies: 30.3
### Description: Kullanıcı bir dosya seçtiğinde, `getDocumentAsync`'tan dönen dosya bilgilerini (URI, isim vb.) bileşenin state'inde saklamak.
### Details:
`FilePicker.js` bileşeninde `useState` hook'u ile `selectedFile` adında bir state değişkeni (`const [selectedFile, setSelectedFile] = useState(null);`) oluşturun. `getDocumentAsync`'tan dönen sonucun `canceled` alanı `false` ise, dönen `assets[0]` nesnesini (URI, isim, boyut vb. içeren) `setSelectedFile` ile state'e kaydedin. Tahmini Süre: 30 dakika. Kabul Kriterleri: Bir Excel dosyası seçildiğinde, `selectedFile` state'i seçilen dosyanın bilgilerini içeren bir nesne ile güncellenmelidir.

## 5. Seçilen Dosya Bilgisini Arayüzde Gösterme [pending]
### Dependencies: 30.4
### Description: Dosya seçimi başarılı olduğunda, seçilen dosyanın adını arayüzde bir metin bileşeni ile kullanıcıya göstermek.
### Details:
`FilePicker.js` bileşeninin JSX kısmına koşullu render mantığı ekleyin. `selectedFile` state'i dolu ise, bir `<Text>` bileşeni içinde `Seçilen Dosya: {selectedFile.name}` gibi bir metin gösterin. Tahmini Süre: 20 dakika. Kabul Kriterleri: Kullanıcı bir dosya seçtiğinde, butonun altında veya yanında seçilen dosyanın adı görünmelidir. Başlangıçta veya seçim iptal edildiğinde bu metin görünmemelidir.

