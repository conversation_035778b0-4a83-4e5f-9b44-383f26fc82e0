# Task ID: 34
# Title: AI Sistemi <PERSON> (Fallback) Mekanizması
# Status: pending
# Dependencies: 33
# Priority: high
# Description: AI servisinin yanıt vermemesi veya hata vermesi durumunda devreye girecek bir yedek (fallback) mekanizması oluşturulması.
# Details:
Gemini API çağrısı başar<PERSON><PERSON><PERSON>z old<PERSON>unda, `catch` bloğu içinde bir yedek yanıt oluşturun. Bu, önceden tanımlanmış statik bir cevap olabilir (ör. 'Yapay zeka şu anda hizmet veremiyor, lütfen daha sonra tekrar deneyin.') veya daha basit, kural tabanlı bir mantıkla üretilmiş bir sonuç olabilir. Bu, uygulamanın AI hatası nedeniyle tamamen işlevsiz kalmasını önler.

# Test Strategy:
AI API'sini kasıtlı olarak başarısız olacak şekilde (ör. yan<PERSON><PERSON><PERSON> endpoint) ya<PERSON><PERSON>. API çağrısı yapıldığında, si<PERSON><PERSON>ökmediğini ve bunun yerine tanımlanmış yedek yanıtı kullanıcıya döndürdüğünü doğrulayın.
