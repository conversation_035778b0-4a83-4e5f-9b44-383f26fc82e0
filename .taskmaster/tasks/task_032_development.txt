# Task ID: 32
# Title: Frontend İçe Aktarma İlerleme Göstergesi
# Status: pending
# Dependencies: 31
# Priority: medium
# Description: Excel dosyasının yüklenmesi ve işlenmesi sırasında kullanıcıya görsel geri bildirim sağlamak için bir ilerleme göstergesi (progress indicator) eklenmesi.
# Details:
Dosya yükleme isteğ<PERSON> g<PERSON>, `axios` veya `fetch`'in yükleme ilerlemesini takip etme <PERSON> (`onUploadProgress`) kullanın. Bu ilerleme verisini bir state'e kaydedin ve `react-native-progress` gibi bir kütüphane kullanarak bir `Progress.Bar` bileşeni ile kullanıcıya gösterin. Yükleme tamamlandıktan sonra, backend'in dosyayı işlemesi için belirsiz bir yükleme animasyonu (ActivityIndicator) gösterin.

# Test Strategy:
Büyük bir dosya yüklerken ilerleme çubuğunun %0'dan %100'e doğru dolduğunu görsel olarak doğrulayın. Yükleme bittikten sonra ve API yanıtı gelene kadar yükleme animasyonunun göründüğünü kontrol edin. İşlem tamamlandığında veya hata oluştuğunda tüm göstergelerin kaybolduğunu test edin.

# Subtasks:
## 1. react-native-progress Kütüphanesinin Kurulumu ve Yapılandırılması [pending]
### Dependencies: None
### Description: Excel dosya yükleme ilerlemesini görsel olarak göstermek için kullanılacak olan `react-native-progress` kütüphanesinin frontend projesine eklenmesi.
### Details:
Bu görev, ilerleme çubuğu (progress bar) bileşenini kullanabilmek için gerekli altyapıyı hazırlar. 
- **Dosya Yolları:** `frontend/package.json`
- **Kod Değişiklikleri:** Projenin ana dizininde `cd frontend` komutu çalıştırıldıktan sonra `npm install react-native-progress --save` veya `yarn add react-native-progress` komutu ile kütüphane kurulmalıdır. 
- **Tahmini Süre:** 0.5 saat
- **Kabul Kriterleri:** Kurulum sonrası `frontend/package.json` dosyasının `dependencies` bölümünde `react-native-progress` ve versiyonu listelenmelidir. `npm install` veya `yarn` komutu hatasız çalışmalıdır.

## 2. İlerleme Göstergesi Arayüz Bileşenlerinin Eklenmesi [pending]
### Dependencies: 32.1
### Description: Dosya yükleme ekranına, yükleme ilerlemesini gösterecek `Progress.Bar` ve backend işlemesini beklerken gösterilecek `ActivityIndicator` bileşenlerinin eklenmesi.
### Details:
Bu adımda, kullanıcı arayüzü ilerleme durumlarını yansıtacak şekilde güncellenir. 
- **Dosya Yolları:** `frontend/src/screens/ExcelImportScreen.js` (veya ilgili ekran bileşeni)
- **Kod Değişiklikleri:** Ekrana `Progress.Bar` ve `ActivityIndicator` bileşenleri eklenir. Bu bileşenlerin görünürlüğünü ve ilerleme çubuğunun doluluk oranını kontrol etmek için `useState` hook'ları ile `progress` (0-1 arası sayı), `isUploading` (boolean) ve `isProcessing` (boolean) state'leri oluşturulur. JSX içinde bu state'lere bağlı koşullu render mantığı kurulur. 
- **Tahmini Süre:** 2 saat
- **Kabul Kriterleri:** `isUploading` state'i `true` olduğunda `Progress.Bar` görünmeli, `isProcessing` state'i `true` olduğunda `ActivityIndicator` görünmelidir. State'ler `false` olduğunda bu bileşenler gizlenmelidir.

## 3. Axios ile Dosya Yükleme İlerlemesinin Takibi ve State'e Aktarılması [pending]
### Dependencies: 32.2
### Description: Dosyayı backend'e gönderen `axios` isteğine, yükleme ilerlemesini gerçek zamanlı olarak takip eden `onUploadProgress` konfigürasyonunun eklenmesi.
### Details:
Bu görev, dosya yükleme işleminin teknik altyapısını oluşturur. 
- **Dosya Yolları:** `frontend/src/services/fileUploadService.js` (veya API çağrısının yapıldığı dosya)
- **Kod Değişiklikleri:** Dosya yükleme fonksiyonundaki `axios.post` çağrısına, `onUploadProgress` callback'ini içeren bir config nesnesi eklenir. Bu callback, `progressEvent` parametresinden `loaded` ve `total` değerlerini alarak ilerlemeyi `(progressEvent.loaded / progressEvent.total)` formülüyle hesaplar. Hesaplanan bu değer, bir callback fonksiyonu aracılığıyla `ExcelImportScreen` bileşenindeki `progress` state'ini günceller. İstek başlamadan önce `isUploading` state'i `true` olarak ayarlanır. 
- **Tahmini Süre:** 3 saat
- **Kabul Kriterleri:** Kullanıcı dosyayı yüklemeye başladığında, `Progress.Bar` bileşeni ağ üzerinden gönderilen veri miktarına göre gerçek zamanlı olarak dolmalıdır.

## 4. Yükleme Sonrası Backend İşleme Durumunun Yönetimi [pending]
### Dependencies: 32.3
### Description: Dosya yüklemesi %100'e ulaştığında `Progress.Bar`'ı gizleyip, backend'in dosyayı işlediğini belirten `ActivityIndicator`'ı gösterme mantığının implementasyonu.
### Details:
Bu adım, yükleme ve işleme adımları arasındaki geçişi yönetir. 
- **Dosya Yolları:** `frontend/src/screens/ExcelImportScreen.js`
- **Kod Değişiklikleri:** `axios` isteği başarıyla tamamlandığında (promise'in `.then()` bloğunda), `isUploading` state'i `false` ve `isProcessing` state'i `true` olarak ayarlanır. Bu durum değişikliği, arayüzde `Progress.Bar`'ın gizlenip `ActivityIndicator`'ın gösterilmesini tetikler. Backend'den işlemin bittiğine dair nihai yanıt geldiğinde, `isProcessing` state'i `false` yapılır ve `ActivityIndicator` gizlenir. 
- **Tahmini Süre:** 1.5 saat
- **Kabul Kriterleri:** Dosya yüklemesi tamamlandığı anda ilerleme çubuğu kaybolmalı ve yerine dönen yükleme animasyonu (ActivityIndicator) görünmelidir. Backend'den yanıt geldiğinde bu animasyon da kaybolmalıdır.

## 5. Hata Yönetimi ve Arayüzün Sıfırlanması [pending]
### Dependencies: 32.4
### Description: Dosya yükleme veya backend işleme sırasında oluşabilecek ağ veya sunucu hatalarını yakalayarak kullanıcıya anlamlı bir geri bildirim sunmak ve arayüzü temizlemek.
### Details:
Uygulamanın kararlılığını ve kullanıcı deneyimini artırmaya yönelik son adımdır. 
- **Dosya Yolları:** `frontend/src/screens/ExcelImportScreen.js`, `frontend/src/services/fileUploadService.js`
- **Kod Değişiklikleri:** `axios` isteği `try...catch` bloğu içine alınır. `catch` bloğunda, `isUploading` ve `isProcessing` state'leri `false` olarak ayarlanır, `progress` state'i `0`'a sıfırlanır. `react-native`'in `Alert` modülü veya bir toast/snackbar bileşeni kullanılarak kullanıcıya hatanın kaynağı hakkında bilgi verilir (örn: 'Dosya yüklenemedi, lütfen internet bağlantınızı kontrol edin.'). 
- **Tahmini Süre:** 2 saat
- **Kabul Kriterleri:** Geçersiz bir dosyayla, internet bağlantısı olmadan veya backend bir hata (örn: 500) döndürdüğünde işlem iptal edilmeli, tüm ilerleme göstergeleri gizlenmeli ve kullanıcıya bir hata mesajı gösterilmelidir. Arayüz, yeni bir denemeye hazır hale gelmelidir.

