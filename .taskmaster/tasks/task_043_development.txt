# Task ID: 43
# Title: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Offline) Gösterge Banner'ı
# Status: pending
# Dependencies: 42
# Priority: medium
# Description: Cihaz çevrimdışı olduğunda kullanıcıyı bilgilendirmek için ekranın üst kısmında kalıcı bir bildirim (banner) gösterilmesi.
# Details:
Ağ durumunu tutan global state'i dinleyen bir `OfflineIndicator` bileşeni oluşturun. Cihaz çevrimdışı olduğunda (`isConnected === false`), ekranın en üstünde 'İnternet bağlantısı yok' gibi bir mesaj içeren, dikkat çekici ama rahatsız etmeyen bir banner gösterin. Bağlantı geri geldiğinde bu banner otomatik olarak kaybolmalıdır.

# Test Strategy:
Cihazın internet bağlantısını kesin. Çevrimdışı göstergesinin hemen göründüğünü doğrulayın. İnternet bağlantısını geri getirin ve göstergenin kaybolduğunu kontrol edin. Bu banner'ın uygulamanın kullanılabilirliğini engellemediğinden emin olun.
