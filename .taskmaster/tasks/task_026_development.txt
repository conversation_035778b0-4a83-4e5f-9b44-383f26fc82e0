# Task ID: 26
# Title: Backend Bağımlılıklarının <PERSON>u
# Status: pending
# Dependencies: None
# Priority: high
# Description: Projenin backend'i için PRD'de belirtilen eksik NPM paketlerinin kurulması ve yapılandırılması. Bu, projenin temel işlevselliği için kritik bir adımdır.
# Details:
Proje kök dizininde `npm install @google/generative-ai exceljs node-cache express-slow-down` komutunu çalıştırın. Paketlerin en son kararlı sürümlerinin yüklendiğinden emin olun. Kurulum sonrası `package.json` ve `package-lock.json` dosyalarını kontrol ederek bağımlılıkların doğru bir şekilde eklendiğini doğrulayın.

# Test Strategy:
Tüm bağımlılıkların başar<PERSON>yla kurulduğunu ve `npm start` komutuyla uygulamanın hatasız başladığ<PERSON>n<PERSON> doğrulayın. Her paketin temel bir fonksiyonunu (<PERSON><PERSON><PERSON><PERSON>, `node-cache` için bir değer set/get etme) test eden basit bir script yazın.

# Subtasks:
## 1. Temel NPM Paketlerinin Kurulumu [pending]
### Dependencies: None
### Description: Projenin backend'i için gereken @google/generative-ai, exceljs, node-cache ve express-slow-down NPM paketlerinin kurulumunu gerçekleştirir.
### Details:
Projenin `backend/` dizininde `npm install @google/generative-ai exceljs node-cache express-slow-down` komutunu çalıştırın. Bu işlem, paketlerin en son kararlı sürümlerini `node_modules` dizinine yükleyecek ve `package.json` dosyasını güncelleyecektir. Tahmini Süre: 10 dakika. Kabul Kriterleri: Komutun hatasız bir şekilde tamamlanması ve `backend/node_modules` dizininde ilgili paket klasörlerinin oluşturulması. Risk Faktörleri: NPM registry'ye erişim sorunları, internet bağlantısı kesintileri.

## 2. Bağımlılık Kurulumunun Doğrulanması ve Versiyon Kontrolü [pending]
### Dependencies: 26.1
### Description: Kurulum sonrası `package.json` ve `package-lock.json` dosyalarını kontrol ederek bağımlılıkların doğru bir şekilde eklendiğini ve sürümlerin kilitlendiğini doğrular.
### Details:
`backend/package.json` dosyasının `dependencies` bölümünü inceleyerek 4 yeni paketin eklendiğini kontrol edin. `backend/package-lock.json` dosyasında yapılan değişiklikleri gözden geçirerek kurulumun deterministik olduğundan emin olun. Tahmini Süre: 5 dakika. Kabul Kriterleri: `package.json` dosyasında ilgili bağımlılıkların `^x.y.z` formatında yer alması. `package-lock.json` dosyasının güncellenmiş olması ve bu değişikliklerin versiyon kontrol sistemine (örn: Git) commit edilmeye hazır olması. Risk Faktörleri: `package-lock.json` dosyasındaki olası çakışmalar (conflicts).

## 3. Rate Limiting için `express-slow-down` Yapılandırması [pending]
### Dependencies: 26.1
### Description: API'ye yönelik brute-force saldırılarını önlemek ve kaynak kullanımını dengelemek amacıyla `express-slow-down` paketini bir middleware olarak Express uygulamasına entegre eder.
### Details:
`backend/src/app.js` (veya ana sunucu dosyası) içine `const slowDown = require('express-slow-down');` satırını ekleyin. Ardından, `const apiLimiter = slowDown({ windowMs: 15 * 60 * 1000, delayAfter: 100, delayMs: () => 500 });` gibi bir yapılandırma oluşturun ve `app.use('/api/', apiLimiter);` ile belirli route'lara uygulayın. Tahmini Süre: 20 dakika. Kabul Kriterleri: Belirlenen istek limitini (delayAfter) aşan API çağrılarının yanıt süresinde gözle görülür bir gecikme (delayMs) olması. Risk Faktörleri: Yanlış yapılandırma sonucu tüm isteklerin yavaşlaması veya limitlendirmenin hiç çalışmaması.

## 4. `node-cache` için Soyutlanmış Önbellek Servisi Oluşturma [pending]
### Dependencies: 26.1
### Description: Sık erişilen verileri bellekte tutarak veritabanı yükünü azaltmak için `node-cache` paketini kullanan yeniden kullanılabilir bir servis modülü oluşturur.
### Details:
`backend/src/services/cacheService.js` adında yeni bir dosya oluşturun. İçerisinde `const NodeCache = require('node-cache');` ile paketi import edin ve `const cache = new NodeCache({ stdTTL: 3600 });` gibi bir instance oluşturun. Bu instance'ı kullanarak `get`, `set`, `del` gibi temel önbellek fonksiyonlarını export eden bir modül tasarlayın. Tahmini Süre: 25 dakika. Kabul Kriterleri: `cacheService` modülünün `set` ile veri ekleyip `get` ile bu veriyi başarıyla geri döndürmesi. Belirlenen TTL (Time-To-Live) süresi sonunda verinin otomatik olarak silinmesi. Risk Faktörleri: Bellek sızıntıları, yanlış TTL yapılandırması.

## 5. AI ve Excel Servisleri için Başlangıç İskeletlerinin Oluşturulması [pending]
### Dependencies: 26.1
### Description: `@google/generative-ai` ve `exceljs` paketleri için gelecekteki geliştirmelere zemin hazırlayacak temel servis dosyalarını ve başlangıç yapılandırmalarını oluşturur.
### Details:
`backend/src/services/` dizini altında `aiService.js` ve `excelService.js` adında iki yeni dosya oluşturun. `aiService.js` içinde, `@google/generative-ai` paketini import edin ve API anahtarını `.env` dosyasından alacak şekilde temel bir client instance'ı oluşturun. `excelService.js` içinde, `exceljs` paketini import edin ve boş bir Excel dosyası oluşturup kaydedecek basit bir fonksiyon iskeleti (`createReport`) yazın. Tahmini Süre: 20 dakika. Kabul Kriterleri: Her iki servis dosyasının da ilgili paketleri hatasız bir şekilde import etmesi ve temel başlangıç yapılandırmalarını içermesi. Risk Faktörleri: API anahtarı gibi hassas bilgilerin koda yazılması (hard-coding).

