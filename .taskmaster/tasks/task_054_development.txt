# Task ID: 54
# Title: Frontend Hata Yönetimi Standardizasyonu
# Status: pending
# Dependencies: 41
# Priority: medium
# Description: Uygulama genelinde tutarlı ve kullanıcı dostu hata yönetimi için bir çerçeve oluşturulması. <PERSON><PERSON> g<PERSON><PERSON><PERSON>, a<PERSON>alarını, API yanıt hatalarını ve çevrimdışı durumları ele alacak standart bileşenler ve mantıklar geliştirmeyi içerir.
# Details:
Task 41'de oluşturulan global state yönetimi (Zustand/Context) altyapısını kullanarak global bir hata durumu (`error`, `setError`) oluşturun. Bu state, hata mesajını ve isteğe bağlı olarak bir 'tekrar dene' (retry) fonksiyonunu tutmalıdır. Tüm API çağrılarını sarmalamak için bir axios interceptor'ı veya bir yardımcı fonksiyon geliştirin. <PERSON><PERSON> ya<PERSON><PERSON>, ağ hatalarını (`TypeError: Network request failed`) ve API'den dönen başarısız durum kodlarını (4xx, 5xx) yakalamalı ve bunları global hata durumuna işlemelidir. Yakalanan teknik hataları, 'Sunucuya bağlanılamadı' veya 'Bir şeyler ters gitti' gibi kullanıcı dostu mesajlara dönüştürün. Global hata durumuna bağlı olarak bir modal veya toast (`GlobalErrorDisplay`) bileşeni oluşturun. Bu bileşen hata mesajını, bir 'Kapat' butonunu ve varsa bir 'Tekrar Dene' butonunu göstermelidir. Ayrıca, `react-native-netinfo` kütüphanesini kullanarak internet bağlantısı kesildiğinde ekranın üst kısmında kalıcı bir 'Çevrimdışı' uyarısı gösteren bir `OfflineIndicator` banner'ı implemente edin.

# Test Strategy:
Cihazın internetini kapatıp bir API isteği tetikleyerek ağ hatası senaryosunu test edin; 'Sunucuya bağlanılamadı' mesajının ve 'Tekrar Dene' butonunun göründüğünü doğrulayın. İnterneti tekrar açıp 'Tekrar Dene' butonuna basıldığında isteğin başarılı olması ve hata mesajının kaybolması gerekir. Kasıtlı olarak 500 hatası döndüren bir API endpoint'i ile sunucu hatası senaryosunu test edin ve ilgili genel hata mesajının gösterildiğini kontrol edin. Uygulama çalışırken cihazın Wi-Fi/mobil verisini kapatıp açarak 'Çevrimdışı' banner'ının doğru zamanda görünüp kaybolduğunu doğrulayın.
