#!/bin/bash

# Test script for emoji validation in season creation API
# Tests the fix for migration validation errors

API_BASE_URL="http://localhost:3000/api/v1"

echo "🧪 Emoji Validation Test Başlıyor..."
echo ""

# Test 1: Migration Emoji (🌱)
echo "📝 Test 1: Migration Emoji (🌱)"
MOCK_TOKEN_1="Bearer dev-test-token-emoji-1-$(date +%s)"

TEST_DATA_1='{
  "name": "Test Season Migration Emoji",
  "description": "Migration emoji test",
  "startDate": "'$(date -u +%Y-%m-%dT%H:%M:%S.000Z)'",
  "isActive": true,
  "isDefault": false,
  "color": "#4CAF50",
  "emoji": "🌱"
}'

echo "Gönderilen veri:"
echo "$TEST_DATA_1"
echo ""

RESPONSE_1=$(curl -s -w "\nHTTP_STATUS:%{http_code}" \
  -X POST \
  -H "Authorization: $MOCK_TOKEN_1" \
  -H "Content-Type: application/json" \
  -d "$TEST_DATA_1" \
  "$API_BASE_URL/seasons")

HTTP_STATUS_1=$(echo "$RESPONSE_1" | grep "HTTP_STATUS:" | cut -d: -f2)
RESPONSE_BODY_1=$(echo "$RESPONSE_1" | sed '/HTTP_STATUS:/d')

echo "HTTP Status: $HTTP_STATUS_1"
echo "Response:"
echo "$RESPONSE_BODY_1" | jq . 2>/dev/null || echo "$RESPONSE_BODY_1"

echo ""
echo "=================================================="
echo ""

# Test 2: Different Emojis
echo "📝 Test 2: Farklı Emoji'ler"
MOCK_TOKEN_2="Bearer dev-test-token-emoji-2-$(date +%s)"

TEST_DATA_2='{
  "name": "Test Season Different Emojis",
  "description": "Farklı emoji test",
  "startDate": "'$(date -u +%Y-%m-%dT%H:%M:%S.000Z)'",
  "isActive": true,
  "isDefault": false,
  "color": "#FF5722",
  "emoji": "🚜"
}'

echo "Gönderilen veri:"
echo "$TEST_DATA_2"
echo ""

RESPONSE_2=$(curl -s -w "\nHTTP_STATUS:%{http_code}" \
  -X POST \
  -H "Authorization: $MOCK_TOKEN_2" \
  -H "Content-Type: application/json" \
  -d "$TEST_DATA_2" \
  "$API_BASE_URL/seasons")

HTTP_STATUS_2=$(echo "$RESPONSE_2" | grep "HTTP_STATUS:" | cut -d: -f2)
RESPONSE_BODY_2=$(echo "$RESPONSE_2" | sed '/HTTP_STATUS:/d')

echo "HTTP Status: $HTTP_STATUS_2"
echo "Response:"
echo "$RESPONSE_BODY_2" | jq . 2>/dev/null || echo "$RESPONSE_BODY_2"

echo ""
echo "=================================================="
echo ""

# Test 3: Face Emoji
echo "📝 Test 3: Yüz Emoji'si"
MOCK_TOKEN_3="Bearer dev-test-token-emoji-3-$(date +%s)"

TEST_DATA_3='{
  "name": "Test Season Face Emoji",
  "description": "Yüz emoji test",
  "startDate": "'$(date -u +%Y-%m-%dT%H:%M:%S.000Z)'",
  "isActive": true,
  "isDefault": false,
  "color": "#2196F3",
  "emoji": "😊"
}'

echo "Gönderilen veri:"
echo "$TEST_DATA_3"
echo ""

RESPONSE_3=$(curl -s -w "\nHTTP_STATUS:%{http_code}" \
  -X POST \
  -H "Authorization: $MOCK_TOKEN_3" \
  -H "Content-Type: application/json" \
  -d "$TEST_DATA_3" \
  "$API_BASE_URL/seasons")

HTTP_STATUS_3=$(echo "$RESPONSE_3" | grep "HTTP_STATUS:" | cut -d: -f2)
RESPONSE_BODY_3=$(echo "$RESPONSE_3" | sed '/HTTP_STATUS:/d')

echo "HTTP Status: $HTTP_STATUS_3"
echo "Response:"
echo "$RESPONSE_BODY_3" | jq . 2>/dev/null || echo "$RESPONSE_BODY_3"

echo ""
echo "=================================================="
echo ""

# Test 4: Invalid Emoji (should fail)
echo "📝 Test 4: Geçersiz Emoji (başarısız olmalı)"
MOCK_TOKEN_4="Bearer dev-test-token-emoji-4-$(date +%s)"

TEST_DATA_4='{
  "name": "Test Season Invalid Emoji",
  "description": "Geçersiz emoji test",
  "startDate": "'$(date -u +%Y-%m-%dT%H:%M:%S.000Z)'",
  "isActive": true,
  "isDefault": false,
  "color": "#9C27B0",
  "emoji": "invalid_text"
}'

echo "Gönderilen veri:"
echo "$TEST_DATA_4"
echo ""

RESPONSE_4=$(curl -s -w "\nHTTP_STATUS:%{http_code}" \
  -X POST \
  -H "Authorization: $MOCK_TOKEN_4" \
  -H "Content-Type: application/json" \
  -d "$TEST_DATA_4" \
  "$API_BASE_URL/seasons")

HTTP_STATUS_4=$(echo "$RESPONSE_4" | grep "HTTP_STATUS:" | cut -d: -f2)
RESPONSE_BODY_4=$(echo "$RESPONSE_4" | sed '/HTTP_STATUS:/d')

echo "HTTP Status: $HTTP_STATUS_4"
echo "Response:"
echo "$RESPONSE_BODY_4" | jq . 2>/dev/null || echo "$RESPONSE_BODY_4"

echo ""
echo "🏁 Test tamamlandı!"

# Test sonuçlarını değerlendir
SUCCESS_COUNT=0
if [ "$HTTP_STATUS_1" = "201" ]; then
    echo "✅ Test 1 (🌱) başarılı!"
    SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
else
    echo "❌ Test 1 (🌱) başarısız!"
fi

if [ "$HTTP_STATUS_2" = "201" ]; then
    echo "✅ Test 2 (🚜) başarılı!"
    SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
else
    echo "❌ Test 2 (🚜) başarısız!"
fi

if [ "$HTTP_STATUS_3" = "201" ]; then
    echo "✅ Test 3 (😊) başarılı!"
    SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
else
    echo "❌ Test 3 (😊) başarısız!"
fi

if [ "$HTTP_STATUS_4" = "400" ] || [ "$HTTP_STATUS_4" = "422" ]; then
    echo "✅ Test 4 (invalid) doğru şekilde reddedildi!"
    SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
else
    echo "❌ Test 4 (invalid) beklenmedik sonuç: $HTTP_STATUS_4"
fi

echo ""
echo "📊 Sonuç: $SUCCESS_COUNT/4 test başarılı"

if [ "$SUCCESS_COUNT" = "4" ]; then
    echo "🎉 Tüm testler başarılı!"
    exit 0
else
    echo "⚠️  Bazı testler başarısız"
    exit 1
fi
